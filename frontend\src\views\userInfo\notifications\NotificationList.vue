<template>
  <div class="notification-list-page">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <BellOutlined />
          通知中心
        </h1>
        <p class="page-subtitle">查看和管理所有系统通知</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ statistics.total_count }}</span>
          <span class="stat-label">总通知</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ statistics.unread_count }}</span>
          <span class="stat-label">未读</span>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="unified-filter-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="filters.notificationType" placeholder="通知类型" allowClear class="filter-select"
            size="large" @change="handleFilterChange">
            <a-select-option value="approval">审批通知</a-select-option>
            <a-select-option value="purchase">采购通知</a-select-option>
            <a-select-option value="acceptance">验收通知</a-select-option>
            <a-select-option value="reimbursement">报销通知</a-select-option>
            <a-select-option value="rejection">驳回通知</a-select-option>
            <a-select-option value="return">退回通知</a-select-option>
            <a-select-option value="system">系统通知</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="filters.priority" placeholder="优先级" allowClear class="filter-select" size="large"
            @change="handleFilterChange">
            <a-select-option value="urgent">紧急</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="normal">普通</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="filters.isRead" placeholder="阅读状态" allowClear size="large" class="filter-select"
            @change="handleFilterChange">
            <a-select-option :value="false">未读</a-select-option>
            <a-select-option :value="true">已读</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <a-range-picker v-model:value="filters.dateRange" class="filter-input" :placeholder="['开始时间', '结束时间']"
            show-time size="large" format="YYYY-MM-DD HH:mm:ss" />
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="3">
          <a-button @click="handleSearch" class="secondary-action-btn">
            <SearchOutlined />
            查询
          </a-button>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="3">
          <a-button @click="handleReset" class="secondary-action-btn">
            <ReloadOutlined />
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-buttons-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col>
          <a-button @click="markAllAsRead" class="primary-action-btn" :disabled="statistics.unread_count === 0">
            <CheckOutlined />
            全部标记为已读
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="handleRefresh" class="secondary-action-btn">
            <ReloadOutlined />
            刷新数据
          </a-button>
        </a-col>

        <!-- 批量操作按钮 -->
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button @click="deleteSelected" class="primary-action-btn">
            <DeleteOutlined />
            删除选中 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 通知列表 -->
    <div class="table-section business-card">
      <a-table :columns="columns" :data-source="notifications" :loading="loading" :pagination="paginationConfig"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange" row-key="id"
        class="unified-table" size="large" :scroll="{ x: 1400 }">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <div class="notification-title-cell">
              <a-badge v-if="!record.is_read" status="processing" />
              <span :class="{ 'unread-title': !record.is_read }" @click="handleNotificationClick(record)">
                {{ record.title }}
              </span>
            </div>
          </template>
          <template v-else-if="column.key === 'notification_type'">
            <a-tag :color="getNotificationTypeColor(record.notification_type)">
              {{ record.notification_type_display }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ record.priority_display }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'is_read'">
            <a-tag :color="record.is_read ? 'green' : 'orange'">
              {{ record.is_read ? "已读" : "未读" }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button v-if="!record.is_read" type="link" size="small" @click="markAsRead(record)">
                <CheckOutlined />
                标记已读
              </a-button>
              <a-button type="link" size="small" @click="viewDetail(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-popconfirm title="确定要删除这条通知吗？" @confirm="deleteNotification(record.id)">
                <a-button type="link" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 通知详情模态框 -->
    <a-modal v-model:open="detailVisible" title="通知详情" :footer="null" width="700px">
      <div v-if="currentNotification" class="notification-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="通知标题" :span="2">
            <div class="notification-title-detail">
              <a-badge v-if="!currentNotification.is_read" status="processing" />
              <span :class="{ 'unread-title': !currentNotification.is_read }">
                {{ currentNotification.title }}
              </span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="通知类型">
            <a-tag :color="getNotificationTypeColor(currentNotification.notification_type)
              ">
              {{ currentNotification.notification_type_display }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentNotification.priority)">
              {{ currentNotification.priority_display }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="阅读状态">
            <a-tag :color="currentNotification.is_read ? 'green' : 'orange'">
              <CheckCircleOutlined v-if="currentNotification.is_read" />
              <ClockCircleOutlined v-else />
              {{ currentNotification.is_read ? "已读" : "未读" }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发送者">
            <span v-if="currentNotification.sender">
              {{
                currentNotification.sender.username ||
                currentNotification.sender.name
              }}
            </span>
            <span v-else class="text-muted">系统</span>
          </a-descriptions-item>
          <a-descriptions-item label="通知内容" :span="2">
            <div class="notification-content-detail">
              {{ currentNotification.content }}
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            <span class="time-text">{{
              formatDateTime(currentNotification.created_at)
              }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="阅读时间">
            <span v-if="currentNotification.read_at" class="time-text">
              {{ formatDateTime(currentNotification.read_at) }}
            </span>
            <span v-else class="text-muted">未读</span>
          </a-descriptions-item>
          <a-descriptions-item label="相关链接" :span="2"
            v-if="currentNotification.target_url || (currentNotification.target_model && currentNotification.target_id)">
            <a-button type="link" @click="goToTarget" class="target-link-btn">
              <LinkOutlined />
              查看相关内容
            </a-button>
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <template #footer>
        <div class="detail-footer">
          <a-button @click="detailVisible = false">关闭</a-button>
          <a-button v-if="currentNotification && !currentNotification.is_read" type="primary"
            @click="markAsReadFromDetail">
            <CheckOutlined />
            标记为已读
          </a-button>
          <a-button
            v-if="currentNotification && (currentNotification.target_url || (currentNotification.target_model && currentNotification.target_id))"
            type="primary" @click="goToTarget">
            <LinkOutlined />
            查看相关内容
          </a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import { message } from "ant-design-vue";
import {
  BellOutlined,
  SearchOutlined,
  ReloadOutlined,
  CheckOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  LinkOutlined,
  EyeOutlined,
} from "@ant-design/icons-vue";
import api from "@/api";

const router = useRouter();
const route = useRoute();
const store = useStore();

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);

const currentNotification = ref(null);
const notifications = ref([]);
const selectedRowKeys = ref([]);
const users = ref([]);
const statistics = ref({
  total_count: 0,
  unread_count: 0,
});

// 筛选条件
const filters = reactive({
  notificationType: undefined,
  priority: undefined,
  isRead: undefined,
  dateRange: undefined,
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
});

const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: pagination.showSizeChanger,
  showQuickJumper: pagination.showQuickJumper,
  showTotal: pagination.showTotal,
  pageSizeOptions: ["10", "20", "50", "100"],
}));

// 表格列配置
const columns = [
  {
    title: "序号",
    key: "index",
    width: 60,
    customRender: ({ index }) =>
      pagination.pageSize * (pagination.current - 1) + index + 1,
  },
  {
    title: "通知标题",
    dataIndex: "title",
    key: "title",
    width: 250,
    ellipsis: true,
  },
  {
    title: "通知类型",
    dataIndex: "notification_type",
    key: "notification_type",
    width: 100,
  },
  {
    title: "优先级",
    dataIndex: "priority",
    key: "priority",
    width: 80,
  },
  {
    title: "阅读状态",
    dataIndex: "is_read",
    key: "is_read",
    width: 80,
  },
  {
    title: "发送时间",
    dataIndex: "time_ago",
    key: "created_at",
    width: 130,
  },
  {
    title: "操作",
    key: "action",
    width: 200,
    fixed: "right",
  },
];

// 通知类型颜色映射
const getNotificationTypeColor = (type) => {
  const colors = {
    approval: "blue",
    purchase: "green",
    acceptance: "orange",
    reimbursement: "purple",
    rejection: "red",
    return: "volcano",
    system: "default",
  };
  return colors[type] || "default";
};

// 优先级颜色映射
const getPriorityColor = (priority) => {
  const colors = {
    low: "default",
    normal: "blue",
    high: "orange",
    urgent: "red",
  };
  return colors[priority] || "default";
};

// 获取通知列表
const getNotifications = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
    };

    // 处理筛选参数，映射前端字段名到后端字段名
    if (filters.notificationType) {
      params.notification_type = filters.notificationType;
    }
    if (filters.priority) {
      params.priority = filters.priority;
    }
    if (filters.isRead !== undefined) {
      params.is_read = filters.isRead;
    }

    // 处理日期范围
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0].format("YYYY-MM-DD HH:mm:ss");
      params.end_date = filters.dateRange[1].format("YYYY-MM-DD HH:mm:ss");
    }

    const response = await api.notifications.getList(params);
    if (response.code === 200) {
      notifications.value = response.data.results || [];
      pagination.total = response.data.count || 0;
    }
  } catch (error) {
    console.error("获取通知列表失败:", error);
    message.error("获取通知列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  getNotifications();
};

// 重置
const handleReset = () => {
  Object.keys(filters).forEach((key) => {
    filters[key] = undefined;
  });
  pagination.current = 1;
  getNotifications();
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  getNotifications();
};

// 筛选变化处理
const handleFilterChange = () => {
  pagination.current = 1;
  getNotifications();
};

// 选择变化
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 处理通知点击
const handleNotificationClick = async (notification) => {
  try {
    // 标记为已读
    if (!notification.is_read) {
      await markAsRead(notification);
    }

    // 打开详情模态框
    viewDetail(notification);
  } catch (error) {
    console.error("处理通知点击失败:", error);
    message.error("操作失败，请稍后重试");
  }
};

// 标记为已读
const markAsRead = async (notification) => {
  try {
    const response = await api.notifications.markAsRead(notification.id);
    // 检查响应格式
    if (response && (response.code === 200 || response.status === 200)) {
      notification.is_read = true;
      // 重新获取统计信息以确保准确性
      await getStatistics();
      message.success("已标记为已读");
    } else {
      throw new Error(response?.message || "标记已读失败");
    }
  } catch (error) {
    console.error("标记已读失败:", error);
    message.error(`标记已读失败: ${error.message || "未知错误"}`);
  }
};

// 全部标记为已读
const markAllAsRead = async () => {
  try {
    const response = await api.notifications.markAllAsRead();
    // 检查响应格式
    if (response && (response.code === 200 || response.status === 200)) {
      notifications.value.forEach((n) => {
        n.is_read = true;
      });
      // 重新获取统计信息以确保准确性
      await getStatistics();
      message.success("所有通知已标记为已读");
    } else {
      throw new Error(response?.message || "标记已读失败");
    }
  } catch (error) {
    console.error("标记已读失败:", error);
    message.error(`标记已读失败: ${error.message || "未知错误"}`);
  }
};

// 查看详情
const viewDetail = (notification) => {
  currentNotification.value = notification;
  detailVisible.value = true;
};

// 从详情页面标记为已读
const markAsReadFromDetail = async () => {
  if (currentNotification.value && !currentNotification.value.is_read) {
    await markAsRead(currentNotification.value);
    detailVisible.value = false;
  }
};

// 跳转到相关内容
const goToTarget = () => {
  if (!currentNotification.value) {
    message.error('通知信息不完整');
    return;
  }

  const notification = currentNotification.value;

  try {
    // 关闭详情模态框
    detailVisible.value = false;

    // 根据目标模型和ID构建跳转路径
    if (notification.target_model && notification.target_id) {
      const targetModel = notification.target_model.toLowerCase();
      const targetId = notification.target_id;

      // 根据不同的模型类型跳转到对应页面
      if (targetModel === 'purchaserequest' || targetModel === 'purchase_request') {
        // 直接跳转到需求提报详情页面
        router.push({
          path: `/purchase/requests/${targetId}`,
          query: {
            from: 'notification'
          }
        });
      } else if (targetModel === 'acceptance') {
        // 跳转到验收管理页面
        router.push({
          path: '/purchase/acceptance',
          query: {
            detail: targetId,
            from: 'notification'
          }
        });
      } else if (targetModel === 'reimbursement') {
        // 跳转到报销管理页面
        router.push({
          path: '/purchase/reimbursement',
          query: {
            detail: targetId,
            from: 'notification'
          }
        });
      } else {
        // 默认跳转到采购总览页面
        router.push({
          path: '/purchase/overview',
          query: {
            detail: targetId,
            from: 'notification'
          }
        });
      }
    } else if (notification.target_url) {
      // 如果有target_url，使用原来的逻辑
      const targetUrl = notification.target_url.trim();

      if (targetUrl.startsWith("/")) {
        // 内部路由
        router.push(targetUrl);
      } else if (targetUrl.startsWith("http")) {
        // 外部链接
        window.open(targetUrl, "_blank");
      } else {
        // 相对路径，添加前缀
        router.push(`/${targetUrl}`);
      }
    } else {
      message.warning('该通知没有关联的内容');
    }
  } catch (error) {
    console.error("跳转失败:", error);
    message.error("页面跳转失败");
  }
};

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// 删除通知
const deleteNotification = async (notification) => {
  try {
    await api.notifications.delete(notification.id);
    message.success("删除成功");
    getNotifications();
  } catch (error) {
    console.error("删除失败:", error);
    message.error("删除失败");
  }
};

// 刷新数据
const handleRefresh = () => {
  getNotifications();
  getStatistics(); // 同时刷新统计信息
  message.success("数据已刷新");
};

// 删除选中
const deleteSelected = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要删除的通知");
    return;
  }

  try {
    await api.notifications.batchDelete(selectedRowKeys.value);
    message.success(`已删除 ${selectedRowKeys.value.length} 条通知`);
    selectedRowKeys.value = [];
    getNotifications();
  } catch (error) {
    console.error("批量删除失败:", error);
    message.error("批量删除失败");
  }
};

// 获取统计信息
const getStatistics = async () => {
  try {
    const response = await api.notifications.getStatistics();
    if (response.code === 200) {
      statistics.value = {
        total_count: response.data.total_count || 0,
        unread_count: response.data.unread_count || 0,
      };
    }
  } catch (error) {
    console.error("获取统计信息失败:", error);
    // 如果统计API失败，设置默认值
    statistics.value = {
      total_count: 0,
      unread_count: 0,
    };
  }
};

// 获取用户列表
const getUsers = async () => {
  try {
    const response = await api.users.getList({ page_size: 1000 });
    if (response.code === 200) {
      users.value = response.data.results || [];
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
  }
};

// 生命周期
onMounted(async () => {
  // 确保权限数据已加载
  if (!store.getters.userPermissions || store.getters.userPermissions.length === 0) {
    try {
      await store.dispatch('getUserPermissions');
    } catch (error) {
      console.error('获取权限失败:', error);
    }
  }

  await getNotifications();
  await getStatistics(); // 独立获取统计信息
  await getUsers();

  // 检查是否从通知中心跳转过来，需要打开详情
  const detailId = route.query.detail;
  if (detailId && route.query.from === 'notification-center') {
    // 延迟一下确保数据加载完成
    setTimeout(() => {
      const targetNotification = notifications.value.find(item => item.id == detailId);
      if (targetNotification) {
        viewDetail(targetNotification);
      } else {
        // 如果当前页面没有找到，尝试通过API获取
        fetchAndViewNotificationDetail(detailId);
      }
    }, 500);
  }
});

// 获取并查看指定ID的通知详情
const fetchAndViewNotificationDetail = async (id) => {
  try {
    // 使用单个通知详情API
    const response = await api.notifications.getDetail(id);

    if (response.code === 200) {
      const targetNotification = response.data;
      // 将通知添加到列表中（如果不存在）
      const existingIndex = notifications.value.findIndex(item => item.id == id);
      if (existingIndex === -1) {
        notifications.value.unshift(targetNotification);
      } else {
        notifications.value[existingIndex] = targetNotification;
      }
      // 打开详情
      viewDetail(targetNotification);
    } else {
      message.error('获取通知详情失败');
    }
  } catch (error) {
    console.error('获取通知详情失败:', error);
    message.error('获取通知详情失败');
  }
};


</script>

<style scoped>
.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.filter-select {
  width: 200px;
}

.secondary-action-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-overlay);
}

.notification-list-page {
  padding: 0px;
}

/* 表格区域样式 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* 表格样式已在统一样式文件中定义 */

/* 分页样式 - 参考日志管理页面 */
:deep(.ant-pagination) {
  margin: var(--space-lg) 0 0 0;
  text-align: right;
  padding: var(--space-md) 0;
}

:deep(.ant-pagination .ant-pagination-item) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s;
  margin: 0 4px;
}

:deep(.ant-pagination .ant-pagination-item:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

:deep(.ant-pagination .ant-pagination-item-active) {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

:deep(.ant-pagination .ant-pagination-item-active a) {
  color: var(--text-inverse);
  font-weight: 600;
}

:deep(.ant-pagination .ant-pagination-prev),
:deep(.ant-pagination .ant-pagination-next) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s;
}

:deep(.ant-pagination .ant-pagination-prev:hover),
:deep(.ant-pagination .ant-pagination-next:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

:deep(.ant-pagination .ant-pagination-options) {
  margin-left: var(--space-md);
}

:deep(.ant-pagination .ant-pagination-options .ant-select) {
  margin-right: var(--space-sm);
}

:deep(.ant-pagination .ant-pagination-total-text) {
  margin-right: var(--space-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .notification-list-page {
    padding: var(--space-sm);
  }

  .table-section {
    margin: 0 -var(--space-xs);
  }
}

.notification-title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-title-cell span {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 2px 4px;
  border-radius: 4px;
}

.notification-title-cell span:hover {
  background-color: var(--bg-hover);
  color: var(--primary-color);
}

.unread-title {
  font-weight: 600;
  color: var(--primary-color);
}

.unread-title:hover {
  background-color: var(--primary-bg-hover);
  color: var(--primary-color-hover);
}

/* 通知详情样式 */
.notification-detail {
  padding: 16px 0;
}

.notification-title-detail {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-title-detail .unread-title {
  font-weight: 600;
  color: #1890ff;
}

.notification-content-detail {
  color: #595959;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  background: #fafafa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.time-text {
  color: #8c8c8c;
  font-size: 13px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
}

.text-muted {
  color: #bfbfbf;
}

.target-link-btn {
  color: #1890ff;
  padding: 0;
}

.target-link-btn:hover {
  color: #40a9ff;
}

.detail-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 详情模态框内的描述列表样式优化 */
:deep(.notification-detail .ant-descriptions-item-label) {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  width: 120px;
}

:deep(.notification-detail .ant-descriptions-item-content) {
  background: white;
  color: #262626;
}

:deep(.notification-detail .ant-descriptions-bordered .ant-descriptions-item) {
  padding: 12px 16px;
}

:deep(.notification-detail .ant-descriptions) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
