"""
自定义权限类
"""
from rest_framework import permissions


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    只有创建者可以编辑，其他人只能查看
    """
    
    def has_object_permission(self, request, view, obj):
        # 读取权限对所有人开放
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写入权限只对创建者开放
        return obj.requester == request.user


class IsApproverOrReadOnly(permissions.BasePermission):
    """
    只有审批人可以审批，其他人只能查看
    """
    
    def has_permission(self, request, view):
        # 检查用户是否有审批权限
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 这里可以根据用户角色判断是否有审批权限
        return hasattr(request.user, 'role') and request.user.role in ['admin', 'approver']


class IsAcceptorOrReadOnly(permissions.BasePermission):
    """
    只有验收人可以验收，其他人只能查看
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return hasattr(request.user, 'role') and request.user.role in ['admin', 'acceptor']


class IsFinanceOrReadOnly(permissions.BasePermission):
    """
    只有财务人员可以结算，其他人只能查看
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return hasattr(request.user, 'role') and request.user.role in ['admin', 'finance']
