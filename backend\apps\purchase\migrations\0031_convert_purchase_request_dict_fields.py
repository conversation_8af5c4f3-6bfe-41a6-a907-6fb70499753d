# Generated by Django 5.2.4 on 2025-07-17 11:55

from django.db import migrations


def convert_purchase_request_dict_fields(apps, schema_editor):
    """将采购请求表中的字典字段从英文代码转换为中文名称"""
    PurchaseRequest = apps.get_model('purchase', 'PurchaseRequest')
    Dictionary = apps.get_model('purchase', 'Dictionary')

    # 构建字典映射
    dict_mappings = {}
    dict_types = ['status', 'item_category', 'unit', 'requirement_source',
                  'procurement_method', 'fund_project', 'purchase_type']

    for dict_type in dict_types:
        dict_items = Dictionary.objects.filter(type=dict_type, status=True)
        dict_mappings[dict_type] = {}
        for item in dict_items:
            dict_mappings[dict_type][item.code] = item.name

    # 转换采购请求表中的字典字段
    requests = PurchaseRequest.objects.all()
    updated_count = 0

    for request in requests:
        updated = False

        # 转换status字段
        if request.status and request.status in dict_mappings['status']:
            request.status = dict_mappings['status'][request.status]
            updated = True

        # 转换item_category字段
        if request.item_category and request.item_category in dict_mappings['item_category']:
            request.item_category = dict_mappings['item_category'][request.item_category]
            updated = True

        # 转换unit字段
        if request.unit and request.unit in dict_mappings['unit']:
            request.unit = dict_mappings['unit'][request.unit]
            updated = True

        # 转换requirement_source字段
        if request.requirement_source and request.requirement_source in dict_mappings['requirement_source']:
            request.requirement_source = dict_mappings['requirement_source'][request.requirement_source]
            updated = True

        # 转换procurement_method字段
        if request.procurement_method and request.procurement_method in dict_mappings['procurement_method']:
            request.procurement_method = dict_mappings['procurement_method'][request.procurement_method]
            updated = True

        # 转换fund_project字段
        if request.fund_project and request.fund_project in dict_mappings['fund_project']:
            request.fund_project = dict_mappings['fund_project'][request.fund_project]
            updated = True

        # 转换purchase_type字段
        if request.purchase_type and request.purchase_type in dict_mappings['purchase_type']:
            request.purchase_type = dict_mappings['purchase_type'][request.purchase_type]
            updated = True

        if updated:
            request.save()
            updated_count += 1

    print(f"转换完成，共更新了 {updated_count} 条记录")


def reverse_convert_purchase_request_dict_fields(apps, schema_editor):
    """回滚操作 - 这里不实现具体的回滚逻辑，因为原始数据可能丢失"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0030_fix_dictionary_type_normalization"),
    ]

    operations = [
        migrations.RunPython(
            convert_purchase_request_dict_fields,
            reverse_convert_purchase_request_dict_fields
        ),
    ]
