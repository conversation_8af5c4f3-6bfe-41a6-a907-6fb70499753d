<template>
  <div class="page-container">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <SendOutlined />
          物资采购
        </h1>
        <p class="page-subtitle">执行采购任务，管理采购流程</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ pendingPurchaseCount }}</span>
          <span class="stat-label">待采购</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ purchasedCount }}</span>
          <span class="stat-label">已采购</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">¥{{ totalPurchaseAmount }}</span>
          <span class="stat-label">采购总金额</span>
        </div>
      </div>
    </div>

    <!-- 同一个面板内包括上方按钮区、下方表格区 -->
    <div class="department-tables business-card">
      <!-- 表格区域 -->
      <div class="table-section">
        <!-- 表格标题和按钮区域 - 使用flex布局，两端对齐 -->
        <div class="table-header-flex">
          <div class="table-title-section">
            <div class="table-title">
              <span>物资采购列表</span>
              <span class="table-subtitle">共 {{ totalCount }} 条记录</span>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="table-actions">
            <a-space :size="8">
              <!-- 主要业务操作按钮 -->
              <a-button v-if="selectedRowKeys.length > 0 && hasPurchasedRequests" type="primary"
                @click="batchNotifyShipping" size="small" class="compact-action-btn" :loading="loading"
                style="background: #52c41a; border-color: #52c41a">
                <SendOutlined />
                批量发货通知 ({{ purchasedSelectedCount }})
              </a-button>

              <!-- 分隔线 -->
              <a-divider v-if="selectedRowKeys.length > 0 && hasPurchasedRequests" type="vertical" />

              <!-- 筛选和视图操作按钮 -->
              <a-button @click="toggleFilters" size="small" class="compact-action-btn">
                <template #icon>
                  <component :is="showFilters ? 'UpOutlined' : 'DownOutlined'" />
                </template>
                {{ showFilters ? "收起筛选" : "展开筛选" }}
              </a-button>
              <a-button @click="resetFilters" size="small" class="compact-action-btn">重置筛选</a-button>
              <PermissionButton permission="purchase:procurement:export" @click="showExportModal = true"
                size="small" class="compact-action-btn">
                <DownloadOutlined />
                导出
              </PermissionButton>
              <a-button size="small" class="compact-action-btn" @click="showColumnFilter = true">
                <template #icon>
                  <SettingOutlined />
                </template>
                字段筛选
                <a-badge :count="selectedColumns?.length || 0" :number-style="{
                  backgroundColor: '#52c41a',
                  fontSize: '10px',
                }" />
              </a-button>

              <!-- 字段筛选模态框 -->
              <a-modal v-model:open="showColumnFilter" title="字段筛选配置" :footer="null" width="auto" :centered="true"
                :mask-closable="true" :destroy-on-close="true" wrap-class-name="column-filter-modal">
                <template #closeIcon>
                  <CloseOutlined />
                </template>
                <div class="column-filter-panel">
                  <div class="preset-section">

                    <a-dropdown v-model:open="presetDropdownOpen" :trigger="['click']" placement="bottomRight"
                      :overlay-style="{ zIndex: 9999 }" @click.stop>
                      <a-button class="preset-trigger" @click.stop size="large">
                        <template #icon>
                          <SettingOutlined />
                        </template>
                        预设配置
                        <DownOutlined />
                      </a-button>
                      <template #overlay>
                        <a-menu @click="handlePresetClick" @click.stop>
                          <template v-for="item in presetMenuItems" :key="item.key || 'divider'">
                            <a-menu-divider v-if="item.type === 'divider'" />
                            <a-menu-item v-else :key="item.key">{{ item.title }}</a-menu-item>
                          </template>
                        </a-menu>
                      </template>
                    </a-dropdown>
                    <div>
                      <a-button type="primary" size="large" @click="handleSelectAll"
                        style="margin-left: 12px">全选</a-button>
                      <a-button size="large" @click="handleReset" style="margin-left: 8px">重置</a-button>
                    </div>
                  </div>

                  <div class="filter-tip">
                    <span>已选择 {{ selectedColumns.length }} / {{ columnOptions.length }} 个字段</span>
                  </div>

                  <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnChange">
                    <!-- 动态字段分类 - 使用flex横向布局 -->
                    <div class="field-categories-container">
                      <div v-for="category in fieldCategories" :key="category.key" class="field-category-section">
                        <h5 class="category-title">{{ category.title }}</h5>
                        <div class="category-fields">
                          <div v-for="option in columnOptions.filter(opt => opt.category === category.key)"
                            :key="option.key" class="column-option">
                            <a-checkbox :value="option.key" :disabled="option.required" @click.stop>
                              <span class="column-title">{{ option.title }}</span>
                              <a-tag v-if="option.required" size="small" color="blue">必选</a-tag>
                            </a-checkbox>
                          </div>
                        </div>
                      </div>
                    </div>
                  </a-checkbox-group>
                </div>
              </a-modal>
            </a-space>
          </div>
        </div>

        <!-- 详细筛选控件区域 - 分两行布局，与页面同宽 -->
        <div v-show="showFilters" class="detailed-filters-fullwidth">
          <!-- 第一行：基础筛选项 -->
          <div class="filter-row">
            <a-row :gutter="[16, 12]" align="middle">
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-select v-model:value="filters.status" placeholder="状态" allowClear @change="handleFilterChange"
                  class="filter-select">
                  <a-select-option value="">全部状态</a-select-option>
                  <a-select-option value="pending_purchase">待采购</a-select-option>
                  <a-select-option value="purchased">已采购</a-select-option>
                  <a-select-option value="returned">已退回</a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-select v-model:value="filters.purchaseType" placeholder="采购类型" allowClear
                  @change="handleFilterChange" class="filter-select">
                  <a-select-option value="">全部类型</a-select-option>
                  <a-select-option v-for="type in purchaseTypes" :key="type.value" :value="type.value">
                    {{ type.label }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-select v-model:value="filters.department" placeholder="需求单位" allowClear @change="handleFilterChange"
                  class="filter-select" show-search :filter-option="filterOption">
                  <a-select-option value="">全部需求单位</a-select-option>
                  <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                    {{ dept.dept_name }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-select v-model:value="filters.purchaser" placeholder="采购员" allowClear @change="handleFilterChange"
                  class="filter-select" show-search :filter-option="filterOption">
                  <a-select-option value="">全部采购员</a-select-option>
                  <a-select-option v-for="user in purchasers" :key="user.id" :value="user.id">
                    {{ user.username }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.minAmount" placeholder="最小金额" @input="debouncedFilterChange"
                  class="filter-input" type="number" :min="0">
                  <template #prefix>
                    ¥
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.maxAmount" placeholder="最大金额" @input="debouncedFilterChange"
                  class="filter-input" type="number" :min="0">
                  <template #prefix>
                    ¥
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-range-picker v-model:value="filters.dateRange" :placeholder="['开始时间', '结束时间']"
                  @change="handleFilterChange" class="filter-date-picker" />
              </a-col>
            </a-row>
          </div>

          <!-- 第二行：搜索和其他筛选项 -->
          <div class="filter-row">
            <a-row :gutter="[16, 12]" align="middle">
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.id" placeholder="ID" @input="debouncedFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.itemName" placeholder="搜索物品名称" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.specification" placeholder="搜索规格型号" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.procurementMethod" placeholder="采购方式" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.requirementSource" placeholder="需求来源" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.fundProject" placeholder="经费项目" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.unit" placeholder="计量单位" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.itemCategory" placeholder="物品种类" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.remarks" placeholder="需求备注" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>

      <!-- 表格内容 -->
      <div class="table-container">
        <a-table :columns="filteredColumns" :data-source="requests" :row-key="(record) => record.id"
          :row-selection="rowSelection" :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: (page, pageSize) =>
              handleTableChange({ current: page, pageSize }),
            onShowSizeChange: (current, size) =>
              handleTableChange({ current, pageSize: size }),
          }" :loading="loading" @change="handleTableChange" :scroll="{ x: 'max-content', y: 'calc(100vh - 400px)' }"
          :virtual="false" :row-height="54" bordered class="unified-table">
          <!-- 空状态模板 -->
          <template #emptyText>
            <a-empty description="暂无采购需求数据" />
          </template>
          <!-- 状态列自定义渲染 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status_display || record.status }}
              </a-tag>
            </template>

            <!-- 金额列自定义渲染 -->
            <template v-else-if="
              [
                'budget_total_amount',
                'purchase_amount',
                'settlement_amount',
              ].includes(column.dataIndex)
            ">
              {{ formatAmount(record[column.dataIndex]) }}
            </template>

            <!-- 采购类型列自定义渲染 -->
            <template v-else-if="column.dataIndex === 'purchase_type'">
              {{
                record.purchase_type === "unified"
                  ? "统一采购"
                  : record.purchase_type === "self"
                    ? "自行采购"
                    : record.purchase_type || "-"
              }}
            </template>

            <!-- 时间列自定义渲染 -->
            <template v-else-if="
              [
                'created_at',
                'updated_at',
                'purchase_date',
                'approved_at',
                'delivery_date',
                'acceptance_date',
                'settlement_date',
                'submission_date',
              ].includes(column.dataIndex)
            ">
              {{
                record[column.dataIndex]
                  ? formatDate(record[column.dataIndex])
                  : "-"
              }}
            </template>

            <!-- 操作列自定义渲染 -->
            <template v-else-if="column.dataIndex === 'action'">
              <a-space>
                <PermissionButton permission="purchase:procurement:view" type="link" size="small" @click="viewDetail(record)">
                  详情
                </PermissionButton>

                <PermissionButton permission="purchase:procurement:execute" type="primary" size="small" @click="openPurchaseModal(record)"
                  v-if="(record._originalStatus || record.status) === 'pending_purchase'">
                  采购信息
                </PermissionButton>
                <PermissionButton permission="purchase:procurement:notify" type="primary" size="small" @click="notifyShipping(record)"
                  v-if="(record._originalStatus || record.status) === 'purchased'">
                  发货通知
                </PermissionButton>
                <PermissionButton permission="purchase:procurement:return" type="default" size="small" @click="returnRequest(record)" v-if="
                  ['approved', 'pending_purchase'].includes(record._originalStatus || record.status)
                " style="color: #ff4d4f; border-color: #ff4d4f">
                  退回
                </PermissionButton>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>


    <!-- 退回对话框 -->
    <a-modal v-model:open="showReturnModal" title="退回需求" width="600px" :mask-closable="false"
      :confirm-loading="returnLoading" wrap-class-name="return-modal" @cancel="cancelReturn" @ok="handleReturn">
      <div class="modal-content">
        <!-- 需求信息 -->
        <div class="request-info" v-if="currentReturnRequest">
          <a-descriptions :column="2" size="small" bordered>
            <a-descriptions-item label="需求编号">{{
              currentReturnRequest.request_number
              }}</a-descriptions-item>
            <a-descriptions-item label="物品名称">{{
              currentReturnRequest.item_name
              }}</a-descriptions-item>
            <a-descriptions-item label="申请人">{{
              currentReturnRequest.requester_name
              }}</a-descriptions-item>
            <a-descriptions-item label="申请金额">¥{{ currentReturnRequest.total_amount }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <a-divider />

        <!-- 退回表单 -->
        <a-form layout="vertical" :model="returnForm">
          <a-form-item label="退回原因" name="reasons" :rules="[{ required: true, message: '请选择退回原因' }]">
            <a-checkbox-group v-model:value="returnForm.reasons" class="return-reasons">
              <a-row :gutter="[16, 16]">
                <a-col :span="12">
                  <a-checkbox value="价格超预算">价格超预算</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="商品无货">商品无货</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="需求单位撤回">需求单位撤回</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="其他">其他</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>

          <a-form-item label="详细说明" name="detail" v-if="
            returnForm.reasons.includes('其他') ||
            returnForm.reasons.length > 0
          ">
            <a-textarea v-model:value="returnForm.detail" placeholder="请详细说明退回原因" :rows="4" show-count
              :maxlength="500" />
          </a-form-item>
        </a-form>
      </div>

      <template #footer>
        <div class="modal-footer">
          <a-button @click="cancelReturn" size="large"> 取消 </a-button>
          <a-button type="primary" danger @click="handleReturn" :loading="returnLoading" size="large">
            <RollbackOutlined />
            确认退回
          </a-button>
        </div>
      </template>
    </a-modal>

    <!-- 采购信息模态框 -->
    <a-modal v-model:open="showPurchaseModal" title="采购信息" width="800px" :footer="null" :destroy-on-close="true">
      <div class="purchase-form">
        <a-form ref="purchaseFormRef" :model="purchaseForm" :rules="purchaseFormRules" layout="vertical">
          <!-- 基本信息展示 -->
          <div class="form-section">
            <h4>需求信息</h4>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="物品名称">
                  <a-input :value="purchaseForm.item_name" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="规格型号">
                  <a-input :value="purchaseForm.specification" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="需求单位">
                  <a-input :value="purchaseForm.hierarchy_path" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="申请人">
                  <a-input :value="purchaseForm.requester_name" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 采购信息填报 -->
          <div class="form-section">
            <h4>采购信息</h4>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="采购数量" name="purchase_quantity" required>
                  <a-input-number v-model:value="purchaseForm.purchase_quantity" :min="1"
                    :max="purchaseForm.budget_quantity" style="width: 100%" placeholder="请输入采购数量" />
                  <div class="form-hint">
                    原需求数量：{{ purchaseForm.budget_quantity }}
                  </div>
                  <!-- 历史数据标签 -->
                  <div class="historical-data-tags" v-if="historicalData.lastPurchase || historicalData.totalPurchase">
                    <!-- 上一次购买数据（绿色标签） -->
                    <div class="historical-row" v-if="historicalData.lastPurchase">
                      <a-tag color="green" size="small">上一次购买数量：{{ historicalData.lastPurchase.quantity || 0 }}</a-tag>
                      <a-tag color="green" size="small">上一次购买单价：¥{{ historicalData.lastPurchase.unitPrice || 0
                        }}</a-tag>
                      <a-tag color="green" size="small">上一次购买金额：¥{{ historicalData.lastPurchase.totalAmount || 0
                        }}</a-tag>
                    </div>
                    <!-- 历史购买数据（黄色标签） -->
                    <div class="historical-row" v-if="historicalData.totalPurchase">
                      <a-tag color="orange" size="small">历史购买数量：{{ historicalData.totalPurchase.totalQuantity || 0
                        }}</a-tag>
                      <a-tag color="orange" size="small">历史购买平均单价：¥{{ historicalData.totalPurchase.averageUnitPrice || 0
                        }}</a-tag>
                      <a-tag color="orange" size="small">历史购买金额：¥{{ historicalData.totalPurchase.totalAmount || 0
                        }}</a-tag>
                    </div>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="purchase_unit_price" required>
                  <template #label>
                    <span>采购单价</span>
                    <span v-if="isPriceExceedsBudget" style="color: #ff4d4f;">（超出预算金额）</span>
                  </template>
                  <a-input-number v-model:value="purchaseForm.purchase_unit_price" :min="0" :precision="2"
                    :style="{ width: '100%', color: isPriceExceedsBudget ? '#ff4d4f' : undefined }"
                    placeholder="请输入采购单价" @change="checkPriceDifference" />
                  <div class="form-hint">
                    预算单价：¥{{ purchaseForm.budget_unit_price }}
                  </div>
                  <div class="form-hint" v-if="historicalAveragePrice > 0">
                    该规格型号历次采购平均价：¥{{
                      historicalAveragePrice.toFixed(2)
                    }}
                  </div>
                  <div class="price-warning" v-if="priceDifferenceRate >= 20">
                    <a-alert :message="`价格差异率 ${priceDifferenceRate.toFixed(
                      1
                    )}% ≥ 20%，需要异常处理`" type="warning" show-icon closable />
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="采购金额">
                  <a-input :value="`¥${purchaseAmount}`" disabled style="width: 100%" />
                  <div class="form-hint">
                    预算总金额：¥{{ purchaseForm.budget_total_amount }}
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="采购状态">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <a-tag :color="hasPriceException ? 'error' : 'success'" size="large">
                      {{ hasPriceException ? "异常" : "正常" }}
                    </a-tag>
                    <span v-if="hasPriceException" class="form-hint" style="color: #ff4d4f;">
                      价格差异率≥20%需要异常处理
                    </span>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="价格差异率">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <span :style="{ color: hasPriceException ? '#ff4d4f' : '#52c41a' }">
                      {{ priceDifferenceRate.toFixed(2) }}%
                    </span>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="采购方式" name="purchase_method" required>
                  <a-select v-model:value="purchaseForm.purchase_method" placeholder="请选择采购方式">
                    <a-select-option v-for="option in procurementMethodOptions" :key="option.code" :value="option.code">
                      {{ option.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="供应商" name="supplier_name" required>
                  <a-input v-model:value="purchaseForm.supplier_name" placeholder="请输入供应商名称" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16" v-if="hasPriceException">
              <a-col :span="24">
                <a-form-item label="价格异常原因说明" name="price_exception_reason">
                  <a-textarea v-model:value="purchaseForm.price_exception_reason" :rows="3" placeholder="请说明价格异常的原因" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item label="采购备注" name="purchase_remarks">
                  <a-textarea v-model:value="purchaseForm.purchase_remarks" :rows="3" placeholder="请输入采购备注信息" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <a-space>
            <a-button @click="showPurchaseModal = false">取消</a-button>
            <a-button @click="savePurchaseInfo" :loading="purchaseLoading">保存</a-button>
            <a-button type="primary" @click="submitPurchaseInfo" :loading="purchaseLoading">
              提交采购
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 导出配置对话框 -->
    <a-modal v-model:open="showExportModal" title="导出Excel配置" width="80%" :footer="null" :destroy-on-close="true">
      <div class="export-config-container">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <h4>筛选条件</h4>
          <div class="filter-form-container">
            <a-form :model="exportFilters" :label-col="{ style: { width: '70px', textAlign: 'right' } }">
              <a-row :gutter="[12, 6]">
                <a-col :span="4">
                  <a-form-item label="采购状态">
                    <a-select v-model:value="exportFilters.status" placeholder="选择状态" allowClear mode="multiple"
                      size="small">
                      <a-select-option value="pending_purchase">待采购</a-select-option>
                      <a-select-option value="purchased">已采购</a-select-option>
                      <a-select-option value="returned">已退回</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="4">
                  <a-form-item label="需求单位">
                    <a-select v-model:value="exportFilters.department" placeholder="选择需求单位" allowClear show-search
                      size="small">
                      <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                        {{ dept.hierarchy_path || dept.dept_name || dept.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="4">
                  <a-form-item label="采购类型">
                    <a-select v-model:value="exportFilters.purchaseType" placeholder="选择采购类型" allowClear size="small">
                      <a-select-option value="unified">统一采购</a-select-option>
                      <a-select-option value="self">自行采购</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="4">
                  <a-form-item label="日期范围">
                    <a-range-picker v-model:value="exportFilters.dateRange" size="small" style="width: 100%" />
                  </a-form-item>
                </a-col>
                <a-col :span="4">
                  <a-form-item label="采购人">
                    <a-select v-model:value="exportFilters.purchaser" placeholder="选择采购人" allowClear show-search
                      size="small">
                      <a-select-option v-for="purchaser in purchasers" :key="purchaser.id" :value="purchaser.id">
                        {{ purchaser.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="4">
                  <a-form-item>
                    <a-space>
                      <a-button @click="resetExportFilters" size="small">重置</a-button>
                      <a-button type="primary" @click="searchExportRecords" size="small">搜索</a-button>
                    </a-space>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </div>

        <!-- 导出字段配置 -->
        <div class="fields-section" style="border-radius: 6px; margin: 16px 0;">
          <a-collapse v-model:activeKey="fieldsCollapseKey" size="small">
            <a-collapse-panel key="1" header="导出字段配置">
              <template #extra>
                <a-space>
                  <a-button @click.stop="selectAllExportFields" size="small">全选</a-button>
                  <a-button @click.stop="resetExportFields" size="small">重置</a-button>
                  <a-button @click.stop="selectRequiredExportFields" size="small">仅必选</a-button>
                </a-space>
              </template>
              <a-checkbox-group v-model:value="selectedExportFields" @change="onExportFieldsChange">
                <div class="export-fields-container">
                  <div v-for="category in exportFieldCategories" :key="category.key" class="export-field-category">
                    <div class="export-category-header">
                      <h5 class="export-category-title">{{ category.title }}</h5>
                    </div>
                    <div class="export-category-fields">
                      <div v-for="field in exportFieldOptions.filter(
                        (opt) => opt.category === category.key
                      )" :key="field.value" class="export-field-option">
                        <a-checkbox :value="field.value" :disabled="field.required">
                          <span class="field-title">{{ field.label }}</span>
                          <a-tag v-if="field.required" size="small" color="blue">必选</a-tag>
                        </a-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </a-checkbox-group>
            </a-collapse-panel>
          </a-collapse>
        </div>

        <!-- 选择导出记录 -->
        <div class="records-section">
          <div class="section-header">
            <h4>选择导出记录</h4>
            <div class="section-actions">
              <span class="record-count">共 {{ exportRecords.length }} 条记录，已选择
                {{ selectedExportRecords.length }} 条</span>
              <a-space>
                <a-select v-model:value="exportRecordPageSize" @change="onExportRecordPageSizeChange" size="small"
                  style="width: 80px">
                  <a-select-option :value="10">10</a-select-option>
                  <a-select-option :value="20">20</a-select-option>
                  <a-select-option :value="50">50</a-select-option>
                  <a-select-option :value="100">100</a-select-option>
                </a-select>
                <a-button @click="searchExportRecords" size="small" :loading="exportSearchLoading">
                  <ReloadOutlined />
                  刷新
                </a-button>
              </a-space>
            </div>
          </div>
          <a-table :columns="exportRecordColumns" :data-source="exportRecords" :row-selection="exportRecordRowSelection"
            :row-key="record => record.id" :pagination="{
              current: exportRecordPagination.current,
              total: exportRecordPagination.total,
              pageSize: exportRecordPageSize,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: onExportRecordPageChange,
              onShowSizeChange: onExportRecordPageSizeChange,
            }" size="small" :scroll="{ x: 800 }" bordered>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ record.status_display || record.status }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'purchase_type'">
                {{ record.purchase_type_display || record.purchase_type }}
              </template>
              <template v-else-if="column.key === 'procurement_method'">
                {{ record.procurement_method_display || record.procurement_method || '-' }}
              </template>
              <template v-else-if="column.key === 'budget_total_amount'">
                ¥{{ record.budget_total_amount?.toLocaleString() || "0" }}
              </template>
              <template v-else-if="
                [
                  'created_at',
                  'purchase_date',
                  'updated_at',
                  'approved_at',
                  'delivery_date',
                  'acceptance_date',
                  'settlement_date',
                  'submission_date',
                ].includes(column.key)
              ">
                {{
                  record[column.key] ? formatDateToYMD(record[column.key]) : "-"
                }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 导出预览和操作 -->
        <div class="actions-section">
          <a-space>
            <a-button @click="previewExportData" :disabled="selectedExportRecords.length === 0">
              <EyeOutlined />
              预览 ({{ selectedExportRecords.length }}条)
            </a-button>
            <a-button type="primary" @click="executeExport" :disabled="selectedExportRecords.length === 0"
              :loading="exportLoading">
              <DownloadOutlined />
              导出Excel ({{ selectedExportRecords.length }}条)
            </a-button>
            <a-button @click="handleExportCancel">取消</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 预览模态框 -->
    <a-modal v-model:open="showPreviewModal" title="导出数据预览" width="80%" :footer="null" :destroy-on-close="true"
      :style="{ top: '10vh' }" :body-style="{ height: '80vh', overflow: 'auto' }">
      <div class="preview-container">
        <div class="preview-header">
          <a-space>
            <span>预览数据（前10条）</span>
            <a-tag color="blue">已选择 {{ selectedExportRecords.length }} 条记录</a-tag>
            <a-tag color="green">导出字段 {{ selectedExportFields.length }} 个</a-tag>
          </a-space>
        </div>

        <a-table :columns="previewColumns" :data-source="previewData" :loading="previewLoading" :pagination="false"
          size="small" :scroll="{ x: 1000 }" bordered>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status_display || record.status }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'purchase_type'">
              {{ record.purchase_type_display || record.purchase_type }}
            </template>
            <template v-else-if="column.key === 'procurement_method'">
              {{ record.procurement_method_display || record.procurement_method || '-' }}
            </template>
            <template v-else-if="
              ['budget_total_amount', 'purchase_total_amount'].includes(
                column.key
              )
            ">
              ¥{{ record[column.key]?.toLocaleString() || "0" }}
            </template>
            <template v-else-if="
              [
                'created_at',
                'purchase_date',
                'updated_at',
                'approved_at',
                'delivery_date',
                'acceptance_date',
                'settlement_date',
                'submission_date',
              ].includes(column.key)
            ">
              {{ record[column.key] ? formatDate(record[column.key]) : "-" }}
            </template>
          </template>
        </a-table>

        <div class="preview-footer">
          <a-space>
            <a-button @click="showPreviewModal = false">关闭</a-button>
            <a-button type="primary" @click="executeExport" :disabled="selectedExportRecords.length === 0">
              确认导出 ({{ selectedExportRecords.length }}条)
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, h, watch } from "vue";
import { useRouter } from "vue-router";
import { message, Modal } from "ant-design-vue";
import { debounce } from "@/utils/debounce";
import { useDictMixin } from '@/mixins/dictMixin';
import {
  SendOutlined,
  RollbackOutlined,
  DownloadOutlined,
  UpOutlined,
  DownOutlined,
  SearchOutlined,
  SettingOutlined,
} from "@ant-design/icons-vue";
import api from "@/api";

import {
  validateFormData,
  getPageColumnOptions,
  getPageColumnPresets,
  getPageFieldCategories,
  getPagePresetMenuItems,
  getPageExportFieldOptions,
  getPageDefaultExportFields,
  getPageDefaultColumns,
  sortColumnsByOrder,
} from "@/utils/validation";



export default {
  name: "PurchaseProcurement",
  components: {
    SendOutlined,
    RollbackOutlined,
    DownloadOutlined,
    UpOutlined,
    DownOutlined,
    SearchOutlined,
    SettingOutlined,
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const requests = ref([]);
    const selectedRowKeys = ref([]);

    // 使用字典混入 - 只保留必要的功能
    const {
      formatDate,
      getStatusColor,
      formatDateToYMD
    } = useDictMixin();



    const formatAmount = (amount) => {
      if (!amount) return "-";
      return `¥${parseFloat(amount).toFixed(2)}`;
    };

    // 状态选项 - 从字典数据动态生成
    const statusOptions = ref([
      { value: "", label: "全部状态" }
    ]);

    // 加载状态字典数据
    const loadStatusOptions = async () => {
      try {
        const response = await api.dicts.getDict('status', true);
        if (response.code === 200 && response.data && response.data.items) {
          const dictItems = response.data.items.map(item => ({
            value: item.code,
            label: item.name
          }));
          statusOptions.value = [
            { value: "", label: "全部状态" },
            ...dictItems
          ];
        }
      } catch (error) {
        console.error('加载状态字典失败:', error);
      }
    };

    // 退回相关
    const showReturnModal = ref(false);
    const returnLoading = ref(false);
    const currentReturnRequest = ref(null);
    const returnForm = reactive({
      reasons: [],
      detail: "",
    });

    // 采购信息填报相关
    const showPurchaseModal = ref(false);
    const purchaseLoading = ref(false);
    const purchaseFormRef = ref(null);
    const purchaseForm = reactive({
      id: null,
      item_name: "",
      specification: "",
      unit: "", // 添加计量单位字段
      hierarchy_path: "",
      requester_name: "",
      budget_quantity: 0,
      budget_unit_price: 0,
      budget_total_amount: 0,
      purchase_quantity: 0, // 采购数量
      purchase_unit_price: 0, // 采购单价
      actual_quantity: 0,
      actual_unit_price: 0,
      purchase_method: "",
      supplier_name: "",
      purchase_remarks: "",
      price_exception_reason: "",
    });

    // 价格差异率检查相关
    const historicalAveragePrice = ref(0);
    const priceDifferenceRate = ref(0);

    // 历史数据
    const historicalData = reactive({
      lastPurchase: null,
      totalPurchase: null
    });

    // 判断是否有价格异常
    const hasPriceException = computed(() => {
      return priceDifferenceRate.value >= 20;
    });

    // 判断采购单价是否超出预算单价
    const isPriceExceedsBudget = computed(() => {
      const purchasePrice = parseFloat(purchaseForm.purchase_unit_price) || 0;
      const budgetPrice = parseFloat(purchaseForm.budget_unit_price) || 0;
      return purchasePrice > budgetPrice;
    });

    // 基于业务阶段的采购表单验证规则
    const purchaseFormRules = computed(() => {
      // 采购阶段的验证规则
      return {
        purchase_quantity: [
          {
            validator: (_, value) => {
              if (value === null || value === undefined || value === "") {
                return Promise.reject(new Error("请输入采购数量"));
              }
              const num = Number(value);
              if (isNaN(num) || num < 1) {
                return Promise.reject(new Error("数量必须大于0"));
              }
              return Promise.resolve();
            },
            trigger: "blur",
          },
        ],
        purchase_unit_price: [
          {
            validator: (_, value) => {
              if (value === null || value === undefined || value === "") {
                return Promise.reject(new Error("请输入采购单价"));
              }
              const num = Number(value);
              if (isNaN(num) || num <= 0) {
                return Promise.reject(new Error("单价必须大于0"));
              }
              return Promise.resolve();
            },
            trigger: "blur",
          },
        ],
        supplier_name: [
          { required: true, message: "请输入供应商名称", trigger: "blur" },
        ],
      };
    });

    // 计算采购金额
    const purchaseAmount = computed(() => {
      return (
        (purchaseForm.purchase_quantity || 0) *
        (purchaseForm.purchase_unit_price || 0)
      ).toFixed(2);
    });

    // 部门列表
    const departments = ref([]);

    // 采购方式选项
    const procurementMethodOptions = ref([]);

    // 筛选选项数据源
    const purchaseTypes = ref([
      { value: "unified", label: "统一采购" },
      { value: "self", label: "自行采购" },
    ]);
    const purchasers = ref([]);
    const suppliers = ref([]);

    // 筛选控件显示状态
    const showFilters = ref(false);
    const presetDropdownOpen = ref(false);
    const showExportModal = ref(false);
    const showColumnFilter = ref(false);

    // 导出相关状态
    const exportActiveTab = ref("fields");
    const exportLoading = ref(false);
    const previewLoading = ref(false);
    const previewData = ref([]);
    const previewTotal = ref(0);
    const showPreviewModal = ref(false);

    // 基于业务流程的导出字段配置（采购阶段）
    const exportFieldOptions = getPageExportFieldOptions("purchase");
    const exportFieldCategories = getPageFieldCategories("purchase");
    const selectedExportFields = ref(getPageDefaultExportFields("purchase"));
    const fieldsCollapseKey = ref([]);

    // 导出筛选条件
    const exportFilters = reactive({
      status: [],
      department: [],
      purchaseType: "",
      itemName: "",
      requester: "",
      purchaser: "",
      supplier: "",
      minAmount: "",
      maxAmount: "",
      dateRange: [],
      // 新增导出筛选字段
      procurementMethod: "",
      requirementSource: "",
      fundProject: "",
      unit: "",
      itemCategory: "",
      remarks: "",
    });

    // 导出记录选择相关
    const exportRecords = ref([]);
    const selectedExportRecords = ref([]);
    const selectedExportRecordData = ref([]);
    const exportSearchLoading = ref(false);
    const exportRecordPageSize = ref(20);
    const exportRecordPagination = ref({
      current: 1,
      total: 0,
    });

    // 筛选条件
    const filters = reactive({
      status: "",
      id: "",
      itemName: "",
      specification: "",
      department: "",
      purchaser: "",
      supplier_name: "",
      purchaseType: "",
      minBudgetAmount: "",
      maxBudgetAmount: "",
      minPurchaseAmount: "",
      maxPurchaseAmount: "",
      dateRange: [],
      // 新增缺失的筛选字段
      procurementMethod: "", // 采购方式
      requirementSource: "", // 需求来源
      fundProject: "", // 经费项目
      unit: "", // 计量单位
      itemCategory: "", // 物品种类
      remarks: "", // 需求备注
    });

    // 基于业务流程的字段筛选配置（采购阶段，排除操作列）
    const columnOptions = getPageColumnOptions("purchase").filter(
      (opt) => opt.key !== "action"
    );

    // 从本地存储读取字段配置
    const getStoredColumns = () => {
      const COLUMNS_VERSION = "2.2"; // 版本号，更新时清除旧缓存
      const STORAGE_KEY = `purchase-procurement-columns-v${COLUMNS_VERSION}`;

      try {
        // 清除旧版本的缓存
        const oldKeys = ["purchase-procurement-columns", "purchase-procurement-columns-v2.1"];
        oldKeys.forEach((key) => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
          }
        });

        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const parsed = JSON.parse(stored);
          // 确保必选字段始终被包含，排除操作列
          const requiredColumns = columnOptions
            .filter((opt) => opt.required)
            .map((opt) => opt.key);
          return [...new Set([...parsed, ...requiredColumns])].filter(col => col !== 'action');
        }
      } catch (error) {
        console.warn("读取字段配置失败:", error);
      }
      // 使用默认字段配置（按顺序排列），排除操作列
      const defaultColumns = getPageDefaultColumns("purchase").filter(col => col !== 'action');
      return defaultColumns;
    };

    // 保存字段配置到本地存储
    const saveColumnsToStorage = (columns) => {
      const COLUMNS_VERSION = "2.2";
      const STORAGE_KEY = `purchase-procurement-columns-v${COLUMNS_VERSION}`;
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(columns));
      } catch (error) {
        console.warn("保存字段配置失败:", error);
      }
    };

    const selectedColumns = ref(getStoredColumns());

    // 基于业务流程的预设配置（采购阶段）
    const columnPresets = getPageColumnPresets("purchase");

    // 基于业务流程的字段分类配置（采购阶段）
    const fieldCategories = getPageFieldCategories("purchase");

    // 基于业务流程的预设菜单选项（采购阶段）
    const presetMenuItems = getPagePresetMenuItems("purchase") || [];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) =>
        `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
      pageSizeOptions: ["10", "20", "50", "100"],
    });

    // 行选择配置 - 只允许选择已采购状态的记录进行批量发货通知
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys;
      },
      getCheckboxProps: (record) => ({
        disabled: (record._originalStatus || record.status) !== "purchased",
        name: record.item_name,
      }),
    };

    // 计算属性
    const pendingPurchaseCount = computed(() => {
      return requests.value.filter((item) => (item._originalStatus || item.status) === "pending_purchase")
        .length;
    });

    // 计算选中记录中是否有已采购状态的记录
    const hasPurchasedRequests = computed(() => {
      return selectedRowKeys.value.some((id) => {
        const record = requests.value.find((r) => r.id === id);
        return record && (record._originalStatus || record.status) === "purchased";
      });
    });

    // 计算已采购状态的选中数量
    const purchasedSelectedCount = computed(() => {
      return selectedRowKeys.value.filter((id) => {
        const record = requests.value.find((r) => r.id === id);
        return record && (record._originalStatus || record.status) === "purchased";
      }).length;
    });

    const purchasedCount = computed(() => {
      return requests.value.filter((item) => (item._originalStatus || item.status) === "purchased")
        .length;
    });

    const totalPurchaseAmount = computed(() => {
      return requests.value
        .filter((item) =>
          ["pending_purchase", "purchased"].includes(item._originalStatus || item.status)
        )
        .reduce((sum, item) => sum + parseFloat(item.purchase_amount || item.amount || 0), 0)
        .toFixed(2);
    });



    // 动态筛选后的列
    const filteredColumns = computed(() => {
      // 构建选中的列（排除操作列）
      const selectedCols = selectedColumns.value
        .map((columnKey) => {
          const option = columnOptions.find((opt) => opt.key === columnKey);
          if (!option) {
            return null;
          }

          const baseColumn = {
            title: option.title,
            dataIndex: columnKey,
            key: columnKey,
            width: 120,
            ellipsis: true,
            align: "center", // 所有列居中显示
            // 为数值和日期字段以及新增字段添加排序支持
            sorter: [
              "quantity",
              "unit_price",
              "total_amount",
              "purchase_quantity",
              "purchase_unit_price",
              "purchase_total_amount",
              "created_at",
              "purchase_date",
              "acceptance_date",
              "settlement_date",
              "item_name",
              "specification",
              "procurement_method",
              "requirement_source",
              "fund_project",
              "unit",
              "item_category",
              "hierarchy_path",
              "purchase_type",
              "status",
              "requester_name",
              "approver_name",
              "purchaser_name",
            ].includes(columnKey),
          };

          // 特殊字段的自定义处理
          switch (columnKey) {
            case "hierarchy_path":
              return {
                ...baseColumn,
                width: 150,
                ellipsis: false,
                customRender: ({ text }) => {
                  return h(
                    "div",
                    {
                      style: {
                        whiteSpace: "normal",
                        wordBreak: "break-all",
                        lineHeight: "1.4",
                      },
                    },
                    text || "-"
                  );
                },
              };
            case "remarks":
            case "purchase_remarks":
            case "acceptance_remarks":
            case "settlement_remarks":
              return {
                ...baseColumn,
                width: 200,
                ellipsis: false,
                customRender: ({ text }) => {
                  return h(
                    "div",
                    {
                      style: {
                        whiteSpace: "normal",
                        wordBreak: "break-all",
                        lineHeight: "1.4",
                        maxHeight: "80px",
                        overflow: "auto",
                      },
                    },
                    text || "-"
                  );
                },
              };
            default:
              return baseColumn;
          }
        })
        .filter(Boolean); // 过滤掉 null 值

      // 始终在最右侧添加操作列
      const actionColumn = {
        title: "操作",
        dataIndex: "action",
        key: "action",
        width: 200,
        fixed: "right",
        align: "center",
      };

      return [...selectedCols, actionColumn];
    });

    // 获取部门列表 - 用于筛选选项，使用专门的无分页接口
    const getDepartments = async () => {
      try {
        const response = await api.departments.getAll();
        if (response.code === 200) {
          departments.value = response.data || [];
        }
      } catch (error) {
        console.error("获取部门列表失败:", error);
      }
    };

    // 获取采购方式选项
    const getProcurementMethodOptions = async () => {
      try {
        const response = await api.dicts.getDict('procurement_method');
        if (response.code === 200 && response.data && response.data.items) {
          // 正确使用API返回的数据结构
          procurementMethodOptions.value = response.data.items.map(item => ({
            code: item.code,
            name: item.name
          }));
        } else {
          throw new Error('API返回数据格式不正确');
        }
      } catch (error) {
        console.error("获取采购方式选项失败:", error);
        // 如果获取失败，使用默认选项
        procurementMethodOptions.value = [
          { code: 'public_bidding', name: '公开招标' },
          { code: 'invited_bidding', name: '邀请招标' },
          { code: 'competitive_negotiation', name: '竞争性谈判' },
          { code: 'single_source', name: '单一来源' },
          { code: 'inquiry', name: '询价' },
          { code: 'direct_purchase', name: '直接采购' }
        ];
      }
    };

    // 构建查询参数的辅助函数
    const buildQueryParams = () => {
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        // 物资采购页面只显示：待采购、已采购、已退回状态
        status_in: filters.status || "pending_purchase,purchased,returned",
      };

      // 优化筛选参数构建
      const filterMappings = {
        itemName: ["search", "item_name__icontains"],
        requester: "requester__username__icontains",
        purchaseType: "purchase_type",
        specification: "specification__icontains",
        supplier_name: "supplier_name__icontains",
        procurementMethod: "procurement_method__icontains",
        requirementSource: "requirement_source__icontains",
        fundProject: "fund_project__icontains",
        unit: "unit__icontains",
        itemCategory: "item_category__icontains",
        remarks: "remarks__icontains",
      };

      // 批量处理简单筛选参数
      Object.entries(filterMappings).forEach(([key, mapping]) => {
        if (filters[key]) {
          if (Array.isArray(mapping)) {
            mapping.forEach((param) => (params[param] = filters[key]));
          } else {
            params[mapping] = filters[key];
          }
        }
      });

      // 处理特殊筛选参数
      if (filters.id) {
        const idNum = parseInt(filters.id);
        if (!isNaN(idNum)) params.id = idNum;
      }

      if (filters.minAmount) {
        const minAmount = parseFloat(filters.minAmount);
        if (!isNaN(minAmount)) params.budget_total_amount__gte = minAmount;
      }

      if (filters.maxAmount) {
        const maxAmount = parseFloat(filters.maxAmount);
        if (!isNaN(maxAmount)) params.budget_total_amount__lte = maxAmount;
      }

      // 处理部门筛选
      if (filters.department) {
        const selectedDept = departments.value.find(
          (d) => d.id == filters.department
        );
        if (selectedDept) {
          params.hierarchy_path__icontains = selectedDept.parent_id
            ? selectedDept.hierarchy_path
            : selectedDept.dept_name;
        }
      }

      // 处理日期范围
      if (filters.dateRange?.length === 2) {
        params.created_at__gte = filters.dateRange[0].format("YYYY-MM-DD");
        params.created_at__lte = filters.dateRange[1].format("YYYY-MM-DD");
      }

      // 处理排序
      if (sortField.value && sortOrder.value) {
        params.ordering = `${sortOrder.value === "descend" ? "-" : ""}${sortField.value
          }`;
      }

      return params;
    };

    // 简化数据请求 - 去除复杂缓存
    const getPurchaseRequests = async () => {
      if (loading.value) return;

      loading.value = true;
      try {
        const params = buildQueryParams();
        const response = await api.purchaseRequests.getList(params);

        if (response.code === 200) {
          const rawRequests = response.data.results || [];

          // 使用后端提供的display字段，同时保留原始编码用于逻辑判断
          const convertedRequests = rawRequests.map(request => ({
            ...request,
            // 使用后端提供的display字段进行显示
            status: request.status_display || request.status,
            item_category: request.item_category_display || request.item_category,
            unit: request.unit_display || request.unit,
            procurement_method: request.procurement_method_display || request.procurement_method,
            fund_project: request.fund_project_display || request.fund_project,
            purchase_type: request.purchase_type_display || request.purchase_type,
            // 保存原始编码用于逻辑判断
            _originalStatus: request.status,
            _originalItemCategory: request.item_category,
            _originalUnit: request.unit,
            _originalProcurementMethod: request.procurement_method,
            _originalFundProject: request.fund_project,
            _originalPurchaseType: request.purchase_type
          }));

          requests.value = convertedRequests;
          pagination.total = response.data.count || 0;
        } else {
          throw new Error(`API返回错误: ${response.message}`);
        }
      } catch (error) {
        console.error("获取采购需求失败:", error);
        if (error.response?.status === 404) {
          if (pagination.current > 1) {
            pagination.current = 1;
            return getPurchaseRequests();
          } else {
            requests.value = [];
            pagination.total = 0;
            return;
          }
        }
        message.error(error.message || "获取数据失败，请稍后重试");
      } finally {
        loading.value = false;
      }
    };

    // 筛选变化处理 - 优化版本
    const handleFilterChange = () => {
      pagination.current = 1;
      getPurchaseRequests();
    };

    // 防抖筛选变化处理 - 增加延迟时间减少请求频率
    const debouncedFilterChange = debounce(handleFilterChange, 800);

    // 重置筛选
    const resetFilters = () => {
      Object.assign(filters, {
        status: "",
        id: "",
        itemName: "",
        specification: "",
        department: "",
        purchaser: "",
        supplier_name: "",
        purchaseType: "",
        minBudgetAmount: "",
        maxBudgetAmount: "",
        minPurchaseAmount: "",
        maxPurchaseAmount: "",
        dateRange: [],
        // 重置新增的筛选字段
        procurementMethod: "",
        requirementSource: "",
        fundProject: "",
        unit: "",
        itemCategory: "",
        remarks: "",
      });
      handleFilterChange();
    };

    // 获取筛选选项数据
    const getFilterOptions = async () => {
      try {
        // 获取用户列表（采购员）- 优化分页
        const usersResponse = await api.users.getList({ page_size: 20 });
        if (usersResponse.code === 200) {
          const users = usersResponse.data.results || usersResponse.data;
          // 简化过滤条件
          purchasers.value = users.filter(
            (user) =>
              user.role_name &&
              (user.role_name.includes("采购") ||
                user.role_name.includes("purchaser") ||
                user.is_staff)
          );

          // 如果没有找到采购员，取前10个用户
          if (purchasers.value.length === 0) {
            purchasers.value = users.slice(0, 10);
          }
        }

        // 去除供应商列表请求 - 这个请求影响性能且不是必需的
        // 供应商可以通过输入框手动输入，不需要预加载所有供应商
        suppliers.value = [];
      } catch (error) {
        console.error("获取筛选选项失败:", error);
      }
    };

    // 筛选选项过滤方法
    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    // 展开/收起筛选控件
    const toggleFilters = () => {
      showFilters.value = !showFilters.value;
    };

    // 字段筛选方法
    const onColumnChange = (checkedValues) => {
      // 确保必选字段始终被选中
      const requiredColumns = columnOptions
        .filter((opt) => opt.required)
        .map((opt) => opt.key);
      const newSelectedColumns = [
        ...new Set([...checkedValues, ...requiredColumns]),
      ];
      selectedColumns.value = newSelectedColumns;
      saveColumnsToStorage(newSelectedColumns);
    };

    const selectAllColumns = () => {
      const sortedOptions = sortColumnsByOrder(columnOptions);
      const allColumns = sortedOptions.map((opt) => opt.key);
      selectedColumns.value = allColumns;
      saveColumnsToStorage(allColumns);
    };

    const resetColumns = () => {
      // 重置到系统默认配置，排除操作列
      const defaultColumns = getPageDefaultColumns("purchase").filter(col => col !== 'action');
      selectedColumns.value = defaultColumns;
      saveColumnsToStorage(defaultColumns);
    };

    // 应用预设
    const applyPreset = ({ key }) => {
      const presetColumns = columnPresets[key];
      if (presetColumns) {
        // 确保必选字段始终被包含
        const requiredColumns = columnOptions
          .filter((opt) => opt.required)
          .map((opt) => opt.key);

        // 合并预设字段和必选字段
        const combinedColumns = [
          ...new Set([...presetColumns, ...requiredColumns]),
        ];

        // 按照标准顺序排列字段
        const sortedColumns = sortColumnsByOrder(
          combinedColumns,
          columnOptions
        );

        selectedColumns.value = sortedColumns;
        saveColumnsToStorage(sortedColumns);
      }
    };

    // 处理预设点击
    const handlePresetClick = ({ key }) => {
      try {
        applyPreset({ key });
        // 关闭dropdown
        presetDropdownOpen.value = false;
      } catch (error) {
        message.error("应用预设失败");
      }
    };

    // 处理全选按钮点击
    const handleSelectAll = (e) => {
      e.stopPropagation();
      selectAllColumns();
    };

    // 处理重置按钮点击
    const handleReset = (e) => {
      e.stopPropagation();
      resetColumns();
    };

    // 处理字段变更（阻止面板关闭）
    const handleColumnChange = (checkedValues) => {
      onColumnChange(checkedValues);
    };

    // 计算总记录数
    const totalCount = computed(() => {
      return pagination.total;
    });

    // 导出字段选择相关计算属性
    const exportFieldsIndeterminate = computed(() => {
      return (
        selectedExportFields.value.length > 0 &&
        selectedExportFields.value.length < exportFieldOptions.length
      );
    });

    const exportFieldsCheckAll = computed(() => {
      return selectedExportFields.value.length === exportFieldOptions.length;
    });

    // 导出记录表格列 - 根据选中字段动态生成
    const exportRecordColumns = computed(() => {
      // 基础列（始终显示）
      const baseColumns = [
        { title: "ID", dataIndex: "id", width: 80, fixed: "left" },
      ];

      // 根据选中字段生成动态列
      const dynamicColumns = selectedExportFields.value
        .map((fieldKey) => {
          const field = exportFieldOptions.find((f) => f.value === fieldKey);

          if (fieldKey === "id") return null; // ID列已在基础列中

          // 字段标题映射，确保所有字段都有中文标题
          const fieldTitleMap = {
            budget_total_amount: "预算金额",
            purchase_total_amount: "采购金额",
            item_name: "物品名称",
            specification: "规格型号",
            budget_quantity: "预算数量",
            purchase_quantity: "采购数量",
            unit: "计量单位",
            budget_unit_price: "预算单价",
            purchase_unit_price: "采购单价",
            status: "状态",
            created_at: "创建时间",
            purchase_date: "采购时间",
            supplier_name: "供应商",
            procurement_method: "采购方式",
            purchase_type: "采购类型",
            hierarchy_path: "需求单位",
            requirement_source: "需求来源",
            fund_project: "经费项目",
            item_category: "物品种类",
            purchase_remarks: "采购备注",
          };

          const column = {
            title: field ? field.label : fieldTitleMap[fieldKey] || fieldKey,
            dataIndex: fieldKey,
            width: getExportColumnWidth(fieldKey),
            ellipsis: true,
          };

          // 添加自定义渲染
          if (fieldKey === "procurement_method") {
            column.customRender = ({ record }) => record.procurement_method_display || record.procurement_method;
          } else if (fieldKey === "purchase_type") {
            column.customRender = ({ record }) => record.purchase_type_display || record.purchase_type;
          } else if (fieldKey === "status") {
            column.customRender = ({ record }) => record.status_display || record.status;
          } else if (
            [
              "created_at",
              "purchase_date",
              "updated_at",
              "approved_at",
              "delivery_date",
              "acceptance_date",
              "settlement_date",
              "submission_date",
            ].includes(fieldKey)
          ) {
            column.customRender = ({ text }) => formatDate(text);
          }

          return column;
        })
        .filter(Boolean);

      return [...baseColumns, ...dynamicColumns];
    });

    // 获取导出列宽度
    const getExportColumnWidth = (fieldKey) => {
      const widthMap = {
        request_number: 120,
        item_name: 150,
        specification: 120,
        quantity: 80,
        budget_total_amount: 100,
        status: 100,
        dept_name: 120,
        requester_name: 100,
        created_at: 120,
        purchase_type: 100,
        procurement_method: 120,
        requirement_source: 120,
        fund_project: 120,
        unit: 80,
        item_category: 100,
        supplier_name: 120,
        purchase_date: 120,
        purchase_amount: 100,
      };
      return widthMap[fieldKey] || 100;
    };

    // 修复的导出记录行选择 - 支持跨页选择
    const exportRecordRowSelection = computed(() => ({
      selectedRowKeys: selectedExportRecords.value,
      onChange: (selectedRowKeys, selectedRows) => {
        // 直接更新选中状态，支持跨页
        selectedExportRecords.value = [...selectedRowKeys];
        selectedExportRecordData.value = [...selectedRows];
      },
      onSelectAll: (selected, _selectedRows, changeRows) => {
        if (selected) {
          // 全选当前页 - 将当前页的所有记录添加到已选择列表
          const currentPageIds = changeRows.map((row) => row.id);
          const existingIds = selectedExportRecords.value.filter(
            (id) => !currentPageIds.includes(id)
          );
          selectedExportRecords.value = [...existingIds, ...currentPageIds];

          const existingData = selectedExportRecordData.value.filter(
            (row) => !currentPageIds.includes(row.id)
          );
          selectedExportRecordData.value = [...existingData, ...changeRows];
        } else {
          // 取消全选当前页 - 从已选择列表中移除当前页的记录
          const currentPageIds = changeRows.map((row) => row.id);
          selectedExportRecords.value = selectedExportRecords.value.filter(
            (id) => !currentPageIds.includes(id)
          );
          selectedExportRecordData.value =
            selectedExportRecordData.value.filter(
              (row) => !currentPageIds.includes(row.id)
            );
        }
      },
    }));

    // 预览表格列
    const previewColumns = computed(() => {
      return selectedExportFields.value.map((field) => {
        const option = exportFieldOptions.find((opt) => opt.value === field);
        const column = {
          title: option?.label || field,
          dataIndex: field,
          key: field,
          width: 120,
          ellipsis: true,
        };

        // 添加自定义渲染
        if (field === "procurement_method") {
          column.customRender = ({ record }) => record.procurement_method_display || record.procurement_method;
        } else if (field === "purchase_type") {
          column.customRender = ({ record }) => record.purchase_type_display || record.purchase_type;
        } else if (field === "status") {
          column.customRender = ({ record }) => record.status_display || record.status;
        } else if (
          [
            "created_at",
            "purchase_date",
            "updated_at",
            "approved_at",
            "delivery_date",
            "acceptance_date",
            "settlement_date",
            "submission_date",
          ].includes(field)
        ) {
          column.customRender = ({ text }) => formatDate(text);
        }

        return column;
      });
    });

    // 导出字段全选处理
    const onExportFieldsCheckAllChange = (e) => {
      if (e.target.checked) {
        selectedExportFields.value = exportFieldOptions.map(
          (option) => option.value
        );
      } else {
        selectedExportFields.value = [];
      }
    };

    // 导出字段选择变化
    const onExportFieldsChange = (checkedList) => {
      selectedExportFields.value = checkedList;
    };


    // 使用dictMixin中的formatDateToYMD方法

    // 导出记录分页相关
    const onExportRecordPageSizeChange = (_page, size) => {
      exportRecordPageSize.value.size = size;
      searchExportRecords();
    };

    const onExportRecordPageChange = (page) => {
      exportRecordPagination.value.current = page;
      searchExportRecords();
    };

    // 重置导出筛选条件
    const resetExportFilters = () => {
      Object.assign(exportFilters, {
        status: [],
        department: [],
        purchaseType: "",
        purchaser: "",
        dateRange: [],
      });
    };

    // 搜索导出记录
    const searchExportRecords = async () => {
      exportSearchLoading.value = true;
      try {
        const params = {
          page: exportRecordPagination.value.current,
          page_size: exportRecordPageSize.value,
          export_config_mode: true, // 标识这是导出配置模式
        };

        // 添加筛选条件
        if (exportFilters.status && exportFilters.status.length > 0) {
          params.status__in = exportFilters.status.join(",");
        }
        if (exportFilters.department) {
          params.dept_id = exportFilters.department;
        }
        if (exportFilters.purchaseType) {
          params.purchase_type = exportFilters.purchaseType;
        }
        if (exportFilters.purchaser) {
          params.purchaser_id = exportFilters.purchaser;
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.created_at__gte =
            exportFilters.dateRange[0].format("YYYY-MM-DD");
          params.created_at__lte =
            exportFilters.dateRange[1].format("YYYY-MM-DD");
        }

        const response = await api.purchaseRequests.getList(params);
        if (response.code === 200) {
          const rawRecords = response.data.results || response.data || [];
          // 使用后端提供的display字段进行显示
          const processedRecords = rawRecords.map(record => ({
            ...record,
            status: record.status_display || record.status,
            item_category: record.item_category_display || record.item_category,
            unit: record.unit_display || record.unit,
            procurement_method: record.procurement_method_display || record.procurement_method,
            fund_project: record.fund_project_display || record.fund_project,
            purchase_type: record.purchase_type_display || record.purchase_type
          }));
          exportRecords.value = processedRecords;
          exportRecordPagination.value.total =
            response.data.count || exportRecords.value.length;
        }
      } catch (error) {
        console.error("搜索导出记录失败:", error);
        message.error("搜索导出记录失败");
      } finally {
        exportSearchLoading.value = false;
      }
    };

    // 全选导出字段
    const selectAllExportFields = () => {
      selectedExportFields.value = exportFieldOptions.map(
        (field) => field.value
      );
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    };

    // 重置导出字段
    const resetExportFields = () => {
      selectedExportFields.value = getPageDefaultExportFields("purchase");
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    };

    // 仅选择必选字段
    const selectRequiredExportFields = () => {
      selectedExportFields.value = exportFieldOptions
        .filter((field) => field.required)
        .map((field) => field.value);
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    };

    // 预览导出数据
    const previewExportData = async () => {
      if (selectedExportRecords.value.length === 0) {
        message.warning("请先选择要预览的记录");
        return;
      }

      showPreviewModal.value = true;
      await loadPreviewData();
    };

    // 重置导出配置
    const resetExportConfig = () => {
      selectedExportFields.value = getPageDefaultExportFields("purchase");
      Object.assign(exportFilters, {
        status: [],
        department: [],
        itemName: "",
        requester: "",
        purchaser: "",
        supplier: "",
        minAmount: "",
        maxAmount: "",
        dateRange: [],
        // 重置新增的导出筛选字段
        procurementMethod: "",
        requirementSource: "",
        fundProject: "",
        unit: "",
        itemCategory: "",
        remarks: "",
      });
      exportActiveTab.value = "fields";
      previewData.value = [];
      previewTotal.value = 0;
    };

    // 加载预览数据 - 只预览选中的数据
    const loadPreviewData = async () => {
      if (selectedExportFields.value.length === 0) {
        message.warning("请至少选择一个导出字段");
        return;
      }

      if (selectedExportRecords.value.length === 0) {
        message.warning("请先选择要预览的记录");
        previewData.value = [];
        previewTotal.value = 0;
        return;
      }

      previewLoading.value = true;
      try {
        // 使用选中的数据进行预览
        if (selectedExportRecordData.value.length > 0) {
          // 如果有完整的选中数据，直接使用
          previewData.value = selectedExportRecordData.value.slice(0, 10); // 只显示前10条
          previewTotal.value = selectedExportRecordData.value.length;
        } else {
          // 如果没有完整数据，通过API获取选中记录的详细信息
          const selectedIds = selectedExportRecords.value.slice(0, 10); // 只预览前10条
          if (selectedIds.length > 0) {
            const params = {
              id__in: selectedIds.join(","),
              fields: selectedExportFields.value.join(","),
            };

            const response = await api.purchaseRequests.getList(params);
            if (response.code === 200) {
              const rawPreviewData = response.data.results || response.data || [];
              // 使用后端提供的display字段进行显示
              const processedPreviewData = rawPreviewData.map(record => ({
                ...record,
                status: record.status_display || record.status,
                item_category: record.item_category_display || record.item_category,
                unit: record.unit_display || record.unit,
                procurement_method: record.procurement_method_display || record.procurement_method,
                fund_project: record.fund_project_display || record.fund_project,
                purchase_type: record.purchase_type_display || record.purchase_type
              }));
              previewData.value = processedPreviewData;
              previewTotal.value = selectedExportRecords.value.length;
            }
          } else {
            previewData.value = [];
            previewTotal.value = 0;
          }
        }
      } catch (error) {
        console.error("加载预览数据失败:", error);
        message.error("加载预览数据失败");
      } finally {
        previewLoading.value = false;
      }
    };

    // 处理导出取消操作
    const handleExportCancel = () => {
      showExportModal.value = false;
      showPreviewModal.value = false;
      // 重置选择
      selectedExportRecords.value = [];
      selectedExportRecordData.value = [];
      exportRecords.value = [];
      previewData.value = [];
    };

    // 执行导出
    const executeExport = async () => {
      if (selectedExportRecords.value.length === 0) {
        message.warning("请先选择要导出的记录");
        return;
      }
      if (selectedExportFields.value.length === 0) {
        message.warning("请至少选择一个导出字段");
        return;
      }

      exportLoading.value = true;
      try {
        const params = {
          fields: selectedExportFields.value.join(","),
          ids: selectedExportRecords.value.join(","),
          export_config_mode: true, // 标识这是导出配置模式
        };

        // 应用导出筛选条件
        if (exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(",");
        }
        if (exportFilters.department.length > 0) {
          params.department_in = exportFilters.department.join(",");
        }
        if (exportFilters.itemName) {
          params.search = exportFilters.itemName;
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester;
        }
        if (exportFilters.purchaser) {
          params.purchaser__username__icontains = exportFilters.purchaser;
        }
        if (exportFilters.supplier) {
          params.supplier_name__icontains = exportFilters.supplier;
        }
        if (exportFilters.minAmount) {
          params.budget_total_amount__gte = parseFloat(exportFilters.minAmount);
        }
        if (exportFilters.maxAmount) {
          params.budget_total_amount__lte = parseFloat(exportFilters.maxAmount);
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.purchase_date__gte =
            exportFilters.dateRange[0].format("YYYY-MM-DD");
          params.purchase_date__lte =
            exportFilters.dateRange[1].format("YYYY-MM-DD");
        }
        // 新增导出筛选参数
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains =
            exportFilters.procurementMethod;
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains =
            exportFilters.requirementSource;
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject;
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit;
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory;
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks;
        }

        const response = await api.purchaseRequests.exportToExcel(params);

        // 对于blob响应，apiClient返回完整的response对象
        const blob = response.data || response;

        // 检查是否为Blob
        if (blob instanceof Blob) {
          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = `采购执行清单_${new Date().toLocaleDateString()}.xlsx`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        } else {
          // 如果不是Blob，可能是错误响应
          console.error("导出响应不是Blob格式:", response);
          throw new Error("导出响应格式错误");
        }

        message.success(
          `导出成功，共导出 ${selectedExportRecords.value.length} 条记录`
        );
        handleExportCancel();
      } catch (error) {
        console.error("导出失败:", error);
        message.error("导出失败");
      } finally {
        exportLoading.value = false;
      }
    };

    // 查看详情
    const viewDetail = (record) => {
      router.push(`/purchase/requests/${record.id}`);
    };

    // 采购操作
    const purchaseItem = async (record) => {
      try {
        const response = await api.purchaseRequests.purchase(record.id, {});
        if (response.code === 200) {
          message.success("采购成功");
          getPurchaseRequests();
        }
      } catch (error) {
        console.error("采购失败:", error);
        message.error("采购失败");
      }
    };

    // 打开采购信息填报模态框
    const openPurchaseModal = async (record) => {
      try {
        // 获取详细信息包括变更记录
        const response = await api.purchaseRequests.getDetail(record.id);
        if (response.code === 200) {
          const data = response.data;

          // 填充表单数据
          Object.assign(purchaseForm, {
            id: data.id,
            item_name: data.item_name,
            specification: data.specification || "",
            unit: data.unit || "", // 添加计量单位
            hierarchy_path: data.hierarchy_path || "",
            requester_name: data.requester_name || "",
            budget_quantity: data.budget_quantity || data.quantity,
            budget_unit_price: data.budget_unit_price || data.unit_price,
            budget_total_amount: data.budget_total_amount || data.total_amount,
            purchase_quantity:
              data.purchase_quantity ||
              data.actual_quantity ||
              data.budget_quantity ||
              data.quantity,
            purchase_unit_price:
              data.purchase_unit_price ||
              data.actual_unit_price ||
              data.budget_unit_price ||
              data.unit_price,
            purchase_method:
              data.purchase_method || data.procurement_method || "",
            supplier_name: data.supplier_name || "",
            purchase_remarks: data.purchase_remarks || "",
          });

          // 获取历史平均价格
          await getHistoricalAveragePrice(data.item_name, data.spec);

          // 获取历史数据
          await getHistoricalData(data.item_name, data.specification);

          // 检查价格差异率
          checkPriceDifference();

          showPurchaseModal.value = true;
        }
      } catch (error) {
        console.error("获取采购详情失败:", error);
        message.error("获取采购详情失败");
      }
    };

    // 保存采购信息（不改变状态）
    const savePurchaseInfo = async () => {
      try {
        await purchaseFormRef.value.validate();

        // 使用新的验证工具进行业务阶段验证
        const validationResult = validateFormData(
          purchaseForm,
          "pending_purchase"
        );
        if (!validationResult.isValid) {
          const errorMessages = Object.values(validationResult.errors).join(
            ", "
          );
          message.error(`验证失败: ${errorMessages}`);
          return;
        }

        purchaseLoading.value = true;
        const params = {
          purchase_quantity: Number(purchaseForm.purchase_quantity),
          purchase_unit_price: Number(purchaseForm.purchase_unit_price),
          supplier_name: purchaseForm.supplier_name,
          purchase_remarks: purchaseForm.purchase_remarks,
        };

        const response = await api.purchaseRequests.updatePurchaseInfo(
          purchaseForm.id,
          params
        );
        if (response.code === 200) {
          message.success("采购信息保存成功");

          // 刷新数据
          getPurchaseRequests();
        }
      } catch (error) {
        console.error("保存采购信息失败:", error);
        message.error("保存采购信息失败");
      } finally {
        purchaseLoading.value = false;
      }
    };

    // 获取历史平均价格
    const getHistoricalAveragePrice = async (itemName, spec) => {
      try {
        const response = await api.purchaseRequests.getHistoricalAveragePrice({
          item_name: itemName,
          spec: spec,
        });
        if (response.code === 200) {
          historicalAveragePrice.value = response.data.average_price || 0;
        }
      } catch (error) {
        console.error("获取历史平均价格失败:", error);
        historicalAveragePrice.value = 0;
      }
    };

    // 获取历史数据
    const getHistoricalData = async (itemName, specification) => {
      try {
        const response = await api.purchaseRequests.getHistoryData(null, itemName, specification);
        if (response.code === 200) {
          const data = response.data;

          // 设置上一次购买数据
          if (data.last_purchase) {
            historicalData.lastPurchase = {
              quantity: data.last_purchase.purchase_quantity,
              unitPrice: parseFloat(data.last_purchase.purchase_unit_price || 0).toFixed(2),
              totalAmount: parseFloat(data.last_purchase.purchase_total_amount || 0).toFixed(2)
            };
          }

          // 设置历史购买汇总数据
          if (data.total_purchase) {
            historicalData.totalPurchase = {
              totalQuantity: data.total_purchase.total_quantity,
              averageUnitPrice: parseFloat(data.total_purchase.average_unit_price || 0).toFixed(2),
              totalAmount: parseFloat(data.total_purchase.total_amount || 0).toFixed(2)
            };
          }
        }
      } catch (error) {
        console.error("获取历史数据失败:", error);
        // 重置历史数据
        historicalData.lastPurchase = null;
        historicalData.totalPurchase = null;
      }
    };

    // 检查价格差异率
    const checkPriceDifference = () => {
      if (
        purchaseForm.actual_unit_price > 0 &&
        historicalAveragePrice.value > 0
      ) {
        const difference = Math.abs(
          purchaseForm.actual_unit_price - historicalAveragePrice.value
        );
        priceDifferenceRate.value =
          (difference / historicalAveragePrice.value) * 100;
      } else {
        priceDifferenceRate.value = 0;
      }
    };

    // 提交采购信息（改变状态为已采购）
    const submitPurchaseInfo = async () => {
      try {
        await purchaseFormRef.value.validate();

        // 如果有价格异常但没有填写异常原因，提示用户
        if (
          hasPriceException.value &&
          !purchaseForm.price_exception_reason.trim()
        ) {
          message.warning("检测到价格异常，请填写价格异常原因说明");
          return;
        }

        purchaseLoading.value = true;
        const params = {
          actual_quantity: Number(purchaseForm.purchase_quantity),
          actual_unit_price: Number(purchaseForm.purchase_unit_price),
          purchase_method: purchaseForm.purchase_method,
          supplier_name: purchaseForm.supplier_name,
          purchase_remarks: purchaseForm.purchase_remarks,
          price_exception_reason: purchaseForm.price_exception_reason,
          status: "purchased",
        };

        const response = await api.purchaseRequests.completePurchase(
          purchaseForm.id,
          params
        );
        if (response.code === 200) {
          message.success("采购完成");

          showPurchaseModal.value = false;
          getPurchaseRequests();
        }
      } catch (error) {
        console.error("提交采购信息失败:", error);
        message.error("提交采购信息失败");
      } finally {
        purchaseLoading.value = false;
      }
    };

    // 查看采购信息
    const viewPurchaseInfo = (record) => {
      Modal.info({
        title: "采购信息详情",
        content: `
          <div style="padding: 16px;">
            <p style="margin-bottom: 12px;"><strong>物品名称：</strong>${record.item_name
          }</p>
            <p style="margin-bottom: 12px;"><strong>规格型号：</strong>${record.spec || "无"
          }</p>
            <p style="margin-bottom: 12px;"><strong>采购数量：</strong>${record.quantity
          }</p>
            <p style="margin-bottom: 12px;"><strong>预算单价：</strong>¥${record.unit_price
          }</p>
            <p style="margin-bottom: 12px;"><strong>预算金额：</strong>¥${record.amount
          }</p>
            <p style="margin-bottom: 12px;"><strong>采购人：</strong>${record.purchaser_name || "未分配"
          }</p>
            <p style="margin-bottom: 12px;"><strong>采购时间：</strong>${record.purchase_date ? formatDate(record.purchase_date) : "未采购"
          }</p>
            <p style="margin-bottom: 12px;"><strong>需求单位：</strong>${record.hierarchy_path || "未知单位"
          }</p>
          </div>
        `,
        width: 600,
        okText: "关闭",
      });
    };

    // 发货通知
    const notifyShipping = async (record) => {
      try {
        const response = await api.purchaseRequests.notifyShipping(record.id);
        if (response.code === 200) {
          message.success("发货通知成功");
          getPurchaseRequests();
        }
      } catch (error) {
        console.error("发货通知失败:", error);
        message.error("发货通知失败");
      }
    };

    // 退回需求
    const returnRequest = (record) => {
      currentReturnRequest.value = record;
      returnForm.reasons = [];
      returnForm.detail = "";
      showReturnModal.value = true;
    };

    // 处理退回
    const handleReturn = async () => {
      if (returnForm.reasons.length === 0) {
        message.warning("请选择退回原因");
        return;
      }

      if (returnForm.reasons.includes("其他") && !returnForm.detail.trim()) {
        message.warning('选择"其他"时请填写详细说明');
        return;
      }

      returnLoading.value = true;
      try {
        // 组合退回原因
        let reason = returnForm.reasons.join("、");
        if (returnForm.detail.trim()) {
          reason += `：${returnForm.detail}`;
        }

        const response = await api.purchaseRequests.return(
          currentReturnRequest.value.id,
          { reason }
        );
        if (response.code === 200) {
          message.success("退回成功");
          showReturnModal.value = false;
          getPurchaseRequests();
        }
      } catch (error) {
        console.error("退回失败:", error);
        message.error("退回失败");
      } finally {
        returnLoading.value = false;
      }
    };

    // 取消退回
    const cancelReturn = () => {
      showReturnModal.value = false;
      currentReturnRequest.value = null;
    };

    // 批量发货通知
    const batchNotifyShipping = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning("请选择要发货通知的记录");
        return;
      }

      const purchasedRequests = requests.value.filter(
        (item) =>
          selectedRowKeys.value.includes(item.id) && (item._originalStatus || item.status) === "purchased"
      );

      if (purchasedRequests.length === 0) {
        message.warning("所选项目中没有已采购的记录");
        return;
      }

      Modal.confirm({
        title: "批量发货通知确认",
        content: `确定要对选中的 ${purchasedRequests.length} 个已采购记录进行发货通知吗？`,
        okText: "确定",
        cancelText: "取消",
        onOk: async () => {
          try {
            const requestIds = purchasedRequests.map((req) => req.id);
            const response = await api.purchaseRequests.batchNotifyShipping(
              requestIds
            );
            if (response.code === 200) {
              message.success(
                `批量发货通知成功，共处理 ${purchasedRequests.length} 条记录`
              );
              selectedRowKeys.value = [];
              getPurchaseRequests();
            }
          } catch (error) {
            console.error("批量发货通知失败:", error);
            message.error("批量发货通知失败");
          }
        },
      });
    };

    // 排序状态
    const sortField = ref("");
    const sortOrder = ref("");

    // 表格变化处理 - 支持分页和排序
    const handleTableChange = (pag, _filters, sorter) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;

      // 处理排序
      if (sorter && sorter.field) {
        sortField.value = sorter.field;
        sortOrder.value = sorter.order;
      } else {
        sortField.value = "";
        sortOrder.value = "";
      }

      getPurchaseRequests();
    };

    // 监听导出模态框打开
    watch(
      () => showExportModal.value,
      (newVal) => {
        if (newVal) {
          // 重置分页状态
          exportRecordPagination.value.current = 1;
          exportRecordPagination.value.total = 0;
          searchExportRecords();
        }
      }
    );

    onMounted(async () => {
      await loadStatusOptions();
      await getDepartments();
      await getProcurementMethodOptions();
      await getFilterOptions();
      await getPurchaseRequests();
    });

    return {
      loading,
      requests,
      selectedRowKeys,

      departments,
      procurementMethodOptions,
      purchaseTypes,
      purchasers,
      suppliers,
      showFilters,
      presetDropdownOpen,
      showExportModal,
      showColumnFilter,
      filters,
      columnOptions,
      selectedColumns,
      // 导出相关
      exportActiveTab,
      exportLoading,
      previewLoading,
      previewData,
      previewTotal,
      showPreviewModal,
      selectedExportFields,
      exportFieldOptions,
      exportFieldCategories,
      exportFilters,
      exportFieldsIndeterminate,
      exportFieldsCheckAll,
      previewColumns,
      onExportFieldsCheckAllChange,
      onExportFieldsChange,
      resetExportConfig,
      loadPreviewData,
      executeExport,
      resetExportFilters,
      searchExportRecords,
      selectAllExportFields,
      resetExportFields,
      selectRequiredExportFields,
      previewExportData,
      fieldsCollapseKey,
      fieldCategories,
      presetMenuItems,
      pagination,
      rowSelection,

      filteredColumns,
      pendingPurchaseCount,
      purchasedCount,
      totalPurchaseAmount,
      totalCount,
      hasPurchasedRequests,
      purchasedSelectedCount,

      // 退回相关
      showReturnModal,
      returnLoading,
      currentReturnRequest,
      returnForm,
      // 采购信息填报相关
      showPurchaseModal,
      purchaseLoading,
      purchaseFormRef,
      purchaseForm,
      purchaseFormRules,
      purchaseAmount,
      historicalAveragePrice,
      historicalData,
      priceDifferenceRate,
      isPriceExceedsBudget,
      // 方法
      getPurchaseRequests,
      getDepartments,
      getFilterOptions,
      filterOption,
      toggleFilters,
      onColumnChange,
      handleColumnChange,
      handlePresetClick,
      handleSelectAll,
      handleReset,
      selectAllColumns,
      resetColumns,
      applyPreset,
      handleFilterChange,
      debouncedFilterChange,
      resetFilters,
      getStatusColor,
      formatAmount,
      statusOptions,
      viewDetail,
      purchaseItem,
      openPurchaseModal,
      savePurchaseInfo,
      submitPurchaseInfo,
      getHistoricalAveragePrice,
      checkPriceDifference,
      hasPriceException,
      viewPurchaseInfo,
      notifyShipping,
      batchNotifyShipping,

      returnRequest,
      handleReturn,
      cancelReturn,
      handleTableChange,
      sortField,
      sortOrder,
      // 导出记录选择相关
      exportRecords,
      selectedExportRecords,
      selectedExportRecordData,
      exportSearchLoading,
      exportRecordPageSize,
      exportRecordPagination,
      exportRecordColumns,
      exportRecordRowSelection,
      // 格式化函数
      formatDate,
      formatDateToYMD,
      // 导出记录操作函数
      onExportRecordPageSizeChange,
      onExportRecordPageChange,
      handleExportCancel,
    };
  },
};
</script>

<style scoped>
@import "@/styles/business-panels.css";

.purchase-procurement {
  padding: 0px;
}

/* 表格头部flex布局 - 与采购总览页面保持一致 */
.table-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-light);
  gap: 24px;
}

.table-title-section {
  flex-shrink: 0;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: 13px;
  font-weight: 400;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

/* 移除响应式布局，保持固定布局 */

/* 表格标题区域样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
}

.table-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.record-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-filters {
  margin-top: 8px;
  padding: 12px 0;
  border-top: 1px solid var(--border-light);
}

.table-actions {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

/* 详细筛选控件样式 */
.detailed-filters {
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.filter-select,
.filter-input,
.filter-date-picker {
  height: 32px;
  font-size: var(--text-sm);
}

.budget-range-input {
  width: 200px;
}

.budget-range-input .ant-input {
  height: 32px;
  font-size: var(--text-sm);
}

/* 紧凑型操作按钮样式 */
.compact-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 统一筛选区域样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);

  background: linear-gradient(135deg,
      var(--bg-secondary) 0%,
      var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

/* 次要操作按钮样式 */
.secondary-action-btn {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 48px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 140px;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
  transform: translateY(-1px);
}

.action-buttons-section {
  margin: 24px 0;
  text-align: center;
}

.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  color: white;
}

/* 筛选区按钮高度对齐 */
.unified-filter-section .secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-filter-section .secondary-action-btn:hover {
  border-color: var(--primary-light);
}

/* 采购信息填报模态框样式 */
.purchase-form {
  padding: 16px 0;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

/* 历史数据标签样式 */
.historical-data-tags {
  margin-top: 8px;
}

.historical-row {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 4px;
}

.historical-row:last-child {
  margin-bottom: 0;
}

.form-section h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 600;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
}

.form-hint {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.price-warning {
  margin-top: 8px;
}

.price-warning .ant-alert {
  font-size: 12px;
}

.form-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
  text-align: right;
}

/* 退回对话框样式 */
:deep(.return-modal .ant-modal-header) {
  background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
  border-bottom: none;
  border-radius: 8px 8px 0 0;
}

:deep(.return-modal .ant-modal-title) {
  color: white;
  font-weight: 600;
}

.modal-title {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.modal-content {
  padding: 20px 0;
}

.request-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.return-reasons {
  width: 100%;
}

.return-reasons .ant-checkbox-wrapper {
  margin-bottom: 12px;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.3s;
  width: 100%;
  display: flex;
  align-items: center;
}

.return-reasons .ant-checkbox-wrapper:hover {
  border-color: #ff4d4f;
  background-color: #fff2f0;
}

.return-reasons .ant-checkbox-wrapper-checked {
  border-color: #ff4d4f;
  background-color: #ffece8;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-modal-footer) {
  border-top: none;
  padding: 0;
}

.field-categories-container {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 16px;
  align-items: flex-start;
}

.field-category-section {
  flex: 0 0 auto;
  min-width: 120px;
}

.category-title {
  margin: 0 0 6px 0;
  font-size: 13px;
  font-weight: 600;
  color: #666;
}

.category-fields {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.column-option {
  display: flex;
  align-items: center;
}

.column-title {
  font-size: 12px;
}

/* 导出字段选择样式 - 与字段筛选保持一致 */
.export-field-category {
  margin-bottom: 12px;
}

.export-category-title {
  margin: 0 0 6px 0;
  font-weight: 600;
  font-size: 13px;
  color: #666;
}

.export-category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.export-field-option {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 表格标题区域样式 */
.table-header {
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg,
      var(--bg-secondary) 0%,
      var(--bg-primary) 100%);
  width: 100%;
  box-sizing: border-box;
}

.table-title-section {
  flex: 1;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title span:first-child {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: normal;
}

.table-actions {
  flex-shrink: 1;
  display: flex;
  justify-content: flex-end;
}

/* 导出配置样式 */
.export-config-container {
  padding: 0;
  display: flex;
  flex-direction: column;
  max-height: 70vh;
  overflow-y: auto;
}

.filter-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.filter-form-container {
  padding: 8px 0;
}

/* 导出字段配置样式 */
.export-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
}

.export-field-category {
  flex: 1;
  min-width: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  overflow: hidden;
}

.export-category-header {
  background-color: #e6f7ff;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 12px;
}

.export-category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.export-category-fields {
  padding: 12px;
}

.export-field-option {
  margin-bottom: 8px;
}

.export-field-option:last-child {
  margin-bottom: 0;
}

.field-title {
  margin-right: 8px;
}



.export-field-category {
  margin-bottom: 16px;
}

.export-category-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #595959;
}

.export-category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.export-field-option {
  padding: 4px 0;
}

/* 记录选择部分样式 */
.records-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-count {
  font-size: 13px;
  color: #666;
}

.actions-section {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.table-container {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 修复操作列透明背景问题 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5 !important;
}

:deep(.ant-table-tbody > tr:hover > td.ant-table-cell-fix-right) {
  background-color: #f5f5f5 !important;
}

/* 预览模态框样式 */
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.preview-header {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.preview-footer {
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  text-align: center;
}

/* 记录选择部分样式 */
.records-section {
  margin-top: 16px;
}
</style>
