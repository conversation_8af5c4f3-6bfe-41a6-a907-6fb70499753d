<template>
  <div class="user-management">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <UserOutlined />
          用户管理
        </h1>
        <p class="page-subtitle">管理系统用户账号，维护用户权限和基础信息</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalCount }}</span>
          <span class="stat-label">用户总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ activeCount }}</span>
          <span class="stat-label">活跃用户</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ inactiveCount }}</span>
          <span class="stat-label">停用用户</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ adminCount }}</span>
          <span class="stat-label">管理员</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ onlineCount }}</span>
          <span class="stat-label">在线用户</span>
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="unified-filter-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-input v-model:value="searchTerm" placeholder="用户名、姓名、电话" allow-clear @pressEnter="searchUsers"
            class="filter-input">
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="departmentFilter" placeholder="选择部门" allowClear
            @change="handleDepartmentFilterChange" class="filter-select">
            <a-select-option value="">全部部门</a-select-option>
            <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
              {{ dept.dept_name }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="roleFilter" placeholder="选择角色" allowClear @change="handleRoleFilterChange"
            class="filter-select">
            <a-select-option value="">全部角色</a-select-option>
            <a-select-option v-for="role in roles" :key="role.id" :value="role.code">
              {{ role.name }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="statusFilter" placeholder="选择状态" allowClear @change="handleStatusFilterChange"
            class="filter-select">
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">停用</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="searchUsers" class="secondary-action-btn">
            <SearchOutlined />
            搜索
          </a-button>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="resetSearch" class="secondary-action-btn">
            <ReloadOutlined />
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 操作栏 -->
    <div class="action-buttons-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col>
          <a-button type="primary" @click="showAddModal" class="primary-action-btn">
            <PlusOutlined />
            新增用户
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="showImportModal" class="secondary-action-btn">
            <UploadOutlined />
            批量导入
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="exportUsers" class="secondary-action-btn">
            <DownloadOutlined />
            导出
          </a-button>
        </a-col>
        <!-- 批量操作按钮 -->
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button type="primary" @click="batchEnable" class="primary-action-btn">
            <CheckCircleOutlined />
            批量启用 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button @click="batchDisable" class="secondary-action-btn">
            <StopOutlined />
            批量停用 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button @click="batchResetPassword" class="secondary-action-btn">
            <KeyOutlined />
            批量重置密码 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button danger @click="batchDelete" class="danger-action-btn">
            <DeleteOutlined />
            批量删除 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 用户表格 -->
    <div class="table-section business-card">
      <a-table :key="tableKey" :columns="columns" :data-source="users" :row-key="record => record.id"
        :row-selection="rowSelection" :pagination="paginationConfig" @change="handleTableChange" class="unified-table"
        size="large" :scroll="{ x: 1200 }">
        <template #bodyCell="{ column, record }">


          <!-- 部门显示 -->
          <template v-if="column.dataIndex === 'dept_name'">
            <span v-if="record.dept_name" class="department-info">
              {{ record.dept_name }}
            </span>
            <span v-else class="no-department">未分配</span>
          </template>

          <!-- 角色显示 -->
          <template v-if="column.dataIndex === 'role_name'">
            <div class="roles-container">
              <a-tag v-if="record.role_name" :color="getRoleColor(record.role)" class="role-tag">
                {{ record.role_name }}
              </a-tag>
              <span v-if="!record.role_name" class="no-roles">未分配角色</span>
            </div>
          </template>

          <!-- 最后登录时间显示 -->
          <template v-if="column.dataIndex === 'last_login'">
            <span v-if="record.last_login" class="last-login-info">
              <ClockCircleOutlined />
              {{ formatDateTime(record.last_login) }}
            </span>
            <span v-else class="never-login">从未登录</span>
          </template>

          <!-- 账号状态 -->
          <template v-if="column.dataIndex === 'is_active'">
            <a-tag :color="record.is_active ? 'success' : 'error'">
              <CheckCircleOutlined v-if="record.is_active" />
              <StopOutlined v-else />
              {{ record.is_active ? '启用' : '停用' }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" @click="viewDetail(record)" size="small">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="link" @click="editUser(record)" size="small">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="link" @click="resetPassword(record)" size="small">
                <KeyOutlined />
                重置密码
              </a-button>
              <a-popconfirm :title="record.is_active ? '确定要停用这个用户吗？' : '确定要启用这个用户吗？'"
                @confirm="toggleUserStatus(record)" ok-text="确定" cancel-text="取消">
                <a-button type="link" :class="record.is_active ? 'text-warning' : 'text-success'" size="small">
                  <StopOutlined v-if="record.is_active" />
                  <CheckCircleOutlined v-else />
                  {{ record.is_active ? '停用' : '启用' }}
                </a-button>
              </a-popconfirm>
              <a-button type="link" danger size="small" @click="deleteUser(record)">
                <DeleteOutlined />
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增/编辑用户模态框 -->
    <a-modal v-model:open="modalVisible" :title="isEditMode ? '编辑用户' : '新增用户'" @ok="handleSubmit" @cancel="handleCancel"
      width="800px" class="user-modal" ok-text="确定" cancel-text="取消">
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical" class="user-form">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="登录账号" name="username">
              <a-input v-model:value="formData.username" placeholder="请输入登录账号" size="large" :disabled="isEditMode" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="真实姓名" name="real_name">
              <a-input v-model:value="formData.real_name" placeholder="请输入真实姓名" size="large" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="所属部门" name="dept_id">
              <DepartmentTreeSelect v-model="formData.dept_id" :return-path="false" size="large" style="width: 100%"
                @change="handleDepartmentChange" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="角色" name="role">
              <RoleSelector v-model="formData.role" size="large" style="width: 100%" @change="handleRoleChange" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="联系电话" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入联系电话" size="large" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="账号状态" name="is_active">
              <a-switch v-model:checked="formData.is_active" checked-children="启用" un-checked-children="停用" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" v-if="!isEditMode">
          <a-col :span="12">
            <a-form-item label="密码" name="password">
              <a-input-password v-model:value="formData.password" placeholder="请输入密码" size="large" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="确认密码" name="confirm_password">
              <a-input-password v-model:value="formData.confirm_password" placeholder="请确认密码" size="large" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 用户详情模态框 -->
    <a-modal v-model:open="detailVisible" title="用户详情" :footer="null" width="800px" class="user-detail-modal">
      <div class="user-detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="登录账号">
            <span class="detail-value">{{ currentUser.username }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="真实姓名">
            <span class="detail-value">{{ currentUser.real_name }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            <span class="detail-value">{{ currentUser.dept_name || '未分配' }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="角色">
            <a-tag v-if="currentUser.role_name" color="blue">{{ currentUser.role_name }}</a-tag>
            <span v-else class="detail-empty">未分配角色</span>
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            <span class="detail-value">{{ currentUser.phone || '未填写' }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="账号状态">
            <a-tag :color="currentUser.is_active ? 'success' : 'error'">
              <CheckCircleOutlined v-if="currentUser.is_active" />
              <StopOutlined v-else />
              {{ currentUser.is_active ? '启用' : '停用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            <span class="detail-value">{{ formatDateTime(currentUser.created_at) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="最后登录">
            <span v-if="currentUser.last_login" class="detail-value">
              {{ formatDateTime(currentUser.last_login) }}
            </span>
            <span v-else class="detail-empty">从未登录</span>
          </a-descriptions-item>
          <a-descriptions-item label="最后更新" :span="2">
            <span class="detail-value">{{ formatDateTime(currentUser.updated_at) }}</span>
          </a-descriptions-item>
        </a-descriptions>

        <div class="detail-actions">
          <a-space>
            <a-button @click="editUser(currentUser)" type="primary">
              <EditOutlined />
              编辑用户
            </a-button>
            <a-button @click="resetPassword(currentUser)">
              <KeyOutlined />
              重置密码
            </a-button>
            <a-button @click="detailVisible = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 批量导入模态框 -->
    <a-modal v-model:open="importModalVisible" title="批量导入用户" @ok="handleImport" @cancel="handleImportCancel"
      width="600px" :confirm-loading="importLoading" ok-text="导入" cancel-text="取消">
      <div class="import-section">
        <a-alert message="导入说明" description="请下载模板文件，按照模板格式填写用户信息后上传。支持Excel格式(.xlsx)文件。" type="info" show-icon
          style="margin-bottom: 16px;" />

        <div style="margin-bottom: 16px;">
          <a-button @click="downloadUserTemplate" type="link">
            <DownloadOutlined />
            下载用户导入模板
          </a-button>
        </div>

        <a-upload-dragger v-model:fileList="importFileList" :before-upload="beforeUpload" :remove="handleRemoveFile"
          accept=".xlsx,.xls" :multiple="false">
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持单个文件上传，仅支持Excel格式(.xlsx, .xls)
          </p>
        </a-upload-dragger>

        <!-- 导入结果显示 -->
        <div v-if="importResult" style="margin-top: 16px;">
          <a-alert :message="importResult.title" :description="importResult.message"
            :type="importResult.success ? 'success' : 'error'" show-icon />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  UserOutlined,
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  UploadOutlined,
  CheckCircleOutlined,
  StopOutlined,
  EyeOutlined,
  EditOutlined,
  KeyOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  DownloadOutlined,
  InboxOutlined
} from '@ant-design/icons-vue';
import api from '@/api';
import DepartmentTreeSelect from '@/components/Department/DepartmentTreeSelect.vue';
import RoleSelector from '@/components/Role/RoleSelector.vue';

// 搜索关键词
const searchTerm = ref('');
// 部门筛选
const departmentFilter = ref('');
// 角色筛选
const roleFilter = ref('');
// 状态筛选
const statusFilter = ref('');

// 表单引用
const formRef = ref(null);

// 模态框控制
const modalVisible = ref(false);
// 是否为编辑模式
const isEditMode = ref(false);

// 表单数据
const formData = ref({
  username: '',
  real_name: '',
  dept_id: null,
  role: '',
  phone: '',
  email: '',
  is_active: true,
  password: '',
  confirm_password: ''
});

// 用户列表
const users = ref([]);
// 部门列表
const departments = ref([]);
// 角色列表
const roles = ref([]);

// 导入相关状态
const importModalVisible = ref(false);
const importLoading = ref(false);
const importFileList = ref([]);
const importResult = ref(null);

const selectedRowKeys = ref([]);

// 表格刷新key
const tableKey = ref(0);

// 统计数据
const statistics = ref({
  total_count: 0,
  active_count: 0,
  inactive_count: 0,
  admin_count: 0,
  online_count: 0
});

// 计算属性
const totalCount = computed(() => statistics.value.total_count);
const activeCount = computed(() => statistics.value.active_count);
const inactiveCount = computed(() => statistics.value.inactive_count);
const adminCount = computed(() => statistics.value.admin_count);
const onlineCount = computed(() => statistics.value.online_count);

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: () => ({
    disabled: false, // 可以根据需要设置禁用条件
  }),
};

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }) => pagination.pageSize * (pagination.current - 1) + index + 1
  },
  {
    title: '登录账号',
    dataIndex: 'username',
    width: 100,
    sorter: true
  },
  {
    title: '真实姓名',
    dataIndex: 'real_name',
    width: 90,
    sorter: true
  },
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 120
  },
  {
    title: '角色',
    dataIndex: 'role_name',
    width: 100
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 110
  },
  {
    title: '最后登录',
    dataIndex: 'last_login',
    width: 130,
    sorter: true
  },
  {
    title: '账号状态',
    dataIndex: 'is_active',
    width: 80
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 280,
    fixed: 'right'
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
});

const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: pagination.showSizeChanger,
  showQuickJumper: pagination.showQuickJumper,
  showTotal: pagination.showTotal,
  pageSizeOptions: ['10', '20', '50', '100']
}));

// 表单验证规则
const rules = {
  username: [{ required: true, message: '请输入登录账号' }],
  real_name: [{ required: true, message: '请输入真实姓名' }],
  dept_id: [{ required: true, message: '请选择所属部门' }],
  role: [{ required: true, message: '请选择角色' }],
  phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }],
  email: [{ type: 'email', message: '请输入正确的邮箱地址' }]
};

// 获取用户列表
const getUsers = async () => {
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize
    };

    // 添加搜索参数
    if (searchTerm.value) {
      params.search = searchTerm.value;
    }

    // 添加筛选参数
    if (departmentFilter.value) {
      params.dept_id = departmentFilter.value;
    }
    if (roleFilter.value) {
      params.role = roleFilter.value;
    }
    if (statusFilter.value !== '') {
      params.is_active = statusFilter.value;
    }

    const response = await api.users.getList(params);

    if (response.code === 200) {
      const userData = response.data.results || response.data || [];

      // 确保数据是数组格式
      if (Array.isArray(userData)) {
        // 强制触发响应性更新
        users.value = [];
        await new Promise(resolve => setTimeout(resolve, 10));
        users.value = [...userData];
        pagination.total = response.data.count || userData.length;

        // 强制重新渲染表格
        tableKey.value += 1;
        await new Promise(resolve => setTimeout(resolve, 50));
      } else {
        users.value = [];
        pagination.total = 0;
      }
    } else {
      message.error(response.message || '获取用户列表失败');
      users.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    message.error('获取用户列表失败');
    users.value = [];
    pagination.total = 0;
  }
};

// 获取统计信息
const getStatistics = async () => {
  try {
    const response = await api.users.getAdminStatistics();
    if (response.code === 200) {
      statistics.value = {
        total_count: response.data.total_count || 0,
        active_count: response.data.active_count || 0,
        inactive_count: response.data.inactive_count || 0,
        admin_count: response.data.admin_count || 0,
        online_count: response.data.online_count || 0
      };
    }
  } catch (error) {
    console.error('获取统计信息失败:', error);
    // 如果统计API失败，设置默认值
    statistics.value = {
      total_count: 0,
      active_count: 0,
      inactive_count: 0,
      admin_count: 0,
      online_count: 0
    };
  }
};

// 获取角色颜色
const getRoleColor = (role) => {
  switch (role) {
    case 'admin': return 'blue';
    case 'approver': return 'green';
    case 'acceptor': return 'orange';
    case 'finance': return 'purple';
    default: return 'default';
  }
};



// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '';
  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 显示新增用户模态框
const showAddModal = () => {
  isEditMode.value = false;
  formData.value = {
    username: '',
    real_name: '',
    dept_id: null,
    role: '',
    phone: '',
    email: '',
    is_active: true,
    password: '',
    confirm_password: ''
  };
  modalVisible.value = true;
};

// 用户详情模态框控制
const detailVisible = ref(false);
const currentUser = ref({});

// 查看用户详情
const viewDetail = (record) => {
  currentUser.value = { ...record };
  detailVisible.value = true;
};

// 编辑用户
const editUser = (record) => {
  isEditMode.value = true;
  formData.value = {
    id: record.id,
    username: record.username,
    real_name: record.real_name,
    dept_id: record.dept_id,
    role: record.role,
    phone: record.phone,
    email: record.email,
    is_active: record.is_active
  };

  // 如果是从详情页面跳转过来的，关闭详情模态框
  if (detailVisible.value) {
    detailVisible.value = false;
  }

  modalVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    const values = await formRef.value.validateFields();

    let response;
    if (isEditMode.value) {
      // 更新用户信息
      response = await api.users.update(formData.value.id, values);
    } else {
      // 创建新用户
      response = await api.users.create(values);
    }

    if (response.code === 200) {
      message.success(isEditMode.value ? '更新成功' : '创建成功');
      modalVisible.value = false;
      await getUsers();
      await getStatistics(); // 刷新统计信息
    } else {
      message.error(response.message || '操作失败');
    }
  } catch (error) {
    message.error('操作失败');
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
};

// 切换用户状态（启用/停用）
const toggleUserStatus = async (record) => {
  try {
    const action = record.is_active ? '停用' : '启用';
    const response = await api.users.toggleStatus(record.id, !record.is_active);
    if (response.code === 200) {
      message.success(`用户${action}成功`);
      getUsers();
      getStatistics(); // 刷新统计信息
    } else {
      message.error(response.message || `${action}失败`);
    }
  } catch (error) {
    console.error('切换用户状态失败:', error);
    message.error('操作失败，请稍后重试');
  }
};

// 删除用户
const deleteUser = async (record) => {
  Modal.confirm({
    title: "确认删除",
    content: `确定删除用户 ${record.real_name} 吗？`,
    okText: "确定",
    cancelText: "取消",
    okType: "danger",
    onOk: async () => {
      try {
        const response = await api.users.delete(record.id);
        if (response.code === 200) {
          message.success('删除成功');
          getUsers();
          getStatistics(); // 刷新统计信息
        }
      } catch (error) {
        message.error('删除失败');
      }
    }
  });
};

// 搜索用户
const searchUsers = () => {
  getUsers();
};

// 处理部门选择变化
const handleDepartmentChange = (value, selectedNode) => {
  console.log('部门选择变化:', { value, selectedNode });
  formData.value.dept_id = value;
};

// 处理角色选择变化
const handleRoleChange = (value, selectedRole) => {
  console.log('角色选择变化:', { value, selectedRole });
  formData.value.role = value;
};

// 重置搜索
const resetSearch = () => {
  searchTerm.value = '';
  departmentFilter.value = '';
  roleFilter.value = '';
  statusFilter.value = '';
  pagination.current = 1;
  getUsers();
};

// 部门筛选变化处理
const handleDepartmentFilterChange = () => {
  pagination.current = 1;
  getUsers();
};

// 角色筛选变化处理
const handleRoleFilterChange = () => {
  pagination.current = 1;
  getUsers();
};

// 状态筛选变化处理
const handleStatusFilterChange = () => {
  pagination.current = 1;
  getUsers();
};



// 显示导入模态框
const showImportModal = () => {
  importModalVisible.value = true;
  importFileList.value = [];
  importResult.value = null;
};

// 下载用户导入模板
const downloadUserTemplate = async () => {
  try {
    const response = await api.users.downloadTemplate();

    // 检查响应数据
    if (!response || !response.data) {
      throw new Error('响应数据为空');
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error('下载的文件为空');
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', '用户导入模板.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    message.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error(`下载模板失败: ${error.message || '未知错误'}`);
  }
};

// 文件上传前检查
const beforeUpload = (file) => {
  // 如果已经有文件，先清除
  if (importFileList.value.length > 0) {
    importFileList.value = [];
  }

  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传Excel文件！');
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }
  return false; // 阻止自动上传
};

// 移除文件
const handleRemoveFile = () => {
  importFileList.value = [];
  importResult.value = null;
};

// 处理导入
const handleImport = async () => {
  if (importFileList.value.length === 0) {
    message.error('请选择要导入的文件');
    return;
  }

  try {
    importLoading.value = true;
    const formData = new FormData();

    // 获取原始文件对象
    const file = importFileList.value[0].originFileObj || importFileList.value[0];
    formData.append('file', file);

    const response = await api.users.importFromExcel(formData);

    if (response.code === 200) {
      importResult.value = {
        success: true,
        title: '导入成功',
        message: response.message || '用户数据导入成功'
      };
      message.success('导入成功');
      getUsers(); // 刷新用户列表
    } else {
      importResult.value = {
        success: false,
        title: '导入失败',
        message: response.message || '导入过程中发生错误'
      };
      message.error(response.message || '导入失败');
    }
  } catch (error) {
    console.error('导入失败:', error);
    importResult.value = {
      success: false,
      title: '导入失败',
      message: error.response?.data?.message || '导入过程中发生错误'
    };
    message.error('导入失败');
  } finally {
    importLoading.value = false;
  }
};

// 取消导入
const handleImportCancel = () => {
  importModalVisible.value = false;
  importFileList.value = [];
  importResult.value = null;
};

// 导出用户数据
const exportUsers = async () => {
  try {
    const response = await api.users.export({
      dept_id: departmentFilter.value,
      role: roleFilter.value,
      status: statusFilter.value,
      search: searchTerm.value
    });

    // 检查响应数据
    if (!response || !response.data) {
      throw new Error('响应数据为空');
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error('导出的文件为空');
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `用户信息_${new Date().toLocaleDateString()}.xlsx`);
    document.body.appendChild(link);
    link.click();
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error(`导出失败: ${error.message || '未知错误'}`);
  }
};



// 重置密码
const resetPassword = (record) => {
  Modal.confirm({
    title: '重置密码确认',
    content: `确定要重置用户 "${record.real_name}" 的密码吗？密码将重置为默认密码 "123456"。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const response = await api.users.resetPassword(record.id);
        if (response.code === 200) {
          message.success(`用户 ${record.real_name} 的密码重置成功`);
        } else {
          message.error(response.message || '重置密码失败');
        }
      } catch (error) {
        console.error('重置密码失败:', error);
        message.error('重置密码失败，请稍后重试');
      }
    }
  });
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  getUsers();
};

// 获取部门列表 - 用于下拉选择，使用专门的无分页接口
const getDepartments = async () => {
  try {
    const response = await api.departments.getAll();
    if (response.code === 200) {
      departments.value = response.data || [];
    }
  } catch (error) {
    console.error('获取部门列表失败:', error);
  }
};

// 获取角色列表
const getRoles = async () => {
  try {
    const response = await api.roles.getList();
    if (response.code === 200) {
      roles.value = response.data.results || response.data || [];
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
};

// 批量启用用户
const batchEnable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要启用的用户');
    return;
  }

  try {
    const promises = selectedRowKeys.value.map(id => {
      const user = users.value.find(u => u.id === id);
      return api.users.update(id, { ...user, is_active: true });
    });

    await Promise.all(promises);
    message.success(`成功启用 ${selectedRowKeys.value.length} 个用户`);
    selectedRowKeys.value = [];
    getUsers();
  } catch (error) {
    console.error('批量启用失败:', error);
    message.error('批量启用失败');
  }
};

// 批量停用用户
const batchDisable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要停用的用户');
    return;
  }

  try {
    const promises = selectedRowKeys.value.map(id => {
      const user = users.value.find(u => u.id === id);
      return api.users.update(id, { ...user, is_active: false });
    });

    await Promise.all(promises);
    message.success(`成功停用 ${selectedRowKeys.value.length} 个用户`);
    selectedRowKeys.value = [];
    getUsers();
  } catch (error) {
    console.error('批量停用失败:', error);
    message.error('批量停用失败');
  }
};

// 批量重置密码
const batchResetPassword = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要重置密码的用户');
    return;
  }

  Modal.confirm({
    title: '批量重置密码确认',
    content: `确定要重置选中的 ${selectedRowKeys.value.length} 个用户的密码吗？密码将重置为默认密码。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const promises = selectedRowKeys.value.map(id => api.users.resetPassword(id));
        await Promise.all(promises);
        message.success(`成功重置 ${selectedRowKeys.value.length} 个用户的密码`);
        selectedRowKeys.value = [];
      } catch (error) {
        console.error('批量重置密码失败:', error);
        message.error('批量重置密码失败');
      }
    }
  });
};

// 批量删除用户
const batchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的用户');
    return;
  }

  Modal.confirm({
    title: '批量删除确认',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个用户吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const promises = selectedRowKeys.value.map(id => api.users.delete(id));
        await Promise.all(promises);
        message.success(`成功删除 ${selectedRowKeys.value.length} 个用户`);
        selectedRowKeys.value = [];
        getUsers();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    }
  });
};

// 定时器引用
let statisticsTimer = null;

// 页面加载时初始化
onMounted(() => {
  getUsers();
  getStatistics(); // 获取统计信息
  getDepartments();
  getRoles();

  // 设置定时器，每30秒更新一次在线用户统计
  statisticsTimer = setInterval(() => {
    getStatistics();
  }, 30000);
});

// 页面卸载时清理定时器
onUnmounted(() => {
  if (statisticsTimer) {
    clearInterval(statisticsTimer);
    statisticsTimer = null;
  }
});
</script>

<style scoped>
.user-management {
  padding: 0;
  background: transparent;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  letter-spacing: 0.5px;
}

.page-title .anticon {
  font-size: var(--text-3xl);
}

.page-subtitle {
  font-size: var(--text-base);
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: var(--space-xl);
}

.stat-item {
  text-align: center;
  padding: var(--space-md) var(--space-lg);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.9;
  font-weight: 500;
}

/* 搜索和筛选区域 */
.filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
}

.filter-form {
  gap: var(--space-lg);
  flex-wrap: wrap;
}

:deep(.filter-form .ant-form-item) {
  margin-bottom: 0;
}

:deep(.filter-form .ant-form-item-label) {
  font-weight: 500;
  color: var(--text-secondary);
}

/* 搜索输入框样式 */
.search-input {
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.search-input .ant-input) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: all 0.3s;
}

:deep(.search-input .ant-input:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

:deep(.search-input .ant-input:hover) {
  border-color: var(--primary-light);
}

/* 下拉选择框样式 */
.filter-select {
  border-radius: var(--radius-sm);
}

:deep(.filter-select .ant-select-selector) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.filter-select .ant-select-focused .ant-select-selector) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

:deep(.filter-select .ant-select-selector:hover) {
  border-color: var(--primary-light);
}

/* 按钮样式 */
.search-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.search-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.reset-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  background: var(--bg-primary);
  transition: all 0.3s;
}

.reset-btn:hover {
  border-color: var(--border-dark);
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
}

.action-left {
  display: flex;
  gap: var(--space-md);
}

.primary-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.secondary-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.secondary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-overlay);
}

.danger-btn {
  border: 2px solid var(--error-color);
  color: var(--error-color);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.danger-btn:hover {
  border-color: #dc2626;
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 表格区域 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* 表格样式已在统一样式文件中定义 */

/* 层级路径样式 */
.hierarchy-path {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  flex-wrap: wrap;
}

.path-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.path-separator {
  color: var(--text-tertiary);
  font-size: 10px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

:deep(.action-buttons .ant-btn-link) {
  color: var(--primary-color);
  font-weight: 500;
  transition: all 0.3s;
  border-radius: var(--radius-sm);
  padding: var(--space-xs) var(--space-sm);
  height: auto;
  font-size: var(--text-sm);
}

:deep(.action-buttons .ant-btn-link:hover) {
  color: var(--primary-dark);
  background: var(--bg-overlay);
}

:deep(.action-buttons .ant-btn-link.ant-btn-dangerous) {
  color: var(--error-color);
}

:deep(.action-buttons .ant-btn-link.ant-btn-dangerous:hover) {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 状态标签优化 */
:deep(.ant-tag) {
  border-radius: 20px;
  font-weight: 500;
  font-size: var(--text-sm);
  padding: 4px var(--space-md);
  border: none;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 分页样式 - 参考日志管理页面 */
:deep(.ant-pagination) {
  margin: var(--space-lg) 0 0 0;
  text-align: right;
  padding: var(--space-md) 0;
}

:deep(.ant-pagination .ant-pagination-item) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s;
  margin: 0 4px;
}

:deep(.ant-pagination .ant-pagination-item:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

:deep(.ant-pagination .ant-pagination-item-active) {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

:deep(.ant-pagination .ant-pagination-item-active a) {
  color: var(--text-inverse);
  font-weight: 600;
}

:deep(.ant-pagination .ant-pagination-prev),
:deep(.ant-pagination .ant-pagination-next) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s;
}

:deep(.ant-pagination .ant-pagination-prev:hover),
:deep(.ant-pagination .ant-pagination-next:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

:deep(.ant-pagination .ant-pagination-options) {
  margin-left: var(--space-md);
}

:deep(.ant-pagination .ant-pagination-options .ant-select) {
  margin-right: var(--space-sm);
}

:deep(.ant-pagination .ant-pagination-total-text) {
  margin-right: var(--space-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* 模态框样式 */
:deep(.user-modal .ant-modal-header) {
  background: var(--primary-gradient);
  border-bottom: none;
}

:deep(.user-modal .ant-modal-title) {
  color: var(--text-inverse);
  font-weight: 600;
}

:deep(.user-modal .ant-modal-close) {
  color: var(--text-inverse);
}

:deep(.user-modal .ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.8);
}

.user-form {
  padding: var(--space-lg) 0;
}

:deep(.user-form .ant-form-item-label) {
  font-weight: 500;
  color: var(--text-primary);
}

:deep(.user-form .ant-input) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.user-form .ant-input:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

:deep(.user-form .ant-select-selector) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.user-form .ant-select-focused .ant-select-selector) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-lg);
  }

  .header-stats {
    justify-content: center;
  }

  .action-bar {
    flex-direction: column;
    gap: var(--space-md);
  }

  .action-left {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .filter-section {
    padding: var(--space-md) var(--space-lg);
  }

  .action-bar {
    padding: var(--space-md) var(--space-lg);
  }

  .page-header {
    padding: var(--space-lg) var(--space-lg);
  }

  .page-title {
    font-size: var(--text-2xl);
  }

  .header-stats {
    gap: var(--space-md);
  }

  .stat-item {
    padding: var(--space-md) var(--space-md);
    min-width: 80px;
  }
}

/* 统一筛选区样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

/* 统一按钮样式 */
.action-buttons-section {
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.primary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: white;
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.secondary-action-btn:hover {
  border-color: var(--primary-light);
  color: var(--primary-color);
}

.danger-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--error-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--error-color);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.danger-action-btn:hover {
  border-color: #dc2626;
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 用户详情模态框样式 */
.user-detail-modal .user-detail-content {
  padding: var(--space-md) 0;
}

.detail-value {
  font-weight: 500;
  color: var(--text-primary);
}

.detail-empty {
  color: var(--text-tertiary);
  font-style: italic;
}

.detail-actions {
  margin-top: var(--space-lg);
  padding-top: var(--space-md);
  border-top: 1px solid var(--border-light);
  text-align: right;
}

:deep(.user-detail-modal .ant-descriptions-item-label) {
  font-weight: 600;
  color: var(--text-secondary);
  background: var(--bg-secondary);
}

:deep(.user-detail-modal .ant-descriptions-item-content) {
  background: var(--bg-primary);
}

/* 操作按钮颜色 */
.text-warning {
  color: var(--warning-color) !important;
}

.text-success {
  color: var(--success-color) !important;
}
</style>