/**
 * 权限控制工具类
 * 提供统一的权限检查和按钮权限控制功能
 */
import store from '@/store'

/**
 * 采购模块按钮权限映射
 */
export const PURCHASE_PERMISSIONS = {
  // 采购需求页面
  REQUEST: {
    ADD: 'purchase:request:add',
    EDIT: 'purchase:request:edit',
    DELETE: 'purchase:request:delete',
    SUBMIT: 'purchase:request:submit',
    EXPORT: 'purchase:request:export',
    IMPORT: 'purchase:request:import',
    PRINT: 'purchase:request:print'
  },
  
  // 采购审批页面
  APPROVAL: {
    APPROVE: 'purchase:approval:approve',
    REJECT: 'purchase:approval:reject',
    BATCH: 'purchase:approval:batch',
    EXPORT: 'purchase:approval:export',
    VIEW_DETAIL: 'purchase:approval:view'
  },
  
  // 采购执行页面
  PROCUREMENT: {
    EXECUTE: 'purchase:procurement:execute',
    UPDATE: 'purchase:procurement:update',
    NOTIFY: 'purchase:procurement:notify',
    RETURN: 'purchase:procurement:return',
    EXPORT: 'purchase:procurement:export'
  },

  // 验收页面
  ACCEPTANCE: {
    ACCEPT: 'purchase:acceptance:accept',
    REJECT: 'purchase:acceptance:reject',
    GENERATE_REIMBURSEMENT: 'purchase:acceptance:generate_reimbursement',
    EXPORT: 'purchase:acceptance:export',
    PRINT: 'purchase:acceptance:print',
    SAVE: 'purchase:acceptance:save'
  },

  // 报销页面
  REIMBURSEMENT: {
    SETTLE: 'purchase:reimbursement:settle',
    EXPORT: 'purchase:reimbursement:export',
    PRINT: 'purchase:reimbursement:print',
    FINANCE_RETURN: 'purchase:reimbursement:finance_return',
    SAVE: 'purchase:reimbursement:save'
  },

  // 系统管理模块权限
  SYSTEM: {
    // 菜单管理
    MENU: {
      VIEW: 'system:menu:view',
      ADD: 'system:menu:add',
      EDIT: 'system:menu:edit',
      DELETE: 'system:menu:delete',
      TOGGLE: 'system:menu:toggle',
      EXPORT: 'system:menu:export',
      IMPORT: 'system:menu:import',
      BATCH_ENABLE: 'system:menu:batch_enable',
      BATCH_DISABLE: 'system:menu:batch_disable',
      ADD_CHILD: 'system:menu:add_child'
    },
    // 用户管理
    USER: {
      VIEW: 'system:user:view',
      ADD: 'system:user:add',
      EDIT: 'system:user:edit',
      DELETE: 'system:user:delete'
    },
    // 角色管理
    ROLE: {
      VIEW: 'system:role:view',
      ADD: 'system:role:add',
      EDIT: 'system:role:edit',
      DELETE: 'system:role:delete'
    },
    // 权限管理
    PERMISSION: {
      VIEW: 'system:permission:view',
      ASSIGN: 'system:permission:assign'
    }
  }
}

/**
 * 权限检查工具类
 */
export class PermissionChecker {
  /**
   * 检查用户是否有指定权限
   * @param {string|Array} permission 权限代码或权限数组
   * @returns {boolean}
   */
  static hasPermission(permission) {
    return store.getters.hasPermission(permission)
  }

  /**
   * 检查用户是否有指定角色
   * @param {string|Array} role 角色代码或角色数组
   * @returns {boolean}
   */
  static hasRole(role) {
    return store.getters.hasRole(role)
  }

  /**
   * 检查按钮权限
   * @param {string} module 模块名
   * @param {string} action 操作名
   * @returns {boolean}
   */
  static hasButtonPermission(module, action) {
    return store.getters.hasButtonPermission(module, action)
  }

  /**
   * 检查页面权限
   * @param {string} pagePath 页面路径
   * @returns {boolean}
   */
  static hasPagePermission(pagePath) {
    return store.getters.hasPagePermission(pagePath)
  }

  /**
   * 检查采购流程状态权限
   * @param {string} status 状态
   * @returns {boolean}
   */
  static canOperateStatus(status) {
    const userRole = store.getters.userRole?.code
    
    const statusPermissions = {
      'draft': ['requester', 'super_admin'],
      'pending_approval': ['approver', 'manager_approver', 'super_admin'],
      'approved': ['purchaser', 'super_admin'],
      'purchased': ['acceptor', 'super_admin'],
      'accepted': ['finance', 'super_admin'],
      'pending_purchase': ['purchaser', 'super_admin'],
      'pending_acceptance': ['acceptor', 'super_admin'],
      'pending_reimbursement': ['finance', 'super_admin']
    }
    
    return statusPermissions[status]?.includes(userRole) || false
  }

  /**
   * 获取用户在采购审批页面的可用操作
   * @returns {Object} 可用操作对象
   */
  static getApprovalPagePermissions() {
    return {
      canApprove: this.hasPermission(PURCHASE_PERMISSIONS.APPROVAL.APPROVE),
      canReject: this.hasPermission(PURCHASE_PERMISSIONS.APPROVAL.REJECT),
      canBatchApprove: this.hasPermission(PURCHASE_PERMISSIONS.APPROVAL.BATCH),
      canExport: this.hasPermission(PURCHASE_PERMISSIONS.APPROVAL.EXPORT),
      canViewDetail: this.hasPermission(PURCHASE_PERMISSIONS.APPROVAL.VIEW_DETAIL)
    }
  }

  /**
   * 获取用户在采购需求页面的可用操作
   * @returns {Object} 可用操作对象
   */
  static getRequestPagePermissions() {
    return {
      canAdd: this.hasPermission(PURCHASE_PERMISSIONS.REQUEST.ADD),
      canEdit: this.hasPermission(PURCHASE_PERMISSIONS.REQUEST.EDIT),
      canDelete: this.hasPermission(PURCHASE_PERMISSIONS.REQUEST.DELETE),
      canSubmit: this.hasPermission(PURCHASE_PERMISSIONS.REQUEST.SUBMIT),
      canExport: this.hasPermission(PURCHASE_PERMISSIONS.REQUEST.EXPORT),
      canImport: this.hasPermission(PURCHASE_PERMISSIONS.REQUEST.IMPORT),
      canPrint: this.hasPermission(PURCHASE_PERMISSIONS.REQUEST.PRINT)
    }
  }
}

/**
 * Vue 3 组合式 API 权限检查 Hook
 */
export function usePermission() {
  const hasPermission = (permission) => {
    return PermissionChecker.hasPermission(permission)
  }

  const hasRole = (role) => {
    return PermissionChecker.hasRole(role)
  }

  const hasButtonPermission = (module, action) => {
    return PermissionChecker.hasButtonPermission(module, action)
  }

  const canOperateStatus = (status) => {
    return PermissionChecker.canOperateStatus(status)
  }

  const getApprovalPermissions = () => {
    return PermissionChecker.getApprovalPagePermissions()
  }

  const getRequestPermissions = () => {
    return PermissionChecker.getRequestPagePermissions()
  }

  return {
    hasPermission,
    hasRole,
    hasButtonPermission,
    canOperateStatus,
    getApprovalPermissions,
    getRequestPermissions,
    PURCHASE_PERMISSIONS
  }
}

/**
 * 权限检查混入（Options API）
 */
export const permissionMixin = {
  methods: {
    $hasPermission: PermissionChecker.hasPermission,
    $hasRole: PermissionChecker.hasRole,
    $hasButtonPermission: PermissionChecker.hasButtonPermission,
    $canOperateStatus: PermissionChecker.canOperateStatus,
    $getApprovalPermissions: PermissionChecker.getApprovalPagePermissions,
    $getRequestPermissions: PermissionChecker.getRequestPagePermissions
  }
}

// 导出权限常量
export { PURCHASE_PERMISSIONS as PERMISSIONS }

// 默认导出权限检查器
export default PermissionChecker
