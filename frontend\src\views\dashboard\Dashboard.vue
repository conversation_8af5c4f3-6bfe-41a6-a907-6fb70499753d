<template>
  <div class="dashboard">
    <!-- 时间筛选器 -->
    <a-row :gutter="[24, 24]" class="filter-row">
      <a-col :span="24">
        <a-card class="filter-card">
          <a-space size="large" wrap>
            <span class="filter-label">统计时间范围：</span>
            <a-radio-group v-model:value="timeRange" @change="handleTimeRangeChange">
              <a-radio-button value="month">本月</a-radio-button>
              <a-radio-button value="quarter">本季度</a-radio-button>
              <a-radio-button value="year">本年</a-radio-button>
              <a-radio-button value="custom">自定义</a-radio-button>
            </a-radio-group>
            <a-range-picker v-if="timeRange === 'custom'" v-model:value="customDateRange"
              @change="handleCustomDateChange" style="margin-left: 16px;" />
          </a-space>
        </a-card>
      </a-col>
    </a-row>

    <!-- 统计卡片区域 - 6个控件同一行，时间预警置于最后 -->
    <a-row :gutter="[16, 16]" class="stats-row">
      <a-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
        <div class="stat-card clickable" @click="navigateToOverview()">
          <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <ShoppingCartOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatCurrency(statisticsData.overview?.period_amount || 0) }}</div>
            <div class="stat-label">本月采购金额</div>
          </div>
        </div>
      </a-col>
      <a-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
        <div class="stat-card clickable" @click="navigateToOverview('pending')">
          <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <ClockCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statusCounts.pending || 0 }}</div>
            <div class="stat-label">待审批</div>
          </div>
        </div>
      </a-col>
      <a-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
        <div class="stat-card clickable" @click="navigateToOverview('pending_purchase')">
          <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <ShoppingCartOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statusCounts.pending_purchase || 0 }}</div>
            <div class="stat-label">待采购</div>
          </div>
        </div>
      </a-col>
      <a-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
        <div class="stat-card clickable" @click="navigateToOverview('pending_acceptance')">
          <div class="stat-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <CheckCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statusCounts.pending_acceptance || 0 }}</div>
            <div class="stat-label">待验收</div>
          </div>
        </div>
      </a-col>
      <a-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
        <div class="stat-card clickable" @click="navigateToOverview('pending_reimbursement')">
          <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <DollarCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statusCounts.pending_reimbursement || 0 }}</div>
            <div class="stat-label">待结算</div>
          </div>
        </div>
      </a-col>
      <a-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
        <div class="stat-card clickable warning-card" @click="showTimeWarnings = true">
          <div class="stat-icon" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
            <ExclamationCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ timeWarningStats.total_count || 0 }}</div>
            <div class="stat-label">时间预警</div>
          </div>
          <div class="stat-extra" v-if="timeWarningStats.high_priority_count > 0">
            <a-tag color="red" size="small">高优先级: {{ timeWarningStats.high_priority_count }}</a-tag>
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <!-- 各单位待处理状态分布 - 单独占一行 -->
    <a-row :gutter="[24, 24]" class="charts-row">
      <a-col :span="24">
        <a-card title="各单位待处理状态分布" class="chart-card">
          <div ref="departmentStatusChartContainer" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>



    <!-- 采购方式分布和采购类型统计 - 同一行 -->
    <a-row :gutter="[24, 24]" class="charts-row">
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="采购方式分布" class="chart-card">
          <div ref="methodChartContainer" class="chart-container"></div>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card title="采购类型统计" class="chart-card">
          <div ref="purchaseTypeChartContainer" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>



    <!-- 采购趋势分析 -->
    <a-row :gutter="[24, 24]" class="charts-row">
      <a-col :span="24">
        <a-card title="采购趋势分析" class="chart-card">
          <a-spin :spinning="loading || chartLoading">
            <div ref="trendChartContainer" class="chart-container"></div>
          </a-spin>
        </a-card>
      </a-col>
    </a-row>

    <!-- 价格预警 -->
    <a-row :gutter="16" style="margin-top: 24px" v-if="priceWarnings.length > 0">
      <a-col :span="24">
        <a-card title="价格预警">
          <template #extra>
            <a-button type="link" @click="viewAllWarnings">查看全部</a-button>
          </template>
          <a-table :columns="warningColumns" :data-source="priceWarnings" :pagination="false" size="small">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'price_increase'">
                <a-tag color="red">+{{ record.price_increase }}%</a-tag>
              </template>
              <template v-else-if="column.key === 'current_price'">
                ¥{{ record.current_price.toFixed(2) }}
              </template>
              <template v-else-if="column.key === 'history_avg_price'">
                ¥{{ record.history_avg_price.toFixed(2) }}
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 时间预警模态框 -->
    <a-modal v-model:open="showTimeWarnings" title="时间预警管理" width="1200px" :footer="null" :destroy-on-close="true">
      <TimeWarnings @navigate="handleWarningNavigate" />
    </a-modal>
  </div>
</template>

<script>
/**
 * 数据看板组件
 *
 * 功能说明：
 * 1. 展示采购管理系统的核心数据统计和分析
 * 2. 包含统计卡片、趋势图表、分布图表等多种数据可视化
 * 3. 支持时间范围筛选和实时数据刷新
 * 4. 集成时间预警功能，及时提醒用户关注重要事项
 *
 * 技术特点：
 * - 使用ECharts实现丰富的图表展示
 * - 响应式设计，支持不同屏幕尺寸
 * - 数据缓存和防抖优化，提升性能
 * - 模块化的数据获取和图表初始化
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-08
 */
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import TimeWarnings from '@/components/TimeWarnings.vue'
import {
  ShoppingCartOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  DollarCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import api from '@/api'

export default {
  name: 'DashboardView',
  components: {
    ShoppingCartOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined,
    DollarCircleOutlined,
    ExclamationCircleOutlined,
    TimeWarnings
  },
  setup() {
    const router = useRouter()
    const trendChartContainer = ref()
    const methodChartContainer = ref()
    const purchaseTypeChartContainer = ref()
    const departmentChartContainer = ref()
    const departmentStatusChartContainer = ref()

    // 统计数据
    const statisticsData = ref({})
    const trendData = ref([])
    const priceWarnings = ref([])
    const timeRange = ref('month')
    const customDateRange = ref([])
    const statusCounts = ref({})

    // 加载状态
    const loading = ref(false)
    const chartLoading = ref(false)

    // 新增图表数据
    const departmentStatusData = ref([])

    // 时间预警相关
    const showTimeWarnings = ref(false)
    const timeWarningStats = reactive({
      total_count: 0,
      high_priority_count: 0,
      medium_priority_count: 0
    })



    // 采购方式数据
    const methodData = ref([])
    const methodStatistics = ref({
      total_amount: 0,
      total_count: 0,
      methods: []
    })

    // 采购类型数据
    const purchaseTypeData = ref([])
    const purchaseTypeStatistics = ref({
      total_amount: 0,
      total_count: 0,
      types: []
    })

    // 部门数据（一级单位）
    const departmentData = ref([])
    const departmentStatistics = ref({
      total_amount: 0,
      total_count: 0,
      departments: []
    })

    // 价格预警表格列
    const warningColumns = [
      {
        title: '物品名称',
        dataIndex: 'item_name',
        key: 'item_name'
      },
      {
        title: '当前单价',
        dataIndex: 'current_price',
        key: 'current_price',
        customRender: ({ text }) => `¥${text}`
      },
      {
        title: '历史均价',
        dataIndex: 'history_avg_price',
        key: 'history_avg_price',
        customRender: ({ text }) => `¥${text}`
      },
      {
        title: '涨幅',
        dataIndex: 'price_increase',  // 修复：使用正确的字段名
        key: 'price_increase',
        customRender: ({ text }) => `+${text}%`
      },
      {
        title: '申请人',
        dataIndex: 'requester_name',  // 修复：使用正确的字段名
        key: 'requester_name'
      }
    ]

    // 图表实例
    let trendChart = null
    let methodChart = null
    let purchaseTypeChart = null
    let departmentChart = null
    let departmentStatusChart = null

    // 格式化货币
    const formatCurrency = (value) => {
      if (value >= 10000) {
        return (value / 10000).toFixed(1) + '万'
      }
      return value.toLocaleString()
    }

    // 防抖处理
    let debounceTimer = null
    const debounceLoadData = () => {
      if (debounceTimer) clearTimeout(debounceTimer)
      debounceTimer = setTimeout(() => {
        refreshAllData()
      }, 300)
    }

    // 时间范围变化处理
    const handleTimeRangeChange = async () => {
      if (timeRange.value !== 'custom') {
        customDateRange.value = []
      }
      debounceLoadData()
    }



    // 自定义时间范围变化处理
    const handleCustomDateChange = async () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('自定义时间范围变化:', customDateRange.value)
      }
      if (customDateRange.value && customDateRange.value.length === 2) {
        // 使用防抖处理，避免与handleTimeRangeChange重复调用
        debounceLoadData()
      }
    }

    // 根据时间范围获取天数
    const getDaysFromTimeRange = () => {
      switch (timeRange.value) {
        case 'month':
          return 30
        case 'quarter':
          return 90
        case 'year':
          return 365
        case 'custom':
          if (customDateRange.value && customDateRange.value.length === 2) {
            const start = customDateRange.value[0]
            const end = customDateRange.value[1]
            return end.diff(start, 'day') + 1
          }
          return 30
        default:
          return 30
      }
    }

    // 获取日期范围参数
    const getDateRangeParams = () => {
      if (timeRange.value === 'custom' && customDateRange.value && customDateRange.value.length === 2) {
        return {
          days: null,
          start_date: customDateRange.value[0].format('YYYY-MM-DD'),
          end_date: customDateRange.value[1].format('YYYY-MM-DD')
        }
      } else {
        return {
          days: getDaysFromTimeRange(),
          start_date: null,
          end_date: null
        }
      }
    }



    // 刷新所有数据 - 优化版本，避免重复渲染
    const refreshAllData = async () => {
      try {
        // 并行获取数据，提高加载速度
        await Promise.all([
          getStatisticsData(),
          getStatusCounts(),
          getTrendData(),
          getMethodStatistics(),
          getPurchaseTypeStatistics(),
          getDepartmentStatistics(),
          getDepartmentStatusData()
        ])

        // 数据加载完成后，统一初始化图表，避免重复渲染
        await nextTick()
        initTrendChart()
        initMethodChart()
        initPurchaseTypeChart()
        initDepartmentChart()
        await initDepartmentStatusChart()
      } catch (error) {
        // 刷新数据失败，静默处理
      }
    }

    // 导航到采购总览页面
    const navigateToOverview = (status) => {
      router.push({
        path: '/purchase/overview',
        query: { status }
      })
    }



    // 获取统计数据
    const getStatisticsData = async () => {
      if (loading.value) return

      try {
        loading.value = true
        const params = getDateRangeParams()
        const response = await api.dashboard.getStatistics(params.days, params.start_date, params.end_date)
        if (response.code === 200) {
          statisticsData.value = response.data
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取统计数据失败:', error)
        }
        message.error('获取统计数据失败')
      } finally {
        loading.value = false
      }
    }

    // 获取状态统计数据
    const getStatusCounts = async () => {
      try {
        const response = await api.dashboard.getStatusDistribution()
        if (response.code === 200) {
          statusCounts.value = response.data
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取状态统计失败:', error)
        }
      }
    }



    // 获取趋势数据
    const getTrendData = async () => {
      try {
        const params = getDateRangeParams()
        // 统一使用天为单位，提供更精细的数据
        const groupBy = 'day'
        const response = await api.dashboard.getTrend(params.days, params.start_date, params.end_date, groupBy)

        if (response.code === 200) {
          const apiTrendData = response.data.trend_data || response.data || []

          // 如果API返回的数据有效且长度大于1，使用API数据
          if (apiTrendData && Array.isArray(apiTrendData) && apiTrendData.length > 1) {
            trendData.value = apiTrendData
          } else {
            trendData.value = generateDefaultTrendData()
          }
        } else {
          trendData.value = generateDefaultTrendData()
        }
      } catch (error) {
        console.error('获取趋势数据失败:', error)
        // 使用默认数据
        trendData.value = generateDefaultTrendData()
      }
    }

    // 生成默认趋势数据
    const generateDefaultTrendData = () => {
      const days = getDaysFromTimeRange()
      const data = []
      const today = new Date()

      if (process.env.NODE_ENV === 'development') {
        console.log(`生成默认趋势数据，天数: ${days}`)
      }

      // 根据时间范围生成不同间隔的数据
      let interval = 1 // 默认每天一条数据
      if (days > 90) {
        interval = 7 // 超过90天，每周一条数据
      } else if (days > 30) {
        interval = 3 // 超过30天，每3天一条数据
      }

      for (let i = days - 1; i >= 0; i -= interval) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)

        // 生成更真实的数据，有一定的趋势性
        const baseAmount = 80000
        const trend = Math.sin((days - i) / days * Math.PI) * 20000
        const randomVariation = (Math.random() - 0.5) * 30000

        data.push({
          date: date.toISOString().split('T')[0],
          total_amount: Math.max(10000, Math.floor(baseAmount + trend + randomVariation)),
          count: Math.max(1, Math.floor(15 + (Math.random() - 0.5) * 10))
        })
      }


      return data
    }

    // 获取部门状态分布数据（彻底修复版本）
    const getDepartmentStatusData = async () => {
      try {
        const params = getDateRangeParams()
        const response = await api.dashboard.getDepartmentStatusDistribution(params.days, params.start_date, params.end_date)

        if (response.code === 200 && response.data) {
          // 后端已确保返回所有部门数据，直接使用
          departmentStatusData.value = response.data
          // 获取部门状态数据成功，部门数量: response.data.length
        } else {
          // 部门状态数据API返回异常，使用空数组
          departmentStatusData.value = []
        }

      } catch (error) {
        // 获取部门状态数据失败，显示错误信息
        departmentStatusData.value = []
        message.error('获取部门状态数据失败，请检查网络连接')
      }
    }



    // 获取价格预警
    const getPriceWarnings = async () => {
      try {
        const response = await api.dashboard.getPriceWarnings()
        if (response.code === 200) {
          // 修复：使用正确的数据字段名，并添加安全检查
          const warningRequests = response.data.warning_requests || []
          priceWarnings.value = warningRequests.slice(0, 5) // 只显示前5条
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取价格预警失败:', error)
        }
        message.error('获取价格预警失败')
      }
    }



    // 获取采购方式统计数据
    const getMethodStatistics = async () => {
      try {
        const params = getDateRangeParams()
        const response = await api.dashboard.getMethodStatistics(params.days, params.start_date, params.end_date)
        if (response.code === 200) {
          methodStatistics.value = response.data || {}
          // 转换数据格式用于图表显示，包含所有字典中的采购方式
          const methods = response.data.methods || []
          methodData.value = methods.map((item, index) => ({
            name: item.name || item.method_name || '未知',
            value: item.count || 0,
            amount: item.amount || 0,
            percentage: item.percentage || 0,
            code: item.code,
            color: getMethodColor(index),
            // 标记是否有数据，用于图表显示优化
            hasData: (item.count || 0) > 0
          }))

          // 确保图表更新
          await nextTick()
          initMethodChart()
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取采购方式统计失败:', error)
        }
        // 如果API失败，显示空数据
        console.warn('采购方式统计API失败，显示空数据')
        methodData.value = []
        await nextTick()
        initMethodChart()
      }
    }

    // 获取部门统计数据（显示所有一、二级单位）
    const getDepartmentStatistics = async () => {
      try {
        const params = getDateRangeParams()
        const response = await api.dashboard.getDepartmentStatistics(params.days, params.start_date, params.end_date)
        if (response.code === 200) {
          departmentStatistics.value = response.data || {}
          // 显示所有一、二级单位数据
          const departments = response.data.departments || []
          departmentData.value = departments.map(item => ({
            name: item.full_name || item.dept_name || item.name,
            amount: item.amount || 0,
            count: item.count || 0,
            percentage: item.percentage || 0,
            level: item.level || 1
          }))

        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取部门统计失败:', error)
        }
        // 使用默认数据（一、二级单位示例）
        departmentData.value = [
          { name: '行政部', amount: 125000, count: 45, percentage: 25, level: 1 },
          { name: '行政部-办公室', amount: 98000, count: 32, percentage: 20, level: 2 },
          { name: '技术部', amount: 87000, count: 28, percentage: 18, level: 1 },
          { name: '技术部-研发室', amount: 65000, count: 22, percentage: 15, level: 2 },
          { name: '财务部-会计室', amount: 43000, count: 18, percentage: 12, level: 2 }
        ]

      }
    }

    // 获取采购类型统计数据
    const getPurchaseTypeStatistics = async () => {
      try {
        const params = getDateRangeParams()
        const response = await api.dashboard.getPurchaseTypeStatistics(params.days, params.start_date, params.end_date)
        if (response.code === 200) {
          purchaseTypeStatistics.value = response.data || {}
          // 转换数据格式用于图表显示 - 后端已返回中文名称，直接使用
          const types = response.data.types || []
          purchaseTypeData.value = types.map((item, index) => ({
            name: item.name || '未分类',  // 使用后端返回的中文名称
            value: item.count || 0,
            amount: item.amount || 0,
            percentage: item.percentage || 0,
            color: index === 0 ? '#1890ff' : '#52c41a'
          }))

        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取采购类型统计失败:', error)
        }
        // 使用默认数据 - 从字典服务获取采购类型名称
        try {
          const { getDictOptions } = await import('@/services/dictService')
          const purchaseTypes = await getDictOptions('purchase_type')

          // 使用字典数据生成默认数据
          const defaultTypes = [
            { code: 'unified', value: 75, amount: 750000, percentage: 75, color: '#1890ff' },
            { code: 'self', value: 25, amount: 250000, percentage: 25, color: '#52c41a' }
          ]

          purchaseTypeData.value = defaultTypes.map(item => {
            const dictItem = purchaseTypes.find(dict => dict.value === item.code)
            return {
              name: dictItem ? dictItem.label : '未知采购类型',
              value: item.value,
              amount: item.amount,
              percentage: item.percentage,
              color: item.color
            }
          })
        } catch (dictError) {
          // 如果字典服务也失败，使用最基本的默认数据
          purchaseTypeData.value = [
            { name: '统一采购', value: 75, amount: 750000, percentage: 75, color: '#1890ff' },
            { name: '自行采购', value: 25, amount: 250000, percentage: 25, color: '#52c41a' }
          ]
        }

      }
    }

    // 获取采购方式颜色
    const getMethodColor = (index) => {
      const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96']
      return colors[index % colors.length]
    }

    // 初始化趋势图表
    const initTrendChart = () => {
      if (!trendChartContainer.value) return

      if (trendChart) {
        trendChart.dispose()
      }

      trendChart = echarts.init(trendChartContainer.value)

      // 处理趋势数据
      if (!trendData.value || !Array.isArray(trendData.value)) {
        return
      }

      const dates = trendData.value.map(item => {
        const date = new Date(item.date || item.month)
        // 统一使用月-日格式，提供更精细的显示
        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
      })
      const amounts = trendData.value.map(item => item.total_amount || item.amount || 0)
      const counts = trendData.value.map(item => item.count || 0)

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.seriesName === '采购金额') {
                result += param.marker + param.seriesName + ': ¥' + param.value.toLocaleString() + '<br/>'
              } else {
                result += param.marker + param.seriesName + ': ' + param.value + '件<br/>'
              }
            })
            return result
          }
        },
        legend: {
          data: ['采购金额', '采购数量'],
          top: 10
        },
        grid: {
          top: 50,
          bottom: 80,
          left: 60,
          right: 60
        },
        xAxis: {
          type: 'category',
          data: dates,
          axisLabel: {
            rotate: 45,
            interval: 'auto',
            margin: 15,
            fontSize: 12
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '金额(元)',
            position: 'left',
            axisLabel: {
              formatter: '¥{value}'
            }
          },
          {
            type: 'value',
            name: '数量(件)',
            position: 'right'
          }
        ],
        series: [
          {
            name: '采购金额',
            type: 'line',
            yAxisIndex: 0,
            data: amounts,
            smooth: true,
            lineStyle: { color: '#1890FF' },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: '#1890FF80' },
                  { offset: 1, color: '#1890FF10' }
                ]
              }
            }
          },
          {
            name: '采购数量',
            type: 'bar',
            yAxisIndex: 1,
            data: counts,
            itemStyle: { color: '#52c41a' }
          }
        ],
        // 添加数据缩放和滑动条功能
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: timeRange.value === 'year' ? 80 : 0, // 本年默认显示最后20%（当前月份附近）
            end: 100,
            bottom: 10,
            height: 20,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          },
          {
            type: 'inside',
            xAxisIndex: [0],
            start: timeRange.value === 'year' ? 80 : 0,
            end: 100
          }
        ],
        // 添加工具箱
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: {}
          },
          right: 20,
          top: 20
        }
      }

      trendChart.setOption(option)
    }

    // 初始化采购方式饼图
    const initMethodChart = () => {
      if (!methodChartContainer.value) return

      if (methodChart) {
        methodChart.dispose()
      }

      methodChart = echarts.init(methodChartContainer.value)

      // 过滤出有数据的项目用于饼图显示，但图例显示所有项目
      const chartData = methodData.value.filter(item => item.hasData)
      const legendData = methodData.value.map(item => item.name)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            const data = params.data
            return `${params.seriesName}<br/>
                    ${params.name}: ${params.value}项 (${params.percent}%)<br/>
                    采购金额: ¥${(data.amount || 0).toLocaleString()}`
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'center',
          data: legendData,
          formatter: function (name) {
            const item = methodData.value.find(d => d.name === name)
            const count = item ? item.value : 0
            const hasData = item ? item.hasData : false
            // 对于没有数据的项目，在图例中用灰色显示
            return hasData ? `${name} (${count}项)` : `{inactive|${name} (${count}项)}`
          },
          textStyle: {
            rich: {
              inactive: {
                color: '#999'
              }
            }
          }
        },
        series: [
          {
            name: '采购方式分布',
            type: 'pie',
            radius: ['45%', '75%'],
            center: ['65%', '50%'],
            data: chartData.length > 0 ? chartData : [
              // 如果没有任何数据，显示一个占位项
              { name: '暂无数据', value: 1, color: '#f0f0f0' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: chartData.length > 0,
              formatter: '{d}%',
              fontSize: 12,
              fontWeight: 'bold'
            },
            labelLine: {
              show: chartData.length > 0,
              length: 10,
              length2: 5
            }
          }
        ]
      }

      methodChart.setOption(option)

      // 添加点击事件
      methodChart.on('click', function () {
        // 可以跳转到具体的采购方式详情页面
      })
    }

    // 初始化采购类型饼图
    const initPurchaseTypeChart = () => {
      if (!purchaseTypeChartContainer.value) return

      if (purchaseTypeChart) {
        purchaseTypeChart.dispose()
      }

      purchaseTypeChart = echarts.init(purchaseTypeChartContainer.value)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            const data = params.data
            return `${params.seriesName}<br/>
                    ${params.name}: ${params.value}项 (${params.percent}%)<br/>
                    采购金额: ¥${(data.amount || 0).toLocaleString()}`
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'center',
          data: purchaseTypeData.value.map(item => item.name),
          formatter: function (name) {
            const item = purchaseTypeData.value.find(d => d.name === name)
            return `${name}\n${item ? item.value : 0}项 | ¥${(item ? item.amount : 0).toLocaleString()}`
          }
        },
        series: [
          {
            name: '采购类型统计',
            type: 'pie',
            radius: ['45%', '75%'],
            center: ['65%', '50%'],
            data: purchaseTypeData.value,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: true,
              formatter: '{d}%',
              fontSize: 14,
              fontWeight: 'bold'
            },
            labelLine: {
              show: true,
              length: 10,
              length2: 5
            }
          }
        ]
      }

      purchaseTypeChart.setOption(option)

      // 添加点击事件
      purchaseTypeChart.on('click', function () {
        // 可以跳转到具体的采购类型详情页面
      })
    }

    // 初始化部门统计柱状图（一级单位）
    const initDepartmentChart = () => {
      if (!departmentChartContainer.value) return

      if (departmentChart) {
        departmentChart.dispose()
      }

      departmentChart = echarts.init(departmentChartContainer.value)

      const option = {
        title: {
          text: '二级单位采购统计',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#666'
          },
          left: 'center',
          top: 5
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            let result = `<strong>${params[0].name}</strong><br/>`
            params.forEach(param => {
              if (param.seriesName === '采购金额') {
                result += `${param.marker} ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`
              } else {
                result += `${param.marker} ${param.seriesName}: ${param.value}项<br/>`
              }
            })
            // 添加占比信息
            const deptData = departmentData.value.find(d => d.name === params[0].name)
            if (deptData && deptData.percentage) {
              result += `占比: ${deptData.percentage}%`
            }
            return result
          }
        },
        legend: {
          data: ['采购金额', '采购数量'],
          top: 25,
          left: 'center'
        },
        grid: {
          top: 70,
          bottom: 60,
          left: 80,
          right: 80
        },
        xAxis: {
          type: 'category',
          data: departmentData.value.map(item => item.name),
          axisLabel: {
            rotate: 30,
            fontSize: 11,
            color: '#666'
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '金额(万元)',
            position: 'left',
            axisLabel: {
              formatter: function (value) {
                return (value / 10000).toFixed(0)
              },
              fontSize: 11,
              color: '#666'
            },
            nameTextStyle: {
              color: '#1890ff',
              fontSize: 12
            }
          },
          {
            type: 'value',
            name: '数量(项)',
            position: 'right',
            axisLabel: {
              fontSize: 11,
              color: '#666'
            },
            nameTextStyle: {
              color: '#52c41a',
              fontSize: 12
            }
          }
        ],
        series: [
          {
            name: '采购金额',
            type: 'bar',
            yAxisIndex: 0,
            data: departmentData.value.map(item => item.amount),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1890ff' },
                { offset: 1, color: '#40a9ff' }
              ])
            },
            barWidth: '35%'
          },
          {
            name: '采购数量',
            type: 'bar',
            yAxisIndex: 1,
            data: departmentData.value.map(item => item.count),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#52c41a' },
                { offset: 1, color: '#73d13d' }
              ])
            },
            barWidth: '35%'
          }
        ]
      }

      departmentChart.setOption(option)

      // 添加点击事件
      departmentChart.on('click', function () {
        // 可以跳转到具体部门的采购详情页面
      })
    }





    // 查看所有预警
    const viewAllWarnings = () => {
      router.push('/dashboard/price-warnings')
    }

    // 获取时间预警数据
    const getTimeWarnings = async () => {
      try {
        const response = await api.purchaseRequests.getTimeWarnings()
        if (response.code === 200) {
          Object.assign(timeWarningStats, {
            total_count: response.data.total_count || 0,
            high_priority_count: response.data.high_priority_count || 0,
            medium_priority_count: response.data.medium_priority_count || 0
          })
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取时间预警数据失败:', error)
        }
      }
    }

    // 处理预警导航
    const handleWarningNavigate = ({ path, query }) => {
      showTimeWarnings.value = false
      router.push({ path, query })
    }

    // 初始化部门状态分布图表（彻底修复版本）
    const initDepartmentStatusChart = async () => {
      if (!departmentStatusChartContainer.value) return

      if (departmentStatusChart) {
        departmentStatusChart.dispose()
      }

      departmentStatusChart = echarts.init(departmentStatusChartContainer.value)

      // 直接使用后端返回的部门状态数据，后端已确保包含所有部门
      const chartData = departmentStatusData.value || []

      if (chartData.length === 0) {
        // 如果没有数据，显示空状态
        const option = {
          title: {
            text: '各单位待处理状态分布',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#666'
            },
            left: 'center',
            top: 5
          },
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 16,
              fill: '#999'
            }
          }
        }
        departmentStatusChart.setOption(option)
        return
      }

      // 处理部门名称显示 - 扁平化显示所有部门
      const departments = chartData.map(item => {
        // 使用后端返回的display_name，如果没有则使用name或dept_name
        let displayName = item.display_name || item.name || item.dept_name || '未知部门'

        // 如果名称过长，进行智能截断处理
        if (displayName.length > 10) {
          // 对于包含"-"的部门名称，优先保留后半部分
          if (displayName.includes('-')) {
            const parts = displayName.split('-')
            if (parts.length === 2) {
              const [parent, child] = parts
              // 如果子部门名称不太长，显示简化格式
              if (child.length <= 6) {
                return `${parent.substring(0, 3)}...${child}`
              }
            }
          }
          return displayName.substring(0, 10) + '...'
        }

        return displayName
      })

      const option = {
        title: {
          text: '各单位待处理状态分布',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#666'
          },
          left: 'center',
          top: 5
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: function (params) {
            if (!params || params.length === 0) return ''

            const dataIndex = params[0].dataIndex
            const fullData = chartData[dataIndex]

            // 显示完整的扁平化部门名称
            let fullName = fullData?.display_name || fullData?.name || params[0].name || '未知部门'

            let result = `<strong>${fullName}</strong><br/>`

            // 显示状态统计
            let hasData = false
            params.forEach(param => {
              if (param.value > 0) {
                result += `${param.marker} ${param.seriesName}: ${param.value}项<br/>`
                hasData = true
              }
            })

            if (!hasData) {
              result += '<span style="color: #999;">暂无待处理事项</span><br/>'
            }

            // 添加采购类型统计
            if (fullData && (fullData.unified_count > 0 || fullData.self_count > 0)) {
              result += `<br/><strong>采购类型分布：</strong><br/>`
              if (fullData.unified_count > 0) {
                result += `统一采购: ${fullData.unified_count}项 (¥${(fullData.unified_amount || 0).toLocaleString()})<br/>`
              }
              if (fullData.self_count > 0) {
                result += `自行采购: ${fullData.self_count}项 (¥${(fullData.self_amount || 0).toLocaleString()})`
              }
            }

            return result
          }
        },
        legend: {
          data: ['待审批', '待采购', '待验收', '待结算'],
          top: 25,
          left: 'center'
        },
        grid: {
          top: 70,
          bottom: 80,  // 增加底部空间
          left: 60,
          right: 60
        },
        xAxis: {
          type: 'category',
          data: departments,
          axisLabel: {
            rotate: 45,  // 旋转45度避免重叠
            fontSize: 10,
            interval: 0,  // 显示所有标签，确保扁平化显示
            margin: 20,   // 增加标签与轴的距离
            color: '#666',
            formatter: function (value) {
              // 直接返回处理后的显示名称
              return value
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#e0e0e0',
              width: 1
            }
          },
          axisTick: {
            show: true,
            alignWithLabel: true,
            lineStyle: {
              color: '#e0e0e0'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '数量(项)',
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          }
        },
        series: await (async () => {
          // 导入状态配置
          const { getStatusChartColor } = await import('@/utils/status')

          return [
            {
              name: '待审批',
              type: 'bar',
              stack: 'total',
              data: chartData.map(item => item.pending || 0),
              itemStyle: { color: getStatusChartColor('pending_approval') }
            },
            {
              name: '待采购',
              type: 'bar',
              stack: 'total',
              data: chartData.map(item => item.pending_purchase || 0),
              itemStyle: { color: getStatusChartColor('pending_purchase') }
            },
            {
              name: '待验收',
              type: 'bar',
              stack: 'total',
              data: chartData.map(item => item.pending_acceptance || 0),
              itemStyle: { color: getStatusChartColor('pending_acceptance') }
            },
            {
              name: '待结算',
              type: 'bar',
              stack: 'total',
              data: chartData.map(item => item.pending_reimbursement || 0),
              itemStyle: { color: getStatusChartColor('pending_reimbursement') }
            }
          ]
        })()
      }

      // 添加数据缩放功能，支持横向滚动 - 扁平化显示优化
      if (chartData.length > 6) {
        const displayCount = Math.min(6, chartData.length)  // 扁平化显示时减少同时显示的部门数
        const endPercent = (displayCount / chartData.length) * 100

        option.dataZoom = [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: endPercent,
            bottom: 20,
            height: 25,
            textStyle: {
              fontSize: 10,
              color: '#666'
            },
            handleStyle: {
              color: '#1890ff',
              borderColor: '#1890ff'
            },
            fillerColor: 'rgba(24, 144, 255, 0.1)',
            borderColor: '#e0e0e0'
          },
          {
            type: 'inside',
            xAxisIndex: [0],
            start: 0,
            end: endPercent,
            zoomOnMouseWheel: true,
            moveOnMouseMove: true,
            preventDefaultMouseMove: false
          }
        ]
        // 调整grid底部空间以容纳滚动条和旋转标签
        option.grid.bottom = 140
      } else {
        // 部门较少时，确保有足够的底部空间显示旋转标签
        option.grid.bottom = 110
      }

      departmentStatusChart.setOption(option)
    }





    // 页面加载时初始化
    onMounted(async () => {
      await refreshAllData()
      await getPriceWarnings()
      await getTimeWarnings()
    })

    // 窗口大小变化时调整图表
    const handleResize = () => {
      if (trendChart) trendChart.resize()
      if (methodChart) methodChart.resize()
      if (purchaseTypeChart) purchaseTypeChart.resize()
      if (departmentChart) departmentChart.resize()
      if (departmentStatusChart) departmentStatusChart.resize()

    }

    onMounted(() => {
      window.addEventListener('resize', handleResize)
    })

    // 组件卸载时清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      if (trendChart) trendChart.dispose()
      if (methodChart) methodChart.dispose()
      if (purchaseTypeChart) purchaseTypeChart.dispose()
      if (departmentChart) departmentChart.dispose()
      if (departmentStatusChart) departmentStatusChart.dispose()

    })

    return {
      trendChartContainer,
      methodChartContainer,
      purchaseTypeChartContainer,
      departmentChartContainer,
      departmentStatusChartContainer,
      statisticsData,
      priceWarnings,
      warningColumns,
      timeRange,
      customDateRange,
      statusCounts,
      methodData,
      departmentData,
      departmentStatusData,
      formatCurrency,
      handleTimeRangeChange,
      handleCustomDateChange,
      getDaysFromTimeRange,
      navigateToOverview,
      viewAllWarnings,
      // 时间预警相关
      showTimeWarnings,
      timeWarningStats,
      getTimeWarnings,
      handleWarningNavigate,
      // 加载状态
      loading,
      chartLoading
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
  background: transparent;
}

/* 统计卡片样式 */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 120px;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.warning-card {
  border: 1px solid #ffccc7;
}

.warning-card:hover {
  border-color: #ff7875;
}

.stat-extra {
  margin-top: 4px;
  font-size: 10px;
}

.stat-card.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card.clickable:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.card-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  position: relative;
}

/* 统计卡片优化 */
:deep(.ant-card) {
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

:deep(.ant-card:hover) {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

:deep(.ant-card-head) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 16px 16px 0 0;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  letter-spacing: 0.5px;
}

:deep(.ant-card-body) {
  padding: 24px;
}

/* 统计数字样式 */
:deep(.ant-statistic) {
  text-align: center;
  padding: 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s;
}

:deep(.ant-statistic:hover) {
  background: linear-gradient(135deg, #667eea10 0%, #764ba210 100%);
  border-color: rgba(102, 126, 234, 0.2);
}

:deep(.ant-statistic-title) {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  letter-spacing: 0.3px;
}

:deep(.ant-statistic-content) {
  color: #1e293b;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

:deep(.ant-statistic-content-value) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 徽章样式 */
:deep(.ant-badge) {
  position: relative;
}

:deep(.ant-badge-count) {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  font-weight: 600;
}

/* 活动时间样式 */
.activity-time {
  color: #94a3b8;
  font-size: 12px;
  font-weight: 500;
}

/* 列表项优化 */
:deep(.ant-list-item) {
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s;
}

:deep(.ant-list-item:hover) {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 8px;
  padding-left: 12px;
  padding-right: 12px;
}

:deep(.ant-list-item-meta-title) {
  color: #334155;
  font-weight: 500;
  font-size: 14px;
}

:deep(.ant-list-item-meta-description) {
  color: #64748b;
  font-size: 13px;
}

/* 表格优化 */
:deep(.ant-table) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
  color: #475569;
  font-weight: 600;
  font-size: 13px;
  letter-spacing: 0.3px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: linear-gradient(135deg, #667eea05 0%, #764ba205 100%);
}

/* 标签优化 */
:deep(.ant-tag) {
  border-radius: 20px;
  font-weight: 500;
  font-size: 12px;
  padding: 4px 12px;
  border: none;
}

/* 按钮优化 */
:deep(.ant-btn-link) {
  color: #667eea;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.ant-btn-link:hover) {
  color: #5a6fd8;
  text-decoration: none;
}

/* 筛选区样式 */
.filter-row {
  margin-bottom: 24px;
}

.filter-card {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.filter-label {
  font-weight: 600;
  color: var(--text-primary);
}

/* 图表区域样式 */
.charts-row,
.bottom-row {
  margin-bottom: 24px;
}

.chart-card {
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.chart-container {
  height: 350px;
  width: 100%;
}

.chart-container-small {
  height: 300px;
  width: 100%;
}

/* 消息中心样式 */
.message-card,
.activity-card {
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.message-list,
.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.message-item,
.activity-item {
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 8px;
  margin: 4px 0;
  padding: 8px;
}

.message-item:hover,
.activity-item:hover {
  background: var(--bg-overlay);
}

.message-item.unread {
  background: rgba(24, 144, 255, 0.05);
  border-left: 3px solid var(--primary-color);
}

.message-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  color: var(--text-primary);
}

.message-desc,
.activity-desc {
  color: var(--text-secondary);
  font-size: 13px;
  margin-bottom: 4px;
}

.message-time,
.activity-time {
  color: var(--text-tertiary);
  font-size: 12px;
}

.activity-title {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

/* 单选按钮组样式 */
:deep(.ant-radio-button-wrapper) {
  border-radius: 6px;
  border: 2px solid var(--border-light);
  margin-right: 8px;
}

:deep(.ant-radio-button-wrapper-checked) {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  color: white;
}

/* 移除响应式优化，保持固定布局 */
</style>