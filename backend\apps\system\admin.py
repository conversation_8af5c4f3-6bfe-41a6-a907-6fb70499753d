from django.contrib import admin
from .models import Department, SystemLog, Notification, Menu


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """部门管理"""
    list_display = ['dept_name', 'dept_code', 'parent_name', 'status', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['dept_name', 'dept_code']
    ordering = ['dept_code']

    def parent_name(self, obj):
        return obj.parent.dept_name if obj.parent else '无'
    parent_name.short_description = '上级部门'


@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    """系统日志管理"""
    list_display = ['username', 'log_type', 'action', 'target_model', 'ip_address', 'created_at']
    list_filter = ['log_type', 'created_at']
    search_fields = ['username', 'action', 'target_model']
    ordering = ['-created_at']
    readonly_fields = ['created_at']

    def has_add_permission(self, request):
        return False  # 不允许手动添加日志

    def has_change_permission(self, request, obj=None):
        return False  # 不允许修改日志


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """通知管理"""
    list_display = ['title', 'recipient', 'notification_type', 'priority', 'is_read', 'created_at']
    list_filter = ['notification_type', 'priority', 'is_read', 'created_at']
    search_fields = ['title', 'content', 'recipient__username']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'read_at']


@admin.register(Menu)
class MenuAdmin(admin.ModelAdmin):
    """菜单管理"""
    list_display = ['menu_name', 'menu_code', 'parent_name', 'menu_type', 'sort_order', 'is_active']
    list_filter = ['menu_type', 'is_active', 'is_visible']
    search_fields = ['menu_name', 'menu_code', 'route_path']
    ordering = ['sort_order', 'menu_code']

    def parent_name(self, obj):
        return obj.parent.menu_name if obj.parent else '无'
    parent_name.short_description = '父菜单'
