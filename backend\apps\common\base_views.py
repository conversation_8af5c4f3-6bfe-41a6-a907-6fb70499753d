"""
通用基础视图类
减少重复代码，统一响应格式和错误处理
"""
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db import transaction
from django.core.exceptions import ValidationError
import logging

logger = logging.getLogger(__name__)


class StandardPagination(PageNumberPagination):
    """标准分页配置"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 1000
    
    def get_paginated_response(self, data):
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'results': data,
                'count': self.page.paginator.count,  # 改为count以匹配前端期望
                'current_page': self.page.number,
                'page_size': self.page.paginator.per_page,
                'total_pages': self.page.paginator.num_pages,
                'has_next': self.page.has_next(),
                'has_previous': self.page.has_previous(),
                'next': self.get_next_link(),
                'previous': self.get_previous_link(),
            }
        })


class BaseAPIView:
    """基础API视图混入类"""
    
    def success_response(self, data=None, message='success', status_code=status.HTTP_200_OK):
        """成功响应"""
        return Response({
            'code': 200,
            'message': message,
            'data': data
        }, status=status_code)
    
    def error_response(self, message='操作失败', errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        """错误响应"""
        response_data = {
            'code': status_code,
            'message': message,
            'data': errors
        }
        return Response(response_data, status=status_code)
    
    def handle_exception(self, exc):
        """统一异常处理"""
        if isinstance(exc, ValidationError):
            return self.error_response(
                message='数据验证失败',
                errors=exc.message_dict if hasattr(exc, 'message_dict') else str(exc),
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        logger.error(f"API异常: {type(exc).__name__}: {str(exc)}")
        return super().handle_exception(exc)


class BaseListCreateView(BaseAPIView, generics.ListCreateAPIView):
    """基础列表创建视图"""
    pagination_class = StandardPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    
    def list(self, request, *args, **kwargs):
        """列表查询"""
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = self.get_serializer(queryset, many=True)
            return self.success_response(serializer.data)
        except Exception as exc:
            return self.handle_exception(exc)
    
    def create(self, request, *args, **kwargs):
        """创建记录"""
        try:
            with transaction.atomic():
                serializer = self.get_serializer(data=request.data)
                if serializer.is_valid():
                    instance = serializer.save()
                    return self.success_response(
                        data=self.get_serializer(instance).data,
                        message='创建成功',
                        status_code=status.HTTP_201_CREATED
                    )
                
                return self.error_response(
                    message='数据验证失败',
                    errors=serializer.errors
                )
        except Exception as exc:
            return self.handle_exception(exc)


class BaseDetailView(BaseAPIView, generics.RetrieveUpdateDestroyAPIView):
    """基础详情视图"""
    
    def retrieve(self, request, *args, **kwargs):
        """获取详情"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return self.success_response(serializer.data)
        except Exception as exc:
            return self.handle_exception(exc)
    
    def update(self, request, *args, **kwargs):
        """更新记录"""
        try:
            with transaction.atomic():
                partial = kwargs.pop('partial', False)
                instance = self.get_object()
                serializer = self.get_serializer(instance, data=request.data, partial=partial)
                
                if serializer.is_valid():
                    instance = serializer.save()
                    return self.success_response(
                        data=self.get_serializer(instance).data,
                        message='更新成功'
                    )
                
                return self.error_response(
                    message='数据验证失败',
                    errors=serializer.errors
                )
        except Exception as exc:
            return self.handle_exception(exc)
    
    def destroy(self, request, *args, **kwargs):
        """删除记录"""
        try:
            with transaction.atomic():
                instance = self.get_object()
                instance.delete()
                return self.success_response(message='删除成功')
        except Exception as exc:
            return self.handle_exception(exc)


class BaseBatchView(BaseAPIView):
    """基础批量操作视图混入类"""
    
    def batch_operation(self, request, operation_func, success_message='批量操作成功'):
        """通用批量操作"""
        try:
            ids = request.data.get('ids', [])
            if not ids:
                return self.error_response('请选择要操作的记录')
            
            with transaction.atomic():
                result = operation_func(ids, request.data)
                return self.success_response(
                    data=result,
                    message=success_message
                )
        except Exception as exc:
            return self.handle_exception(exc)


class BaseStatusView(BaseAPIView):
    """基础状态管理视图混入类"""
    
    def update_status(self, request, instance, new_status, reason=''):
        """更新状态"""
        try:
            with transaction.atomic():
                old_status = instance.status
                instance.status = new_status
                
                # 如果有原因字段，保存原因
                if hasattr(instance, 'status_reason'):
                    instance.status_reason = reason
                
                instance.full_clean()  # 验证模型
                instance.save()
                
                # 记录状态变更日志
                self.log_status_change(instance, old_status, new_status, reason, request.user)
                
                return self.success_response(
                    message=f'状态已更新为{new_status}'
                )
        except ValidationError as e:
            return self.error_response(
                message='状态更新失败',
                errors=e.message_dict if hasattr(e, 'message_dict') else str(e)
            )
        except Exception as exc:
            return self.handle_exception(exc)
    
    def log_status_change(self, instance, old_status, new_status, reason, user):
        """记录状态变更日志"""
        try:
            from apps.system.services import LoggingService

            # 尝试将状态编码转换为名称
            old_status_name = old_status
            new_status_name = new_status

            try:
                from apps.purchase.services.dict_service import DictService
                # 根据模型类型确定字典类型
                if instance.__class__.__name__ == 'PurchaseRequest':
                    old_status_name = DictService.get_text('status', old_status, old_status)
                    new_status_name = DictService.get_text('status', new_status, new_status)
            except Exception:
                pass  # 如果转换失败，使用原始值

            # 获取请求上下文信息
            ip_address = None
            user_agent = None
            try:
                from apps.system.request_context import RequestContext
                ip_address = RequestContext.get_client_ip()
                user_agent = RequestContext.get_user_agent()
            except Exception:
                pass

            LoggingService.log_user_action(
                user=user,
                log_type='status_change',
                action=f'状态变更: {old_status_name} -> {new_status_name}',
                target_model=instance.__class__.__name__,
                target_id=instance.id,
                ip_address=ip_address,
                user_agent=user_agent,
                extra_data={
                    'old_status': old_status_name,
                    'new_status': new_status_name,
                    'old_status_code': old_status,
                    'new_status_code': new_status,
                    'reason': reason
                }
            )
        except Exception as e:
            logger.warning(f"记录状态变更日志失败: {e}")


class BaseExportView(BaseAPIView):
    """基础导出视图混入类"""
    
    def export_data(self, request, queryset, filename='export.xlsx'):
        """导出数据"""
        try:
            from django.http import HttpResponse
            import openpyxl
            from openpyxl.utils.dataframe import dataframe_to_rows
            import pandas as pd
            
            # 转换为DataFrame
            data = []
            for item in queryset:
                serializer = self.get_serializer(item)
                data.append(serializer.data)
            
            df = pd.DataFrame(data)
            
            # 创建Excel文件
            wb = openpyxl.Workbook()
            ws = wb.active
            
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # 设置响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            wb.save(response)
            return response
            
        except Exception as exc:
            return self.handle_exception(exc)


# 常用的响应函数
def success_response(data=None, message='success'):
    """成功响应快捷函数"""
    return Response({
        'code': 200,
        'message': message,
        'data': data
    })


def error_response(message='操作失败', errors=None, status_code=status.HTTP_400_BAD_REQUEST):
    """错误响应快捷函数"""
    return Response({
        'code': status_code,
        'message': message,
        'data': errors
    }, status=status_code)
