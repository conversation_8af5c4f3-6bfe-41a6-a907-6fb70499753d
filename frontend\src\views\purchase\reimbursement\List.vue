<template>
  <div class="reimbursement-list">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <DollarOutlined />
          结算报销
        </h1>
        <p class="page-subtitle">处理已验收物资的结算报销，管理财务流程</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalCount }}</span>
          <span class="stat-label">报销总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ pendingCount }}</span>
          <span class="stat-label">待结算</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ settledCount }}</span>
          <span class="stat-label">已结算</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ totalAmount }}</span>
          <span class="stat-label">总金额</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ departmentCount }}</span>
          <span class="stat-label">涉及部门</span>
        </div>
      </div>
    </div>

    <!-- 同一个面板内包括上方按钮区、下方表格区 -->
    <div class="department-tables business-card">
      <!-- 表格区域 -->
      <div class="table-section">
        <!-- 表格标题和按钮区域 - 使用flex布局，两端对齐 -->
        <div class="table-header-flex">
          <div class="table-title-section">
            <div class="table-title">
              <span>结算报销列表</span>
              <span class="table-subtitle">共 {{ totalCount }} 条记录</span>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="table-actions">
            <a-space :size="8">
              <!-- 主要业务操作按钮 -->
              <PermissionButton v-if="selectedRowKeys.length > 0 && hasPendingReimbursements"
                permission="purchase:reimbursement:settle" type="primary"
                @click="batchSettle" size="small" class="compact-action-btn" :loading="loading"
                style="background: #52c41a; border-color: #52c41a">
                <CheckCircleOutlined />
                批量结算 ({{ pendingReimbursementSelectedCount }})
              </PermissionButton>

              <!-- 分隔线 -->
              <a-divider v-if="selectedRowKeys.length > 0 && hasPendingReimbursements" type="vertical" />

              <!-- 筛选和视图操作按钮 -->
              <a-button @click="toggleFilters" size="small" class="compact-action-btn">
                <template #icon>
                  <component :is="showFilters ? 'UpOutlined' : 'DownOutlined'" />
                </template>
                {{ showFilters ? "收起筛选" : "展开筛选" }}
              </a-button>
              <a-button @click="resetFilters" size="small" class="compact-action-btn">重置筛选</a-button>
              <PermissionButton permission="purchase:reimbursement:export" @click="showExportModal = true" size="small" class="compact-action-btn">
                <DownloadOutlined />
                导出
              </PermissionButton>
              <a-button size="small" class="compact-action-btn" @click="showColumnFilter = true">
                <template #icon>
                  <SettingOutlined />
                </template>
                字段筛选
                <a-badge :count="selectedColumns.length" :number-style="{
                  backgroundColor: '#52c41a',
                  fontSize: '10px',
                }" />
              </a-button>

              <!-- 字段筛选模态框 -->
              <a-modal v-model:open="showColumnFilter" title="字段筛选配置" :footer="null" width="auto" :centered="true"
                :mask-closable="true" :destroy-on-close="true" wrap-class-name="column-filter-modal">
                <template #closeIcon>
                  <CloseOutlined />
                </template>
                <div class="column-filter-panel">
                  <div class="preset-section">
                    <a-dropdown v-model:open="presetDropdownOpen" :trigger="['click']" placement="bottomRight"
                      :overlay-style="{ zIndex: 9999 }" @click.stop>
                      <a-button class="preset-trigger" @click.stop size="large">
                        <template #icon>
                          <SettingOutlined />
                        </template>
                        预设配置
                        <DownOutlined />
                      </a-button>
                      <template #overlay>
                        <a-menu @click="handlePresetClick" @click.stop>
                          <template v-for="item in presetMenuItems" :key="item.key || 'divider'">
                            <a-menu-divider v-if="item.type === 'divider'" />
                            <a-menu-item v-else :key="item.key">{{
                              item.title
                              }}</a-menu-item>
                          </template>
                        </a-menu>
                      </template>
                    </a-dropdown>
                    <div>
                      <a-button type="primary" size="large" @click="handleSelectAll"
                        style="margin-left: 12px">全选</a-button>
                      <a-button size="large" @click="handleReset" style="margin-left: 8px">重置</a-button>
                    </div>
                  </div>

                  <div class="filter-tip">
                    <span>已选择 {{ selectedColumns.length }} /
                      {{ columnOptions.length }} 个字段</span>
                  </div>

                  <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnChange">
                    <!-- 动态字段分类 - 使用flex横向布局 -->
                    <div class="field-categories-container">
                      <div v-for="category in fieldCategories" :key="category.key" class="field-category-section">
                        <h5 class="category-title">{{ category.title }}</h5>
                        <div class="category-fields">
                          <div v-for="option in columnOptions.filter(
                            (opt) => opt.category === category.key
                          )" :key="option.key" class="column-option">
                            <a-checkbox :value="option.key" :disabled="option.required" @click.stop>
                              <span class="column-title">{{
                                option.title
                                }}</span>
                              <a-tag v-if="option.required" size="small" color="blue">必选</a-tag>
                            </a-checkbox>
                          </div>
                        </div>
                      </div>
                    </div>
                  </a-checkbox-group>
                </div>
              </a-modal>
            </a-space>
          </div>
        </div>

        <!-- 详细筛选控件区域 - 分两行布局，与页面同宽 -->
        <div v-show="showFilters" class="detailed-filters-fullwidth">
          <!-- 第一行：基础筛选项 -->
          <div class="filter-row">
            <a-row :gutter="[16, 12]" align="middle">
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-select v-model:value="filters.status" placeholder="状态" allowClear @change="handleFilterChange"
                  class="filter-select">
                  <a-select-option value="">全部状态</a-select-option>
                  <a-select-option v-for="status in settlementStatusOptions" :key="status.value" :value="status.value">
                    {{ status.label }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-select v-model:value="filters.purchaseType" placeholder="采购类型" allowClear
                  @change="handleFilterChange" class="filter-select">
                  <a-select-option value="">全部类型</a-select-option>
                  <a-select-option v-for="type in purchaseTypes" :key="type.value" :value="type.value">
                    {{ type.label }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-select v-model:value="filters.department" placeholder="需求单位" allowClear @change="handleFilterChange"
                  class="filter-select" show-search :filter-option="filterOption">
                  <a-select-option value="">全部需求单位</a-select-option>
                  <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                    {{ dept.dept_name }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-select v-model:value="filters.financeReviewer" placeholder="财务审核人" allowClear
                  @change="handleFilterChange" class="filter-select" show-search :filter-option="filterOption">
                  <a-select-option value="">全部审核人</a-select-option>
                  <a-select-option v-for="user in financeReviewers" :key="user.id" :value="user.id">
                    {{ user.username }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.minSettlementAmount" placeholder="最小金额" @input="debouncedFilterChange"
                  class="filter-input" type="number" :min="0">
                  <template #prefix> ¥ </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.maxSettlementAmount" placeholder="最大金额" @input="debouncedFilterChange"
                  class="filter-input" type="number" :min="0">
                  <template #prefix> ¥ </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-range-picker v-model:value="filters.reimbursementTimeRange" :placeholder="['开始时间', '结束时间']"
                  @change="handleFilterChange" class="filter-date-picker" />
              </a-col>
            </a-row>
          </div>

          <!-- 第二行：搜索和其他筛选项 -->
          <div class="filter-row">
            <a-row :gutter="[16, 12]" align="middle">
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.id" placeholder="ID" @input="debouncedFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.itemName" placeholder="搜索物品名称" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.specification" placeholder="搜索规格型号" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.procurementMethod" placeholder="采购方式" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.requirementSource" placeholder="需求来源" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.fundProject" placeholder="经费项目" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.unit" placeholder="计量单位" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.itemCategory" placeholder="物品种类" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                <a-input v-model:value="filters.remarks" placeholder="需求备注" @change="handleFilterChange"
                  class="filter-input">
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>

      <div class="table-container">
        <a-table :columns="filteredColumns" :data-source="reimbursements" :row-key="(record) => record.id"
          :row-selection="rowSelection" :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: (page, pageSize) =>
              handleTableChange({ current: page, pageSize }),
            onShowSizeChange: (current, size) =>
              handleTableChange({ current, pageSize: size }),
          }" :loading="loading" @change="handleTableChange" :scroll="{ x: 1600, y: 'calc(100vh - 400px)' }"
          :virtual="false" :row-height="54" bordered class="unified-table reimbursement-table" table-layout="fixed">
          <!-- 空状态模板 -->
          <template #emptyText>
            <a-empty description="暂无结算报销数据" />
          </template>
          <template #bodyCell="{ column, record }">
            <!-- 状态标签 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status_display || record.status }}
              </a-tag>
            </template>

            <!-- 金额显示 -->
            <template v-if="column.dataIndex === 'total_amount'">
              ¥{{ record.total_amount?.toLocaleString() || "0" }}
            </template>

            <!-- 操作列 -->
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <PermissionButton permission="purchase:reimbursement:view" type="link" size="small" @click="viewDetail(record)">
                  详情
                </PermissionButton>
                <PermissionButton permission="purchase:reimbursement:settle" type="link" size="small" @click="openSettlementModal(record)" v-if="
                  (record._originalStatus || record.status) ===
                  'pending_reimbursement'
                " style="color: #52c41a">
                  结算信息
                </PermissionButton>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 结算信息模态框 -->
    <a-modal v-model:open="showSettlementModal" title="结算信息" width="50%" :footer="null" :destroy-on-close="true">
      <div class="settlement-form">
        <a-form ref="settlementFormRef" :model="settlementForm" :rules="settlementFormRules" layout="vertical">
          <!-- 基本信息展示 -->
          <div class="form-section">
            <h4>需求信息</h4>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="物品名称">
                  <a-input :value="settlementForm.item_name" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="规格型号">
                  <a-input :value="settlementForm.specification" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="需求单位">
                  <a-input :value="settlementForm.hierarchy_path" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="申请人">
                  <a-input :value="settlementForm.requester_name" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="验收数量">
                  <a-input :value="settlementForm.actual_quantity" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="采购单价">
                  <a-input :value="`¥${settlementForm.actual_unit_price ||
                    settlementForm.unit_price
                    }`" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="采购金额">
                  <a-input :value="`¥${settlementForm.purchase_total_amount ||
                    settlementForm.total_amount ||
                    0
                    }`" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 结算信息填报 -->
          <div class="form-section">
            <h4>结算信息</h4>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="实际结算金额" name="settlement_amount" required>
                  <a-input-number v-model:value="settlementForm.settlement_amount" :min="0" :precision="2"
                    style="width: 100%" placeholder="请输入实际结算金额" />
                  <div class="form-hint">
                    采购金额：¥{{
                      settlementForm.purchase_total_amount ||
                      settlementForm.total_amount ||
                      0
                    }}
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="金额差异">
                  <a-input :value="amountDifference" disabled style="width: 100%" />
                  <div class="form-hint">
                    差异率：{{ amountDifferenceRate }}%
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item label="结算状态">
                  <a-tag :color="hasAmountException ? 'error' : 'success'">
                    {{ hasAmountException ? "异常" : "正常" }}
                  </a-tag>
                  <div class="form-hint" v-if="hasAmountException">
                    差异率≥5%需要异常处理
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item label="交易流水号" name="transaction_number" required>
                  <a-input v-model:value="settlementForm.transaction_number" placeholder="请手动输入19位交易流水号"
                    :maxlength="19" />
                  <div class="form-hint">
                    格式：前8位日期(YYYYMMDD) +
                    后11位数字，如：2025062512345678901
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="收款人户名" name="payee_name" required>
                  <a-input v-model:value="settlementForm.payee_name" placeholder="请输入收款人户名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="收款人账号" name="payee_account" required>
                  <a-input v-model:value="settlementForm.payee_account" placeholder="请输入收款人账号" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16" v-if="hasAmountException">
              <a-col :span="24">
                <a-form-item label="异常原因说明" name="settlement_exception_reason">
                  <a-textarea v-model:value="settlementForm.settlement_exception_reason" :rows="3"
                    placeholder="请说明金额差异的原因" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item label="结算备注" name="settlement_remarks">
                  <a-textarea v-model:value="settlementForm.settlement_remarks" :rows="3" placeholder="请输入结算备注信息" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <a-space>
            <a-button @click="showSettlementModal = false">取消</a-button>
            <PermissionButton permission="purchase:reimbursement:save" @click="saveSettlementInfo" :loading="settlementLoading">保存</PermissionButton>
            <PermissionButton permission="purchase:reimbursement:settle" type="primary" @click="submitSettlementInfo" :loading="settlementLoading">
              提交结算
            </PermissionButton>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 导出配置对话框 -->
    <a-modal v-model:open="showExportModal" title="导出Excel配置" width="80%" :footer="null" :destroy-on-close="true">
      <div class="export-config-container">
        <!-- 筛选条件 -->
        <div class="filter-section" style="
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
            background: #fafafa;
          ">
          <h4>筛选条件</h4>
          <a-form :model="exportFilters">
            <a-row :gutter="[12, 8]" align="bottom">
              <a-col :span="4">
                <a-form-item label="结算状态">
                  <a-select v-model:value="exportFilters.status" placeholder="选择状态" allowClear mode="multiple">
                    <a-select-option v-for="status in settlementStatusOptions" :key="status.value"
                      :value="status.value">
                      {{ status.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="需求单位">
                  <a-select v-model:value="exportFilters.department" placeholder="选择需求单位" allowClear show-search>
                    <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                      {{ dept.dept_name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="结算员">
                  <a-select v-model:value="exportFilters.reimburser" placeholder="选择结算员" allowClear show-search>
                    <a-select-option v-for="user in users" :key="user.id" :value="user.id">
                      {{ user.real_name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="日期范围">
                  <a-range-picker v-model:value="exportFilters.dateRange" style="width: 100%"
                    placeholder="['开始日期', '结束日期']" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item>
                  <a-space>
                    <a-button @click="resetExportFilters" size="large">重置</a-button>
                    <a-button type="primary" @click="searchExportRecords" size="large">搜索</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 导出字段配置 -->
        <div class="fields-section" style="border-radius: 6px; margin: 16px 0">
          <a-collapse v-model:activeKey="fieldsCollapseKey" size="small">
            <a-collapse-panel key="1" header="导出字段配置">
              <template #extra>
                <a-space>
                  <a-button @click.stop="selectAllExportFields" size="small">全选</a-button>
                  <a-button @click.stop="resetExportFields" size="small">重置</a-button>
                  <a-button @click.stop="selectRequiredExportFields" size="small">仅必选</a-button>
                </a-space>
              </template>
              <a-checkbox-group v-model:value="selectedExportFields" @change="onExportFieldsChange">
                <div class="export-fields-container">
                  <div v-for="category in exportFieldCategories" :key="category.key" class="export-field-category">
                    <div class="export-category-header">
                      <h5 class="export-category-title">
                        {{ category.title }}
                      </h5>
                    </div>
                    <div class="export-category-fields">
                      <div v-for="field in exportFieldOptions.filter(
                        (opt) => opt.category === category.key
                      )" :key="field.value" class="export-field-option">
                        <a-checkbox :value="field.value" :disabled="field.required">
                          {{ field.label }}
                          <a-tag v-if="field.required" color="red" size="small" style="margin-left: 4px">必选</a-tag>
                        </a-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </a-checkbox-group>
            </a-collapse-panel>
          </a-collapse>
        </div>

        <!-- 选择导出记录 -->
        <div class="records-section" style="
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
          ">
          <div class="section-header">
            <h4>选择导出记录</h4>
            <div class="section-actions">
              <span class="record-count">共 {{ exportRecords.length }} 条记录，已选择
                {{ selectedExportRecords.length }} 条</span>
              <a-space>
                <a-select v-model:value="exportRecordPageSize" @change="onExportRecordPageSizeChange" size="small"
                  style="width: 80px">
                  <a-select-option :value="10">10</a-select-option>
                  <a-select-option :value="20">20</a-select-option>
                  <a-select-option :value="50">50</a-select-option>
                  <a-select-option :value="100">100</a-select-option>
                </a-select>
                <a-button @click="searchExportRecords" size="small" :loading="exportSearchLoading">
                  <ReloadOutlined />
                  刷新
                </a-button>
              </a-space>
            </div>
          </div>
          <a-table :columns="exportRecordColumns" :data-source="exportRecords" :row-selection="exportRecordRowSelection"
            :row-key="(record) => record.id" :pagination="{
              current: exportRecordPagination.current,
              total: exportRecordPagination.total,
              pageSize: exportRecordPageSize,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: onExportRecordPageChange,
            }" size="small" :scroll="{ x: 800 }">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ record.status_display || record.status }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'settlement_amount'">
                ¥{{ record.settlement_amount?.toLocaleString() || "0" }}
              </template>
              <template v-else-if="column.key === 'created_at'">
                {{
                  record.created_at ? formatDateToYMD(record.created_at) : "-"
                }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 导出预览和操作 -->
        <div class="actions-section">
          <a-space>
            <a-button @click="previewExportData" :disabled="selectedExportRecords.length === 0">
              <EyeOutlined />
              预览 ({{ selectedExportRecords.length }}条)
            </a-button>
            <a-button type="primary" @click="executeExport" :disabled="selectedExportRecords.length === 0"
              :loading="exportLoading">
              <DownloadOutlined />
              导出Excel ({{ selectedExportRecords.length }}条)
            </a-button>
            <a-button @click="handleExportCancel">取消</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 导出预览模态框 -->
    <a-modal :open="showPreviewModal" title="导出预览" width="90%" :footer="null" :centered="true" :mask-closable="true"
      :destroy-on-close="true" wrap-class-name="preview-modal-wrapper"
      :body-style="{ height: '80vh', overflow: 'auto' }" @cancel="showPreviewModal = false">
      <div class="preview-container">
        <div class="preview-header">
          <a-space>
            <span>预览数据 (共{{ previewTotal }}条记录)</span>
            <a-button size="small" @click="loadPreviewData" :loading="previewLoading">
              <ReloadOutlined />
              刷新
            </a-button>
          </a-space>
        </div>
        <div class="preview-table-container">
          <a-table :columns="previewColumns" :data-source="previewData" :pagination="false" size="small"
            :scroll="{ x: 'max-content', y: 'calc(80vh - 200px)' }" :loading="previewLoading" row-key="id" bordered>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getStatusColor(record[column.dataIndex])">
                  {{ record.status_display || record[column.dataIndex] }}
                </a-tag>
              </template>
              <template v-else-if="column.dataIndex === 'purchase_type'">
                {{ record.purchase_type_display || record[column.dataIndex] }}
              </template>
              <template v-else-if="column.dataIndex === 'procurement_method'">
                {{ record.procurement_method_display || record[column.dataIndex] || '-' }}
              </template>
              <template v-else-if="column.dataIndex === 'unit'">
                {{ record.unit_display || record[column.dataIndex] || '-' }}
              </template>
              <template v-else-if="column.dataIndex === 'item_category'">
                {{ record.item_category_display || record[column.dataIndex] }}
              </template>
              <template v-else-if="column.dataIndex === 'requirement_source'">
                {{ getRequirementSourceText(record[column.dataIndex]) }}
              </template>
              <template v-else-if="column.dataIndex === 'fund_project_name'">
                {{ getFundProjectText(record[column.dataIndex]) }}
              </template>
              <template v-else-if="
                column.dataIndex.includes('_at') ||
                column.dataIndex.includes('_date')
              ">
                {{
                  record[column.dataIndex]
                    ? formatDateToYMD(record[column.dataIndex])
                    : "-"
                }}
              </template>
              <template v-else-if="
                column.dataIndex.includes('amount') ||
                column.dataIndex.includes('price')
              ">
                {{
                  record[column.dataIndex]
                    ? `¥${parseFloat(record[column.dataIndex]).toFixed(2)}`
                    : "-"
                }}
              </template>
              <template v-else>
                {{ record[column.dataIndex] || "-" }}
              </template>
            </template>
          </a-table>
        </div>
      </div>

      <template #footer>
        <a-space>
          <a-button @click="showPreviewModal = false">关闭预览</a-button>
          <a-button type="primary" @click="executeExport" :loading="exportLoading">
            <DownloadOutlined />
            确认导出
          </a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { message } from "ant-design-vue";
import { debounce } from "@/utils/debounce";
import {
  DollarOutlined,
  DownloadOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  SettingOutlined,
  DownOutlined,
  UpOutlined,
  SearchOutlined,
  CloseOutlined,
} from "@ant-design/icons-vue";
import api from "@/api";
import {
  getPageColumnOptions,
  getPageColumnPresets,
  getPageFieldCategories,
  getPagePresetMenuItems,
  getPageExportFieldOptions,
  getPageDefaultExportFields,
  getPageDefaultColumns,
  sortColumnsByOrder,
} from "@/utils/validation";
import { useDictMixin, PAGE_DICT_TYPES } from "@/mixins/dictMixin";
import PermissionButton from '@/components/Permission/PermissionButton.vue';

export default {
  name: "ReimbursementList",
  components: {
    DollarOutlined,
    DownloadOutlined,
    CheckOutlined,
    CheckCircleOutlined,
    SettingOutlined,
    DownOutlined,
    UpOutlined,
    SearchOutlined,
    CloseOutlined,
    PermissionButton,
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const reimbursements = ref([]);
    const departments = ref([]);
    const selectedRowKeys = ref([]);

    // 使用字典混入 - 只保留必要的功能
    const { getDictOptions, getStatusColor, formatDateToYMD } = useDictMixin(
      PAGE_DICT_TYPES.SETTLEMENT
    );
    const users = ref([]);

    // 筛选选项数据源 - 使用字典数据
    const purchaseTypes = ref([]);
    const reimbursers = ref([]);
    const financeReviewers = ref([]);

    // 筛选控件显示状态
    const showFilters = ref(false);
    const presetDropdownOpen = ref(false);
    const showExportModal = ref(false);
    const showColumnFilter = ref(false);

    const showPreviewModal = ref(false); // 预览模态框显示状态

    // 导出相关状态
    const exportActiveTab = ref("fields");
    const exportLoading = ref(false);
    const previewLoading = ref(false);
    const previewData = ref([]);
    const previewTotal = ref(0);

    // 基于业务流程的导出字段配置（结算阶段）
    const exportFieldOptions = getPageExportFieldOptions("settlement");
    const exportFieldCategories = getPageFieldCategories("settlement");

    // 初始化选中字段，确保必选字段被包含
    const initializeSelectedFields = () => {
      const defaultFields = getPageDefaultExportFields("settlement");
      const requiredFields = exportFieldOptions
        .filter((field) => field.required)
        .map((field) => field.value);
      // 合并默认字段和必选字段，去重
      const allFields = [...new Set([...defaultFields, ...requiredFields])];
      return allFields;
    };

    const selectedExportFields = ref(initializeSelectedFields());
    const fieldsCollapseKey = ref([]); // 默认折叠状态

    // 结算报销页面的状态选项
    const settlementStatusOptions = [
      { value: "pending_reimbursement", label: "待结算" },
      { value: "settled", label: "已结算" },
    ];

    // 导出筛选条件
    const exportFilters = reactive({
      status: [],
      department: [],
      itemName: "",
      requester: "",
      reimburser: "",
      minAmount: "",
      maxAmount: "",
      dateRange: [],
      // 新增导出筛选字段
      procurementMethod: "",
      requirementSource: "",
      fundProject: "",
      unit: "",
      itemCategory: "",
      remarks: "",
    });

    // 导出记录选择相关
    const exportRecords = ref([]);
    const selectedExportRecords = ref([]);
    const selectedExportRecordData = ref([]);
    const exportSearchLoading = ref(false);
    const exportRecordPageSize = ref(20);
    const exportRecordPagination = ref({
      current: 1,
      total: 0,
    });

    // 筛选条件
    const filters = reactive({
      status: "",
      id: "",
      itemName: "",
      specification: "",
      department: "",
      reimburser: "",
      financeReviewer: "",
      purchaseType: "",
      reimbursementTimeRange: [],
      minPurchaseAmount: "",
      maxPurchaseAmount: "",
      minSettlementAmount: "",
      maxSettlementAmount: "",
      // 新增缺失的筛选字段
      procurementMethod: "", // 采购方式
      requirementSource: "", // 需求来源
      fundProject: "", // 经费项目
      unit: "", // 计量单位
      itemCategory: "", // 物品种类
      remarks: "", // 需求备注
    });

    // 从本地存储读取字段配置
    const getStoredColumns = () => {
      const COLUMNS_VERSION = "2.0"; // 版本号，更新时清除旧缓存
      const STORAGE_KEY = `reimbursements-columns-v${COLUMNS_VERSION}`;

      try {
        // 清除旧版本的缓存
        const oldKeys = ["reimbursements-columns"];
        oldKeys.forEach((key) => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
          }
        });

        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const parsed = JSON.parse(stored);
          // 确保必选字段始终被包含
          const requiredColumns = columnOptions
            .filter((opt) => opt.required)
            .map((opt) => opt.key);
          return [...new Set([...parsed, ...requiredColumns])];
        }
      } catch (error) {
        console.warn("读取字段配置失败:", error);
      }
      // 使用默认字段配置（按顺序排列）
      const defaultColumns = getPageDefaultColumns("settlement");
      return defaultColumns; // 默认配置已包含action列
    };

    // 保存字段配置到本地存储
    const saveColumnsToStorage = (columns) => {
      const COLUMNS_VERSION = "2.0";
      const STORAGE_KEY = `reimbursements-columns-v${COLUMNS_VERSION}`;
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(columns));
      } catch (error) {
        console.warn("保存字段配置失败:", error);
      }
    };

    // 基于业务流程的字段筛选配置（结算阶段，排除操作列）
    const columnOptions = getPageColumnOptions("settlement").filter(
      (opt) => opt.key !== "action"
    );

    // 初始化选中的字段
    const selectedColumns = ref(getStoredColumns());

    // 基于业务流程的预设配置（结算阶段）
    const columnPresets = getPageColumnPresets("settlement");

    // 基于业务流程的字段分类配置（结算阶段）
    const fieldCategories = getPageFieldCategories("settlement");

    // 基于业务流程的预设菜单选项（结算阶段）
    const presetMenuItems = getPagePresetMenuItems("settlement");

    // 结算信息填报相关
    const showSettlementModal = ref(false);
    const settlementLoading = ref(false);
    const settlementFormRef = ref(null);
    const settlementForm = reactive({
      id: null,
      item_name: "",
      specification: "",
      hierarchy_path: "",
      requester_name: "",
      actual_quantity: 0,
      unit_price: 0,
      actual_unit_price: 0,
      total_amount: 0,
      purchase_total_amount: 0,
      settlement_amount: 0,
      transaction_number: "",
      payee_account: "",
      payee_name: "",
      settlement_remarks: "",
    });

    // 结算表单验证规则
    const settlementFormRules = {
      settlement_amount: [
        { required: true, message: "请输入实际结算金额", trigger: "blur" },
        {
          validator: (_, value) => {
            if (value === null || value === undefined || value === "") {
              return Promise.reject(new Error("请输入实际结算金额"));
            }
            const num = Number(value);
            if (isNaN(num)) {
              return Promise.reject(new Error("请输入有效的金额"));
            }
            if (num < 0) {
              return Promise.reject(new Error("金额不能为负数"));
            }
            return Promise.resolve();
          },
          trigger: "blur",
        },
      ],
      transaction_number: [
        { required: true, message: "请输入交易流水号", trigger: "blur" },
        {
          pattern: /^\d{19}$/,
          message: "交易流水号必须为19位数字",
          trigger: "blur",
        },
      ],
      payee_account: [
        { required: true, message: "请输入收款人账号", trigger: "blur" },
      ],
      payee_name: [
        { required: true, message: "请输入收款人户名", trigger: "blur" },
      ],
    };

    // 计算金额差异
    const amountDifference = computed(() => {
      const purchaseAmount =
        settlementForm.purchase_total_amount ||
        settlementForm.total_amount ||
        0;
      const settlementAmount = settlementForm.settlement_amount || 0;
      return `¥${(settlementAmount - purchaseAmount).toFixed(2)}`;
    });

    // 计算差异率
    const amountDifferenceRate = computed(() => {
      const purchaseAmount =
        settlementForm.purchase_total_amount ||
        settlementForm.total_amount ||
        0;
      const settlementAmount = settlementForm.settlement_amount || 0;

      if (purchaseAmount > 0) {
        const difference = Math.abs(settlementAmount - purchaseAmount);
        return ((difference / purchaseAmount) * 100).toFixed(2);
      }
      return "0.00";
    });

    // 判断是否有金额异常
    const hasAmountException = computed(() => {
      return parseFloat(amountDifferenceRate.value) >= 5;
    });

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) =>
        `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
      pageSizeOptions: ["10", "20", "50", "100"],
    });

    // 统计数据 - 使用响应式数据而非计算属性，通过API获取正确的全局统计
    const totalCount = ref(0);
    const pendingCount = ref(0);
    const settledCount = ref(0);
    const totalAmount = ref("¥0");
    const departmentCount = ref(0);

    // 获取结算报销统计数据
    const getReimbursementStatistics = async () => {
      try {
        const response = await api.reimbursements.getStatistics();
        if (response.code === 200) {
          const data = response.data;
          totalCount.value = data.total_count || 0;
          pendingCount.value = data.pending_count || 0;
          settledCount.value = data.settled_count || 0;
          totalAmount.value = `¥${(data.total_amount || 0).toLocaleString()}`;
          departmentCount.value = data.department_count || 0;
        }
      } catch (error) {
        console.error("获取结算报销统计失败:", error);
        // 如果API失败，从当前数据计算（作为备选方案）
        calculateStatsFromCurrentData();
      }
    };

    // 从当前数据计算统计（备选方案）
    const calculateStatsFromCurrentData = () => {
      totalCount.value = pagination.total;
      pendingCount.value = reimbursements.value.filter(
        (item) =>
          (item._originalStatus || item.status) === "pending_reimbursement"
      ).length;
      settledCount.value = reimbursements.value.filter(
        (item) => item.status === "settled"
      ).length;

      const total = reimbursements.value.reduce(
        (sum, item) => sum + parseFloat(item.amount || 0),
        0
      );
      totalAmount.value = `¥${total.toLocaleString()}`;

      const deptSet = new Set(
        reimbursements.value.map((item) => {
          let hierarchyPath = item.hierarchy_path || "未知单位";
          hierarchyPath = hierarchyPath.trim();
          if (!hierarchyPath || hierarchyPath === "") {
            return "未知单位";
          }
          let firstLevelDept =
            hierarchyPath.split("-")[0]?.trim() || "未知单位";
          if (!firstLevelDept || firstLevelDept === "") {
            firstLevelDept = "未知单位";
          }
          return firstLevelDept;
        })
      );
      departmentCount.value = deptSet.size;
    };

    // 行选择配置 - 只允许选择待结算状态的记录进行批量结算
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (selectedKeys) => {
        selectedRowKeys.value = selectedKeys;
      },
      getCheckboxProps: (record) => ({
        disabled:
          (record._originalStatus || record.status) !== "pending_reimbursement",
        name: record.item_name,
      }),
    };

    // 计算选中记录中是否有待结算状态的记录
    const hasPendingReimbursements = computed(() => {
      return selectedRowKeys.value.some((id) => {
        const record = reimbursements.value.find((r) => r.id === id);
        return (
          record &&
          (record._originalStatus || record.status) === "pending_reimbursement"
        );
      });
    });

    // 计算待结算状态的选中数量
    const pendingReimbursementSelectedCount = computed(() => {
      return selectedRowKeys.value.filter((id) => {
        const record = reimbursements.value.find((r) => r.id === id);
        return (
          record &&
          (record._originalStatus || record.status) === "pending_reimbursement"
        );
      }).length;
    });

    // 所有可用的表格列定义
    const allColumns = [
      {
        title: "物品名称",
        dataIndex: "item_name",
        key: "item_name",
        width: 150,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "规格型号",
        dataIndex: "specification",
        key: "specification",
        width: 180,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "需求单位",
        dataIndex: "hierarchy_path",
        key: "hierarchy_path",
        width: 160,
        sorter: true,
        align: "center",
        ellipsis: true,
        customRender: ({ text }) => {
          return text || "未知单位";
        },
      },
      {
        title: "申请人",
        dataIndex: "requester_name",
        key: "requester_name",
        width: 100,
        sorter: true,
        align: "center",
      },
      // 基础信息补充
      {
        title: "需求数量",
        dataIndex: "quantity",
        key: "quantity",
        width: 80,
        sorter: true,
        align: "center",
      },
      {
        title: "预算单价",
        dataIndex: "unit_price",
        key: "unit_price",
        width: 100,
        sorter: true,
        align: "center",
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "预算金额",
        dataIndex: "total_amount",
        key: "total_amount",
        width: 120,
        sorter: true,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "采购类型",
        dataIndex: "purchase_type",
        key: "purchase_type",
        width: 120,
        sorter: true,
        customRender: ({ record }) => {
          return record.purchase_type_display || record.purchase_type;
        },
      },
      {
        title: "创建时间",
        dataIndex: "created_at",
        key: "created_at",
        width: 150,
        sorter: true,
        customRender: ({ text }) => {
          return text ? formatDateToYMD(text) : "-";
        },
      },
      {
        title: "备注",
        dataIndex: "remarks",
        key: "remarks",
        width: 200,
      },
      // 审批相关
      {
        title: "提交时间",
        dataIndex: "submission_date",
        key: "submission_date",
        width: 150,
        sorter: true,
        customRender: ({ text }) => {
          return text ? formatDateToYMD(text) : "-";
        },
      },
      {
        title: "审批人",
        dataIndex: "approver_name",
        key: "approver_name",
        width: 120,
      },
      {
        title: "审批时间",
        dataIndex: "approved_at",
        key: "approved_at",
        width: 150,
        sorter: true,
        customRender: ({ text }) => {
          return text ? formatDateToYMD(text) : "-";
        },
      },
      // 采购相关
      {
        title: "采购数量",
        dataIndex: "actual_quantity",
        key: "actual_quantity",
        width: 100,
        sorter: true,
      },
      {
        title: "采购单价",
        dataIndex: "actual_unit_price",
        key: "actual_unit_price",
        width: 120,
        sorter: true,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "采购金额",
        dataIndex: "actual_total_amount",
        key: "actual_total_amount",
        width: 120,
        sorter: true,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "供应商",
        dataIndex: "supplier",
        key: "supplier",
        width: 150,
      },
      {
        title: "采购员",
        dataIndex: "purchaser_name",
        key: "purchaser_name",
        width: 120,
      },
      {
        title: "采购时间",
        dataIndex: "purchase_date",
        key: "purchase_date",
        width: 150,
        customRender: ({ text }) => {
          return text ? formatDateToYMD(text) : "-";
        },
      },
      // 验收相关
      {
        title: "验收数量",
        dataIndex: "acceptance_quantity",
        key: "acceptance_quantity",
        width: 100,
      },
      {
        title: "验收员",
        dataIndex: "acceptor_name",
        key: "acceptor_name",
        width: 120,
      },
      {
        title: "验收时间",
        dataIndex: "acceptance_date",
        key: "acceptance_date",
        width: 150,
        customRender: ({ text }) => {
          return text ? formatDateToYMD(text) : "-";
        },
      },
      // 结算相关
      {
        title: "结算金额",
        dataIndex: "settlement_amount",
        key: "settlement_amount",
        width: 120,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "交易流水号",
        dataIndex: "transaction_number",
        key: "transaction_number",
        width: 150,
      },
      {
        title: "收款人",
        dataIndex: "payee_name",
        key: "payee_name",
        width: 120,
      },
      {
        title: "报销人",
        dataIndex: "reimburser_name",
        key: "reimburser_name",
        width: 120,
        customRender: ({ text }) => text || "-",
      },
      {
        title: "财务审核人",
        dataIndex: "finance_reviewer_name",
        key: "finance_reviewer_name",
        width: 120,
      },
      {
        title: "结算时间",
        dataIndex: "reimbursement_date",
        key: "reimbursement_date",
        width: 150,
        customRender: ({ text }) => {
          return text ? formatDateToYMD(text) : "-";
        },
      },
      {
        title: "结算备注",
        dataIndex: "settlement_remark",
        key: "settlement_remark",
        width: 200,
      },
      {
        title: "状态",
        dataIndex: "status",
        key: "status",
        width: 100,
        align: "center",
        customRender: ({ record }) => {
          return record.status_display || record.status;
        },
      },
      {
        title: "操作",
        dataIndex: "action",
        key: "action",
        width: 150,
        fixed: "right",
        align: "center",
      },
    ];

    // 为所有列添加居中对齐（如果还没有的话）
    allColumns.forEach((column) => {
      if (!column.align) {
        column.align = "center";
      }
    });

    // 动态筛选后的列
    const filteredColumns = computed(() => {
      // 构建选中的列
      const selectedCols = selectedColumns.value
        .map((columnKey) => {
          // 从 allColumns 中找到对应的列定义
          const columnDef = allColumns.find((col) => col.key === columnKey);
          if (columnDef) {
            return columnDef;
          }
          // 如果在 allColumns 中找不到，则创建一个基本的列定义
          const option = columnOptions.find((opt) => opt.key === columnKey);
          if (option) {
            return {
              title: option.title,
              dataIndex: columnKey,
              key: columnKey,
              width: 120,
              ellipsis: true,
              align: "center", // 所有列居中显示
            };
          }
          return null;
        })
        .filter(Boolean); // 过滤掉 null 值

      // 移除可能存在的操作列
      const nonActionCols = selectedCols.filter((col) => col.key !== "action");

      // 始终添加操作列到最右侧
      const actionColumn = allColumns.find((col) => col.key === "action");
      if (actionColumn) {
        return [...nonActionCols, actionColumn];
      }

      return selectedCols;
    });

    // 获取部门列表 - 用于筛选选项，使用专门的无分页接口
    const getDepartments = async () => {
      try {
        const response = await api.departments.getAll();
        if (response.code === 200) {
          departments.value = response.data || [];
        }
      } catch (error) {
        console.error("获取部门列表失败:", error);
      }
    };

    // 构建查询参数的辅助函数
    const buildQueryParams = () => {
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        // 启用结算报销导出模式，后端会自动筛选状态
        reimbursement_export: "true",
      };

      // 如果用户选择了特定状态，则添加状态筛选
      if (filters.status) {
        params.status_in = filters.status;
      }

      // 优化筛选参数构建
      const filterMappings = {
        itemName: "search",
        requester: "requester__username__icontains",
        reimburser: "reimburser__username__icontains",
        purchaseType: "purchase_type",
      };

      // 批量处理简单筛选参数
      Object.entries(filterMappings).forEach(([key, mapping]) => {
        if (filters[key]) {
          params[mapping] = filters[key];
        }
      });

      // 处理特殊筛选参数
      if (filters.id) {
        const idNum = parseInt(filters.id);
        if (!isNaN(idNum)) params.id = idNum;
      }

      if (filters.minSettlementAmount) {
        const minAmount = parseFloat(filters.minSettlementAmount);
        if (!isNaN(minAmount)) params.settlement_amount__gte = minAmount;
      }

      if (filters.maxSettlementAmount) {
        const maxAmount = parseFloat(filters.maxSettlementAmount);
        if (!isNaN(maxAmount)) params.settlement_amount__lte = maxAmount;
      }

      // 处理部门筛选
      if (filters.department) {
        const selectedDept = departments.value.find(
          (d) => d.id == filters.department
        );
        if (selectedDept) {
          params.hierarchy_path__icontains = selectedDept.parent_id
            ? selectedDept.hierarchy_path
            : selectedDept.dept_name;
        }
      }

      // 处理排序
      if (sortField.value && sortOrder.value) {
        params.ordering = `${sortOrder.value === "descend" ? "-" : ""}${sortField.value
          }`;
      }

      return params;
    };

    // 获取结算报销列表 - 优化版本
    const getReimbursements = async () => {
      // 防止重复请求
      if (loading.value) return;

      loading.value = true;
      try {
        const params = buildQueryParams();
        // 添加额外的筛选参数
        const additionalFilters = {
          procurementMethod: "procurement_method__icontains",
          requirementSource: "requirement_source__icontains",
          fundProject: "fund_project__icontains",
          unit: "unit__icontains",
          itemCategory: "item_category__icontains",
          remarks: "remarks__icontains",
        };

        Object.entries(additionalFilters).forEach(([key, mapping]) => {
          if (filters[key]) {
            params[mapping] = filters[key];
          }
        });

        // 处理日期范围
        if (filters.dateRange?.length === 2) {
          params.created_at__gte = filters.dateRange[0].format("YYYY-MM-DD");
          params.created_at__lte = filters.dateRange[1].format("YYYY-MM-DD");
        }

        // 恢复使用原来稳定的API
        const response = await api.purchaseRequests.getList(params);

        if (response.code === 200) {
          const rawReimbursements = response.data.results || [];
          // 使用后端提供的display字段，同时保留原始编码用于逻辑判断
          const processedReimbursements = rawReimbursements.map(
            (reimbursement) => ({
              ...reimbursement,
              // 使用后端提供的display字段进行显示
              status: reimbursement.status_display || reimbursement.status,
              item_category:
                reimbursement.item_category_display ||
                reimbursement.item_category,
              unit: reimbursement.unit_display || reimbursement.unit,
              procurement_method:
                reimbursement.procurement_method_display ||
                reimbursement.procurement_method,
              fund_project:
                reimbursement.fund_project_display ||
                reimbursement.fund_project,
              purchase_type:
                reimbursement.purchase_type_display ||
                reimbursement.purchase_type,
              // 保存原始编码用于逻辑判断
              _originalStatus: reimbursement.status,
              _originalItemCategory: reimbursement.item_category,
              _originalUnit: reimbursement.unit,
              _originalProcurementMethod: reimbursement.procurement_method,
              _originalFundProject: reimbursement.fund_project,
              _originalPurchaseType: reimbursement.purchase_type,
            })
          );
          reimbursements.value = processedReimbursements;
          pagination.total = response.data.count || 0;
        } else {
          throw new Error(`API返回错误: ${response.message}`);
        }
      } catch (error) {
        console.error("获取结算报销列表失败:", error);
        message.error(error.message || "获取数据失败，请稍后重试");
      } finally {
        loading.value = false;
      }
    };

    // 筛选变化处理
    const handleFilterChange = () => {
      pagination.current = 1;
      getReimbursements();
    };

    // 防抖筛选变化处理
    const debouncedFilterChange = debounce(handleFilterChange, 500);

    // 重置筛选
    const resetFilters = () => {
      Object.assign(filters, {
        status: "",
        id: "",
        itemName: "",
        specification: "",
        department: "",
        reimburser: "",
        financeReviewer: "",
        purchaseType: "",
        reimbursementTimeRange: [],
        minPurchaseAmount: "",
        maxPurchaseAmount: "",
        minSettlementAmount: "",
        maxSettlementAmount: "",
        // 重置新增的筛选字段
        procurementMethod: "",
        requirementSource: "",
        fundProject: "",
        unit: "",
        itemCategory: "",
        remarks: "",
      });
      handleFilterChange();
    };

    // 获取筛选选项数据
    const getFilterOptions = async () => {
      try {
        // 获取用户列表（报销人、财务审核人）
        const usersResponse = await api.users.getList({ page_size: 100 });
        if (usersResponse.code === 200) {
          const allUsers = usersResponse.data.results || usersResponse.data;
          users.value = allUsers; // 设置users变量供导出功能使用
          reimbursers.value = allUsers.filter(
            (user) => user.role_name && user.role_name.includes("报销")
          );
          financeReviewers.value = allUsers.filter(
            (user) => user.role_name && user.role_name.includes("财务")
          );
        }
      } catch (error) {
        console.error("获取筛选选项失败:", error);
      }
    };

    // 筛选选项过滤方法
    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    // 展开/收起筛选控件
    const toggleFilters = () => {
      showFilters.value = !showFilters.value;
    };

    // 字段筛选方法
    const onColumnChange = (checkedValues) => {
      // 确保必选字段始终被选中
      const requiredColumns = columnOptions
        .filter((opt) => opt.required)
        .map((opt) => opt.key);
      const newSelectedColumns = [
        ...new Set([...checkedValues, ...requiredColumns]),
      ];
      selectedColumns.value = newSelectedColumns;
      saveColumnsToStorage(newSelectedColumns);
    };

    const selectAllColumns = () => {
      const sortedOptions = sortColumnsByOrder(columnOptions);
      const allColumns = sortedOptions.map((opt) => opt.key);
      selectedColumns.value = allColumns;
      saveColumnsToStorage(allColumns);
    };

    const resetColumns = () => {
      // 重置到系统默认配置，排除操作列
      const defaultColumns = getPageDefaultColumns("settlement").filter(
        (col) => col !== "action"
      );
      selectedColumns.value = defaultColumns;
      saveColumnsToStorage(defaultColumns);
    };

    // 应用预设
    const applyPreset = ({ key }) => {
      const presetColumns = columnPresets[key];
      if (presetColumns) {
        // 确保必选字段始终被包含
        const requiredColumns = columnOptions
          .filter((opt) => opt.required)
          .map((opt) => opt.key);

        // 合并预设字段和必选字段
        const combinedColumns = [
          ...new Set([...presetColumns, ...requiredColumns]),
        ];

        // 按照标准顺序排列字段
        const sortedColumns = sortColumnsByOrder(
          combinedColumns,
          columnOptions
        );

        selectedColumns.value = sortedColumns;
        saveColumnsToStorage(sortedColumns);
      }
    };

    // 处理字段变更（阻止面板关闭）
    const handleColumnChange = (checkedValues) => {
      onColumnChange(checkedValues);
    };

    // 处理预设点击（阻止面板关闭）
    const handlePresetClick = ({ key }) => {
      applyPreset({ key });
      // 保持dropdown打开状态
      presetDropdownOpen.value = true;
    };

    // 处理全选按钮点击
    const handleSelectAll = (e) => {
      e.stopPropagation();
      selectAllColumns();
    };

    // 处理重置按钮点击
    const handleReset = (e) => {
      e.stopPropagation();
      resetColumns();
    };

    // 排序状态
    const sortField = ref("");
    const sortOrder = ref("");

    // 表格变化处理 - 支持分页和排序
    const handleTableChange = (pag, _filters, sorter) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;

      // 处理排序
      if (sorter && sorter.field) {
        sortField.value = sorter.field;
        sortOrder.value = sorter.order;
      } else {
        sortField.value = "";
        sortOrder.value = "";
      }

      getReimbursements();
    };

    // 查看详情
    const viewDetail = (record) => {
      router.push(`/purchase/requests/${record.id}`);
    };

    // 批量结算
    const batchSettle = async () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning("请选择要结算的记录");
        return;
      }

      const pendingReimbursements = reimbursements.value.filter(
        (item) =>
          selectedRowKeys.value.includes(item.id) &&
          (item._originalStatus || item.status) === "pending_reimbursement"
      );

      if (pendingReimbursements.length === 0) {
        message.warning("所选项目中没有待结算的记录");
        return;
      }

      try {
        const promises = pendingReimbursements.map((record) =>
          api.purchaseRequests.reimburse(record.id)
        );

        await Promise.all(promises);
        message.success(`成功结算 ${pendingReimbursements.length} 条记录`);
        selectedRowKeys.value = [];
        getReimbursements();
        getReimbursementStatistics(); // 刷新统计数据
      } catch (error) {
        message.error("批量结算失败");
        console.error(error);
      }
    };

    // 打开结算信息模态框
    const openSettlementModal = async (record) => {
      try {
        // 获取详细信息
        const response = await api.purchaseRequests.getDetail(record.id);
        if (response.code === 200) {
          const data = response.data;

          // 填充表单数据
          Object.assign(settlementForm, {
            id: data.id,
            item_name: data.item_name,
            specification: data.specification || "",
            hierarchy_path: data.hierarchy_path || "",
            requester_name: data.requester_name || "",
            actual_quantity: data.actual_quantity || data.quantity,
            unit_price: data.unit_price || data.budget_unit_price,
            actual_unit_price:
              data.actual_unit_price ||
              data.purchase_unit_price ||
              data.unit_price,
            total_amount: data.total_amount || data.budget_total_amount,
            purchase_total_amount:
              data.purchase_total_amount || data.actual_total_amount,
            settlement_amount:
              data.settlement_amount ||
              data.purchase_total_amount ||
              data.total_amount,
            transaction_number: data.transaction_number || "",
            payee_account: data.payee_account || "",
            payee_name: data.payee_name || "",
            settlement_remarks: data.settlement_remarks || "",
          });

          showSettlementModal.value = true;
        }
      } catch (error) {
        console.error("获取结算详情失败:", error);
        message.error("获取结算详情失败");
      }
    };

    // 保存结算信息（不改变状态）
    const saveSettlementInfo = async () => {
      try {
        await settlementFormRef.value.validate();

        settlementLoading.value = true;
        const params = {
          settlement_amount: settlementForm.settlement_amount,
          transaction_number: settlementForm.transaction_number,
          payee_account: settlementForm.payee_account,
          payee_name: settlementForm.payee_name,
          settlement_remarks: settlementForm.settlement_remarks,
        };

        const response = await api.purchaseRequests.updateSettlementInfo(
          settlementForm.id,
          params
        );
        if (response.code === 200) {
          message.success("结算信息保存成功");

          // 刷新数据
          getReimbursements();
        }
      } catch (error) {
        console.error("保存结算信息失败:", error);
        message.error("保存结算信息失败");
      } finally {
        settlementLoading.value = false;
      }
    };

    // 提交结算信息（改变状态为已结算）
    const submitSettlementInfo = async () => {
      try {
        await settlementFormRef.value.validate();

        // 如果有异常但没有填写异常原因，提示用户
        if (
          hasAmountException.value &&
          !settlementForm.settlement_exception_reason.trim()
        ) {
          message.warning("检测到金额异常，请填写异常原因说明");
          return;
        }

        settlementLoading.value = true;
        const params = {
          settlement_amount: settlementForm.settlement_amount,
          transaction_number: settlementForm.transaction_number,
          payee_account: settlementForm.payee_account,
          payee_name: settlementForm.payee_name,
          settlement_remarks: settlementForm.settlement_remarks,
          status: "settled",
        };

        const response = await api.purchaseRequests.completeSettlement(
          settlementForm.id,
          params
        );
        if (response.code === 200) {
          message.success("结算完成");

          showSettlementModal.value = false;
          getReimbursements();
          getReimbursementStatistics(); // 刷新统计数据
        }
      } catch (error) {
        console.error("提交结算信息失败:", error);
        message.error("提交结算信息失败");
      } finally {
        settlementLoading.value = false;
      }
    };

    // 结算报销
    const settleReimbursement = async (record) => {
      try {
        const response = await api.purchaseRequests.reimburse(record.id);

        if (response.code === 200) {
          message.success("结算成功");
          getReimbursements();
          getReimbursementStatistics(); // 刷新统计数据
        }
      } catch (error) {
        console.error("结算失败:", error);
        message.error("结算失败");
      }
    };

    // 注意：状态颜色、状态文本和日期格式化函数已通过字典混入提供

    // 导出字段选择相关计算属性
    const exportFieldsIndeterminate = computed(() => {
      return (
        selectedExportFields.value.length > 0 &&
        selectedExportFields.value.length < exportFieldOptions.length
      );
    });

    const exportFieldsCheckAll = computed(() => {
      return selectedExportFields.value.length === exportFieldOptions.length;
    });

    // 导出记录表格列 - 根据选中字段动态生成，添加居中对齐和字典转换
    const exportRecordColumns = computed(() => {
      // 基础列（始终显示）
      const baseColumns = [
        {
          title: "ID",
          dataIndex: "id",
          width: 80,
          fixed: "left",
          align: "center",
        },
      ];

      // 根据选中字段生成动态列
      const dynamicColumns = selectedExportFields.value
        .map((fieldKey) => {
          const field = exportFieldOptions.find((f) => f.value === fieldKey);

          if (fieldKey === "id") return null; // ID列已在基础列中

          const column = {
            title: field ? field.label : fieldKey,
            dataIndex: fieldKey,
            width: getExportColumnWidth(fieldKey),
            ellipsis: true,
            align: "center",
          };

          // 添加自定义渲染 - 使用字典转换
          if (fieldKey === "status") {
            column.customRender = ({ record }) => {
              return record.status_display || record.status;
            };
          } else if (fieldKey === "purchase_type") {
            column.customRender = ({ record }) => {
              return record.purchase_type_display || record.purchase_type;
            };
          } else if (fieldKey === "procurement_method") {
            column.customRender = ({ record }) => {
              return (
                record.procurement_method_display || record.procurement_method
              );
            };
          } else if (fieldKey === "unit") {
            column.customRender = ({ record }) => {
              return record.unit_display || record.unit;
            };
          } else if (fieldKey === "item_category") {
            column.customRender = ({ record }) => {
              return record.item_category_display || record.item_category;
            };
          } else if (fieldKey === "requirement_source") {
            column.customRender = ({ record }) => {
              return record.requirement_source || "-";
            };
          } else if (fieldKey === "fund_project_name") {
            column.customRender = ({ record }) => {
              return record.fund_project_display || record.fund_project;
            };
          } else if (fieldKey.includes("_at") || fieldKey.includes("_date")) {
            // 时间字段格式化为年/月/日
            column.customRender = ({ text }) => {
              return formatDateToYMD(text);
            };
          }

          return column;
        })
        .filter(Boolean);

      return [...baseColumns, ...dynamicColumns];
    });

    // 获取导出列宽度
    const getExportColumnWidth = (fieldKey) => {
      const widthMap = {
        request_number: 120,
        item_name: 150,
        specification: 120,
        quantity: 80,
        settlement_amount: 100,
        status: 100,
        dept_name: 120,
        requester_name: 100,
        created_at: 120,
        purchase_type: 100,
        procurement_method: 120,
        requirement_source: 120,
        fund_project: 120,
        unit: 80,
        item_category: 100,
        reimbursement_voucher_no: 120,
        reimbursement_date: 120,
        reimbursement_person_name: 100,
        transaction_number: 150,
        payee_name: 100,
        payee_account: 150,
        settlement_remarks: 200,
      };
      return widthMap[fieldKey] || 100;
    };

    // 导出记录行选择 - 统一逻辑
    const exportRecordRowSelection = computed(() => ({
      selectedRowKeys: selectedExportRecords.value,
      onChange: (selectedRowKeys, selectedRows) => {
        // 直接更新选中状态，不支持跨页
        selectedExportRecords.value = selectedRowKeys;
        selectedExportRecordData.value = selectedRows;
      },
      onSelectAll: (selected, _selectedRows, changeRows) => {
        if (selected) {
          // 全选当前页
          selectedExportRecords.value = changeRows.map((row) => row.id);
          selectedExportRecordData.value = changeRows;
        } else {
          // 取消全选
          selectedExportRecords.value = [];
          selectedExportRecordData.value = [];
        }
      },
    }));

    // 预览表格列 - 参考需求提报页面的实现方式
    const previewColumns = computed(() => {
      let columns = [];

      // 如果选中字段包含ID，先添加ID列并固定在左侧
      if (selectedExportFields.value.includes("id")) {
        columns.push({
          title: "ID",
          dataIndex: "id",
          key: "id",
          width: 80,
          ellipsis: true,
          align: "center",
          fixed: "left",
        });
      }

      // 添加其他列（除了ID）
      const otherColumns = selectedExportFields.value
        .filter((field) => field !== "id")
        .map((field) => {
          const option = exportFieldOptions.find((opt) => opt.value === field);
          return {
            title: option?.label || field,
            dataIndex: field,
            key: field,
            width: getExportColumnWidth(field),
            ellipsis: true,
            align: "center",
          };
        });

      columns = columns.concat(otherColumns);
      return columns;
    });

    // 导出字段全选处理
    const onExportFieldsCheckAllChange = (e) => {
      if (e.target.checked) {
        selectedExportFields.value = exportFieldOptions.map(
          (option) => option.value
        );
      } else {
        selectedExportFields.value = [];
      }
    };

    // 导出字段选择变化
    const onExportFieldsChange = (checkedList) => {
      selectedExportFields.value = checkedList;
    };

    // 导出记录分页相关
    const onExportRecordPageSizeChange = (value) => {
      exportRecordPageSize.value = value;
      searchExportRecords();
    };

    const onExportRecordPageChange = (page) => {
      exportRecordPagination.value.current = page;
      searchExportRecords();
    };

    // 重置导出筛选条件
    const resetExportFilters = () => {
      Object.assign(exportFilters, {
        status: [],
        department: [],
        reimburser: [],
        dateRange: [],
      });
    };

    // 搜索导出记录
    const searchExportRecords = async () => {
      exportSearchLoading.value = true;
      try {
        const params = {
          page: exportRecordPagination.value.current,
          page_size: exportRecordPageSize.value,
          // 启用结算报销导出模式，后端会自动筛选状态
          reimbursement_export: "true",
        };

        // 添加筛选条件（如果用户选择了特定状态，则使用用户选择的状态）
        // 注意：由于已启用reimbursement_export模式，后端会自动筛选为pending_reimbursement和settled状态
        // 这里的状态筛选是在后端已筛选的基础上进一步细化
        if (exportFilters.status && exportFilters.status.length > 0) {
          // 确保用户选择的状态在允许的范围内
          const allowedStatuses = ["pending_reimbursement", "settled"];
          const filteredStatuses = exportFilters.status.filter((status) =>
            allowedStatuses.includes(status)
          );
          if (filteredStatuses.length > 0) {
            params.status__in = filteredStatuses.join(",");
          }
        }
        if (exportFilters.department) {
          params.dept_id = exportFilters.department;
        }
        if (exportFilters.reimburser) {
          params.reimburser_id = exportFilters.reimburser;
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.created_at__gte =
            exportFilters.dateRange[0].format("YYYY-MM-DD");
          params.created_at__lte =
            exportFilters.dateRange[1].format("YYYY-MM-DD");
        }

        const response = await api.purchaseRequests.getList(params);
        if (response.code === 200) {
          const rawRecords = response.data.results || response.data || [];
          // 使用后端提供的display字段进行显示
          const processedRecords = rawRecords.map((record) => ({
            ...record,
            status: record.status_display || record.status,
            item_category: record.item_category_display || record.item_category,
            unit: record.unit_display || record.unit,
            procurement_method:
              record.procurement_method_display || record.procurement_method,
            fund_project: record.fund_project_display || record.fund_project,
            purchase_type: record.purchase_type_display || record.purchase_type,
          }));
          exportRecords.value = processedRecords;
          exportRecordPagination.value.total =
            response.data.count || exportRecords.value.length;
        }
      } catch (error) {
        console.error("搜索导出记录失败:", error);
        message.error("搜索导出记录失败");
      } finally {
        exportSearchLoading.value = false;
      }
    };

    // 全选导出字段
    const selectAllExportFields = () => {
      selectedExportFields.value = exportFieldOptions.map(
        (field) => field.value
      );
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    };

    // 重置导出字段
    const resetExportFields = () => {
      selectedExportFields.value = initializeSelectedFields();
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    };

    // 仅选择必选字段
    const selectRequiredExportFields = () => {
      selectedExportFields.value = exportFieldOptions
        .filter((field) => field.required)
        .map((field) => field.value);
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    };

    // 预览导出数据
    const previewExportData = () => {
      if (selectedExportRecords.value.length === 0) {
        message.warning("请先选择要预览的记录");
        return;
      }
      if (selectedExportFields.value.length === 0) {
        message.warning("请至少选择一个导出字段");
        return;
      }

      showPreviewModal.value = true;
      loadPreviewData();
    };

    // 重置导出配置
    const resetExportConfig = () => {
      selectedExportFields.value = initializeSelectedFields();
      Object.assign(exportFilters, {
        status: [],
        department: [],
        itemName: "",
        requester: "",
        reimburser: "",
        minAmount: "",
        maxAmount: "",
        dateRange: [],
        // 重置新增的导出筛选字段
        procurementMethod: "",
        requirementSource: "",
        fundProject: "",
        unit: "",
        itemCategory: "",
        remarks: "",
      });
      exportActiveTab.value = "fields";
      previewData.value = [];
      previewTotal.value = 0;
    };

    // 加载预览数据 - 参考需求提报页面的实现方式
    const loadPreviewData = async () => {
      if (selectedExportFields.value.length === 0) {
        message.warning("请至少选择一个导出字段");
        return;
      }

      if (selectedExportRecords.value.length === 0) {
        message.warning("请至少选择一条记录进行预览");
        return;
      }

      previewLoading.value = true;
      try {
        // 使用选中的记录数据进行预览，不进行字典转换（在模板中处理）
        const selectedData = exportRecords.value.filter((record) =>
          selectedExportRecords.value.includes(record.id)
        );

        // 设置预览数据为选中的完整记录，只显示前10条
        previewData.value = selectedData.slice(0, 10);
        previewTotal.value = selectedData.length;
      } catch (error) {
        console.error("加载预览数据失败:", error);
        message.error("加载预览数据失败");
      } finally {
        previewLoading.value = false;
      }
    };

    // 处理导出取消操作
    const handleExportCancel = () => {
      showExportModal.value = false;
      // 重置选择
      selectedExportRecords.value = [];
      selectedExportRecordData.value = [];
      exportRecords.value = [];
    };

    // 执行导出
    const executeExport = async () => {
      if (selectedExportRecords.value.length === 0) {
        message.warning("请先选择要导出的记录");
        return;
      }
      if (selectedExportFields.value.length === 0) {
        message.warning("请至少选择一个导出字段");
        return;
      }

      exportLoading.value = true;
      try {
        const params = {
          fields: selectedExportFields.value.join(","),
          ids: selectedExportRecords.value.join(","),
          // 启用结算报销导出模式，后端会自动筛选状态
          reimbursement_export: "true",
        };

        // 应用导出筛选条件（如果用户选择了特定状态，则使用用户选择的状态）
        // 注意：由于已启用reimbursement_export模式，后端会自动筛选为pending_reimbursement和settled状态
        // 这里的状态筛选是在后端已筛选的基础上进一步细化
        if (exportFilters.status.length > 0) {
          // 确保用户选择的状态在允许的范围内
          const allowedStatuses = ["pending_reimbursement", "settled"];
          const filteredStatuses = exportFilters.status.filter((status) =>
            allowedStatuses.includes(status)
          );
          if (filteredStatuses.length > 0) {
            params.status_in = filteredStatuses.join(",");
          }
        }
        if (exportFilters.department.length > 0) {
          params.department_in = exportFilters.department.join(",");
        }
        if (exportFilters.itemName) {
          params.search = exportFilters.itemName;
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester;
        }
        if (exportFilters.reimburser) {
          params.reimburser__username__icontains = exportFilters.reimburser;
        }
        if (exportFilters.minAmount) {
          params.settlement_amount__gte = parseFloat(exportFilters.minAmount);
        }
        if (exportFilters.maxAmount) {
          params.settlement_amount__lte = parseFloat(exportFilters.maxAmount);
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.reimbursement_date__gte =
            exportFilters.dateRange[0].format("YYYY-MM-DD");
          params.reimbursement_date__lte =
            exportFilters.dateRange[1].format("YYYY-MM-DD");
        }
        // 新增导出筛选参数
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains =
            exportFilters.procurementMethod;
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains =
            exportFilters.requirementSource;
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject;
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit;
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory;
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks;
        }

        // 使用结算报销专用的导出接口，确保状态筛选正确
        const response = await api.purchaseRequests.exportToExcel({
          ...params,
          export_config_mode: "false", // 不使用导出配置模式
          reimbursement_export: "true", // 标识为结算报销导出
        });

        // 检查响应类型并创建下载链接
        let blob;
        if (response instanceof Blob) {
          blob = response;
        } else if (response.data instanceof Blob) {
          blob = response.data;
        } else {
          // 如果响应不是Blob，尝试创建Blob
          blob = new Blob([response], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
        }

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `结算报销清单_${formatDateToYMD(new Date())}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        message.success(
          `导出成功，共导出 ${selectedExportRecords.value.length} 条记录`
        );
        handleExportCancel();
      } catch (error) {
        console.error("导出失败:", error);
        message.error("导出失败");
      } finally {
        exportLoading.value = false;
      }
    };

    // 监听导出模态框打开
    watch(
      () => showExportModal.value,
      (newVal) => {
        if (newVal) {
          searchExportRecords();
        }
      }
    );

    // 初始化加载数据
    onMounted(async () => {
      await getDepartments();
      await getFilterOptions();
      await getReimbursements();
      await getReimbursementStatistics(); // 获取统计数据

      // 加载字典数据并更新筛选选项
      try {
        const purchaseTypeOptions = await getDictOptions("purchase_type");
        purchaseTypes.value = purchaseTypeOptions;
      } catch (error) {
        console.error("加载采购类型字典失败:", error);
      }
    });

    return {
      loading,
      reimbursements,
      departments,
      selectedRowKeys,
      users,
      purchaseTypes,
      reimbursers,
      financeReviewers,
      showFilters,
      presetDropdownOpen,
      showExportModal,
      showColumnFilter,
      showPreviewModal,

      filters,
      columnOptions,
      selectedColumns,
      fieldCategories,
      presetMenuItems,
      pagination,
      rowSelection,

      filteredColumns,
      totalCount,
      pendingCount,
      settledCount,
      totalAmount,
      hasPendingReimbursements,
      pendingReimbursementSelectedCount,
      departmentCount,
      getDepartments,
      getFilterOptions,
      filterOption,
      toggleFilters,
      onColumnChange,
      handleColumnChange,
      handlePresetClick,
      handleSelectAll,
      handleReset,
      selectAllColumns,
      resetColumns,
      applyPreset,
      getReimbursements,
      getReimbursementStatistics,
      calculateStatsFromCurrentData,
      handleFilterChange,
      debouncedFilterChange,
      resetFilters,
      handleTableChange,
      sortField,
      sortOrder,
      viewDetail,
      batchSettle,
      settleReimbursement,
      getStatusColor,
      formatDateToYMD,
      // 导出相关
      exportActiveTab,
      exportLoading,
      previewLoading,
      previewData,
      previewTotal,
      selectedExportFields,
      exportFieldOptions,
      exportFieldCategories,
      exportFilters,
      settlementStatusOptions,
      exportFieldsIndeterminate,
      exportFieldsCheckAll,
      previewColumns,
      onExportFieldsCheckAllChange,
      onExportFieldsChange,
      resetExportConfig,
      loadPreviewData,
      executeExport,
      resetExportFilters,
      searchExportRecords,
      selectAllExportFields,
      resetExportFields,
      selectRequiredExportFields,
      previewExportData,
      fieldsCollapseKey,
      // 导出记录选择相关
      exportRecords,
      selectedExportRecords,
      selectedExportRecordData,
      exportSearchLoading,
      exportRecordPageSize,
      exportRecordPagination,
      exportRecordColumns,
      exportRecordRowSelection,
      // 导出记录操作函数
      onExportRecordPageSizeChange,
      onExportRecordPageChange,
      handleExportCancel,
      // 结算信息填报相关
      showSettlementModal,
      settlementLoading,
      settlementFormRef,
      settlementForm,
      settlementFormRules,
      amountDifference,
      amountDifferenceRate,
      hasAmountException,
      openSettlementModal,
      saveSettlementInfo,
      submitSettlementInfo,
    };
  },
};
</script>

<style scoped>
@import "@/styles/business-panels.css";

.reimbursement-list {
  padding: 0;
  background: transparent;
}

/* 预览模态框样式 */
:deep(.preview-modal-wrapper .ant-modal) {
  height: 80vh;
  top: 10vh;
  margin: 0;
}

:deep(.preview-modal-wrapper .ant-modal-content) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.preview-modal-wrapper .ant-modal-body) {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.preview-table-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 表格头部flex布局 - 与采购总览页面保持一致 */
.table-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-light);
  gap: 24px;
}

.table-title-section {
  flex-shrink: 0;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: 13px;
  font-weight: 400;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .anticon {
  font-size: var(--text-2xl);
  color: rgba(255, 255, 255, 0.9);
}

.page-subtitle {
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 24px;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-inverse);
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 报销表格区域样式 */
.reimbursement-tables {
  margin-top: var(--space-xl);
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 结算表单样式 */
.settlement-form {
  padding: 20px;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #1890ff;
  font-weight: 600;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.form-hint {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.form-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 表格标题样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
}

.table-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
  justify-content: flex-start;
}

.table-title .anticon {
  color: var(--primary-color);
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.table-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
  word-break: break-word;
}

/* 统一筛选区域样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg,
      var(--bg-secondary) 0%,
      var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .amount-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .amount-input .ant-input-number,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .amount-input .ant-input-number:focus-within,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .amount-input .ant-input-number:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

.filter-result-text {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  margin-left: var(--space-lg);
}

/* 操作按钮区域样式 */
.action-buttons-section {
  padding: var(--space-xl) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  text-align: center;
}

/* 主要操作按钮样式 */
.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 48px;
  padding: 0 var(--space-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-sm);
  min-width: 140px;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
  color: var(--text-inverse);
}

.primary-action-btn:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  transform: none;
  box-shadow: none;
}

/* 次要操作按钮样式 */
.secondary-action-btn {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 48px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 140px;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
  transform: translateY(-1px);
}

/* 说明区域样式 */
.info-section {
  margin-bottom: var(--space-lg);
  padding: var(--space-md);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

/* 金额显示 */
.amount-text {
  font-weight: 600;
  color: var(--success-color);
  font-size: var(--text-base);
}

/* 标签页样式 */
.department-tabs {
  padding: var(--space-lg);
  width: 100%;
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav) {
  margin-bottom: var(--space-lg);
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav-wrap) {
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav-list) {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  scrollbar-width: thin;
  scrollbar-color: var(--border-light) transparent;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar) {
  height: 4px;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-thumb) {
  background: var(--border-light);
  border-radius: 2px;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-thumb:hover) {
  background: var(--primary-color);
}

:deep(.department-tabs .ant-tabs-tab) {
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  transition: var(--transition-normal);
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 120px;
  text-align: center;
}

:deep(.department-tabs .ant-tabs-tab-active) {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

:deep(.department-tabs .ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: var(--text-inverse);
}

:deep(.department-tabs .ant-tabs-content-holder) {
  background: var(--bg-primary);
  border-radius: 0 var(--radius-sm) var(--radius-sm) var(--radius-sm);
  border: 1px solid var(--border-light);
  padding: var(--space-lg);
  width: 100%;
  overflow-x: auto;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.reimbursement-tables {
  margin-top: var(--space-lg);
}

.table-header {
  padding: var(--space-md) var(--space-lg);
}

.table-title {
  font-size: var(--text-md);
  gap: var(--space-xs);
}

.table-title .anticon {
  font-size: var(--text-md);
}

.table-subtitle {
  font-size: var(--text-xs);
}

.unified-filter-section {
  padding: var(--space-md) var(--space-lg);
}

.action-buttons-section {
  padding: var(--space-lg) var(--space-lg);
}

.action-buttons-section .ant-space {
  flex-wrap: wrap;
  justify-content: center;
}

/* 筛选区按钮高度对齐 */
.unified-filter-section .secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-filter-section .secondary-action-btn:hover {
  border-color: var(--primary-light);
}

/* 表格标题区域样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.record-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-filters {
  margin-top: 8px;
  padding: 12px 0;
  border-top: 1px solid var(--border-light);
}

.table-actions {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

/* 详细筛选控件样式 */
.detailed-filters {
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.filter-select,
.filter-input,
.filter-date-picker {
  height: 32px;
  font-size: var(--text-sm);
}

.budget-range-input {
  width: 200px;
}

.budget-range-input .ant-input {
  height: 32px;
  font-size: var(--text-sm);
}

/* 紧凑型操作按钮样式 */
.compact-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 导出字段配置样式 */
.export-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
}

.export-field-category {
  flex: 1;
  min-width: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  overflow: hidden;
}

.export-category-header {
  background-color: #e6f7ff;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 12px;
}

.export-category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
  text-align: center;
}

.export-category-fields {
  padding: 12px;
  background-color: #ffffff;
}

.export-field-option {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.export-config-container {
  padding: 0;
}

.filter-section {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.fields-sections {
  margin-bottom: 16px;
}

.export-category-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #595959;
}

.export-category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.export-field-option {
  padding: 4px 0;
}

/* 记录选择部分样式 */
.records-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-count {
  font-size: 13px;
  color: #666;
}

.actions-section {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.table-container {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 修复操作列透明背景问题 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5 !important;
}

:deep(.ant-table-tbody > tr:hover > td.ant-table-cell-fix-right) {
  background-color: #f5f5f5 !important;
}

/* 结算报销表格专用样式 - 简化版本 */
:deep(.reimbursement-table .ant-table-placeholder) {
  border: none !important;
}

:deep(.reimbursement-table .ant-table-placeholder .ant-empty) {
  border: none !important;
  background: transparent !important;
}
</style>
