<template>
  <div class="user-profile">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <UserOutlined />
          个人中心
        </h1>
        <p class="page-subtitle">查看和管理您的个人信息和账户设置</p>
      </div>
    </div>

    <div class="page-content">
      <a-row :gutter="24">
        <!-- 左侧个人信息 -->
        <a-col :xs="24" :lg="12">
          <div class="profile-section business-card">
            <div class="section-header">
              <h3 class="section-title">
                <IdcardOutlined />
                基本信息
              </h3>
              <a-button type="primary" @click="editMode = !editMode">
                {{ editMode ? "取消编辑" : "编辑资料" }}
              </a-button>
            </div>

            <a-form
              ref="formRef"
              :model="formData"
              :rules="rules"
              layout="vertical"
              @finish="handleSubmit"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="用户名" name="username">
                    <a-input v-model:value="formData.username" disabled />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="真实姓名" name="real_name">
                    <a-input
                      v-model:value="formData.real_name"
                      :disabled="!editMode"
                      placeholder="请输入真实姓名"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="邮箱" name="email">
                    <a-input
                      v-model:value="formData.email"
                      :disabled="!editMode"
                      placeholder="请输入邮箱地址"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="手机号码" name="phone">
                    <a-input
                      v-model:value="formData.phone"
                      :disabled="!editMode"
                      placeholder="请输入手机号码"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="所属部门">
                    <a-input :value="userInfo.department_name" disabled />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="用户角色">
                    <a-space>
                      <a-tag
                        v-for="role in userInfo.roles"
                        :key="role.id"
                        color="blue"
                      >
                        {{ role.name }}
                      </a-tag>
                    </a-space>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="个人简介" name="bio">
                <a-textarea
                  v-model:value="formData.bio"
                  :disabled="!editMode"
                  placeholder="请输入个人简介"
                  :rows="3"
                />
              </a-form-item>

              <a-form-item v-if="editMode">
                <a-space>
                  <a-button
                    type="primary"
                    html-type="submit"
                    :loading="loading"
                  >
                    保存修改
                  </a-button>
                  <a-button @click="resetForm"> 重置 </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>
        </a-col>

        <!-- 右侧安全设置 -->
        <a-col :xs="24" :lg="12">
          <div class="security-section business-card">
            <div class="section-header">
              <h3 class="section-title">
                <SafetyCertificateOutlined />
                安全设置
              </h3>
            </div>

            <div class="security-item">
              <div class="security-item-content">
                <div class="security-item-title">修改密码</div>
                <div class="security-item-desc">定期更新密码，保护账户安全</div>
              </div>
              <a-button @click="showPasswordModal = true">修改</a-button>
            </div>

            <div class="security-item">
              <div class="security-item-content">
                <div class="security-item-title">登录日志</div>
                <div class="security-item-desc">查看最近的登录记录</div>
              </div>
              <a-button @click="showLoginLogs">查看</a-button>
            </div>

            <div class="security-item">
              <div class="security-item-content">
                <div class="security-item-title">账户状态</div>
                <div class="security-item-desc">
                  <a-tag :color="userInfo.is_active ? 'green' : 'red'">
                    {{ userInfo.is_active ? "正常" : "已停用" }}
                  </a-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 账户统计 -->
          <div class="stats-section business-card">
            <div class="section-header">
              <h3 class="section-title">
                <BarChartOutlined />
                账户统计
              </h3>
            </div>

            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ statistics.login_count || 0 }}</div>
                <div class="stat-label">登录次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">
                  {{ statistics.request_count || 0 }}
                </div>
                <div class="stat-label">提交需求</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">
                  {{ statistics.approval_count || 0 }}
                </div>
                <div class="stat-label">审批次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ statistics.join_days || 0 }}</div>
                <div class="stat-label">加入天数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">
                  {{ statistics.total_operations || 0 }}
                </div>
                <div class="stat-label">总操作数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">
                  {{ formatAmount(statistics.total_amount) }}
                </div>
                <div class="stat-label">采购总额</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">
                  {{ statistics.recent_operations || 0 }}
                </div>
                <div class="stat-label">近30天操作</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">
                  {{ statistics.pending_requests || 0 }}
                </div>
                <div class="stat-label">待处理需求</div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 修改密码模态框 -->
      <a-modal
        v-model:open="showPasswordModal"
        title="修改密码"
        @ok="handlePasswordChange"
        @cancel="resetPasswordForm"
        :confirm-loading="passwordLoading"
      >
        <a-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          layout="vertical"
        >
          <a-form-item label="当前密码" name="old_password">
            <a-input-password
              v-model:value="passwordForm.old_password"
              placeholder="请输入当前密码"
            />
          </a-form-item>
          <a-form-item label="新密码" name="new_password">
            <a-input-password
              v-model:value="passwordForm.new_password"
              placeholder="请输入新密码"
            />
          </a-form-item>
          <a-form-item label="确认新密码" name="confirm_password">
            <a-input-password
              v-model:value="passwordForm.confirm_password"
              placeholder="请再次输入新密码"
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 个人日志模态框 -->
      <a-modal
        v-model:open="showLogsModal"
        title="个人操作日志"
        :width="'60%'"
        :style="{ top: '15vh' }"
        :footer="null"
        :destroy-on-close="true"
      >
        <div style="height: 70vh; display: flex; flex-direction: column">
          <!-- 筛选区域 -->
          <div
            style="
              margin-bottom: 16px;
              padding: 16px;
              background: #fafafa;
              border-radius: 6px;
            "
          >
            <a-row :gutter="16" align="middle">
              <a-col :span="6">
                <a-select
                  v-model:value="logsFilters.log_type"
                  placeholder="日志类型"
                  allowClear
                  style="width: 100%"
                >
                  <a-select-option value="login">用户登录</a-select-option>
                  <a-select-option value="logout">用户登出</a-select-option>
                  <a-select-option value="create">创建操作</a-select-option>
                  <a-select-option value="update">更新操作</a-select-option>
                  <a-select-option value="delete">删除操作</a-select-option>
                  <a-select-option value="approve">审批操作</a-select-option>
                  <a-select-option value="export">导出操作</a-select-option>
                  <a-select-option value="import">导入操作</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="8">
                <a-range-picker
                  v-model:value="dateRange"
                  style="width: 100%"
                  :placeholder="['开始时间', '结束时间']"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  @change="handleDateRangeChange"
                />
              </a-col>
              <a-col :span="6">
                <a-input
                  v-model:value="logsFilters.search"
                  placeholder="搜索操作描述"
                  allowClear
                />
              </a-col>
              <a-col :span="4">
                <a-space>
                  <a-button type="primary" @click="searchLogs" size="small">
                    搜索
                  </a-button>
                  <a-button @click="resetLogsFilters" size="small">
                    重置
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>

          <!-- 日志表格 -->
          <div style="flex: 1; overflow: hidden">
            <a-table
              :columns="logsColumns"
              :data-source="userLogs"
              :loading="logsLoading"
              :pagination="{
                current: logsPagination.current,
                pageSize: logsPagination.pageSize,
                total: logsPagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }"
              @change="handleLogsTableChange"
              :scroll="{ y: 'calc(70vh - 200px)' }"
              size="small"
              :locale="{ emptyText: '暂无日志数据' }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'log_type'">
                  <a-tag :color="getLogTypeColor(record.log_type)">
                    {{ record.log_type_display }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-tooltip :title="record.action">
                    <span>{{ record.action }}</span>
                  </a-tooltip>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { message } from "ant-design-vue";
import { useStore } from "vuex";
import {
  UserOutlined,
  IdcardOutlined,
  SafetyCertificateOutlined,
  BarChartOutlined,
} from "@ant-design/icons-vue";
import api from "@/api";
import dayjs from "dayjs";

const store = useStore();

// 响应式数据
const loading = ref(false);
const editMode = ref(false);
const showPasswordModal = ref(false);
const passwordLoading = ref(false);
const userInfo = ref({});
const statistics = ref({});

// 表单引用
const formRef = ref(null);
const passwordFormRef = ref(null);

// 表单数据
const formData = reactive({
  username: "",
  real_name: "",
  email: "",
  phone: "",
  bio: "",
});

// 密码表单数据
const passwordForm = reactive({
  old_password: "",
  new_password: "",
  confirm_password: "",
});

// 表单验证规则
const rules = {
  real_name: [{ required: true, message: "请输入真实姓名", trigger: "blur" }],
  email: [{ type: "email", message: "请输入正确的邮箱格式", trigger: "blur" }],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

// 密码验证规则
const passwordRules = {
  old_password: [
    { required: true, message: "请输入当前密码", trigger: "blur" },
  ],
  new_password: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度至少6位", trigger: "blur" },
  ],
  confirm_password: [
    { required: true, message: "请确认新密码", trigger: "blur" },
    {
      validator: (rule, value) => {
        if (value !== passwordForm.new_password) {
          return Promise.reject("两次输入的密码不一致");
        }
        return Promise.resolve();
      },
      trigger: "blur",
    },
  ],
};
// 获取用户信息
const getUserInfo = async () => {
  try {
    const response = await api.users.getCurrentUser();
    if (response.code === 200) {
      userInfo.value = response.data;
      // 填充表单数据
      Object.assign(formData, {
        username: response.data.username,
        real_name: response.data.real_name || "",
        email: response.data.email || "",
        phone: response.data.phone || "",
        bio: response.data.bio || "",
      });
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    message.error("获取用户信息失败");
  }
};

// 获取用户统计信息
const getUserStatistics = async () => {
  try {
    const response = await api.users.getUserStatistics();
    if (response.code === 200) {
      statistics.value = response.data;
    }
  } catch (error) {
    console.error("获取统计信息失败:", error);
  }
};

// 提交表单
const handleSubmit = async () => {
  loading.value = true;
  try {
    const response = await api.users.updateProfile(formData);
    if (response.code === 200) {
      message.success("个人资料更新成功");
      editMode.value = false;
      await getUserInfo();
      // 更新用户store
      store.commit("SET_USER", response.data);
    }
  } catch (error) {
    console.error("更新个人资料失败:", error);
    message.error("更新个人资料失败");
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    username: userInfo.value.username,
    real_name: userInfo.value.real_name || "",
    email: userInfo.value.email || "",
    phone: userInfo.value.phone || "",
    bio: userInfo.value.bio || "",
  });
};

// 修改密码
const handlePasswordChange = async () => {
  try {
    await passwordFormRef.value.validate();
    passwordLoading.value = true;

    const response = await api.users.changePassword(passwordForm);
    if (response.code === 200) {
      message.success("密码修改成功");
      showPasswordModal.value = false;
      resetPasswordForm();
    }
  } catch (error) {
    console.error("修改密码失败:", error);
    message.error("修改密码失败");
  } finally {
    passwordLoading.value = false;
  }
};

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    old_password: "",
    new_password: "",
    confirm_password: "",
  });
  passwordFormRef.value?.resetFields();
};

// 日志相关状态
const showLogsModal = ref(false);
const logsLoading = ref(false);
const userLogs = ref([]);
const logsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 日志筛选条件
const logsFilters = reactive({
  log_type: "",
  start_date: "",
  end_date: "",
  search: "",
});

// 日期范围选择器的值
const dateRange = ref([]);

// 日志表格列定义
const logsColumns = [
  {
    title: "操作类型",
    dataIndex: "log_type_display",
    key: "log_type",
    width: 120,
  },
  {
    title: "操作描述",
    dataIndex: "action",
    key: "action",
    ellipsis: true,
  },
  {
    title: "目标模块",
    dataIndex: "target_model",
    key: "target_model",
    width: 120,
  },
  {
    title: "IP地址",
    dataIndex: "ip_address",
    key: "ip_address",
    width: 140,
  },
  {
    title: "操作时间",
    dataIndex: "created_at",
    key: "created_at",
    width: 180,
    customRender: ({ text }) => {
      return text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-";
    },
  },
];

// 获取用户日志
const getUserLogs = async () => {
  logsLoading.value = true;
  try {
    const params = {
      page: logsPagination.current,
      page_size: logsPagination.pageSize,
      ...logsFilters,
    };

    const response = await api.users.getUserLogs(params);
    if (response.code === 200) {
      userLogs.value = response.data.results || [];
      logsPagination.total = response.data.count || 0;
    } else {
      message.error(response.message || "获取日志失败");
    }
  } catch (error) {
    console.error("获取用户日志失败:", error);
    message.error("获取日志失败");
  } finally {
    logsLoading.value = false;
  }
};

// 查看登录日志
const showLoginLogs = async () => {
  showLogsModal.value = true;
  await getUserLogs();
};

// 日志表格变化处理
const handleLogsTableChange = (pagination) => {
  logsPagination.current = pagination.current;
  logsPagination.pageSize = pagination.pageSize;
  getUserLogs();
};

// 重置日志筛选条件
const resetLogsFilters = () => {
  Object.assign(logsFilters, {
    log_type: "",
    start_date: "",
    end_date: "",
    search: "",
  });
  dateRange.value = []; // 清空日期范围选择器
  logsPagination.current = 1;
  getUserLogs();
};

// 处理日期范围变化
const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    logsFilters.start_date = dates[0].format("YYYY-MM-DD HH:mm:ss");
    logsFilters.end_date = dates[1].format("YYYY-MM-DD HH:mm:ss");
  } else {
    logsFilters.start_date = "";
    logsFilters.end_date = "";
  }
};

// 搜索日志
const searchLogs = () => {
  logsPagination.current = 1;
  getUserLogs();
};

// 获取日志类型颜色
const getLogTypeColor = (logType) => {
  const colorMap = {
    login: "green",
    logout: "orange",
    create: "blue",
    update: "cyan",
    delete: "red",
    approve: "purple",
    reject: "volcano",
    export: "geekblue",
    import: "lime",
    error: "red",
    warning: "orange",
    info: "blue",
  };
  return colorMap[logType] || "default";
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount || amount === 0) return "¥0";
  return `¥${Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

// 页面初始化
onMounted(async () => {
  await getUserInfo();
  await getUserStatistics();
});
</script>

<style scoped>
.user-profile {
  background: #f0f2f5;
  min-height: 100vh;
}

.page-content {
  padding: 0px;
}

/* 页面标题区域样式 - 参考通知管理页面 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.page-title {
  color: var(--text-inverse);
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 var(--space-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  letter-spacing: -0.025em;
}

.page-title .anticon {
  font-size: var(--text-2xl);
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--text-base);
  margin: 0;
  line-height: 1.6;
  font-weight: 400;
}

/* 业务卡片样式 */
.business-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.business-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* 区块样式 */
.profile-section,
.security-section,
.stats-section {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 安全设置项 */
.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.security-item:last-child {
  border-bottom: none;
}

.security-item-content {
  flex: 1;
}

.security-item-title {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.security-item-desc {
  font-size: 14px;
  color: #6b7280;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-profile {
    padding: 16px;
  }

  .page-header {
    padding: 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
