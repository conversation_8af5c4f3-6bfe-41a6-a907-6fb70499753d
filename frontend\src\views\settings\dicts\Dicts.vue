<template>
  <div class="dict-management">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <BookOutlined />
          数据字典管理
        </h1>
        <p class="page-subtitle">管理系统基础数据字典，维护业务配置和枚举值</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalCount }}</span>
          <span class="stat-label">字典总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ activeCount }}</span>
          <span class="stat-label">启用字典</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ typeCount }}</span>
          <span class="stat-label">字典类型</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ inactiveCount }}</span>
          <span class="stat-label">停用字典</span>
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="unified-filter-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-input v-model:value="searchTerm" placeholder="字典类型、编码、名称" allow-clear @pressEnter="searchDicts"
            class="filter-input">
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="typeFilter" placeholder="选择字典类型" allowClear @change="handleTypeFilterChange"
            class="filter-select">
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option v-for="type in dictTypes" :key="type.type_code || type.type"
              :value="type.type_code || type.type">
              {{ type.type_name || type.type_display || type.type_code || type.type }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="statusFilter" placeholder="选择状态" allowClear @change="handleStatusFilterChange"
            class="filter-select">
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">停用</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="editableFilter" placeholder="选择可编辑状态" allowClear @change="handleEditableFilterChange"
            class="filter-select">
            <a-select-option value="">全部</a-select-option>
            <a-select-option :value="1">可编辑</a-select-option>
            <a-select-option :value="2">不可编辑</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="searchDicts" class="secondary-action-btn">
            <SearchOutlined />
            搜索
          </a-button>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="resetSearch" class="secondary-action-btn">
            <ReloadOutlined />
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 操作栏 -->
    <div class="action-buttons-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col>
          <a-button type="primary" @click="showAddModal" class="primary-action-btn">
            <PlusOutlined />
            新增字典
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="exportDicts" class="secondary-action-btn">
            <DownloadOutlined />
            导出字典
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="showImportModal" class="secondary-action-btn">
            <UploadOutlined />
            批量导入
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="refreshDictTypes" class="secondary-action-btn">
            <ReloadOutlined />
            刷新类型
          </a-button>
        </a-col>
        <!-- 批量操作按钮 -->
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button type="primary" @click="batchEnable" class="primary-action-btn">
            <CheckCircleOutlined />
            批量启用 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button @click="batchDisable" class="secondary-action-btn">
            <StopOutlined />
            批量停用 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button danger @click="batchDelete" class="danger-action-btn">
            <DeleteOutlined />
            批量删除 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 数据字典表格 -->
    <div class="table-section business-card">
      <a-table :columns="columns" :data-source="dicts" :row-key="record => record.id" :row-selection="rowSelection"
        :pagination="paginationConfig" @change="handleTableChange" :loading="loading" class="unified-table" size="large"
        :scroll="{ x: 1200 }">
        <!-- 表格列定义 -->
        <template #bodyCell="{ column, record }">
          <!-- 字典类型显示 -->
          <template v-if="column.dataIndex === 'type_code'">
            <a-tag color="blue">
              <TagOutlined />
              {{ record.type_name || record.type_code }}
            </a-tag>
          </template>

          <!-- 编码显示 -->
          <template v-if="column.dataIndex === 'code'">
            <code class="dict-code">{{ record.code }}</code>
          </template>



          <!-- 排序显示 -->
          <template v-if="column.dataIndex === 'order'">
            <a-badge :count="record.order" :number-style="{ backgroundColor: '#52c41a' }" />
          </template>

          <!-- 状态显示 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="record.status ? 'success' : 'error'">
              <CheckCircleOutlined v-if="record.status" />
              <StopOutlined v-else />
              {{ record.status ? '启用' : '停用' }}
            </a-tag>
          </template>

          <!-- 可编辑状态显示 -->
          <template v-if="column.dataIndex === 'editable'">
            <a-tag :color="record.editable === 1 ? 'success' : 'warning'">
              <EditOutlined v-if="record.editable === 1" />
              <LockOutlined v-else />
              {{ record.editable === 1 ? '可编辑' : '不可编辑' }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" @click="viewDetail(record)" size="small">
                <EyeOutlined />
                查看
              </a-button>
              <a-button v-if="record.editable === 1" type="link" @click="editDict(record)" size="small">
                <EditOutlined />
                编辑
              </a-button>
              <a-button v-if="record.editable === 1" type="link" danger size="small" @click="deleteDict(record)">
                <DeleteOutlined />
                删除
              </a-button>
              <a-tooltip v-if="record.editable === 2" title="此字典项不可编辑">
                <a-button type="link" disabled size="small">
                  <LockOutlined />
                  锁定
                </a-button>
              </a-tooltip>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 批量导入模态框 -->
    <a-modal v-model:open="importModalVisible" title="批量导入数据字典" @ok="handleImportSubmit" @cancel="handleImportCancel"
      width="600px" :confirm-loading="importLoading" ok-text="导入" cancel-text="取消">
      <div class="import-section">
        <a-alert message="导入说明" description="请下载模板文件，按照模板格式填写数据字典信息后上传。支持Excel格式(.xlsx)文件。" type="info" show-icon
          style="margin-bottom: 16px;" />

        <div style="margin-bottom: 16px;">
          <a-button @click="downloadTemplate" type="link">
            <DownloadOutlined />
            下载数据字典导入模板
          </a-button>
        </div>

        <a-upload-dragger v-model:fileList="importFileList" :before-upload="beforeUpload" :remove="handleRemoveFile"
          accept=".xlsx,.xls" :multiple="false" :max-count="1">
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持单个文件上传，仅支持Excel格式(.xlsx, .xls)
          </p>
        </a-upload-dragger>

        <div v-if="importResult" class="import-result" style="margin-top: 16px;">
          <a-alert :message="importResult.success ? '导入成功' : '导入失败'" :description="importResult.message"
            :type="importResult.success ? 'success' : 'error'" show-icon />
        </div>
      </div>
    </a-modal>

    <!-- 新增/编辑数据字典模态框 -->
    <a-modal v-model:open="modalVisible" :title="isEditMode ? '编辑字典' : '新增字典'" @ok="handleSubmit" @cancel="handleCancel"
      ok-text="确定" cancel-text="取消">
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="字典类型" name="selectedTypeCode">
              <a-select v-model:value="formData.selectedTypeCode" placeholder="请选择字典类型" @change="onTypeChange"
                :disabled="isEditMode">
                <a-select-option v-for="type in availableTypes" :key="type.type_code" :value="type.type_code">
                  {{ type.type_name }} ({{ type.type_code }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="编码" name="code">
              <a-input v-model:value="formData.code" placeholder="请输入编码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-switch v-model:checked="formData.status" checked-children="启用" un-checked-children="停用" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="可编辑" name="editable">
              <a-switch v-model:checked="editableSwitch" checked-children="可编辑" un-checked-children="不可编辑" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入描述信息" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 字典详情模态框 -->
    <a-modal v-model:open="detailVisible" title="字典详情" :footer="null" width="600px">
      <div class="dict-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="字典类型编码">
            <code class="dict-code">{{ detailData.type_code }}</code>
          </a-descriptions-item>
          <a-descriptions-item label="字典类型名称">
            <a-tag color="blue">{{ detailData.type_name }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="字典编码">
            <code class="dict-code">{{ detailData.code }}</code>
          </a-descriptions-item>
          <a-descriptions-item label="字典名称">
            <span class="dict-name">{{ detailData.name }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="排序">
            <a-tag color="orange">{{ detailData.order }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="detailData.status ? 'success' : 'error'">
              <CheckCircleOutlined v-if="detailData.status" />
              <StopOutlined v-else />
              {{ detailData.status ? '启用' : '停用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="可编辑">
            <a-tag :color="detailData.editable === 1 ? 'success' : 'warning'">
              <EditOutlined v-if="detailData.editable === 1" />
              <LockOutlined v-else />
              {{ detailData.editable === 1 ? '可编辑' : '不可编辑' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="描述" :span="2">
            <div class="dict-description">
              {{ detailData.description || '暂无描述' }}
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" v-if="detailData.created_at">
            <span class="time-text">{{ formatDateTime(detailData.created_at) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="更新时间" v-if="detailData.updated_at">
            <span class="time-text">{{ formatDateTime(detailData.updated_at) }}</span>
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <template #footer>
        <div class="detail-footer">
          <a-button @click="detailVisible = false">关闭</a-button>
          <a-button type="primary" @click="editFromDetail">
            <EditOutlined />
            编辑
          </a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  BookOutlined,
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  InboxOutlined,
  TagOutlined,
  CheckCircleOutlined,
  StopOutlined,
  EyeOutlined,
  EditOutlined,
  LockOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import api from '@/api';

// 表单引用
const formRef = ref(null);

// 模态框控制
const modalVisible = ref(false);
// 是否为编辑模式
const isEditMode = ref(false);

// 表单数据
const formData = ref({
  type_code: '',
  type_name: '',
  code: '',
  name: '',
  description: '',
  order: 1,
  status: true,
  editable: 1,
  selectedTypeCode: ''  // 添加这个字段用于表单验证
});

// 可编辑开关计算属性
const editableSwitch = computed({
  get: () => formData.value.editable === 1,
  set: (value) => {
    formData.value.editable = value ? 1 : 2
  }
});

// 数据字典列表
const dicts = ref([]);
// 加载状态
const loading = ref(false);
// 搜索条件
const searchTerm = ref('');
// 类型筛选
const typeFilter = ref('');
// 状态筛选
const statusFilter = ref('');
// 可编辑状态筛选
const editableFilter = ref('');

// 批量操作相关
const selectedRowKeys = ref([]);
// 字典类型列表
const dictTypes = ref([]);

// 可用的字典类型列表（用于新增时选择）
const availableTypes = ref([]);

// 选中的类型编码
const selectedTypeCode = ref('');

// 统计信息
const totalCount = ref(0);
const activeCount = ref(0);
const inactiveCount = ref(0);

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: () => ({
    disabled: false, // 可以根据需要设置禁用条件
  }),
};

const typeCount = computed(() => {
  return dictTypes.value.length;
});


// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }) => pagination.pageSize * (pagination.current - 1) + index + 1
  },
  {
    title: '字典类型',
    dataIndex: 'type_code',
    width: 100,
    sorter: true
  },
  {
    title: '编码',
    dataIndex: 'code',
    width: 100
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 120
  },

  {
    title: '描述',
    dataIndex: 'description',
    width: 160,
    ellipsis: true
  },
  {
    title: '排序',
    dataIndex: 'order',
    width: 60,
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80
  },
  {
    title: '可编辑',
    dataIndex: 'editable',
    width: 80
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 180,
    fixed: 'right'
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
});

const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: pagination.showSizeChanger,
  showQuickJumper: pagination.showQuickJumper,
  showTotal: pagination.showTotal,
  pageSizeOptions: ['10', '20', '50', '100']
}));



// 批量导入相关
const importModalVisible = ref(false);
const importLoading = ref(false);
const importFileList = ref([]);
const importResult = ref(null);

// 表单验证规则
const rules = {
  selectedTypeCode: [{ required: true, message: '请选择字典类型' }],
  code: [{ required: true, message: '请输入编码' }],
  name: [{ required: true, message: '请输入名称' }]
};

// 获取数据字典列表
const getDicts = async (forceRefresh = false) => {
  try {
    loading.value = true;

    // 构建查询参数
    const params = {};

    // 如果强制刷新，添加时间戳参数来绕过缓存
    if (forceRefresh) {
      params._t = Date.now();
    }

    // 添加筛选参数
    if (searchTerm.value) {
      params.search = searchTerm.value;
    }
    if (typeFilter.value) {
      params.type_code = typeFilter.value;
    }
    if (statusFilter.value !== '') {
      params.status = statusFilter.value;
    }
    if (editableFilter.value !== '') {
      params.editable = editableFilter.value;
    }

    // 添加分页参数
    params.page = pagination.current;
    params.page_size = pagination.pageSize;

    const response = await api.dicts.getAllDicts(params);

    if (response.code === 200) {
      // 处理响应数据
      const rawData = response.data;

      // 统一数据格式为数组
      let dataList = [];
      let totalCount = 0;

      if (rawData.results && Array.isArray(rawData.results)) {
        // 标准分页响应格式：包含 results 数组
        dataList = rawData.results;
        // 使用后端返回的总记录数
        totalCount = rawData.total || rawData.count || 0;
      } else if (rawData.types && rawData.items) {
        // 新版本数据结构：包含类型和项目数组
        dataList = rawData.items;
        totalCount = dataList.length;
      } else if (Array.isArray(rawData)) {
        // 兼容旧版本数组数据
        dataList = rawData;
        totalCount = dataList.length;
      } else if (typeof rawData === 'object') {
        // 对于按类型组织的对象，展平所有值
        dataList = Object.values(rawData).flat();
        totalCount = dataList.length;
      }

      // 直接设置当前页数据
      dicts.value = dataList;

      // 更新分页信息
      pagination.total = totalCount;

      // 如果是第一次加载或强制刷新，获取统计信息
      if (!dictTypes.value.length || forceRefresh) {
        await getStatistics();
      }


    }
  } catch (error) {
    console.error('获取数据字典失败：', error);
    message.error('获取数据字典失败，请检查网络连接');
  } finally {
    loading.value = false;
  }
};

// 获取统计信息
const getStatistics = async () => {
  try {
    const response = await api.dicts.getStatistics();
    if (response.code === 200) {
      const data = response.data;

      // 更新统计信息
      totalCount.value = data.total_count || 0;
      activeCount.value = data.active_count || 0;
      inactiveCount.value = data.inactive_count || 0;

      // 更新字典类型列表
      dictTypes.value = data.types || [];
    }
  } catch (error) {
    console.warn('获取统计信息失败:', error);
  }
};





// 显示新增字典模态框
const showAddModal = async () => {
  isEditMode.value = false;
  selectedTypeCode.value = '';
  formData.value = {
    type_code: '',
    type_name: '',
    code: '',
    name: '',
    description: '',
    order: 1,
    status: true,
    editable: 1,
    selectedTypeCode: ''
  };

  // 加载可用的字典类型
  await loadAvailableTypes();
  modalVisible.value = true;
};

// 显示导入模态框
const showImportModal = () => {
  importModalVisible.value = true;
  importFileList.value = [];
  importResult.value = null;
};

// 导出字典数据
const exportDicts = async () => {
  try {
    const response = await api.dicts.export({
      type: typeFilter.value,
      status: statusFilter.value,
      search: searchTerm.value
    });

    // 检查响应数据
    if (!response || !response.data) {
      throw new Error('响应数据为空');
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error('导出的文件为空');
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `数据字典_${new Date().toLocaleDateString()}.xlsx`);
    document.body.appendChild(link);
    link.click();
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error(`导出失败: ${error.message || '未知错误'}`);
  }
};

// 刷新字典类型
const refreshDictTypes = async () => {
  try {
    const response = await api.dicts.getStatistics();
    if (response.code === 200) {
      dictTypes.value = response.data.types || [];
      message.success('字典类型刷新成功');
    }
  } catch (error) {
    console.error('刷新字典类型失败:', error);
    message.error('刷新字典类型失败');
  }
};

// 获取可用的字典类型列表
const loadAvailableTypes = async () => {
  try {
    const response = await api.dicts.getStatistics();
    if (response.code === 200) {
      availableTypes.value = response.data.types || [];
    }
  } catch (error) {
    console.error('获取字典类型失败:', error);
    message.error('获取字典类型失败');
  }
};

// 类型选择变化处理
const onTypeChange = (typeCode) => {
  const selectedType = availableTypes.value.find(type => type.type_code === typeCode);
  if (selectedType) {
    formData.value.type_code = selectedType.type_code;
    formData.value.type_name = selectedType.type_name;
  }
};

// 详情模态框相关
const detailVisible = ref(false);
const detailData = ref({});

// 查看字典详情
const viewDetail = (record) => {
  detailData.value = { ...record };
  detailVisible.value = true;
};

// 从详情页面编辑
const editFromDetail = () => {
  detailVisible.value = false;
  editDict(detailData.value);
};

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 编辑数据字典
const editDict = async (record) => {
  isEditMode.value = true;
  selectedTypeCode.value = record.type_code;
  formData.value = {
    id: record.id,
    type_code: record.type_code,
    type_name: record.type_name,
    code: record.code,
    name: record.name,
    description: record.description,
    order: record.order,
    status: record.status,
    editable: record.editable || 1,
    selectedTypeCode: record.type_code
  };

  // 加载可用的字典类型（编辑时也需要显示当前类型）
  await loadAvailableTypes();
  modalVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    const values = await formRef.value.validateFields();

    // 确保type_code和type_name已设置
    if (!formData.value.type_code && formData.value.selectedTypeCode) {
      const selectedType = availableTypes.value.find(type => type.type_code === formData.value.selectedTypeCode);
      if (selectedType) {
        formData.value.type_code = selectedType.type_code;
        formData.value.type_name = selectedType.type_name;
      }
    }

    // 准备提交数据
    const submitData = {
      type_code: formData.value.type_code,
      type_name: formData.value.type_name,
      code: values.code,
      name: values.name,
      description: values.description || '',
      status: formData.value.status,
      editable: formData.value.editable
    };

    // 如果是编辑模式，包含ID
    if (isEditMode.value) {
      submitData.id = formData.value.id;
      submitData.order = formData.value.order;
    }

    // 如果是新增，设置为最小的order值，让新增的项目排在最前面
    if (!isEditMode.value) {
      submitData.order = 0; // 设置为0，后端会重新整理排序
    }

    let response;
    if (isEditMode.value) {
      // 更新数据字典
      response = await api.dicts.update(formData.value.id, submitData);
    } else {
      // 创建新字典
      response = await api.dicts.create(submitData);
    }

    // 检查响应状态 - 后端返回自定义格式响应
    if (response.status === 200 || response.status === 201) {
      // 检查响应体中的code字段
      if (response.data && response.data.code === 200) {
        message.success(response.data.message || (isEditMode.value ? '更新成功' : '创建成功'));
      } else {
        message.success(isEditMode.value ? '更新成功' : '创建成功');
      }

      // 立即关闭模态框和刷新数据
      modalVisible.value = false;
      await getDicts(true); // 强制刷新，绕过缓存
      await getStatistics();
    } else {
      // 处理错误响应
      const errorMessage = response.data?.message || response.message || '操作失败';
      message.error(errorMessage);
    }
  } catch (error) {
    console.error('表单提交失败：', error);
    if (error.response && error.response.data) {
      console.error('服务器错误详情:', error.response.data);

      // 显示详细的验证错误
      if (error.response.data.data && typeof error.response.data.data === 'object') {
        const errors = error.response.data.data;
        Object.keys(errors).forEach(field => {
          const fieldErrors = Array.isArray(errors[field]) ? errors[field] : [errors[field]];
          fieldErrors.forEach(err => {
            message.error(`${field}: ${err}`);
          });
        });
      } else {
        message.error(error.response.data.message || '操作失败');
      }
    } else {
      message.error('操作失败');
    }
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
};

// 批量启用字典
const batchEnable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要启用的字典');
    return;
  }

  try {
    const promises = selectedRowKeys.value.map(id => {
      const dict = dicts.value.find(d => d.id === id);
      return api.dicts.update(id, { ...dict, status: true });
    });

    await Promise.all(promises);
    message.success(`成功启用 ${selectedRowKeys.value.length} 个字典`);
    selectedRowKeys.value = [];
    await getDicts(true); // 强制刷新数据
    await getStatistics(); // 更新统计信息
  } catch (error) {
    console.error('批量启用失败:', error);
    message.error('批量启用失败');
  }
};

// 批量停用字典
const batchDisable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要停用的字典');
    return;
  }

  try {
    const promises = selectedRowKeys.value.map(id => {
      const dict = dicts.value.find(d => d.id === id);
      return api.dicts.update(id, { ...dict, status: false });
    });

    await Promise.all(promises);
    message.success(`成功停用 ${selectedRowKeys.value.length} 个字典`);
    selectedRowKeys.value = [];
    await getDicts(true); // 强制刷新数据
    await getStatistics(); // 更新统计信息
  } catch (error) {
    console.error('批量停用失败:', error);
    message.error('批量停用失败');
  }
};

// 批量删除字典
const batchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的字典');
    return;
  }

  Modal.confirm({
    title: '批量删除确认',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个字典吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const promises = selectedRowKeys.value.map(id => api.dicts.delete(id));
        await Promise.all(promises);
        message.success(`成功删除 ${selectedRowKeys.value.length} 个字典`);
        selectedRowKeys.value = [];
        await getDicts(true); // 强制刷新数据
        await getStatistics(); // 更新统计信息
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    }
  });
};

// 删除数据字典
const deleteDict = async (record) => {
  Modal.confirm({
    title: "确认删除",
    content: `确定删除字典 ${record.name} 吗？`,
    okText: "确定",
    cancelText: "取消",
    okType: "danger",
    onOk: async () => {
      try {
        const response = await api.dicts.delete(record.id);
        // 删除操作：检查HTTP状态码和响应体
        if (response.status === 200 || (response.data && response.data.code === 200)) {
          message.success(response.data?.message || '删除成功');
          await getDicts(true); // 强制刷新，绕过缓存
          await getStatistics();
        } else {
          message.error(response.data?.message || '删除失败');
        }
      } catch (error) {
        // 删除失败：显示错误信息给用户
        const errorMessage = error.response?.data?.message || '删除失败';
        message.error(errorMessage);
      }
    }
  });
};

// 搜索数据字典
const searchDicts = async () => {
  // 重置到第一页并重新获取数据
  pagination.current = 1;
  await getDicts(true); // 强制刷新数据
  await getStatistics(); // 更新统计信息
};

// 重置搜索
const resetSearch = async () => {
  searchTerm.value = '';
  typeFilter.value = '';
  statusFilter.value = '';
  editableFilter.value = '';
  pagination.current = 1;
  await getDicts(true); // 强制刷新数据
  await getStatistics(); // 更新统计信息
};

// 类型筛选变化处理
const handleTypeFilterChange = async () => {
  pagination.current = 1;
  await getDicts(true); // 强制刷新数据
  await getStatistics(); // 更新统计信息
};

// 状态筛选变化处理
const handleStatusFilterChange = async () => {
  pagination.current = 1;
  await getDicts(true); // 强制刷新数据
  await getStatistics(); // 更新统计信息
};

// 可编辑状态筛选变化处理
const handleEditableFilterChange = async () => {
  pagination.current = 1;
  await getDicts(true); // 强制刷新数据
  await getStatistics(); // 更新统计信息
};



// 下载标准模板
const downloadTemplate = async () => {
  try {
    const response = await api.dicts.downloadTemplate();

    // 检查响应数据
    if (!response || !response.data) {
      throw new Error('响应数据为空');
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error('下载的文件为空');
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', '数据字典导入模板.xlsx');
    document.body.appendChild(link);
    link.click();
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    message.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error(`下载模板失败: ${error.message || '未知错误'}`);
  }
};



// 文件上传前的检查
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel' ||
    file.name.endsWith('.xlsx') ||
    file.name.endsWith('.xls');

  if (!isExcel) {
    message.error('只能上传Excel文件！');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }

  // 如果已有文件，替换为新文件
  if (importFileList.value.length > 0) {
    importFileList.value = [];
  }

  // 手动添加文件到列表
  importFileList.value = [{
    uid: file.uid,
    name: file.name,
    status: 'done',
    originFileObj: file
  }];

  return false; // 阻止自动上传，手动处理
};

// 移除文件
const handleRemoveFile = () => {
  importFileList.value = [];
  importResult.value = null;
};

// 提交导入
const handleImportSubmit = async () => {
  try {
    if (importFileList.value.length === 0) {
      message.warning('请选择要导入的文件');
      return;
    }

    importLoading.value = true;
    const formData = new FormData();

    // 获取原始文件对象
    const fileItem = importFileList.value[0];
    const file = fileItem.originFileObj || fileItem;

    if (!file) {
      message.error('文件对象无效，请重新选择文件');
      return;
    }

    formData.append('file', file);

    // 调用API进行导入
    const response = await api.dicts.importFromExcel(formData);

    // 检查响应格式
    if (response.data && response.data.code === 200) {
      importResult.value = {
        success: true,
        message: `导入成功！共导入 ${response.data.data.success_count || 0} 条记录`
      };
      message.success('数据导入成功');
      await getDicts(true); // 强制刷新列表
      await getStatistics(); // 更新统计信息
      setTimeout(() => {
        importModalVisible.value = false;
      }, 2000);
    } else if (response.status === 200 && response.data) {
      // 处理直接返回的响应
      importResult.value = {
        success: true,
        message: `导入成功！共导入 ${response.data.success_count || 0} 条记录`
      };
      message.success('数据导入成功');
      await getDicts(true);
      await getStatistics();
      setTimeout(() => {
        importModalVisible.value = false;
      }, 2000);
    } else {
      importResult.value = {
        success: false,
        message: response.data?.message || response.message || '数据导入失败'
      };
      message.error('数据导入失败');
    }
  } catch (error) {
    console.error('文件导入失败：', error);

    // 处理错误响应
    let errorMessage = '导入失败，请检查文件格式';
    if (error.response && error.response.data) {
      errorMessage = error.response.data.message || errorMessage;
    }

    importResult.value = {
      success: false,
      message: errorMessage
    };
    message.error(errorMessage);
  } finally {
    importLoading.value = false;
  }
};

// 取消导入
const handleImportCancel = () => {
  importModalVisible.value = false;
  importFileList.value = [];
  importResult.value = null;
};

// 处理表格分页变化
const handleTableChange = async (newPagination) => {
  // 更新分页配置
  pagination.current = newPagination.current;
  pagination.pageSize = newPagination.pageSize;

  // 重新获取数据
  await getDicts();
};

// 页面加载时初始化
onMounted(async () => {
  await getDicts();
  await getStatistics();
  await loadAvailableTypes();
});
</script>

<style scoped>
.dict-management {
  padding: 0;
  background: transparent;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  letter-spacing: 0.5px;
}

.page-title .anticon {
  font-size: var(--text-3xl);
}

.page-subtitle {
  font-size: var(--text-base);
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: var(--space-xl);
}

.stat-item {
  text-align: center;
  padding: var(--space-md) var(--space-lg);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.9;
  font-weight: 500;
}

/* 搜索和筛选区域 */
.filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
}

.filter-form {
  gap: var(--space-lg);
}

:deep(.filter-form .ant-form-item) {
  margin-bottom: 0;
}

:deep(.filter-form .ant-form-item-label) {
  font-weight: 500;
  color: var(--text-secondary);
}

/* 搜索输入框样式 */
.search-input {
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.search-input .ant-input) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: all 0.3s;
}

:deep(.search-input .ant-input:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

:deep(.search-input .ant-input:hover) {
  border-color: var(--primary-light);
}

/* 按钮样式 */
.search-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.search-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.reset-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  background: var(--bg-primary);
  transition: all 0.3s;
}

.reset-btn:hover {
  border-color: var(--border-dark);
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
}

.action-left {
  display: flex;
  gap: var(--space-md);
}

.primary-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.secondary-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.secondary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-overlay);
}

/* 表格区域 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* 表格样式已在统一样式文件中定义 */

/* 字典编码样式 */
.dict-code {
  background: var(--bg-tertiary);
  color: var(--primary-color);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-family: 'Courier New', monospace;
  font-size: var(--text-xs);
  font-weight: 600;
  border: 1px solid var(--border-light);
}

/* 字典值样式 */
.dict-value {
  color: var(--text-primary);
  font-weight: 500;
  font-size: var(--text-sm);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

:deep(.action-buttons .ant-btn-link) {
  color: var(--primary-color);
  font-weight: 500;
  transition: all 0.3s;
  border-radius: var(--radius-sm);
  padding: var(--space-xs) var(--space-sm);
  height: auto;
  font-size: var(--text-sm);
}

:deep(.action-buttons .ant-btn-link:hover) {
  color: var(--primary-dark);
  background: var(--bg-overlay);
}

:deep(.action-buttons .ant-btn-link.ant-btn-dangerous) {
  color: var(--error-color);
}

:deep(.action-buttons .ant-btn-link.ant-btn-dangerous:hover) {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 状态标签优化 */
:deep(.ant-tag) {
  border-radius: 20px;
  font-weight: 500;
  font-size: var(--text-sm);
  padding: 4px var(--space-md);
  border: none;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 分页样式 */
:deep(.ant-pagination) {
  margin: var(--space-xl) 0 0 0;
  text-align: center;
}

:deep(.ant-pagination .ant-pagination-item) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s;
}

:deep(.ant-pagination .ant-pagination-item:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

:deep(.ant-pagination .ant-pagination-item-active) {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
}

:deep(.ant-pagination .ant-pagination-item-active a) {
  color: var(--text-inverse);
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-lg);
  }

  .header-stats {
    justify-content: center;
  }

  .action-bar {
    flex-direction: column;
    gap: var(--space-md);
  }

  .action-left {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .filter-section {
    padding: var(--space-md) var(--space-lg);
  }

  .action-bar {
    padding: var(--space-md) var(--space-lg);
  }

  .page-header {
    padding: var(--space-lg) var(--space-lg);
  }

  .page-title {
    font-size: var(--text-2xl);
  }

  .header-stats {
    gap: var(--space-md);
  }

  .stat-item {
    padding: var(--space-md) var(--space-md);
    min-width: 80px;
  }
}

/* 统一筛选区样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

.unified-filter-section .filter-select .ant-select-selector {
  height: 32px !important;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: all 0.3s;
}

.unified-filter-section .filter-select:hover .ant-select-selector {
  border-color: var(--primary-light);
}

.unified-filter-section .filter-select.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* 统一按钮样式 */
.action-buttons-section {
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.primary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: white;
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.secondary-action-btn:hover {
  border-color: var(--primary-light);
  color: var(--primary-color);
}

.danger-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--error-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--error-color);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.danger-action-btn:hover {
  border-color: #dc2626;
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 字典详情样式 */
.dict-detail {
  padding: 16px 0;
}

.dict-code {
  background: #f6f8fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #d73a49;
  border: 1px solid #e1e4e8;
}

.dict-name {
  font-weight: 600;
  color: #24292e;
}

.dict-value {
  color: #0366d6;
  font-weight: 500;
}

.dict-description {
  color: #586069;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.time-text {
  color: #6a737d;
  font-size: 13px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.detail-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 详情模态框内的描述列表样式优化 */
:deep(.dict-detail .ant-descriptions-item-label) {
  background: #fafbfc;
  font-weight: 600;
  color: #24292e;
  width: 120px;
}

:deep(.dict-detail .ant-descriptions-item-content) {
  background: white;
  color: #24292e;
}

:deep(.dict-detail .ant-descriptions-bordered .ant-descriptions-item) {
  padding: 12px 16px;
}

:deep(.dict-detail .ant-descriptions) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
