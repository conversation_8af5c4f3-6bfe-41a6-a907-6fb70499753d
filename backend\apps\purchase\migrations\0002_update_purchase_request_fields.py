# Generated migration for purchase request model updates

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0001_initial'),
    ]

    operations = [
        # 更新字段的help_text，明确不再关联数据字典
        migrations.AlterField(
            model_name='purchaserequest',
            name='item_category',
            field=models.CharField(
                help_text='用户手动输入，不再关联数据字典',
                max_length=50,
                verbose_name='物品种类'
            ),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='unit',
            field=models.CharField(
                help_text='用户手动输入，不再关联数据字典',
                max_length=20,
                verbose_name='单位'
            ),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='procurement_method',
            field=models.CharField(
                help_text='用户手动输入，不再关联数据字典',
                max_length=50,
                verbose_name='采购方式'
            ),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='requirement_source',
            field=models.CharField(
                help_text='用户手动输入，不再关联数据字典',
                max_length=100,
                verbose_name='需求来源'
            ),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='fund_project_name',
            field=models.CharField(
                help_text='用户手动输入，不再关联数据字典',
                max_length=100,
                verbose_name='经费项目名称'
            ),
        ),
    ]
