-- 添加关键索引以提升API性能
-- 只添加最重要的索引，避免重复创建错误

-- 检查并添加采购需求表的关键索引
-- 状态字段索引
ALTER TABLE purchase_purchase_request ADD INDEX idx_status (status);

-- 创建时间索引
ALTER TABLE purchase_purchase_request ADD INDEX idx_created_at (created_at);

-- 状态+创建时间复合索引
ALTER TABLE purchase_purchase_request ADD INDEX idx_status_created_at (status, created_at);

-- 通知表索引
-- 接收者+是否已读索引
ALTER TABLE system_notification ADD INDEX idx_recipient_read (recipient_id, is_read);

-- 通知类型索引
ALTER TABLE system_notification ADD INDEX idx_notification_type (notification_type);

-- 是否删除索引
ALTER TABLE system_notification ADD INDEX idx_is_deleted (is_deleted);
