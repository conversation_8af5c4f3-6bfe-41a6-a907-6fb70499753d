<template>
  <div class="data-table">
    <!-- 筛选区域 -->
    <div class="filter-section" v-if="showFilters">
      <a-card :bordered="false" class="filter-card">
        <a-row :gutter="16">
          <slot name="filters"></slot>
        </a-row>
        <div class="filter-actions" v-if="showFilterActions">
          <a-space>
            <a-button @click="handleSearch" type="primary" :loading="loading">
              <SearchOutlined />
              查询
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
            <slot name="filter-actions"></slot>
          </a-space>
        </div>
      </a-card>
    </div>

    <!-- 操作区域 -->
    <div class="action-section" v-if="showActions">
      <a-space>
        <slot name="actions"></slot>
      </a-space>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="displayDataSource"
        :loading="loading"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        :scroll="scrollConfig"
        :size="size"
        @change="handleTableChange"
        v-bind="$attrs"
      >
        <template v-for="(_, name) in $slots" #[name]="slotData">
          <slot :name="name" v-bind="slotData"></slot>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'

export default {
  name: 'DataTable',
  components: {
    SearchOutlined,
    ReloadOutlined
  },
  props: {
    // 表格列配置
    columns: {
      type: Array,
      required: true
    },
    // 数据源
    dataSource: {
      type: Array,
      default: () => []
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 分页配置
    pagination: {
      type: [Object, Boolean],
      default: () => ({
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      })
    },
    // 行选择配置
    rowSelection: {
      type: Object,
      default: null
    },
    // 表格滚动配置
    scroll: {
      type: Object,
      default: () => ({ x: 'max-content' })
    },
    // 表格大小
    size: {
      type: String,
      default: 'middle'
    },
    // 是否显示筛选区域
    showFilters: {
      type: Boolean,
      default: true
    },
    // 是否显示筛选操作按钮
    showFilterActions: {
      type: Boolean,
      default: true
    },
    // 是否显示操作区域
    showActions: {
      type: Boolean,
      default: true
    },
    // 是否启用虚拟滚动
    virtualScroll: {
      type: Boolean,
      default: false
    },
    // 虚拟滚动行高
    rowHeight: {
      type: Number,
      default: 54
    },
    // 虚拟滚动容器高度
    virtualHeight: {
      type: Number,
      default: 400
    }
  },
  emits: ['search', 'reset', 'change'],
  setup(props, { emit }) {
    const visibleRange = ref({ start: 0, end: 20 })
    const scrollTop = ref(0)
    // 虚拟滚动显示的数据
    const displayDataSource = computed(() => {
      if (!props.virtualScroll || props.dataSource.length <= 100) {
        return props.dataSource
      }

      const { start, end } = visibleRange.value
      return props.dataSource.slice(start, end)
    })

    // 滚动配置
    const scrollConfig = computed(() => {
      if (props.virtualScroll && props.dataSource.length > 100) {
        return {
          ...props.scroll,
          y: props.virtualHeight
        }
      }
      return props.scroll
    })

    // 分页配置
    const paginationConfig = computed(() => {
      if (props.pagination === false) {
        return false
      }

      // 虚拟滚动时禁用分页
      if (props.virtualScroll && props.dataSource.length > 100) {
        return false
      }

      return {
        ...props.pagination,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      }
    })

    // 计算可见范围
    const calculateVisibleRange = () => {
      if (!props.virtualScroll) return

      const containerHeight = props.virtualHeight
      const itemHeight = props.rowHeight
      const totalItems = props.dataSource.length

      const startIndex = Math.floor(scrollTop.value / itemHeight)
      const visibleCount = Math.ceil(containerHeight / itemHeight)
      const endIndex = Math.min(startIndex + visibleCount + 5, totalItems) // 预加载5行

      visibleRange.value = {
        start: Math.max(0, startIndex - 5), // 预加载5行
        end: endIndex
      }
    }

    // 处理搜索
    const handleSearch = () => {
      emit('search')
    }

    // 处理重置
    const handleReset = () => {
      emit('reset')
    }

    // 处理表格变化
    const handleTableChange = (pagination, filters, sorter, extra) => {
      emit('change', { pagination, filters, sorter, extra })
    }

    // 监听数据变化，重新计算可见范围
    watch(() => props.dataSource, () => {
      calculateVisibleRange()
    }, { immediate: true })

    return {
      displayDataSource,
      scrollConfig,
      paginationConfig,
      handleSearch,
      handleReset,
      handleTableChange
    }
  }
}
</script>

<style scoped>
.data-table {
  background: transparent;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filter-actions {
  margin-top: 16px;
  text-align: right;
}

.action-section {
  margin-bottom: 16px;
  padding: 16px 0;
}

.table-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.ant-table) {
  background: transparent;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  border-bottom: 2px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f5f5f5;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f8faff;
}

:deep(.ant-pagination) {
  margin-top: 24px;
  text-align: right;
}
</style>
