/**
 * 采购管理系统 - 前端应用入口文件
 *
 * 功能说明：
 * 1. 初始化Vue 3应用实例
 * 2. 配置路由系统、状态管理和UI组件库
 * 3. 引入全局样式和主题配置
 * 4. 注册全局组件和错误处理器
 * 5. 挂载应用到DOM节点
 *
 * 技术栈：
 * - Vue 3 (Composition API)
 * - Vue Router 4 (路由管理)
 * - Vuex 4 (状态管理)
 * - Ant Design Vue (UI组件库)
 * - 自定义全局样式和组件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-08
 */
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'      // Ant Design 样式重置
import '@/assets/styles/variables.css'      // CSS变量定义
import '@/styles/unified-table.css'         // 统一表格样式
import '@/styles/filter-common.css'         // 统一筛选样式
import GlobalComponents from '@/components'  // 全局组件注册
import ErrorHandler from '@/components/ErrorHandler.vue'
import PermissionButton from '@/components/Permission/PermissionButton.vue'
import PermissionWrapper from '@/components/Permission/PermissionWrapper.vue'
import permissionDirective from '@/directives/permission.js'

// 国际化配置
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

// 设置dayjs为中文
dayjs.locale('zh-cn')

// ==================== 全局 Chunk 错误处理 ====================
// 处理 webpack 动态导入失败的情况
window.addEventListener('error', (event) => {
  if (event.error && event.error.name === 'ChunkLoadError') {
    // 清除可能的缓存
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => caches.delete(name));
      });
    }

    // 重新加载页面
    window.location.reload();
  }
});

// 处理 Promise 中未捕获的 ChunkLoadError
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.name === 'ChunkLoadError') {
    event.preventDefault(); // 阻止默认的错误处理

    // 重新加载页面
    window.location.reload();
  }
});

// 创建Vue应用实例
const app = createApp(App)

// 使用Ant Design Vue
app.use(Antd)

// ==================== 全局错误处理配置 ====================

// Vue应用级错误处理器
app.config.errorHandler = (err) => {
  // 忽略 ResizeObserver 相关错误（浏览器兼容性问题）
  if (err.message && err.message.includes('ResizeObserver loop completed')) {
    return false;
  }

  // 处理 ChunkLoadError
  if (err.name === 'ChunkLoadError') {
    const { message } = require('ant-design-vue');
    message.error('页面加载失败，正在重新加载...', 2);

    // 延迟重新加载，给用户看到错误提示的时间
    setTimeout(() => {
      window.location.reload();
    }, 2000);

    return false;
  }

  // 返回 false 阻止错误继续传播
  return false;
};

// 全局错误处理函数
app.config.globalProperties.$handleApiError = function (error, defaultMessage = '请求失败') {
  const { message } = require('ant-design-vue');
  let messageContent = defaultMessage;

  if (error.response) {
    switch (error.response.status) {
      case 400:
        messageContent = '请求参数错误';
        break;
      case 401:
        messageContent = '未授权，请重新登录';
        router.push('/login');
        break;
      case 403:
        messageContent = '没有访问权限';
        break;
      case 404:
        messageContent = '请求资源不存在';
        break;
      case 500:
        messageContent = '服务器内部错误';
        break;
      default:
        messageContent = defaultMessage;
    }
  } else if (error.request) {
    messageContent = '网络异常，请检查您的网络连接';
  } else {
    messageContent = '未知错误';
  }

  message.error(messageContent);
  return Promise.reject(error);
};

// 全局组件注册
app.use(GlobalComponents);
app.component('ErrorHandler', ErrorHandler);

// 权限组件注册
app.component('PermissionButton', PermissionButton);
app.component('PermissionWrapper', PermissionWrapper);

// 权限指令注册
app.directive('permission', permissionDirective);

// 挂载应用
app.use(store).use(router).mount('#app');