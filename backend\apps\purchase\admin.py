from django.contrib import admin
from .models import PurchaseRequest, Dictionary


@admin.register(PurchaseRequest)
class PurchaseRequestAdmin(admin.ModelAdmin):
    """采购需求管理后台（整合验收和报销功能）"""
    list_display = [
        'id', 'item_name', 'quantity', 'unit_price', 'total_amount',
        'hierarchy_path', 'status', 'requester', 'submission_date'
    ]
    list_filter = ['status', 'item_category', 'procurement_method', 'submission_date']
    search_fields = ['item_name', 'specification', 'hierarchy_path']
    ordering = ['-created_at']
    readonly_fields = ['total_amount', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('item_category', 'item_name', 'specification', 'unit', 'quantity', 'unit_price', 'total_amount')
        }),
        ('部门信息', {
            'fields': ('dept_id', 'hierarchy_path')
        }),
        ('采购信息', {
            'fields': ('procurement_method', 'requirement_source', 'fund_project_name', 'remarks')
        }),
        ('状态信息', {
            'fields': ('status', 'submission_date', 'requester')
        }),
        ('审批信息', {
            'fields': ('final_approver_id', 'final_approval_date', 'final_approval_comment')
        }),
        ('采购信息', {
            'fields': ('purchaser', 'purchase_date', 'shipping_date')
        }),
        ('验收信息', {
            'fields': ('acceptor', 'acceptance_date', 'courier_company', 'tracking_number',
                      'actual_quantity', 'quantity_difference', 'difference_rate',
                      'has_exception', 'exception_reason')
        }),
        ('报销信息', {
            'fields': ('reimburser', 'reimbursement_date', 'reimbursement_voucher_no',
                      'financial_serial_no', 'internal_approval', 'reimbursement_remarks')
        }),
        ('历史数据', {
            'fields': ('history_purchase_count', 'history_avg_price')
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(Dictionary)
class DictionaryAdmin(admin.ModelAdmin):
    """数据字典管理后台"""
    list_display = ['id', 'type_code', 'type_name', 'code', 'name', 'order', 'status', 'editable']
    list_filter = ['type_code', 'status', 'editable']
    search_fields = ['code', 'name']
    ordering = ['type_code', 'order']
