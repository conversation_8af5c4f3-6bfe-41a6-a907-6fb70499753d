<template>
  <div>
    <div class="page-container">
      <!-- 页面标题区域 -->
      <div class="page-header business-card">
        <div class="header-content">
          <h1 class="page-title">
            <ShoppingCartOutlined />
            需求提报
          </h1>
          <p class="page-subtitle">提交采购需求，管理需求状态，跟踪审批流程</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-number">{{ totalCount }}</span>
            <span class="stat-label">需求总数</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ pendingCount }}</span>
            <span class="stat-label">待审批</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ approvedCount }}</span>
            <span class="stat-label">已审批</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ rejectedCount }}</span>
            <span class="stat-label">已拒绝</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ departmentCount }}</span>
            <span class="stat-label">涉及部门</span>
          </div>
        </div>
      </div>

      <!-- 按需求单位分组的表格 -->
      <div class="department-tables business-card">
        <!-- 表格区域 -->
        <div class="table-section">
          <!-- 表格标题和按钮区域 - 使用flex布局，两端对齐 -->
          <div class="table-header-flex">
            <div class="table-title-section">
              <div class="table-title">
                <span>需求提报列表</span>
                <span class="table-subtitle">共 {{ totalCount }} 条记录</span>
              </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="table-actions">
              <a-space :size="8">
                <!-- 主要业务操作按钮 -->
                <PermissionButton permission="purchase:request:add" type="primary" @click="createRequest" size="small" class="compact-action-btn">
                  <PlusOutlined />
                  新建需求
                </PermissionButton>
                <PermissionButton permission="purchase:request:create" @click="showImportModal = true" size="small" class="compact-action-btn">
                  <UploadOutlined />
                  批量导入
                </PermissionButton>
                <PermissionButton v-if="selectedRowKeys.length > 0 && hasDraftRequests"
                  permission="purchase:request:submit" type="primary" @click="batchSubmit"
                  :disabled="selectedRowKeys.length === 0" size="small" class="compact-action-btn"
                  style="background: #1890ff; border-color: #1890ff">
                  <UploadOutlined />
                  批量提交 ({{ selectedRowKeys.length }})
                </PermissionButton>
                <PermissionButton v-if="selectedRowKeys.length > 0 && hasApprovedRequests"
                  permission="purchase:request:apply_purchase" type="primary"
                  @click="batchApplyPurchase" :disabled="selectedRowKeys.length === 0" size="small"
                  class="compact-action-btn" style="background: #52c41a; border-color: #52c41a">
                  <ShoppingCartOutlined />
                  申请采购 ({{ selectedRowKeys.length }})
                </PermissionButton>

                <!-- 分隔线 -->
                <a-divider type="vertical" />

                <!-- 筛选和视图操作按钮 -->
                <a-button @click="toggleFilters" size="small" class="compact-action-btn">
                  <template #icon>
                    <component :is="showFilters ? 'UpOutlined' : 'DownOutlined'" />
                  </template>
                  {{ showFilters ? "收起筛选" : "展开筛选" }}
                </a-button>
                <a-button @click="resetFilters" size="small" class="compact-action-btn">重置筛选</a-button>
                <PermissionButton permission="purchase:request:export" @click="showExportModal = true"
                  size="small" class="compact-action-btn">
                  <DownloadOutlined />
                  导出
                </PermissionButton>
                <PermissionButton permission="purchase:request:print" @click="showPrintModal = true"
                  size="small" class="compact-action-btn" type="primary">
                  <PrinterOutlined />
                  打印
                </PermissionButton>
                <a-button size="small" class="compact-action-btn" @click="showColumnFilter = true">
                  <template #icon>
                    <SettingOutlined />
                  </template>
                  字段筛选
                  <a-badge :count="selectedColumns.length" :number-style="{
                    backgroundColor: '#52c41a',
                    fontSize: '10px',
                  }" />
                </a-button>

                <!-- 字段筛选模态框 -->
                <a-modal v-model:open="showColumnFilter" title="字段筛选配置" :footer="null" width="auto" :centered="true"
                  :mask-closable="true" :destroy-on-close="true" wrap-class-name="column-filter-modal">
                  <template #closeIcon>
                    <CloseOutlined />
                  </template>
                  <div class="column-filter-panel">
                    <div class="preset-section">
                      <a-dropdown v-model:open="presetDropdownOpen" :trigger="['click']" placement="bottomRight"
                        :overlay-style="{ zIndex: 9999 }" @click.stop>
                        <a-button class="preset-trigger" @click.stop size="large">
                          <template #icon>
                            <SettingOutlined />
                          </template>
                          预设配置
                          <DownOutlined />
                        </a-button>
                        <template #overlay>
                          <a-menu @click="handlePresetClick" @click.stop>
                            <template v-for="item in presetMenuItems" :key="item.key || 'divider'">
                              <a-menu-divider v-if="item.type === 'divider'" />
                              <a-menu-item v-else :key="item.key">{{
                                item.title
                                }}</a-menu-item>
                            </template>
                          </a-menu>
                        </template>
                      </a-dropdown>
                      <div>
                        <a-button type="primary" size="middle" @click="handleSelectAll"
                          style="margin-left: 12px">全选</a-button>
                        <a-button size="middle" @click="handleReset" style="margin-left: 8px">重置</a-button>
                      </div>
                    </div>

                    <div class="filter-tip">
                      <span>已选择 {{ selectedColumns.length }} /
                        {{ columnOptions.length }} 个字段</span>
                    </div>

                    <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnChange">
                      <!-- 动态字段分类 - 使用flex横向布局 -->
                      <div class="field-categories-container">
                        <div v-for="category in fieldCategories" :key="category.key" class="field-category-section">
                          <h5 class="category-title">{{ category.title }}</h5>
                          <div class="category-fields">
                            <div v-for="option in columnOptions.filter(
                              (opt) => opt.category === category.key
                            )" :key="option.key" class="column-option">
                              <a-checkbox :value="option.key" :disabled="option.required" @click.stop>
                                <span class="column-title">{{
                                  option.title
                                  }}</span>
                                <a-tag v-if="option.required" size="small" color="blue">必选</a-tag>
                              </a-checkbox>
                            </div>
                          </div>
                        </div>
                      </div>
                    </a-checkbox-group>
                  </div>
                </a-modal>
              </a-space>
            </div>
          </div>

          <!-- 详细筛选控件区域 - 分两行布局，与页面同宽 -->
          <div v-show="showFilters" class="detailed-filters-fullwidth">
            <!-- 第一行：基础筛选项 -->
            <div class="filter-row">
              <div class="filter-grid">
                <div class="filter-item">
                  <a-select v-model:value="filters.status" placeholder="状态" allowClear @change="handleFilterChange"
                    class="filter-select">
                    <a-select-option value="">全部状态</a-select-option>
                    <a-select-option v-for="option in getDictOptions('status')" :key="option.value"
                      :value="option.value">
                      {{ option.label }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="filter-item">
                  <a-select v-model:value="filters.purchaseType" placeholder="采购类型" allowClear
                    @change="handleFilterChange" class="filter-select">
                    <a-select-option value="">全部类型</a-select-option>
                    <a-select-option v-for="type in purchaseTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="filter-item">
                  <a-select v-model:value="filters.department" placeholder="需求单位" allowClear
                    @change="handleFilterChange" class="filter-select" show-search :filter-option="filterOption">
                    <a-select-option value="">全部需求单位</a-select-option>
                    <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                      {{ dept.dept_name }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="filter-item">
                  <a-select v-model:value="filters.requester" placeholder="申请人" allowClear @change="handleFilterChange"
                    class="filter-select" show-search :filter-option="filterOption">
                    <a-select-option value="">全部申请人</a-select-option>
                    <a-select-option v-for="user in users" :key="user.id" :value="user.id">
                      {{ user.username }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.minAmount" placeholder="最小金额" @input="debouncedFilterChange"
                    class="filter-input" type="number" :min="0">
                    <template #prefix> ¥ </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.maxAmount" placeholder="最大金额" @input="debouncedFilterChange"
                    class="filter-input" type="number" :min="0">
                    <template #prefix> ¥ </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-range-picker v-model:value="filters.dateRange" :placeholder="['开始时间', '结束时间']"
                    @change="handleFilterChange" class="filter-date-picker" />
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.remarks" placeholder="需求备注" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
              </div>
            </div>

            <!-- 第二行：搜索和其他筛选项 -->
            <div class="filter-row">
              <div class="filter-grid">
                <div class="filter-item">
                  <a-input v-model:value="filters.id" placeholder="ID" @input="debouncedFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.itemName" placeholder="搜索物品名称" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.specification" placeholder="搜索规格型号" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.procurementMethod" placeholder="采购方式" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.requirementSource" placeholder="需求来源" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.fundProject" placeholder="经费项目" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.unit" placeholder="计量单位" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
                <div class="filter-item">
                  <a-input v-model:value="filters.itemCategory" placeholder="物品种类" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="table-container">
          <a-table :columns="filteredColumns" :data-source="requests" :row-key="(record) => record.id" :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: (page, pageSize) =>
              handleTableChange({ current: page, pageSize }),
            onShowSizeChange: (current, size) =>
              handleTableChange({ current, pageSize: size }),
          }" :loading="loading" :row-selection="rowSelection" :scroll="{ x: 1400 }" :virtual="false" :row-height="54"
            bordered class="unified-table">
            <!-- 自定义空状态 -->
            <template #emptyText>
              <div class="table-empty-state">
                <a-empty description="暂无采购需求数据" />
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <!-- 状态标签可视化 -->
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getStatusColor(record.status)">{{
                  record.status_display || record.status
                  }}</a-tag>
              </template>

              <!-- 单价预警显示 -->
              <template v-if="column.dataIndex === 'unit_price'">
                <span :style="record.is_price_warning
                    ? { color: '#ff4d4f', fontWeight: 'bold' }
                    : {}
                  ">
                  ¥{{ record.unit_price }}
                  <a-tooltip v-if="record.is_price_warning" title="价格超过历史均价20%">
                    <ExclamationCircleOutlined style="color: #ff4d4f; margin-left: 4px" />
                  </a-tooltip>
                </span>
              </template>

              <!-- 金额显示 -->
              <template v-if="column.dataIndex === 'total_amount'">
                ¥{{ record.total_amount }}
              </template>

              <!-- 操作列 -->
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="viewDetail(record)" class="action-btn">详情</a-button>
                  <PermissionButton permission="purchase:request:edit" type="link" size="small" @click="editRequest(record)" v-if="
                    ['draft', 'rejected', 'returned'].includes(
                      record._originalStatus || record.status
                    )
                  " class="action-btn">
                    编辑
                  </PermissionButton>
                  <PermissionButton permission="purchase:request:submit" type="link" size="small" @click="submitRequest(record)"
                    v-if="(record._originalStatus || record.status) === 'draft'" class="action-btn">
                    提交
                  </PermissionButton>

                  <PermissionButton permission="purchase:request:delete" type="link" size="small" danger @click="deleteRequest(record)"
                    v-if="(record._originalStatus || record.status) === 'draft'" class="action-btn danger">删除</PermissionButton>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <!-- 批量导入对话框 -->
    <a-modal v-model:open="showImportModal" title="批量导入采购需求" width="800px" :footer="null" :destroy-on-close="true">
      <BatchImport import-type="purchase_requests" template-url="/api/requests/template/"
        import-url="/api/requests/import/" @import-success="handleImportSuccess" />
    </a-modal>

    <!-- 打印配置对话框 -->
    <RequestPrintConfig :visible="showPrintModal" @cancel="showPrintModal = false"
      @print-complete="handlePrintComplete" />



    <!-- 导出配置对话框 -->
    <a-modal v-model:open="showExportModal" title="导出Excel配置" width="80%" :footer="null" :destroy-on-close="true">
      <div class="export-config-container">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <h4>筛选条件</h4>
          <a-form :model="exportFilters">
            <a-row :gutter="[12, 8]" align="bottom">
              <a-col :span="4">
                <a-form-item label="状态">
                  <a-select v-model:value="exportFilters.status" placeholder="选择状态" allowClear mode="multiple"
                    size="middle">
                    <a-select-option v-for="status in getDictOptions('status')" :key="status.value"
                      :value="status.value">
                      {{ status.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="需求单位">
                  <a-select v-model:value="exportFilters.department" placeholder="选择需求单位" allowClear show-search
                    size="middle">
                    <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                      {{ dept.dept_name || dept.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="采购方式">
                  <a-select v-model:value="exportFilters.purchase_type" placeholder="选择采购方式" allowClear size="middle">
                    <a-select-option value="self">自行采购</a-select-option>
                    <a-select-option value="centralized">集中采购</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="日期范围">
                  <a-range-picker v-model:value="exportFilters.dateRange" size="middle" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item>
                  <a-space>
                    <a-button @click="searchExportRecords" type="primary" size="middle">
                      <SearchOutlined />
                      查询
                    </a-button>
                    <a-button @click="resetExportFilters" size="middle">
                      <ReloadOutlined />
                      重置
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>

      <!-- 导出字段配置 -->
      <div class="fields-section">
        <a-collapse v-model:activeKey="fieldsCollapseKey" size="small">
          <a-collapse-panel key="1" header="导出字段配置">
            <template #extra>
              <a-space>
                <a-button @click.stop="selectAllExportFields" size="small">全选</a-button>
                <a-button @click.stop="resetExportFields" size="small">重置</a-button>
                <a-button @click.stop="selectRequiredExportFields" size="small">仅必选</a-button>
              </a-space>
            </template>
            <a-checkbox-group v-model:value="selectedExportFields" @change="onExportFieldsChange">
              <div class="export-fields-container">
                <div v-for="category in exportFieldCategories" :key="category.key" class="export-field-category">
                  <div class="export-category-header">
                    <h5 class="export-category-title">{{ category.title }}</h5>
                  </div>
                  <div class="export-category-fields">
                    <div v-for="field in exportFieldOptions.filter(
                      (opt) => opt.category === category.key
                    )" :key="field.value" class="export-field-option">
                      <a-checkbox :value="field.value" :disabled="field.required">
                        <span class="field-title">{{ field.label }}</span>
                        <a-tag v-if="field.required" size="small" color="blue">必选</a-tag>
                      </a-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </a-checkbox-group>
          </a-collapse-panel>
        </a-collapse>
      </div>

      <!-- 选择导出记录 -->
      <div class="records-section">
        <div class="section-header" style="display: flex; justify-content: space-between">
          <h4>选择导出记录</h4>
          <div class="section-actions">
            <span class="record-count">共 {{ exportRecords.length }} 条记录，已选择
              {{ selectedExportRecords.length }}条</span>
          </div>
        </div>

        <a-table :columns="exportRecordColumns" :data-source="exportRecords" :row-selection="exportRecordRowSelection"
          :pagination="{
            current: exportRecordPagination.current,
            pageSize: exportRecordPageSize,
            total: exportRecordPagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: onExportRecordPageChange,
            onShowSizeChange: onExportRecordPageSizeChange,
          }" :loading="exportSearchLoading" size="small" :scroll="{ x: 'max-content' }" row-key="id" bordered>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'purchase_type'">
              {{ record.purchase_type_display || record.purchase_type || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'procurement_method'">
              {{ record.procurement_method_display || record.procurement_method || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'requirement_source'">
              {{ record.requirement_source || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'fund_project'">
              {{ record.fund_project_display || record.fund_project || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'item_category'">
              {{ record.item_category_display || record.item_category || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'unit'">
              {{ record.unit_display || record.unit || '-' }}
            </template>
            <template v-else-if="
              [
                'budget_total_amount',
                'budget_unit_price',
                'purchase_total_amount',
                'purchase_unit_price',
              ].includes(column.dataIndex)
            ">
              ¥{{
                record[column.dataIndex] &&
                  !isNaN(parseFloat(record[column.dataIndex]))
                  ? parseFloat(record[column.dataIndex]).toFixed(2)
                  : "0.00"
              }}
            </template>
            <template v-else-if="
              [
                'budget_quantity',
                'purchase_quantity',
                'acceptance_quantity',
              ].includes(column.dataIndex)
            ">
              {{ record[column.dataIndex] || 0 }}
            </template>
            <template v-else-if="
              [
                'created_at',
                'updated_at',
                'purchase_date',
                'acceptance_date',
                'approved_at',
                'submission_date',
              ].includes(column.dataIndex)
            ">
              {{
                record[column.dataIndex]
                  ? formatDateToYMD(record[column.dataIndex])
                  : "-"
              }}
            </template>
            <template v-else>
              {{ record[column.dataIndex] || "-" }}
            </template>
          </template>
        </a-table>
      </div>

      <!-- 导出预览和操作 -->
      <div class="actions-section">
        <a-space>
          <a-button @click="previewExportData" :disabled="selectedExportRecords.length === 0">
            <EyeOutlined />
            预览 ({{ selectedExportRecords.length }}条)
          </a-button>
          <a-button type="primary" @click="executeExport" :disabled="selectedExportRecords.length === 0"
            :loading="exportLoading">
            <DownloadOutlined />
            导出Excel ({{ selectedExportRecords.length }}条)
          </a-button>
          <a-button @click="handleExportCancel">取消</a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 预览模态框 -->
    <a-modal v-model:open="showPreviewModal" title="导出预览" width="90%" :footer="null" :centered="true"
      :mask-closable="true" :destroy-on-close="true" wrap-class-name="preview-modal-wrapper"
      :body-style="{ height: '80vh', overflow: 'auto' }">
      <div class="preview-container">
        <div class="preview-header">
          <a-space>
            <span>预览数据 (共 {{ previewTotal }} 条记录)</span>
          </a-space>
        </div>

        <a-table :columns="previewColumns" :data-source="previewData" :loading="previewLoading" :pagination="{
          total: previewTotal,
          pageSize: 50,
          showSizeChanger: fasle,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }" size="small" :scroll="{ x: 1200, y: 'calc(80vh - 100px)' }" row-key="id" bordered>
          <template #bodyCell="{ column, record }">
            <!-- 状态列显示 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status_display || record.status }}
              </a-tag>
            </template>
            <!-- 采购方式列显示 -->
            <template v-else-if="column.dataIndex === 'procurement_method'">
              {{
                record.procurement_method_display || record.procurement_method
              }}
            </template>
            <!-- 采购类型列显示 -->
            <template v-else-if="column.dataIndex === 'purchase_type'">
              {{ record.purchase_type_display || record.purchase_type }}
            </template>
            <!-- 需求来源列显示 -->
            <template v-else-if="column.dataIndex === 'requirement_source'">
              {{ record.requirement_source || "-" }}
            </template>
            <!-- 经费项目列显示 -->
            <template v-else-if="column.dataIndex === 'fund_project'">
              {{ record.fund_project_display || record.fund_project }}
            </template>
            <!-- 物品种类列显示 -->
            <template v-else-if="column.dataIndex === 'item_category'">
              {{ record.item_category_display || record.item_category }}
            </template>
            <!-- 计量单位列字典转换 -->
            <template v-else-if="column.dataIndex === 'unit'">
              {{ record.unit_display || record.unit || '-' }}
            </template>
            <!-- 金额字段格式化 -->
            <template v-else-if="
              [
                'budget_total_amount',
                'budget_unit_price',
                'purchase_total_amount',
                'purchase_unit_price',
              ].includes(column.dataIndex)
            ">
              ¥{{
                record[column.dataIndex] &&
                  !isNaN(parseFloat(record[column.dataIndex]))
                  ? parseFloat(record[column.dataIndex]).toFixed(2)
                  : "0.00"
              }}
            </template>
            <!-- 数量字段格式化 -->
            <template v-else-if="
              [
                'budget_quantity',
                'purchase_quantity',
                'acceptance_quantity',
              ].includes(column.dataIndex)
            ">
              {{ record[column.dataIndex] || 0 }}
            </template>
            <!-- 日期字段格式化 -->
            <template v-else-if="
              [
                'created_at',
                'updated_at',
                'purchase_date',
                'acceptance_date',
                'approved_at',
                'submission_date',
              ].includes(column.dataIndex)
            ">
              {{
                record[column.dataIndex]
                  ? formatDateToYMD(record[column.dataIndex])
                  : "-"
              }}
            </template>
            <!-- 默认显示 -->
            <template v-else>
              {{ record[column.dataIndex] || "-" }}
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { message, Modal } from "ant-design-vue";
import { debounce } from "@/utils/debounce";
import { useDictMixin, PAGE_DICT_TYPES } from "@/mixins/dictMixin";
import {
  ExclamationCircleOutlined,
  PlusOutlined,
  DownloadOutlined,
  UploadOutlined,
  ShoppingCartOutlined,
  ReloadOutlined,
  SettingOutlined,
  DownOutlined,
  UpOutlined,
  SearchOutlined,
  PrinterOutlined,
  CloseOutlined,
  EyeOutlined,
} from "@ant-design/icons-vue";
import BatchImport from "@/components/Import/BatchImport.vue";
import RequestPrintConfig from "@/components/Print/RequestPrintConfig.vue";
import PermissionButton from "@/components/Permission/PermissionButton.vue";

import api from "@/api";
// import dictConverter from "@/utils/dictConverter"; // 不再需要，使用后端提供的display字段
// // import { getStatusConfig } from '@/utils/status' // 暂时未使用 // 暂时未使用
import {
  getPageColumnOptions,
  getPageColumnPresets,
  getPageFieldCategories,
  getPagePresetMenuItems,
  getPageExportFieldOptions,
  getPageDefaultExportFields,
  getPageDefaultColumns,
  sortColumnsByOrder,
} from "@/utils/validation";

export default {
  name: "PurchaseRequestListView",
  components: {
    ExclamationCircleOutlined,
    PlusOutlined,
    DownloadOutlined,
    UploadOutlined,
    ShoppingCartOutlined,
    ReloadOutlined,
    SettingOutlined,
    DownOutlined,
    UpOutlined,
    SearchOutlined,
    PrinterOutlined,
    CloseOutlined,
    EyeOutlined,
    BatchImport,
    RequestPrintConfig,
    PermissionButton,

  },
  setup() {
    const router = useRouter();
    const route = useRoute();

    // 使用字典混入 - 只保留必要的功能
    const { getDictOptions, getStatusColor } = useDictMixin(
      PAGE_DICT_TYPES.REQUIREMENT
    );

    const loading = ref(false);
    const showImportModal = ref(false);
    const showExportModal = ref(false);
    const showPrintModal = ref(false);
    const selectedRowKeys = ref([]);

    // 导出相关状态
    const exportActiveTab = ref("fields");
    const exportLoading = ref(false);
    const previewLoading = ref(false);
    const previewData = ref([]);
    const previewTotal = ref(0);
    const showPreviewModal = ref(false);

    const fieldsCollapseKey = ref([]); // 默认折叠状态

    // 基于业务流程的导出字段配置（需求提报阶段）
    const exportFieldOptions = getPageExportFieldOptions("requirement");
    const exportFieldCategories = getPageFieldCategories("requirement");
    const selectedExportFields = ref(getPageDefaultExportFields("requirement"));
    const exportFieldsCheckAll = ref(false);
    const exportFieldsIndeterminate = ref(true);

    // 导出筛选条件
    const exportFilters = reactive({
      status: [],
      department: "", // 单选部门
      itemName: "",
      requester: "",
      minAmount: "",
      maxAmount: "",
      dateRange: [],
      // 新增导出筛选字段
      procurementMethod: "",
      requirementSource: "",
      fundProject: "",
      unit: "",
      itemCategory: "",
      remarks: "",
    });

    // 导出记录选择相关
    const exportRecords = ref([]);
    const selectedExportRecords = ref([]);
    const selectedExportRecordData = ref([]);
    const exportSearchLoading = ref(false);
    const exportRecordPageSize = ref(20);
    const exportRecordPagination = ref({
      current: 1,
      total: 0,
    });

    const statusOptions = [
      { label: "草稿", value: "draft" },
      { label: "待审批", value: "pending_approval" },
      { label: "已审批", value: "approved" },
      { label: "已驳回", value: "rejected" },
      { label: "已退回", value: "returned" },
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) =>
        `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
      pageSizeOptions: ["10", "20", "50", "100"],
    });

    // 采购需求数据
    const requests = ref([]);

    // 部门列表
    const departments = ref([]);

    // 筛选选项数据源 - 使用字典服务
    const purchaseTypes = computed(() => getDictOptions("purchase_type"));
    const applicants = ref([]);
    const users = ref([]); // 添加users定义

    // 筛选控件显示状态
    const showFilters = ref(false);
    const showColumnFilter = ref(false);
    const presetDropdownOpen = ref(false);

    // 筛选条件
    const filters = reactive({
      status: "",
      id: "",
      itemName: "",
      specification: "",
      department: "",
      applicant: "",
      purchaseType: "",
      minAmount: "",
      maxAmount: "",
      dateRange: [],
      // 新增缺失的筛选字段
      procurementMethod: "", // 采购方式
      requirementSource: "", // 需求来源
      fundProject: "", // 经费项目
      unit: "", // 计量单位
      itemCategory: "", // 物品种类
      remarks: "", // 需求备注
    });

    // 从本地存储读取字段配置
    const getStoredColumns = () => {
      const COLUMNS_VERSION = "2.0"; // 版本号，更新时清除旧缓存
      const STORAGE_KEY = `purchase-requests-columns-v${COLUMNS_VERSION}`;

      try {
        // 清除旧版本的缓存
        const oldKeys = ["purchase-requests-columns"];
        oldKeys.forEach((key) => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
          }
        });

        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const parsed = JSON.parse(stored);
          // 确保必选字段始终被包含
          const requiredColumns = columnOptions
            .filter((opt) => opt.required)
            .map((opt) => opt.key);
          return [...new Set([...parsed, ...requiredColumns])];
        }
      } catch (error) {
        console.warn("读取字段配置失败:", error);
      }
      // 使用默认字段配置（按顺序排列）
      const defaultColumns = getPageDefaultColumns("requirement");
      return defaultColumns; // 默认配置已包含action列
    };

    // 保存字段配置到本地存储
    const saveColumnsToStorage = (columns) => {
      const COLUMNS_VERSION = "2.0";
      const STORAGE_KEY = `purchase-requests-columns-v${COLUMNS_VERSION}`;
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(columns));
      } catch (error) {
        console.warn("保存字段配置失败:", error);
      }
    };

    // 基于业务流程的字段筛选配置（需求提报阶段，排除操作列）
    const columnOptions = getPageColumnOptions("requirement").filter(
      (opt) => opt.key !== "action"
    );

    // 初始化选中的字段
    const selectedColumns = ref(getStoredColumns());

    // 基于业务流程的字段分类配置（需求提报阶段）
    const fieldCategories = getPageFieldCategories("requirement");

    // 基于业务流程的预设菜单选项（需求提报阶段）
    const presetMenuItems = getPagePresetMenuItems("requirement");

    // 基于业务流程的预设配置（需求提报阶段）
    const columnPresets = getPageColumnPresets("requirement");

    // 行选择配置 - 允许选择草稿、已退回、已驳回状态进行批量提交，允许选择已审批状态进行申请采购
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys;
      },
      getCheckboxProps: (record) => ({
        disabled: !["draft", "returned", "rejected", "approved"].includes(
          record._originalStatus || record.status
        ),
        name: record.item_name,
      }),
    };

    // 计算属性
    const totalCount = computed(() => {
      return pagination.total;
    });

    const pendingCount = computed(() => {
      return requests.value.filter(
        (item) => (item._originalStatus || item.status) === "pending_approval"
      ).length;
    });

    const approvedCount = computed(() => {
      return requests.value.filter(
        (item) => (item._originalStatus || item.status) === "approved"
      ).length;
    });

    const rejectedCount = computed(() => {
      return requests.value.filter(
        (item) => (item._originalStatus || item.status) === "rejected"
      ).length;
    });

    const departmentCount = computed(() => {
      const departments = new Set(
        requests.value.map((item) => {
          let hierarchyPath = item.hierarchy_path || "未知单位";

          // 清理和标准化层级路径
          hierarchyPath = hierarchyPath.trim();
          if (!hierarchyPath || hierarchyPath === "") {
            return "未知单位";
          }

          // 只统计一级单位（分公司）
          let firstLevelDept =
            hierarchyPath.split("-")[0]?.trim() || "未知单位";
          if (!firstLevelDept || firstLevelDept === "") {
            firstLevelDept = "未知单位";
          }

          return firstLevelDept;
        })
      );
      return departments.size;
    });

    // 判断选中的记录是否全部为草稿状态
    const hasDraftRequests = computed(() => {
      if (selectedRowKeys.value.length === 0) return false;
      return selectedRowKeys.value.every((id) => {
        const record = requests.value.find((r) => r.id === id);
        return record && (record._originalStatus || record.status) === "draft";
      });
    });

    // 判断选中的记录是否全部为已审批状态
    const hasApprovedRequests = computed(() => {
      if (selectedRowKeys.value.length === 0) return false;
      return selectedRowKeys.value.every((id) => {
        const record = requests.value.find((r) => r.id === id);
        return (
          record && (record._originalStatus || record.status) === "approved"
        );
      });
    });

    // 判断选中的记录是否全部为待审批状态
    const hasPendingRequests = computed(() => {
      if (selectedRowKeys.value.length === 0) return false;
      return selectedRowKeys.value.every((id) => {
        const record = requests.value.find((r) => r.id === id);
        return (
          record &&
          (record._originalStatus || record.status) === "pending_approval"
        );
      });
    });

    // 导出记录表格列 - 根据选中字段动态生成
    const exportRecordColumns = computed(() => {
      // 根据选中字段生成动态列
      const dynamicColumns = selectedExportFields.value
        .map((fieldKey) => {
          const field = exportFieldOptions.find((f) => f.value === fieldKey);

          const column = {
            title: field ? field.label : fieldKey,
            dataIndex: fieldKey,
            width: getExportColumnWidth(fieldKey),
            ellipsis: true,
            align: "center", // 文字居中
          };

          // 添加特殊字段的自定义渲染
          if (fieldKey === "status") {
            column.customRender = ({ record }) =>
              record.status_display || record.status;
          } else if (fieldKey === "purchase_type") {
            column.customRender = ({ record }) =>
              record.purchase_type_display || record.purchase_type;
          } else if (fieldKey === "procurement_method") {
            column.customRender = ({ record }) =>
              record.procurement_method_display || record.procurement_method;
          } else if (fieldKey === "requirement_source") {
            column.customRender = ({ record }) =>
              record.requirement_source || "-";
          } else if (fieldKey === "fund_project") {
            column.customRender = ({ record }) =>
              record.fund_project_display || record.fund_project;
          } else if (fieldKey === "item_category") {
            column.customRender = ({ record }) =>
              record.item_category_display || record.item_category;
          } else if (fieldKey === "unit") {
            column.customRender = ({ record }) =>
              record.unit_display || record.unit;
          } else if (
            [
              "budget_total_amount",
              "budget_unit_price",
              "purchase_total_amount",
              "purchase_unit_price",
            ].includes(fieldKey)
          ) {
            column.customRender = ({ text }) => {
              return text && !isNaN(parseFloat(text))
                ? `¥${parseFloat(text).toFixed(2)}`
                : "¥0.00";
            };
          } else if (
            [
              "budget_quantity",
              "purchase_quantity",
              "acceptance_quantity",
            ].includes(fieldKey)
          ) {
            column.customRender = ({ text }) => text || 0;
          } else if (
            fieldKey === "created_at" ||
            fieldKey.includes("_date") ||
            fieldKey.includes("_at")
          ) {
            column.customRender = ({ text }) => {
              return text ? formatDateToYMD(text) : "-";
            };
          }

          return column;
        })
        .filter(Boolean);

      return dynamicColumns;
    });

    // 获取导出列宽度 - 设置较宽的默认宽度
    const getExportColumnWidth = (fieldKey) => {
      const widthMap = {
        request_number: 150,
        item_name: 200,
        specification: 180,
        quantity: 120,
        budget_total_amount: 150,
        status: 120,
        dept_name: 180,
        requester_name: 140,
        created_at: 150,
        purchase_type: 140,
        procurement_method: 160,
        requirement_source: 160,
        fund_project: 160,
        unit: 120,
        item_category: 140,
        remarks: 200,
      };
      return widthMap[fieldKey] || 140; // 默认宽度增加到140
    };

    // 导出记录行选择
    const exportRecordRowSelection = computed(() => ({
      selectedRowKeys: selectedExportRecords.value,
      onChange: (selectedRowKeys, selectedRows) => {
        selectedExportRecords.value = [...selectedRowKeys];
        selectedExportRecordData.value = [...selectedRows];
      },
    }));

    // 预览表格列
    const previewColumns = computed(() => {
      return exportFieldOptions
        .filter((field) => selectedExportFields.value.includes(field.value))
        .map((field) => {
          const column = {
            title: field.label,
            dataIndex: field.value,
            key: field.value,
            width: 120,
            ellipsis: true,
            align: "center",
          };

          // 添加特殊字段的自定义渲染
          if (field.value === "status") {
            column.customRender = ({ record }) =>
              record.status_display || record.status;
          } else if (field.value === "purchase_type") {
            column.customRender = ({ record }) =>
              record.purchase_type_display || record.purchase_type;
          } else if (field.value === "procurement_method") {
            column.customRender = ({ record }) =>
              record.procurement_method_display || record.procurement_method;
          } else if (field.value === "requirement_source") {
            column.customRender = ({ record }) =>
              record.requirement_source || "-";
          } else if (field.value === "fund_project") {
            column.customRender = ({ record }) =>
              record.fund_project_display || record.fund_project;
          } else if (field.value === "item_category") {
            column.customRender = ({ record }) =>
              record.item_category_display || record.item_category;
          } else if (field.value === "unit") {
            column.customRender = ({ record }) =>
              record.unit_display || record.unit;
          } else if (
            [
              "budget_total_amount",
              "budget_unit_price",
              "purchase_total_amount",
              "purchase_unit_price",
            ].includes(field.value)
          ) {
            column.customRender = ({ text }) => {
              return text && !isNaN(parseFloat(text))
                ? `¥${parseFloat(text).toFixed(2)}`
                : "¥0.00";
            };
          } else if (
            [
              "budget_quantity",
              "purchase_quantity",
              "acceptance_quantity",
            ].includes(field.value)
          ) {
            column.customRender = ({ text }) => text || 0;
          } else if (
            field.value === "created_at" ||
            field.value.includes("_date") ||
            field.value.includes("_at")
          ) {
            column.customRender = ({ text }) => {
              return text ? formatDateToYMD(text) : "-";
            };
          }

          return column;
        });
    });

    // 所有可用的表格列定义
    const allColumns = [
      // 需求编号列隐藏，但保留筛选功能
      {
        title: "物品种类",
        dataIndex: "item_category",
        key: "item_category",
        width: 140,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "物品名称",
        dataIndex: "item_name",
        key: "item_name",
        width: 180,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "规格型号",
        dataIndex: "specification",
        key: "specification",
        width: 220,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "数量",
        dataIndex: "quantity",
        key: "quantity",
        width: 100,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "预算单价",
        dataIndex: "unit_price",
        key: "unit_price",
        width: 140,
        sorter: true,
        align: "center",
        ellipsis: true,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "预算总金额",
        dataIndex: "total_amount",
        key: "total_amount",
        width: 140,
        sorter: true,
        align: "center",
        ellipsis: true,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "需求单位",
        dataIndex: "hierarchy_path",
        key: "hierarchy_path",
        width: 180,
        sorter: true,
        align: "center",
        ellipsis: true,
        customRender: ({ text }) => {
          // 显示完整的层级路径（分公司-办事处）
          return text || "未知单位";
        },
      },
      {
        title: "采购类型",
        dataIndex: "purchase_type",
        key: "purchase_type",
        width: 120,
        sorter: true,
        align: "center",
        ellipsis: true,
        customRender: ({ text }) => {
          return text || "-";
        },
      },
      {
        title: "状态",
        dataIndex: "status",
        key: "status",
        width: 120,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "创建时间",
        dataIndex: "created_at",
        key: "created_at",
        width: 160,
        sorter: true,
        align: "center",
        ellipsis: true,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : "-";
        },
      },
      {
        title: "备注",
        dataIndex: "remarks",
        key: "remarks",
        width: 220,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      // 审批相关
      {
        title: "申请人",
        dataIndex: "requester_name",
        key: "requester_name",
        width: 120,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "提交时间",
        dataIndex: "submission_date",
        key: "submission_date",
        width: 160,
        sorter: true,
        align: "center",
        ellipsis: true,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : "-";
        },
      },
      {
        title: "审批人",
        dataIndex: "approver_name",
        key: "approver_name",
        width: 120,
        sorter: true,
        align: "center",
        ellipsis: true,
      },
      {
        title: "审批时间",
        dataIndex: "approved_at",
        key: "approved_at",
        width: 150,
        sorter: true,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : "-";
        },
      },
      // 采购相关
      {
        title: "采购人",
        dataIndex: "purchaser_name",
        key: "purchaser_name",
        width: 100,
      },
      {
        title: "采购时间",
        dataIndex: "purchase_date",
        key: "purchase_date",
        width: 150,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : "-";
        },
      },
      {
        title: "实际单价",
        dataIndex: "actual_unit_price",
        key: "actual_unit_price",
        width: 120,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "实际采购总金额",
        dataIndex: "actual_total_amount",
        key: "actual_total_amount",
        width: 120,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "供应商",
        dataIndex: "supplier",
        key: "supplier",
        width: 150,
      },
      // 验收相关
      {
        title: "验收人",
        dataIndex: "acceptor_name",
        key: "acceptor_name",
        width: 100,
      },
      {
        title: "验收时间",
        dataIndex: "acceptance_date",
        key: "acceptance_date",
        width: 150,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : "-";
        },
      },
      {
        title: "实际数量",
        dataIndex: "actual_quantity",
        key: "actual_quantity",
        width: 80,
      },
      // 结算相关
      {
        title: "结算金额",
        dataIndex: "settlement_amount",
        key: "settlement_amount",
        width: 120,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : "-";
        },
      },
      {
        title: "结算人",
        dataIndex: "settler_name",
        key: "settler_name",
        width: 100,
      },
      {
        title: "结算时间",
        dataIndex: "settled_at",
        key: "settled_at",
        width: 150,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : "-";
        },
      },
      {
        title: "交易流水号",
        dataIndex: "transaction_number",
        key: "transaction_number",
        width: 150,
      },
      {
        title: "操作",
        dataIndex: "action",
        key: "action",
        width: 250,
        fixed: "right",
        align: "center",
      },
    ];

    // 动态筛选后的列
    const filteredColumns = computed(() => {
      // 获取非操作列
      const dataColumns = selectedColumns.value
        .map((columnKey) => {
          // 从 allColumns 中找到对应的列定义
          const columnDef = allColumns.find((col) => col.key === columnKey);
          if (columnDef) {
            return columnDef;
          }
          // 如果在 allColumns 中找不到，则创建一个基本的列定义
          const option = columnOptions.find((opt) => opt.key === columnKey);
          if (option) {
            return {
              title: option.title,
              dataIndex: columnKey,
              key: columnKey,
              width: 120,
              ellipsis: true,
              align: "center",
            };
          }
          return null;
        })
        .filter(Boolean); // 过滤掉 null 值

      // 始终在最右侧添加操作列
      const actionColumn = allColumns.find((col) => col.key === "action");
      if (actionColumn) {
        dataColumns.push(actionColumn);
      }

      return dataColumns;
    });

    // 获取筛选选项 - 优化版本
    const getFilterOptions = async () => {
      try {
        const response = await api.dashboard.getFilterOptions();
        if (response.code === 200) {
          const data = response.data;
          departments.value = data.departments || [];
          // 可以在这里设置其他筛选选项
          // users.value = data.users || []
          // itemCategories.value = data.item_categories || []
        }
      } catch (error) {
        console.error("获取筛选选项失败:", error);
        // 降级到原来的方法
        await getDepartmentsLegacy();
      }
    };

    // 保留原来的部门获取方法作为降级方案 - 用于筛选选项，使用专门的无分页接口
    const getDepartmentsLegacy = async () => {
      try {
        const response = await api.departments.getAll();
        if (response.code === 200) {
          departments.value = response.data || [];
        }
      } catch (error) {
        console.error("获取部门列表失败:", error);
      }
    };

    // 获取采购需求列表
    const getPurchaseRequests = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.current,
          page_size: pagination.pageSize,
          // 需求提报页面只显示：草稿、待审批、已审批、已驳回、已退回状态
          status_in: "draft,pending_approval,approved,rejected,returned",
        };

        // 添加筛选参数
        if (filters.status) {
          // 如果有状态筛选，覆盖默认的status_in
          params.status_in = filters.status;
        }
        if (filters.itemName) {
          params.search = filters.itemName;
        }
        if (filters.department) {
          // 根据选择的部门获取部门信息
          const selectedDept = departments.value.find(
            (d) => d.id == filters.department
          );
          if (selectedDept) {
            // 如果是一级部门（没有parent_id），筛选该部门及其所有子部门
            if (!selectedDept.parent_id) {
              // 一级部门：使用部门名称匹配层级路径，确保匹配该部门及其所有子部门
              // 使用精确的匹配模式：要么是完全匹配（一级部门），要么是以"部门名称-"开头（子部门）
              params.hierarchy_path__icontains = selectedDept.dept_name;
            } else {
              // 二级部门：使用层级路径精确匹配
              params.hierarchy_path__icontains = selectedDept.hierarchy_path;
            }
          }
        }
        if (filters.requester) {
          params.requester__username__icontains = filters.requester;
        }
        if (filters.purchaseType) {
          params.purchase_type = filters.purchaseType;
        }
        if (filters.id) {
          const idNum = parseInt(filters.id);
          if (!isNaN(idNum)) {
            params.id = idNum;
          }
        }
        if (filters.minAmount) {
          const minAmount = parseFloat(filters.minAmount);
          if (!isNaN(minAmount)) {
            params.budget_total_amount__gte = minAmount;
          }
        }
        if (filters.maxAmount) {
          const maxAmount = parseFloat(filters.maxAmount);
          if (!isNaN(maxAmount)) {
            params.budget_total_amount__lte = maxAmount;
          }
        }
        if (filters.itemName) {
          params.item_name__icontains = filters.itemName;
        }
        if (filters.specification) {
          params.specification__icontains = filters.specification;
        }
        if (filters.dateRange && filters.dateRange.length === 2) {
          params.created_at__gte = filters.dateRange[0].format("YYYY-MM-DD");
          params.created_at__lte = filters.dateRange[1].format("YYYY-MM-DD");
        }
        // 新增筛选参数
        if (filters.procurementMethod) {
          params.procurement_method__icontains = filters.procurementMethod;
        }
        if (filters.requirementSource) {
          params.requirement_source__icontains = filters.requirementSource;
        }
        if (filters.fundProject) {
          params.fund_project__icontains = filters.fundProject;
        }
        if (filters.unit) {
          params.unit__icontains = filters.unit;
        }
        if (filters.itemCategory) {
          params.item_category__icontains = filters.itemCategory;
        }
        if (filters.remarks) {
          params.remarks__icontains = filters.remarks;
        }

        // 添加排序参数
        if (sortField.value && sortOrder.value) {
          const orderPrefix = sortOrder.value === "descend" ? "-" : "";
          params.ordering = `${orderPrefix}${sortField.value}`;
        }

        // 回退到原来稳定的API调用方式
        const response = await api.purchaseRequests.getList(params);

        if (response.code === 200) {
          const rawRequests = response.data.results || [];
          // 使用后端提供的display字段，同时保留原始编码用于逻辑判断
          const processedRequests = rawRequests.map((request) => ({
            ...request,
            // 使用后端提供的display字段进行显示
            status: request.status_display || request.status,
            item_category:
              request.item_category_display || request.item_category,
            unit: request.unit_display || request.unit,
            procurement_method:
              request.procurement_method_display || request.procurement_method,
            fund_project: request.fund_project_display || request.fund_project,
            purchase_type:
              request.purchase_type_display || request.purchase_type,
            // 保存原始编码用于逻辑判断
            _originalStatus: request.status,
            _originalItemCategory: request.item_category,
            _originalUnit: request.unit,
            _originalProcurementMethod: request.procurement_method,
            _originalFundProject: request.fund_project,
            _originalPurchaseType: request.purchase_type,
          }));
          requests.value = processedRequests;
          pagination.total = response.data.count || 0;
        } else {
          console.error("❌ API返回错误:", response);
          message.error(response.message || "获取数据失败");
          requests.value = [];
          pagination.total = 0;
        }
      } catch (error) {
        console.error("获取采购需求列表失败:", error);

        // 如果是404错误且当前页面大于1，则跳转到第一页
        if (error.response?.status === 404 && pagination.current > 1) {
          pagination.current = 1;
          getPurchaseRequests();
          return;
        }

        // 如果是404错误但在第一页，说明没有数据
        if (error.response?.status === 404 && pagination.current === 1) {
          requests.value = [];
          pagination.total = 0;
          return;
        }

        message.error("获取数据失败");
      } finally {
        loading.value = false;
      }
    };

    // 排序状态
    const sortField = ref("");
    const sortOrder = ref("");

    // 表格变化处理 - 支持分页和排序
    const handleTableChange = (pag, _filters, sorter) => {
      pagination.current = pag.current || pagination.current;
      pagination.pageSize = pag.pageSize || pagination.pageSize;

      // 处理排序
      if (sorter && sorter.field) {
        sortField.value = sorter.field;
        sortOrder.value = sorter.order;
      } else {
        sortField.value = "";
        sortOrder.value = "";
      }

      getPurchaseRequests();
    };

    // 创建新需求
    const createRequest = () => {
      router.push("/purchase/requests/new");
    };

    // 查看需求详情
    const viewDetail = (record) => {
      router.push(`/purchase/requests/${record.id}`);
    };

    // 编辑采购需求
    const editRequest = (record) => {
      router.push(`/purchase/requests/${record.id}/edit`);
    };

    // 提交需求
    const submitRequest = async (record) => {
      try {
        const response = await api.purchaseRequests.submit(record.id);
        if (response.code === 200) {
          message.success("需求提交成功");
          getPurchaseRequests();
        }
      } catch (error) {
        console.error("提交需求失败:", error);
        message.error("提交失败");
      }
    };

    // 删除采购需求
    const deleteRequest = async (record) => {
      Modal.confirm({
        title: "确认删除",
        content: `确定要删除采购需求 ${record.item_name} 吗？`,
        okText: "确定",
        cancelText: "取消",
        okType: "danger",
        onOk: async () => {
          try {
            const response = await api.purchaseRequests.delete(record.id);
            if (response.code === 200) {
              message.success("删除成功");
              getPurchaseRequests();
            }
          } catch (error) {
            console.error("删除需求失败:", error);
            message.error("删除失败");
          }
        },
      });
    };

    // 批量提交 - 只提交"草稿"状态的项目
    const batchSubmit = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning("请选择要提交的需求");
        return;
      }

      // 筛选出"草稿"状态的项目（使用原始状态编码进行判断）
      const draftRequests = requests.value.filter(
        (item) =>
          selectedRowKeys.value.includes(item.id) &&
          (item._originalStatus || item.status) === "draft"
      );

      if (draftRequests.length === 0) {
        message.warning("所选项目中没有草稿状态的需求");
        return;
      }

      Modal.confirm({
        title: "批量提交确认",
        content: `确定要批量提交选中的 ${draftRequests.length} 个草稿采购需求吗？`,
        okText: "确认",
        cancelText: "取消",
        onOk: async () => {
          try {
            // 使用批量提交API
            const draftIds = draftRequests.map((req) => req.id);
            const response = await api.purchaseRequests.batchSubmit(draftIds);
            if (response.code === 200) {
              message.success(
                `批量提交成功，共提交 ${draftRequests.length} 个需求`
              );
              selectedRowKeys.value = [];
              getPurchaseRequests();
            }
          } catch (error) {
            console.error("批量提交失败:", error);
            message.error("批量提交失败");
          }
        },
      });
    };

    // 批量申请采购 - 只申请"已审批"状态的项目
    const batchApplyPurchase = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning("请选择要申请采购的需求");
        return;
      }

      // 筛选出"已审批"状态的项目（使用原始状态编码进行判断）
      const approvedRequests = requests.value.filter(
        (item) =>
          selectedRowKeys.value.includes(item.id) &&
          (item._originalStatus || item.status) === "approved"
      );

      if (approvedRequests.length === 0) {
        message.warning("所选项目中没有已审批状态的需求");
        return;
      }

      Modal.confirm({
        title: "申请采购确认",
        content: `确定要对选中的 ${approvedRequests.length} 个已审批需求申请采购吗？申请后状态将变更为"待采购"。`,
        onOk: async () => {
          try {
            // 使用批量申请采购API
            const approvedIds = approvedRequests.map((req) => req.id);
            const response = await api.purchaseRequests.batchApplyPurchase(
              approvedIds
            );
            if (response.code === 200) {
              message.success(
                `申请采购成功，共申请 ${approvedRequests.length} 个需求`
              );
              selectedRowKeys.value = [];
              getPurchaseRequests();
            }
          } catch (error) {
            console.error("申请采购失败:", error);
            message.error("申请采购失败");
          }
        },
      });
    };

    // 导入成功处理
    const handleImportSuccess = () => {
      showImportModal.value = false;
      message.success("导入成功");
      getPurchaseRequests();
    };

    // 导出字段全选处理
    const onExportFieldsCheckAllChange = (e) => {
      Object.assign(
        selectedExportFields,
        e.target.checked ? exportFieldOptions.map((field) => field.value) : []
      );
      exportFieldsIndeterminate.value = false;
      exportFieldsCheckAll.value = e.target.checked;
    };

    // 导出字段选择变化处理
    const onExportFieldsChange = (checkedList) => {
      // 确保必选字段始终被包含
      const requiredFields = exportFieldOptions
        .filter((field) => field.required)
        .map((field) => field.value);
      const finalCheckedList = [
        ...new Set([...checkedList, ...requiredFields]),
      ];

      exportFieldsIndeterminate.value =
        !!finalCheckedList.length &&
        finalCheckedList.length < exportFieldOptions.length;
      exportFieldsCheckAll.value =
        finalCheckedList.length === exportFieldOptions.length;

      // 更新选中的字段，触发表格列的重新计算
      selectedExportFields.value = [...finalCheckedList];
      console.log("Export fields changed:", finalCheckedList);
    };

    // 加载预览数据
    const loadPreviewData = async () => {
      previewLoading.value = true;
      try {
        const params = {
          page: 1,
          page_size: 10,
          status_in: "draft,pending,approved,rejected",
        };

        // 应用导出筛选条件
        if (exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(",");
        }
        if (exportFilters.department) {
          params.department = exportFilters.department;
        }
        if (exportFilters.itemName) {
          params.search = exportFilters.itemName;
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester;
        }
        if (exportFilters.minAmount) {
          params.budget_total_amount__gte = parseFloat(exportFilters.minAmount);
        }
        if (exportFilters.maxAmount) {
          params.budget_total_amount__lte = parseFloat(exportFilters.maxAmount);
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.created_at__gte =
            exportFilters.dateRange[0].format("YYYY-MM-DD");
          params.created_at__lte =
            exportFilters.dateRange[1].format("YYYY-MM-DD");
        }
        // 新增导出筛选参数
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains =
            exportFilters.procurementMethod;
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains =
            exportFilters.requirementSource;
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject;
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit;
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory;
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks;
        }

        const response = await api.purchaseRequests.getList(params);
        if (response.code === 200) {
          previewData.value = response.data.results || response.data || [];
          previewTotal.value = response.data.count || 0;
        }
      } catch (error) {
        console.error("加载预览数据失败:", error);
        message.error("加载预览数据失败");
      } finally {
        previewLoading.value = false;
      }
    };

    // 重置导出配置
    const resetExportConfig = () => {
      selectedExportFields.value = getPageDefaultExportFields("requirement");
      Object.assign(exportFilters, {
        status: [],
        department: "", // 单选部门
        itemName: "",
        requester: "",
        minAmount: "",
        maxAmount: "",
        dateRange: [],
        // 重置新增的导出筛选字段
        procurementMethod: "",
        requirementSource: "",
        fundProject: "",
        unit: "",
        itemCategory: "",
        remarks: "",
      });
      exportActiveTab.value = "fields";
      previewData.value = [];
      previewTotal.value = 0;
    };

    // 导出字段操作方法
    const selectAllExportFields = () => {
      selectedExportFields.value = exportFieldOptions.map(
        (field) => field.value
      );
    };

    const resetExportFields = () => {
      selectedExportFields.value = getPageDefaultExportFields("requirement");
    };

    const selectRequiredExportFields = () => {
      selectedExportFields.value = exportFieldOptions
        .filter((field) => field.required)
        .map((field) => field.value);
    };

    // 时间格式化为年/月/日
    const formatDateToYMD = (dateString) => {
      if (!dateString) return "-";
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "-";
      return `${date.getFullYear()}/${(date.getMonth() + 1)
        .toString()
        .padStart(2, "0")}/${date.getDate().toString().padStart(2, "0")}`;
    };

    // 导出记录分页相关
    const onExportRecordPageSizeChange = (_, size) => {
      exportRecordPageSize.value = size;
      searchExportRecords();
    };

    const onExportRecordPageChange = (page) => {
      exportRecordPagination.value.current = page;
      searchExportRecords();
    };

    // 导出筛选操作方法
    const resetExportFilters = () => {
      Object.assign(exportFilters, {
        status: [],
        department: "", // 单选部门
        itemName: "",
        requester: "",
        minAmount: "",
        maxAmount: "",
        purchase_type: "",
        dateRange: [],
        procurementMethod: "",
        requirementSource: "",
        fundProject: "",
        unit: "",
        itemCategory: "",
        remarks: "",
      });
      // 重新搜索
      searchExportRecords();
    };

    const searchExportRecords = async () => {
      exportSearchLoading.value = true;
      try {
        const params = {
          page: exportRecordPagination.value.current,
          page_size: exportRecordPageSize.value,
        };

        // 添加筛选条件
        if (exportFilters.status && exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(",");
        }
        if (exportFilters.department) {
          params.department = exportFilters.department;
        }
        if (exportFilters.purchase_type) {
          params.purchase_type = exportFilters.purchase_type;
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.created_at__gte =
            exportFilters.dateRange[0].format("YYYY-MM-DD");
          params.created_at__lte =
            exportFilters.dateRange[1].format("YYYY-MM-DD");
        }

        // 添加其他筛选条件
        if (exportFilters.itemName) {
          params.search = exportFilters.itemName;
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester;
        }
        if (exportFilters.minAmount) {
          params.budget_total_amount__gte = parseFloat(exportFilters.minAmount);
        }
        if (exportFilters.maxAmount) {
          params.budget_total_amount__lte = parseFloat(exportFilters.maxAmount);
        }
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains =
            exportFilters.procurementMethod;
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains =
            exportFilters.requirementSource;
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject;
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit;
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory;
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks;
        }

        const response = await api.purchaseRequests.getList(params);
        if (response.code === 200) {
          exportRecords.value = response.data.results || response.data || [];
          exportRecordPagination.value.total =
            response.data.count || exportRecords.value.length;
        }
      } catch (error) {
        console.error("搜索导出记录失败:", error);
        message.error("搜索导出记录失败");
      } finally {
        exportSearchLoading.value = false;
      }
    };

    const previewExportData = () => {
      if (selectedExportRecords.value.length === 0) {
        message.warning("请先选择要导出的记录");
        return;
      }

      // 根据选中的记录ID获取完整的记录数据
      const selectedRecordIds = selectedExportRecords.value;
      const selectedRecordData = exportRecords.value.filter((record) =>
        selectedRecordIds.includes(record.id)
      );

      // 设置预览数据为选中的完整记录
      previewData.value = selectedRecordData;
      previewTotal.value = selectedRecordData.length;
      showPreviewModal.value = true;
    };

    // 处理导出取消操作
    const handleExportCancel = () => {
      showExportModal.value = false;
      // 重置选择
      selectedExportRecords.value = [];
      selectedExportRecordData.value = [];
      exportRecords.value = [];
    };

    // 执行导出
    const executeExport = async () => {
      if (selectedExportRecords.value.length === 0) {
        message.warning("请先选择要导出的记录");
        return;
      }
      if (selectedExportFields.value.length === 0) {
        message.warning("请至少选择一个导出字段");
        return;
      }

      exportLoading.value = true;
      try {
        const params = {
          fields: selectedExportFields.value.join(","),
          ids: selectedExportRecords.value.join(","),
        };

        // 应用导出筛选条件
        if (exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(",");
        }
        if (exportFilters.department) {
          params.department = exportFilters.department;
        }
        if (exportFilters.itemName) {
          params.search = exportFilters.itemName;
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester;
        }
        if (exportFilters.minAmount) {
          params.budget_total_amount__gte = parseFloat(exportFilters.minAmount);
        }
        if (exportFilters.maxAmount) {
          params.budget_total_amount__lte = parseFloat(exportFilters.maxAmount);
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.created_at__gte =
            exportFilters.dateRange[0].format("YYYY-MM-DD");
          params.created_at__lte =
            exportFilters.dateRange[1].format("YYYY-MM-DD");
        }
        // 新增导出筛选参数
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains =
            exportFilters.procurementMethod;
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains =
            exportFilters.requirementSource;
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject;
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit;
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory;
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks;
        }

        const response = await api.purchaseRequests.exportToExcel(params);

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;
        link.download = `采购需求_${new Date().toLocaleDateString()}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        message.success(
          `导出成功，共导出 ${selectedExportRecords.value.length} 条记录`
        );
        handleExportCancel();
      } catch (error) {
        console.error("导出失败:", error);
        message.error("导出失败");
      } finally {
        exportLoading.value = false;
      }
    };

    // 导出到Excel
    const exportToExcel = async () => {
      try {
        // 构建导出参数，使用当前筛选条件
        const exportParams = {};

        // 添加筛选参数
        if (filters.status) {
          exportParams.status = filters.status;
        }
        if (filters.itemName) {
          exportParams.search = filters.itemName;
        }
        if (filters.department) {
          const selectedDept = departments.value.find(
            (d) => d.id == filters.department
          );
          if (selectedDept) {
            if (!selectedDept.parent_id) {
              exportParams.hierarchy_path__icontains = selectedDept.dept_name;
            } else {
              exportParams.hierarchy_path__icontains =
                selectedDept.hierarchy_path;
            }
          }
        }
        if (filters.requester) {
          exportParams.requester__username__icontains = filters.requester;
        }
        if (filters.dateRange && filters.dateRange.length === 2) {
          exportParams.created_at__gte =
            filters.dateRange[0].format("YYYY-MM-DD");
          exportParams.created_at__lte =
            filters.dateRange[1].format("YYYY-MM-DD");
        }

        const response = await api.purchaseRequests.exportToExcel(exportParams);

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute(
          "download",
          `采购需求清单_${new Date().toLocaleDateString()}.xlsx`
        );
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        message.success("导出成功");
      } catch (error) {
        console.error("导出失败:", error);
        message.error("导出失败");
      }
    };

    // 筛选变化处理
    const handleFilterChange = () => {
      pagination.current = 1;
      getPurchaseRequests();
    };

    // 防抖筛选变化处理
    const debouncedFilterChange = debounce(handleFilterChange, 500);

    // 重置筛选
    const resetFilters = () => {
      Object.assign(filters, {
        status: "",
        id: "",
        itemName: "",
        specification: "",
        department: "",
        applicant: "",
        purchaseType: "",
        minAmount: "",
        maxAmount: "",
        dateRange: [],
        // 重置新增的筛选字段
        procurementMethod: "",
        requirementSource: "",
        fundProject: "",
        unit: "",
        itemCategory: "",
        remarks: "",
      });
      handleFilterChange();
    };

    // 获取额外筛选选项数据（用户等）
    const getAdditionalFilterOptions = async () => {
      try {
        // 获取用户列表（申请人）
        const usersResponse = await api.users.getList();
        if (usersResponse.code === 200) {
          const userData = usersResponse.data.results || usersResponse.data;
          applicants.value = userData; // 所有用户都可以是申请人
          users.value = userData; // 同时设置users用于模板
        }
      } catch (error) {
        console.error("获取筛选选项失败:", error);
      }
    };

    // 筛选选项过滤方法
    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    // 展开/收起筛选控件
    const toggleFilters = () => {
      showFilters.value = !showFilters.value;
    };

    // 字段筛选方法
    const onColumnChange = (checkedValues) => {
      // 确保必选字段始终被选中
      const requiredColumns = columnOptions
        .filter((opt) => opt.required)
        .map((opt) => opt.key);
      const newSelectedColumns = [
        ...new Set([...checkedValues, ...requiredColumns]),
      ];
      selectedColumns.value = newSelectedColumns;
      saveColumnsToStorage(newSelectedColumns);
    };

    // 处理字段变更（阻止面板关闭）
    const handleColumnChange = (checkedValues) => {
      onColumnChange(checkedValues);
      // 不关闭面板，让用户可以继续选择
    };

    // 处理预设点击（阻止面板关闭）
    const handlePresetClick = ({ key }) => {
      applyPreset({ key });
      // 保持dropdown打开状态
      presetDropdownOpen.value = true;
    };

    // 处理全选按钮点击
    const handleSelectAll = (e) => {
      e.stopPropagation();
      selectAllColumns();
    };

    // 处理重置按钮点击
    const handleReset = (e) => {
      e.stopPropagation();
      resetColumns();
    };

    // 关闭字段筛选面板
    const closeColumnFilter = () => {
      // 通过触发外层下拉菜单的关闭来关闭面板
      document.body.click();
    };

    const selectAllColumns = () => {
      const sortedOptions = sortColumnsByOrder(columnOptions);
      const allColumns = sortedOptions.map((opt) => opt.key);
      selectedColumns.value = allColumns;
      saveColumnsToStorage(allColumns);
    };

    const resetColumns = () => {
      const defaultColumns = getPageDefaultColumns("requirement");
      selectedColumns.value = defaultColumns;
      saveColumnsToStorage(defaultColumns);
    };

    // 应用预设
    const applyPreset = ({ key }) => {
      const presetColumns = columnPresets[key];
      if (presetColumns) {
        // 确保必选字段始终被包含
        const requiredColumns = columnOptions
          .filter((opt) => opt.required)
          .map((opt) => opt.key);

        // 合并预设字段和必选字段，但排除action字段（因为会在filteredColumns中自动添加）
        const combinedColumns = [
          ...new Set([
            ...presetColumns.filter((col) => col !== "action"),
            ...requiredColumns,
          ]),
        ];

        // 按照标准顺序排列字段
        const sortedColumns = sortColumnsByOrder(
          combinedColumns,
          columnOptions
        );

        selectedColumns.value = sortedColumns;
        saveColumnsToStorage(sortedColumns);
      }
    };

    // 初始化加载数据
    // 打印完成处理
    const handlePrintComplete = (printedRecords) => {
      message.success(`成功打印 ${printedRecords.length} 条需求提报单`);
      showPrintModal.value = false;
      // 可以在这里添加其他处理逻辑，比如记录打印日志等
    };

    // 监听导出模态框打开
    watch(
      () => showExportModal.value,
      (newVal) => {
        if (newVal) {
          searchExportRecords();
        }
      }
    );

    onMounted(async () => {
      await getDepartmentsLegacy(); // 恢复使用原来的部门获取方法
      await getAdditionalFilterOptions(); // 获取额外的筛选选项
      await getPurchaseRequests();

      // 检查是否从通知跳转过来，需要打开详情
      const detailId = route.query.detail;
      if (detailId && route.query.from === "notification") {
        // 延迟一下确保数据加载完成
        setTimeout(() => {
          const targetRecord = requests.value.find(
            (item) => item.id == detailId
          );
          if (targetRecord) {
            viewDetail(targetRecord);
          } else {
            // 如果当前页面没有找到，尝试通过API获取
            fetchAndViewDetail(detailId);
          }
        }, 500);
      }
    });

    // 获取并查看指定ID的详情
    const fetchAndViewDetail = async (id) => {
      try {
        const response = await api.purchaseRequests.getDetail(id);
        if (response.code === 200) {
          viewDetail(response.data);
        } else {
          message.error("未找到对应的采购需求");
        }
      } catch (error) {
        console.error("获取详情失败:", error);
        message.error("获取详情失败");
      }
    };

    return {
      loading,
      showImportModal,
      showExportModal,
      showPrintModal,
      selectedRowKeys,
      statusOptions,
      pagination,
      filteredColumns,
      requests,
      departments,
      purchaseTypes,
      applicants,
      users,
      showFilters,
      showColumnFilter,
      presetDropdownOpen,
      filters,
      columnOptions,
      selectedColumns,
      fieldCategories,
      presetMenuItems,
      rowSelection,
      totalCount,
      pendingCount,
      approvedCount,
      rejectedCount,
      departmentCount,
      handleTableChange,
      sortField,
      sortOrder,
      batchSubmit,
      batchApplyPurchase,
      hasDraftRequests,
      hasApprovedRequests,
      hasPendingRequests,
      handleImportSuccess,
      createRequest,
      viewDetail,
      editRequest,
      submitRequest,
      deleteRequest,
      exportToExcel,
      // 字典服务函数
      getDictOptions,
      getStatusColor,
      formatDateToYMD,
      getFilterOptions,
      getAdditionalFilterOptions,
      filterOption,
      toggleFilters,
      onColumnChange,
      handleColumnChange,
      handlePresetClick,
      handleSelectAll,
      handleReset,
      closeColumnFilter,
      selectAllColumns,
      resetColumns,
      applyPreset,
      handleFilterChange,
      debouncedFilterChange,
      resetFilters,
      // 导出相关
      exportActiveTab,
      exportLoading,
      previewLoading,
      previewData,
      previewTotal,
      showPreviewModal,

      selectedExportFields,
      exportFieldsCheckAll,
      exportFieldsIndeterminate,
      exportFieldOptions,
      exportFieldCategories,
      exportFilters,
      previewColumns,
      onExportFieldsCheckAllChange,
      onExportFieldsChange,
      loadPreviewData,
      resetExportConfig,
      executeExport,
      fieldsCollapseKey,
      selectAllExportFields,
      resetExportFields,
      selectRequiredExportFields,
      resetExportFilters,
      searchExportRecords,
      previewExportData,

      // 导出记录选择相关
      exportRecords,
      selectedExportRecords,
      selectedExportRecordData,
      exportSearchLoading,
      exportRecordPageSize,
      exportRecordPagination,
      exportRecordColumns,
      exportRecordRowSelection,
      // 导出记录操作函数
      onExportRecordPageSizeChange,
      onExportRecordPageChange,
      handleExportCancel,
      // 打印相关
      handlePrintComplete,
    };
  },
};
</script>

<style scoped>
@import "@/styles/business-panels.css";
/* 使用统一样式，只保留页面特有的样式 */

/* 表格头部flex布局 - 与采购总览页面保持一致 */
.table-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-light);
  gap: 24px;
}

.table-title-section {
  flex-shrink: 0;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: 13px;
  font-weight: 400;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}


/* 筛选网格布局 */
.detailed-filters-fullwidth {
  padding: 16px;
  margin:10px;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
  border-radius: initial !important;
}

.filter-row {
  margin-bottom: 16px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 16px;
  align-items: center;
}

.filter-item {
  width: 100%;
}

/* 导出配置容器样式 - 采用物资验收页布局 */
.export-config-container {
  padding: 0;
}

.filter-section {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 筛选头部flex布局 */
.filter-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-header-flex h4 {
  margin: 0;
}

/* 导出筛选区域样式 */
.detailed-filters-export {
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.detailed-filters-export .filter-row {
  margin-bottom: 16px;
}

.detailed-filters-export .filter-row:last-child {
  margin-bottom: 0;
}

.detailed-filters-export .filter-select,
.detailed-filters-export .filter-input {
  width: 100%;
  height: 32px;
}

.detailed-filters-export .filter-select .ant-select-selector,
.detailed-filters-export .filter-input {
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.detailed-filters-export .filter-select .ant-select-selector:hover,
.detailed-filters-export .filter-input:hover {
  border-color: #3b82f6;
}

.detailed-filters-export .filter-select .ant-select-focused .ant-select-selector,
.detailed-filters-export .filter-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 导出筛选网格布局 */
.export-filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr) 1.5fr;
  gap: 16px;
  align-items: center;
}

.export-filter-item {
  width: 100%;
}

.export-filter-item-wide {
  grid-column: span 1;
}

/* 带标签的筛选项样式 */
.filter-item-with-label {
  display: flex;
  align-items: center;
  width: 100%;
}

.filter-item-with-label .filter-label {
  white-space: nowrap;
  margin-right: 8px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 60px;
}

.filter-item-with-label .filter-date-picker,
.filter-item-with-label .filter-select,
.filter-item-with-label .filter-input {
  flex: 1;
  min-width: 0;
}

.fields-section {
  margin-bottom: 16px;
}

/* 导出字段配置样式 - 与验收页面保持一致 */
.export-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
}

.export-field-category {
  flex: 1;
  min-width: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  overflow: hidden;
}

.export-category-header {
  background-color: #e6f7ff;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 12px;
}

.export-category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.export-category-fields {
  padding: 12px;
}

.export-field-option {
  margin-bottom: 8px;
}

.export-field-option:last-child {
  margin-bottom: 0;
}

.field-title {
  margin-right: 8px;
}

.required-mark {
  color: #ff4d4f;
  margin-left: 4px;
}

.actions-section {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .anticon {
  font-size: var(--text-2xl);
  color: rgba(255, 255, 255, 0.9);
}

.page-subtitle {
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 24px;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-inverse);
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 表格标题样式 */
.table-header {
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg,
      var(--bg-secondary) 0%,
      var(--bg-primary) 100%);
  width: 100%;
  box-sizing: border-box;
}

.table-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
  justify-content: flex-start;
}

.table-title .anticon {
  color: var(--primary-color);
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.table-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
  word-break: break-word;
}

/* 部门表格区域样式 */
.department-tables {
  margin-top: var(--space-xl);
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 标签页样式 */
.department-tabs {
  padding: var(--space-lg);
  width: 100%;
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav) {
  margin-bottom: var(--space-lg);
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav-wrap) {
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav-list) {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  scrollbar-width: thin;
  scrollbar-color: var(--border-light) transparent;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar) {
  height: 4px;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-thumb) {
  background: var(--border-light);
  border-radius: 2px;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-thumb:hover) {
  background: var(--primary-color);
}

:deep(.department-tabs .ant-tabs-tab) {
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  transition: var(--transition-normal);
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 120px;
  text-align: center;
}

:deep(.department-tabs .ant-tabs-tab-active) {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

:deep(.department-tabs .ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: var(--text-inverse);
}

:deep(.department-tabs .ant-tabs-content-holder) {
  background: var(--bg-primary);
  border-radius: 0 var(--radius-sm) var(--radius-sm) var(--radius-sm);
  border: 1px solid var(--border-light);
  padding: var(--space-lg);
  width: 100%;
  overflow-x: auto;
}

/* 统一筛选区域样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg,
      var(--bg-secondary) 0%,
      var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .amount-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .amount-input .ant-input-number,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .amount-input .ant-input-number:focus-within,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .amount-input .ant-input-number:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

.filter-result-text {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  margin-left: var(--space-lg);
}

/* 操作按钮区域样式 */
.action-buttons-section {
  padding: var(--space-xl) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  text-align: center;
}

/* 主要操作按钮样式 */
.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 48px;
  padding: 0 var(--space-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-sm);
  min-width: 140px;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
  color: var(--text-inverse);
}

.primary-action-btn:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  transform: none;
  box-shadow: none;
}

/* 次要操作按钮样式 */
.secondary-action-btn {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 48px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 140px;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
  transform: translateY(-1px);
}

/* 页面特有样式 */
:deep(.unified-table .ant-pagination) {
  margin: var(--space-2xl) 0 0 0;
  text-align: center;
}

/* 操作按钮样式覆盖 */
:deep(.action-btn) {
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition-normal);
  border-radius: var(--radius-sm);
  padding: 4px 8px;
  font-size: var(--text-sm);
}

:deep(.action-btn:hover) {
  color: var(--primary-dark);
  background: rgba(30, 58, 138, 0.1);
}

:deep(.action-btn.danger) {
  color: var(--error-color);
}

:deep(.action-btn.danger:hover) {
  color: var(--error-dark);
  background: rgba(239, 68, 68, 0.1);
}

/* 移除响应式优化，保持固定布局 */

/* 导出配置样式 */
.export-config {
  padding: 16px 0;
}

.export-fields {
  padding: 16px 0;
}

.field-categories h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 600;
}

.export-filters {
  padding: 16px 0;
}

.export-filters h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 600;
}

.export-preview {
  padding: 16px 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-weight: 600;
}

.preview-summary {
  margin-top: 16px;
}

.export-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
  text-align: right;
}

:deep(.export-config .ant-tabs-card .ant-tabs-tab) {
  border-radius: 6px 6px 0 0;
  border: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

:deep(.export-config .ant-tabs-card .ant-tabs-tab-active) {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

:deep(.export-config .ant-tabs-card .ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: white;
}

:deep(.export-config .ant-checkbox-group .ant-checkbox-wrapper) {
  margin-bottom: 8px;
}

/* 移除768px响应式样式 */

/* 移除所有响应式样式 */

/* 统一筛选区域样式 */
.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker,
.unified-filter-section .amount-input {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker,
.unified-filter-section .amount-input {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus,
.unified-filter-section .amount-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover,
.unified-filter-section .amount-input:hover {
  border-color: var(--primary-light);
}

/* 筛选区按钮高度对齐 */
.unified-filter-section .secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-filter-section .secondary-action-btn:hover {
  border-color: var(--primary-light);
}

/* 次要操作按钮样式 */
.secondary-action-btn {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 48px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 140px;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
  transform: translateY(-1px);
}

/* 表格标题区域样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
}

.table-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: normal;
}

.table-filters {
  margin-top: 8px;
  padding: 12px 0;
  border-top: 1px solid var(--border-light);
}


.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.record-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-actions {
  border-radius: initial !important;
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

/* 移除重复的详细筛选控件样式 */

.filter-select,
.filter-input,
.filter-date-picker {
  height: 32px;
  font-size: var(--text-sm);
}

.budget-range-input {
  width: 200px;
}

.budget-range-input .ant-input {
  height: 32px;
  font-size: var(--text-sm);
}

/* 紧凑型操作按钮样式 */
.compact-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 展开/收起动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding: 0;
  }

  to {
    opacity: 1;
    max-height: 200px;
    padding: 16px 0;
  }
}
.field-category-section {
  margin-bottom: 12px;
}

.category-title {
  margin: 0 0 6px 0;
  font-size: 13px;
  font-weight: 600;
  color: #666;
}

.category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.column-option {
  display: flex;
  align-items: center;
}

.column-title {
  font-size: 12px;
}

/* 移除重复的导出字段样式定义，使用上方统一的样式 */

/* 记录选择部分样式 */
.records-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  width: max-content;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-count {
  font-size: 13px;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  margin: 20px 0;
}

/* 预览模态框样式 */
:deep(.preview-modal-wrapper .ant-modal) {
  height: 80vh;
  top: 10vh;
  margin: 0;
}

:deep(.preview-modal-wrapper .ant-modal-content) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.preview-modal-wrapper .ant-modal-body) {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.preview-container .ant-table-wrapper) {
  flex: 1;
  overflow: hidden;
}

:deep(.preview-container .ant-table-tbody) {
  overflow-y: auto;
}

/* 修复操作列透明背景问题 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5 !important;
}

:deep(.ant-table-tbody > tr:hover > td.ant-table-cell-fix-right) {
  background-color: #f5f5f5 !important;
}

.table-container {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格区域样式 - 参考日志管理页面 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* 表格样式已在统一样式文件中定义 */
</style>
