<template>
  <a-select v-model:value="selectedValue" :options="dictOptions" show-search :placeholder="placeholder"
    :loading="loading" />
</template>

<script>
import { ref, onMounted, watch, computed } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'DictSelector',
  props: {
    dictType: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    modelValue: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const store = useStore();
    const loading = ref(false);

    // 使用 computed 来处理双向绑定
    const selectedValue = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    });

    const dictOptions = ref([]);

    // 从Vuex中获取数据字典
    const loadDictData = () => {
      const storeDictData = store.getters['dicts/getDict'](props.dictType);
      if (storeDictData && storeDictData.length > 0) {
        dictOptions.value = storeDictData.map(item => ({
          value: item.code,  // 存储编码用于提交
          label: item.name   // 显示名称给用户看
        }));
      }
    };

    // 组件挂载时加载数据
    onMounted(async () => {
      loadDictData();

      // 如果数据不存在，从API获取
      if (dictOptions.value.length === 0) {
        try {
          loading.value = true;
          await store.dispatch('dicts/getDict', props.dictType);
          loadDictData();
        } catch (error) {
          console.error('获取数据字典失败:', error);
        } finally {
          loading.value = false;
        }
      }
    });

    // 监听数据变化
    watch(
      () => store.getters['dicts/getDict'](props.dictType),
      (newData) => {
        if (newData && newData.length > 0) {
          dictOptions.value = newData.map(item => ({
            value: item.code,  // 存储编码用于提交
            label: item.name   // 显示名称给用户看
          }));
        }
      }
    );

    return {
      selectedValue,
      dictOptions,
      loading
    };
  }
};
</script>