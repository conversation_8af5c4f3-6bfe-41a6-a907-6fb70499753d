# Generated by Django 5.2.3 on 2025-06-18 17:25

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0003_department_contact_phone_department_description_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(help_text='记录用户名，防止用户删除后无法追踪', max_length=150, verbose_name='用户名')),
                ('log_type', models.CharField(choices=[('login', '用户登录'), ('logout', '用户登出'), ('create', '创建操作'), ('update', '更新操作'), ('delete', '删除操作'), ('approve', '审批操作'), ('reject', '驳回操作'), ('return', '退回操作'), ('purchase', '采购操作'), ('accept', '验收操作'), ('reimburse', '报销操作'), ('export', '导出操作'), ('import', '导入操作'), ('other', '其他操作')], max_length=20, verbose_name='日志类型')),
                ('action', models.CharField(max_length=200, verbose_name='操作描述')),
                ('target_model', models.CharField(blank=True, max_length=100, null=True, verbose_name='目标模型')),
                ('target_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='目标ID')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('request_data', models.JSONField(blank=True, null=True, verbose_name='请求数据')),
                ('response_data', models.JSONField(blank=True, null=True, verbose_name='响应数据')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作用户')),
            ],
            options={
                'verbose_name': '系统日志',
                'verbose_name_plural': '系统日志',
                'db_table': 'system_log',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='system_log_user_id_70b243_idx'), models.Index(fields=['log_type', 'created_at'], name='system_log_log_typ_3a8013_idx'), models.Index(fields=['created_at'], name='system_log_created_510d42_idx')],
            },
        ),
    ]
