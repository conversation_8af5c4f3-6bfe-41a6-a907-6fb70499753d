"""
请求上下文管理器
用于在信号处理器中获取当前请求的信息
"""
import threading
from typing import Optional
from django.http import HttpRequest


class RequestContext:
    """请求上下文管理器"""
    
    _local = threading.local()
    
    @classmethod
    def set_request(cls, request: HttpRequest):
        """设置当前请求"""
        cls._local.request = request
    
    @classmethod
    def get_request(cls) -> Optional[HttpRequest]:
        """获取当前请求"""
        return getattr(cls._local, 'request', None)
    
    @classmethod
    def get_client_ip(cls) -> Optional[str]:
        """获取客户端IP地址"""
        request = cls.get_request()
        if not request:
            return None
            
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @classmethod
    def get_user_agent(cls) -> Optional[str]:
        """获取用户代理"""
        request = cls.get_request()
        if not request:
            return None
        return request.META.get('HTTP_USER_AGENT', '')
    
    @classmethod
    def get_user(cls):
        """获取当前用户"""
        request = cls.get_request()
        if not request:
            return None
        return request.user if hasattr(request, 'user') and request.user.is_authenticated else None
    
    @classmethod
    def clear(cls):
        """清除当前请求上下文"""
        if hasattr(cls._local, 'request'):
            delattr(cls._local, 'request')


class RequestContextMiddleware:
    """请求上下文中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 设置请求上下文
        RequestContext.set_request(request)
        
        try:
            response = self.get_response(request)
        finally:
            # 清除请求上下文
            RequestContext.clear()
        
        return response
