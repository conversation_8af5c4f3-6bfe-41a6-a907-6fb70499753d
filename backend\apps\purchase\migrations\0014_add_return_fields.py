# Generated by Django 5.2.3 on 2025-06-18 16:30

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0013_purchaserequest_payee_account_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaserequest',
            name='return_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='退回日期'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='return_reason',
            field=models.TextField(blank=True, verbose_name='退回理由'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='returner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='returned_requests', to=settings.AUTH_USER_MODEL, verbose_name='退回人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='status',
            field=models.CharField(choices=[('draft', '草稿'), ('pending', '待审批'), ('approved', '已审批'), ('rejected', '已驳回'), ('returned', '已退回'), ('pending_purchase', '待采购'), ('purchased', '已采购'), ('pending_acceptance', '待验收'), ('accepted', '已验收'), ('pending_reimbursement', '待报销审批'), ('reimbursed', '已结算'), ('reimbursement_rejected', '报销驳回')], default='draft', max_length=30, verbose_name='状态'),
        ),
    ]
