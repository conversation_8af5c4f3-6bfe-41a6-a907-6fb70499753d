# Generated manually for enhanced models

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0002_dictionary'),
    ]

    operations = [
        # 为采购需求表添加新字段
        migrations.AddField(
            model_name='purchaserequest',
            name='attachment',
            field=models.FileField(blank=True, help_text='支持Excel/PDF格式的需求说明文档', null=True, upload_to='purchase_attachments/', verbose_name='需求说明文档'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='rejection_reason',
            field=models.TextField(blank=True, help_text='审批驳回时的详细原因', verbose_name='驳回原因'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='history_max_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='本单位历次采购最高单价'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='history_min_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='本单位历次采购最低单价'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='order_number',
            field=models.CharField(blank=True, help_text='审批通过后自动生成', max_length=50, verbose_name='采购订单号'),
        ),
        
        # 为验收表添加新字段
        migrations.AddField(
            model_name='acceptance',
            name='actual_quantity',
            field=models.IntegerField(default=0, verbose_name='实际验收数量'),
        ),
        migrations.AddField(
            model_name='acceptance',
            name='quantity_difference',
            field=models.IntegerField(default=0, help_text='实际数量 - 采购数量', verbose_name='数量差异'),
        ),
        migrations.AddField(
            model_name='acceptance',
            name='difference_rate',
            field=models.DecimalField(decimal_places=2, default=0, help_text='差异率百分比', max_digits=5, verbose_name='差异率'),
        ),
        migrations.AddField(
            model_name='acceptance',
            name='has_exception',
            field=models.BooleanField(default=False, verbose_name='存在异常'),
        ),
        migrations.AddField(
            model_name='acceptance',
            name='exception_reason',
            field=models.TextField(blank=True, verbose_name='异常原因'),
        ),
        migrations.AddField(
            model_name='acceptance',
            name='exception_approved',
            field=models.BooleanField(default=False, verbose_name='异常已审批'),
        ),
        migrations.AddField(
            model_name='acceptance',
            name='exception_approver_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='异常审批人ID'),
        ),
        migrations.AddField(
            model_name='acceptance',
            name='exception_approval_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='异常审批日期'),
        ),
    ]
