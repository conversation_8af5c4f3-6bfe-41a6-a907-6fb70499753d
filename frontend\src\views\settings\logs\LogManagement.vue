<template>
  <div class="log-management">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <FileTextOutlined />
          系统日志管理
        </h1>
        <p class="page-subtitle">系统操作记录与审计追踪，监控用户行为和系统变更</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ statistics.today_count || 0 }}</span>
          <span class="stat-label">今日日志</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ statistics.week_count || 0 }}</span>
          <span class="stat-label">本周日志</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ statistics.total_count || 0 }}</span>
          <span class="stat-label">总日志数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ statistics.error_count || 0 }}</span>
          <span class="stat-label">错误日志</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="unified-filter-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select
            v-model:value="filters.logLevel"
            placeholder="日志级别"
            allowClear
            class="filter-select"
          >
            <a-select-option value="INFO">信息</a-select-option>
            <a-select-option value="WARNING">警告</a-select-option>
            <a-select-option value="ERROR">错误</a-select-option>
            <a-select-option value="DEBUG">调试</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select
            v-model:value="filters.module"
            placeholder="模块"
            allowClear
            class="filter-select"
          >
            <a-select-option value="authentication">用户认证</a-select-option>
            <a-select-option value="purchase">采购管理</a-select-option>
            <a-select-option value="system">系统管理</a-select-option>
            <a-select-option value="notification">通知系统</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select
            v-model:value="filters.actionType"
            placeholder="操作类型"
            allowClear
            class="filter-select"
          >
            <a-select-option value="create">创建</a-select-option>
            <a-select-option value="update">更新</a-select-option>
            <a-select-option value="delete">删除</a-select-option>
            <a-select-option value="login">登录</a-select-option>
            <a-select-option value="logout">登出</a-select-option>
            <a-select-option value="query">查询</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-input
            v-model:value="filters.username"
            placeholder="用户名"
            allowClear
            class="filter-input"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <a-range-picker
            v-model:value="filters.dateRange"
            class="date-picker"
            :placeholder="['开始时间', '结束时间']"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
          />
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="3">
          <a-button @click="handleSearch" class="secondary-action-btn">
            <SearchOutlined />
            查询
          </a-button>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="3">
          <a-button @click="handleReset" class="secondary-action-btn">
            <ReloadOutlined />
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 操作栏 -->
    <div class="action-buttons-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col>
          <a-button @click="handleExport" class="primary-action-btn">
            <DownloadOutlined />
            导出日志
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="handleRefresh" class="secondary-action-btn">
            <ReloadOutlined />
            刷新数据
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="handleClearOldLogs" class="secondary-action-btn">
            <DeleteOutlined />
            清理旧日志
          </a-button>
        </a-col>
        <!-- 批量操作按钮 -->
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button @click="batchExport" class="primary-action-btn">
            <ExportOutlined />
            批量导出 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 日志表格 -->
    <div class="table-section business-card">
      <a-table
        :columns="columns"
        :data-source="logs"
        :loading="loading"
        :row-key="record => record.id"
        :row-selection="rowSelection"
        :pagination="paginationConfig"
        @change="handleTableChange"
        :scroll="{ x: 1400 }"
        class="unified-table"
        size="large"
        :locale="{ emptyText: '暂无日志数据' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'level'">
            <a-tag :color="getLogLevelColor(record.level)">
              {{ record.level }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'module'">
            <a-tag color="blue">{{ record.module }}</a-tag>
          </template>
          <template v-else-if="column.key === 'message'">
            <a-tooltip :title="record.message">
              <span>{{ record.message.length > 50 ? record.message.substring(0, 50) + '...' : record.message }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'timestamp'">
            {{ formatDateTime(record.timestamp) }}
          </template>
          <template v-else-if="column.key === 'operation'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                <EyeOutlined />
                详情
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="detailVisible"
      title="日志详情"
      :footer="null"
      width="80%"
      style="max-width: 1200px;"
      class="log-detail-modal"
    >
      <a-descriptions :column="2" bordered v-if="currentLog">
        <a-descriptions-item label="日志ID">{{ currentLog.id }}</a-descriptions-item>
        <a-descriptions-item label="日志级别">
          <a-tag :color="getLogLevelColor(currentLog.level)">
            {{ currentLog.level }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="模块">{{ currentLog.module }}</a-descriptions-item>
        <a-descriptions-item label="用户">{{ currentLog.username || '系统' }}</a-descriptions-item>
        <a-descriptions-item label="IP地址">{{ currentLog.ip_address || '-' }}</a-descriptions-item>
        <a-descriptions-item label="记录时间">{{ formatDateTime(currentLog.timestamp) }}</a-descriptions-item>
        <a-descriptions-item label="消息内容" :span="2">
          <div class="log-message-detail">{{ currentLog.message }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="详细信息" :span="2" v-if="currentLog.details">
          <pre class="log-details">{{ JSON.stringify(currentLog.details, null, 2) }}</pre>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 清理旧日志确认模态框 -->
    <a-modal
      v-model:open="clearLogsVisible"
      title="清理旧日志"
      @ok="confirmClearLogs"
      @cancel="clearLogsVisible = false"
      :confirm-loading="clearLoading"
    >
      <p>确定要清理30天前的日志吗？此操作不可恢复。</p>
      <a-alert
        message="注意"
        description="清理操作将永久删除旧日志数据，建议先导出重要日志。"
        type="warning"
        show-icon
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  FileTextOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  ExportOutlined,
  EyeOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import api from '@/api'

// 响应式数据
const loading = ref(false)
const logs = ref([])
const statistics = ref({})
const detailVisible = ref(false)
const clearLogsVisible = ref(false)
const clearLoading = ref(false)
const currentLog = ref(null)

// 批量操作相关
const selectedRowKeys = ref([])

// 筛选条件
const filters = reactive({
  logLevel: undefined,
  module: undefined,
  actionType: undefined,
  username: '',
  dateRange: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: pagination.showSizeChanger,
  showQuickJumper: pagination.showQuickJumper,
  showTotal: pagination.showTotal,
  pageSizeOptions: ['10', '20', '50', '100']
}))

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 表格列配置
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }) => pagination.pageSize * (pagination.current - 1) + index + 1
  },
  {
    title: '级别',
    key: 'level',
    dataIndex: 'level',
    width: 80
  },
  {
    title: '模块',
    key: 'module',
    dataIndex: 'module',
    width: 100
  },
  {
    title: '用户',
    dataIndex: 'username',
    key: 'username',
    width: 120
  },
  {
    title: '消息',
    key: 'message',
    dataIndex: 'message',
    width: 300
  },
  {
    title: 'IP地址',
    dataIndex: 'ip_address',
    key: 'ip_address',
    width: 120
  },
  {
    title: '时间',
    key: 'timestamp',
    dataIndex: 'timestamp',
    width: 160
  },
  {
    title: '操作',
    key: 'operation',
    width: 80,
    fixed: 'right'
  }
]

// 获取日志级别颜色
const getLogLevelColor = (level) => {
  const colors = {
    'INFO': 'blue',
    'WARNING': 'orange',
    'ERROR': 'red',
    'DEBUG': 'default'
  }
  return colors[level] || 'default'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss') : '-'
}

// 获取日志列表
const getLogs = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
      ...filters
    }
    const response = await api.systemLogs.getLogs(params)

    if (response && response.code === 200) {
      logs.value = response.data.results
      pagination.total = response.data.count
    } else {
      message.error(response?.message || '获取日志失败')
    }

  } catch (error) {
    if (error.response?.status === 401) {
      message.error('认证失败，请重新登录')
    } else {
      message.error(`获取日志失败: ${error.message || error}`)
    }
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const getStatistics = async () => {
  try {
    const response = await api.systemLogs.getStatistics()

    if (response && response.code === 200) {
      statistics.value = response.data
    }
  } catch (error) {
    // 静默处理错误
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getLogs()
}

// 重置
const handleReset = () => {
  Object.assign(filters, {
    logLevel: undefined,
    module: undefined,
    username: '',
    dateRange: []
  })
  pagination.current = 1
  getLogs()
}

// 导出
const handleExport = async () => {
  try {
    const params = { ...filters };
    // 处理日期范围
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0].format('YYYY-MM-DD HH:mm:ss');
      params.end_date = filters.dateRange[1].format('YYYY-MM-DD HH:mm:ss');
    }
    const response = await api.systemLogs.exportLogs(params);

    // 检查响应数据 - 修复检查逻辑
    if (!response || !response.data) {
      throw new Error('响应数据为空');
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error('导出的文件为空');
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `system_logs_${dayjs().format('YYYYMMDDHHmmss')}.xlsx`);
    document.body.appendChild(link);
    link.click();
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    message.success('日志导出成功');
  } catch (error) {
    // 日志导出失败：显示错误信息给用户
    message.error(`日志导出失败: ${error.message || '未知错误'}`);
  }
}

// 批量导出
const batchExport = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要导出的日志');
    return;
  }
  try {
    const response = await api.systemLogs.batchExport(selectedRowKeys.value);

    // 检查响应数据
    if (!response || !response.data) {
      throw new Error('响应数据为空');
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob = response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error('导出的文件为空');
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `system_logs_batch_${dayjs().format('YYYYMMDDHHmmss')}.xlsx`);
    document.body.appendChild(link);
    link.click();
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    message.success(`成功导出 ${selectedRowKeys.value.length} 条日志`);
  } catch (error) {
    // 批量导出失败：显示错误信息给用户
    message.error(`批量导出失败: ${error.message || '未知错误'}`);
  }
}

// 刷新数据
const handleRefresh = () => {
  getLogs()
  getStatistics()
}

// 清理旧日志
const handleClearOldLogs = () => {
  clearLogsVisible.value = true
}

// 确认清理日志
const confirmClearLogs = async () => {
  clearLoading.value = true
  try {
    // 模拟清理操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('旧日志清理成功')
    clearLogsVisible.value = false
    getLogs()
    getStatistics()
  } catch (error) {
    message.error('清理失败')
  } finally {
    clearLoading.value = false
  }
}


// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  getLogs()
}

// 查看详情
const viewDetail = (record) => {
  currentLog.value = record
  detailVisible.value = true
}

// 页面加载时初始化
onMounted(async () => {
  await getLogs()
  await getStatistics()
})
</script>

<style scoped>
.log-management {
  padding: 0px;
}

/* 页面标题区域 */
.page-header {
  background: var(--primary-gradient);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* 确保标题区域背景色优先级 */
.page-header.business-card {
  background: var(--primary-gradient) !important;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  letter-spacing: 0.5px;
}

.page-title .anticon {
  font-size: var(--text-3xl);
}

.page-subtitle {
  font-size: var(--text-base);
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: var(--space-xl);
}

.stat-item {
  text-align: center;
  padding: var(--space-md) var(--space-lg);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.9;
  font-weight: 500;
}

/* 业务卡片样式 */
.business-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-lg);
  border: 1px solid var(--border-light);
}

/* 统一筛选区域样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

/* 操作按钮区域 */
.action-buttons-section {
  padding: var(--space-md) var(--space-xl);
}

.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.secondary-action-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-overlay);
}


/* 表格区域 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* 表格样式已在统一样式文件中定义 */

/* 日志详情样式 */
.log-message-detail {
  background: var(--bg-secondary);
  padding: var(--space-md);
  border-radius: var(--radius-sm);
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-details {
  background: var(--bg-secondary);
  padding: var(--space-md);
  border-radius: var(--radius-sm);
  font-family: 'Courier New', monospace;
  font-size: var(--text-xs);
  max-height: 300px;
  overflow-y: auto;
  margin: 0;
}

/* 状态标签优化 */
:deep(.ant-tag) {
  border-radius: 20px;
  font-weight: 500;
  font-size: var(--text-xs);
  padding: 4px var(--space-md);
  border: none;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 日志详情模态框样式 */
:deep(.log-detail-modal) {
  .ant-modal-body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

.log-message-detail {
  word-break: break-word;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.log-details {
  word-break: break-word;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  border: 1px solid #e8e8e8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-lg);
  }

  .header-stats {
    width: 100%;
    justify-content: space-between;
    gap: var(--space-md);
  }

  .stat-number {
    font-size: var(--text-lg);
  }

  .log-management {
    padding: var(--space-md);
  }

  /* 移动端日志详情模态框 */
  :deep(.log-detail-modal) {
    width: 95% !important;
    max-width: none !important;
  }

  .log-message-detail,
  .log-details {
    font-size: 11px;
    max-height: 150px;
  }
}
</style>