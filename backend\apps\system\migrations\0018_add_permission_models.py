# Generated by Django 5.2.4 on 2025-07-25 17:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("roles", "0002_alter_role_permissions"),
        ("system", "0017_menu_rolemenu_delete_dictdata_delete_usersession"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Permission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="权限名称")),
                (
                    "code",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="权限代码"
                    ),
                ),
                (
                    "permission_type",
                    models.CharField(
                        choices=[
                            ("page", "页面权限"),
                            ("button", "按钮权限"),
                            ("api", "API权限"),
                            ("data", "数据权限"),
                        ],
                        default="page",
                        max_length=20,
                        verbose_name="权限类型",
                    ),
                ),
                ("module", models.CharField(max_length=50, verbose_name="所属模块")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="权限描述"),
                ),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "menu",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="permissions",
                        to="system.menu",
                        verbose_name="关联菜单",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="system.permission",
                        verbose_name="父权限",
                    ),
                ),
            ],
            options={
                "verbose_name": "系统权限",
                "verbose_name_plural": "系统权限",
                "db_table": "sys_permission",
                "ordering": ["module", "sort_order", "code"],
            },
        ),
        migrations.CreateModel(
            name="RolePermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                (
                    "granted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权人",
                    ),
                ),
                (
                    "permission",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="system.permission",
                        verbose_name="权限",
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="roles.role",
                        verbose_name="角色",
                    ),
                ),
            ],
            options={
                "verbose_name": "角色权限",
                "verbose_name_plural": "角色权限",
                "db_table": "sys_role_permission_new",
                "unique_together": {("role", "permission")},
            },
        ),
        migrations.CreateModel(
            name="UserPermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_granted",
                    models.BooleanField(default=True, verbose_name="是否授权"),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                (
                    "granted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="granted_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权人",
                    ),
                ),
                (
                    "permission",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="system.permission",
                        verbose_name="权限",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户权限",
                "verbose_name_plural": "用户权限",
                "db_table": "sys_user_permission",
                "unique_together": {("user", "permission")},
            },
        ),
    ]
