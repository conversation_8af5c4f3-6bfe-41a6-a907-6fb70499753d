/**
 * 采购管理系统分阶段验证工具
 * 根据业务流程的不同阶段进行字段验证
 */

/**
 * 业务流程阶段定义
 */
export const BUSINESS_STAGES = {
  REQUIREMENT: 'requirement',    // 需求提报阶段
  APPROVAL: 'approval',         // 审批阶段
  PURCHASE: 'purchase',         // 采购阶段
  ACCEPTANCE: 'acceptance',     // 验收阶段
  SETTLEMENT: 'settlement'      // 结算阶段
}

/**
 * 状态与业务阶段的映射
 */
export const STATUS_STAGE_MAP = {
  'draft': BUSINESS_STAGES.REQUIREMENT,
  'pending_approval': BUSINESS_STAGES.REQUIREMENT,
  'approved': BUSINESS_STAGES.APPROVAL,
  'rejected': BUSINESS_STAGES.REQUIREMENT,
  'pending_purchase': BUSINESS_STAGES.APPROVAL,
  'purchased': BUSINESS_STAGES.PURCHASE,
  'returned': BUSINESS_STAGES.PURCHASE,
  'pending_acceptance': BUSINESS_STAGES.PURCHASE,
  'accepted': BUSINESS_STAGES.ACCEPTANCE,
  'pending_reimbursement': BUSINESS_STAGES.ACCEPTANCE,
  'settled': BUSINESS_STAGES.SETTLEMENT
}

/**
 * 各阶段必填字段定义
 */
const REQUIREMENT_FIELDS = [
  'hierarchy_path',
  'item_name',
  'specification',
  'unit',
  'budget_quantity',
  'budget_unit_price',
  'requester_id',
  'dept_id',
  'procurement_method',
  'requirement_source',
  'fund_project_name',
  'purchase_type',
  'hierarchy_path',
  'item_category'
]

const APPROVAL_FIELDS = [
  ...REQUIREMENT_FIELDS,
  'submission_date'
]

const PURCHASE_FIELDS = [
  ...APPROVAL_FIELDS,
  'purchase_quantity',
  'purchase_unit_price',
  'supplier_name',
  'purchaser_id',
  'purchase_date'
]

const ACCEPTANCE_FIELDS = [
  ...PURCHASE_FIELDS,
  'acceptance_quantity',
  'acceptor_id',
  'acceptance_date'
]

const SETTLEMENT_FIELDS = [
  ...ACCEPTANCE_FIELDS,
  'settlement_amount',
  'reimburser_id',
  'reimbursement_date'
]

export const STAGE_REQUIRED_FIELDS = {
  [BUSINESS_STAGES.REQUIREMENT]: REQUIREMENT_FIELDS,
  [BUSINESS_STAGES.APPROVAL]: APPROVAL_FIELDS,
  [BUSINESS_STAGES.PURCHASE]: PURCHASE_FIELDS,
  [BUSINESS_STAGES.ACCEPTANCE]: ACCEPTANCE_FIELDS,
  [BUSINESS_STAGES.SETTLEMENT]: SETTLEMENT_FIELDS
}

/**
 * 字段验证规则
 */
export const FIELD_VALIDATION_RULES = {
  // 基础信息字段
  item_name: {
    required: true,
    message: '物品名称不能为空',
    validator: (value) => value && value.trim().length > 0
  },
  specification: {
    required: true,
    message: '规格型号不能为空',
    validator: (value) => value && value.trim().length > 0
  },
  unit: {
    required: true,
    message: '计量单位不能为空',
    validator: (value) => value && value.trim().length > 0
  },
  budget_quantity: {
    required: true,
    message: '预算数量必须大于0',
    validator: (value) => value && Number(value) > 0
  },
  budget_unit_price: {
    required: true,
    message: '预算单价必须大于0',
    validator: (value) => value && Number(value) > 0
  },

  // 采购字段
  purchase_quantity: {
    required: true,
    message: '采购数量必须大于0',
    validator: (value) => value && Number(value) > 0
  },
  purchase_unit_price: {
    required: true,
    message: '采购单价必须大于0',
    validator: (value) => value && Number(value) > 0
  },
  supplier_name: {
    required: true,
    message: '供应商名称不能为空',
    validator: (value) => value && value.trim().length > 0
  },

  // 验收字段
  acceptance_quantity: {
    required: true,
    message: '验收数量不能为负数',
    validator: (value) => value !== null && value !== undefined && Number(value) >= 0
  },

  // 结算字段
  settlement_amount: {
    required: true,
    message: '结算金额不能为负数',
    validator: (value) => value !== null && value !== undefined && Number(value) >= 0
  }
}

/**
 * 根据状态获取业务阶段
 * @param {string} status 当前状态
 * @returns {string} 业务阶段
 */
export function getBusinessStage(status) {
  return STATUS_STAGE_MAP[status] || BUSINESS_STAGES.REQUIREMENT
}

/**
 * 获取当前阶段的必填字段
 * @param {string} status 当前状态
 * @returns {Array} 必填字段列表
 */
export function getRequiredFields(status) {
  const stage = getBusinessStage(status)
  return STAGE_REQUIRED_FIELDS[stage] || []
}

/**
 * 验证表单数据
 * @param {Object} formData 表单数据
 * @param {string} status 当前状态
 * @returns {Object} 验证结果 { isValid: boolean, errors: Object }
 */
export function validateFormData(formData, status) {
  const requiredFields = getRequiredFields(status)
  const errors = {}
  let isValid = true

  // 验证必填字段
  requiredFields.forEach(fieldName => {
    const rule = FIELD_VALIDATION_RULES[fieldName]
    if (rule) {
      const value = formData[fieldName]
      if (!rule.validator(value)) {
        errors[fieldName] = rule.message
        isValid = false
      }
    }
  })

  return { isValid, errors }
}

/**
 * 获取当前阶段可编辑的字段
 * @param {string} status 当前状态
 * @returns {Array} 可编辑字段列表
 */
export function getEditableFields(status) {
  const stage = getBusinessStage(status)

  switch (stage) {
    case BUSINESS_STAGES.REQUIREMENT:
      return [
        'item_name', 'specification', 'unit', 'budget_quantity', 'budget_unit_price',
        'procurement_method', 'requirement_source', 'fund_project_name', 'remarks'
      ]

    case BUSINESS_STAGES.APPROVAL:
      return ['approval_comment', 'rejection_reason']

    case BUSINESS_STAGES.PURCHASE:
      return [
        'purchase_quantity', 'purchase_unit_price', 'supplier_name',
        'purchase_remarks', 'shipping_date'
      ]

    case BUSINESS_STAGES.ACCEPTANCE:
      return [
        'acceptance_quantity', 'acceptance_remarks'
      ]

    case BUSINESS_STAGES.SETTLEMENT:
      return [
        'settlement_amount', 'transaction_number', 'payee_name', 'settlement_remarks'
      ]

    default:
      return []
  }
}

/**
 * 检查字段是否在当前阶段可编辑
 * @param {string} fieldName 字段名
 * @param {string} status 当前状态
 * @returns {boolean} 是否可编辑
 */
export function isFieldEditable(fieldName, status) {
  const editableFields = getEditableFields(status)
  return editableFields.includes(fieldName)
}

/**
 * 获取当前阶段的表单配置
 * @param {string} status 当前状态
 * @returns {Object} 表单配置
 */
export function getFormConfig(status) {
  const stage = getBusinessStage(status)
  const requiredFields = getRequiredFields(status)
  const editableFields = getEditableFields(status)

  return {
    stage,
    requiredFields,
    editableFields,
    isReadonly: editableFields.length === 0
  }
}

/**
 * 根据页面类型获取应该显示的字段筛选选项
 * @param {string} pageType 页面类型：'requirement', 'approval', 'purchase', 'acceptance', 'settlement'
 * @returns {Array} 字段筛选选项
 */
export function getPageColumnOptions(pageType) {
  const baseOptions = [
    // 基础需求信息字段
    { key: 'id', title: 'ID', required: true, category: 'basic', order: 1 },
    { key: 'item_category', title: '物品种类', required: false, category: 'basic', order: 1.5 },
    { key: 'item_name', title: '物品名称', required: true, category: 'basic', order: 2 },
    { key: 'specification', title: '规格型号', required: false, category: 'basic', order: 3 },
    { key: 'unit', title: '计量单位', required: false, category: 'basic', order: 4 },
    { key: 'budget_quantity', title: '需求数量', required: false, category: 'requirement', order: 5 },
    { key: 'budget_unit_price', title: '预算单价', required: false, category: 'requirement', order: 6 },
    { key: 'budget_total_amount', title: '预算金额', required: false, category: 'requirement', order: 7 },
    { key: 'requirement_source', title: '需求来源', required: false, category: 'requirement', order: 7.5 },
    { key: 'procurement_method', title: '采购方式', required: false, category: 'requirement', order: 7.6 },
    { key: 'fund_project', title: '经费项目', required: false, category: 'requirement', order: 7.7 },
    { key: 'purchase_type', title: '采购类型', required: false, category: 'basic', order: 7.8 },
    { key: 'remarks', title: '需求备注', required: false, category: 'requirement', order: 7.9 },

    // 组织和人员字段
    { key: 'hierarchy_path', title: '部门层级路径', required: false, category: 'organization', order: 8.1 },
    { key: 'requester_name', title: '申请人姓名', required: false, category: 'organization', order: 8.3 },
    { key: 'approver_name', title: '审批人姓名', required: false, category: 'organization', order: 8.5 },
    { key: 'purchaser_name', title: '采购员姓名', required: false, category: 'organization', order: 8.7 },
    { key: 'acceptor_name', title: '验收人姓名', required: false, category: 'organization', order: 8.9 },
    { key: 'reimburser_name', title: '报销人姓名', required: false, category: 'organization', order: 9.1 },

    // 时间字段
    { key: 'created_at', title: '创建时间', required: false, category: 'basic', order: 10 },
    { key: 'submission_date', title: '提交时间', required: false, category: 'approval', order: 13 },
    { key: 'approved_at', title: '审批时间', required: false, category: 'approval', order: 15 },
    { key: 'approval_comment', title: '审批意见', required: false, category: 'approval', order: 15.1 },
    { key: 'rejection_reason', title: '驳回原因', required: false, category: 'approval', order: 15.2 },

    // 采购相关字段
    { key: 'purchase_quantity', title: '采购数量', required: false, category: 'purchase', order: 17 },
    { key: 'purchase_unit_price', title: '采购单价', required: false, category: 'purchase', order: 16 },
    { key: 'purchase_total_amount', title: '采购金额', required: false, category: 'purchase', order: 17.5 },
    { key: 'supplier_name', title: '供应商名称', required: false, category: 'purchase', order: 18 },
    { key: 'purchase_date', title: '采购时间', required: false, category: 'purchase', order: 20 },
    { key: 'purchase_remarks', title: '采购备注', required: false, category: 'purchase', order: 20.1 },

    // 验收相关字段
    { key: 'acceptance_quantity', title: '验收数量', required: false, category: 'acceptance', order: 21 },
    { key: 'acceptance_date', title: '验收时间', required: false, category: 'acceptance', order: 23 },
    { key: 'acceptance_remarks', title: '验收备注', required: false, category: 'acceptance', order: 23.4 },
    { key: 'courier_company', title: '快递公司', required: false, category: 'acceptance', order: 23.1 },
    { key: 'tracking_number', title: '快递单号', required: false, category: 'acceptance', order: 23.2 },
    { key: 'shipping_origin', title: '商品发货地', required: false, category: 'acceptance', order: 23.3 },

    // 结算相关字段
    { key: 'settlement_amount', title: '结算金额', required: false, category: 'settlement', order: 24 },
    { key: 'reimbursement_voucher_no', title: '报销凭证号', required: false, category: 'settlement', order: 26.5 },
    { key: 'settlement_remarks', title: '结算备注', required: false, category: 'settlement', order: 29 },
    { key: 'reimbursement_date', title: '结算时间', required: false, category: 'settlement', order: 26 },
    { key: 'transaction_number', title: '交易流水号', required: false, category: 'settlement', order: 27 },
    { key: 'payee_name', title: '收款人', required: false, category: 'settlement', order: 28 },
    { key: 'payee_account', title: '收款账号', required: false, category: 'settlement', order: 28.5 },

    // 状态和操作
    { key: 'status', title: '状态', required: true, category: 'basic', order: 30 },
    // 操作列（固定在最后）
    { key: 'action', title: '操作', required: false, category: 'action', order: 99 }
  ]

  // 根据页面类型过滤相应的字段
  switch (pageType) {
    case 'overview':
      // 总览页面：显示所有字段
      return baseOptions

    case 'requirement':
      // 需求提报页面：显示基础信息、需求信息、组织和人员信息
      return baseOptions.filter(opt => ['basic', 'requirement', 'organization', 'action'].includes(opt.category))

    case 'approval':
      // 审批页面：显示基础信息、需求信息、组织和人员信息、审批信息
      return baseOptions.filter(opt => ['basic', 'requirement', 'organization', 'approval', 'action'].includes(opt.category))

    case 'purchase':
      // 采购页面：显示基础信息、需求信息、组织和人员信息、审批信息、采购信息
      return baseOptions.filter(opt => ['basic', 'requirement', 'organization', 'approval', 'purchase', 'action'].includes(opt.category))

    case 'acceptance':
      // 验收页面：显示基础信息、需求信息、组织和人员信息、审批信息、采购信息、验收信息
      return baseOptions.filter(opt => ['basic', 'requirement', 'organization', 'approval', 'purchase', 'acceptance', 'action'].includes(opt.category))

    case 'settlement':
      // 结算页面：显示所有字段
      return baseOptions

    default:
      return baseOptions
  }
}

/**
 * 根据顺序排序字段选项
 * @param {Array} options 字段选项数组
 * @returns {Array} 排序后的字段选项
 */
export function sortColumnsByOrder(columns, columnOptions = null) {
  // 如果传入的是字段选项数组，直接按order排序
  if (!columnOptions) {
    return columns.sort((a, b) => {
      const orderA = a.order || 999
      const orderB = b.order || 999
      return orderA - orderB
    })
  }

  // 如果传入的是字段key数组，需要根据columnOptions排序
  const sortedColumns = [...columns]

  // 确保ID列始终在第一位（如果被选中）
  const idIndex = sortedColumns.indexOf('id')
  if (idIndex > 0) {
    // 将ID从当前位置移除并插入到第一位
    const idColumn = sortedColumns.splice(idIndex, 1)[0]
    sortedColumns.unshift(idColumn)
  }

  // 对其他列按照columnOptions中的order排序
  const otherColumns = sortedColumns.filter(col => col !== 'id')
  const sortedOtherColumns = otherColumns.sort((a, b) => {
    const optionA = columnOptions.find(opt => opt.key === a)
    const optionB = columnOptions.find(opt => opt.key === b)
    const orderA = optionA?.order || 999
    const orderB = optionB?.order || 999
    return orderA - orderB
  })

  // 如果ID列存在，将其放在第一位，否则直接返回排序后的列
  return sortedColumns.includes('id')
    ? ['id', ...sortedOtherColumns]
    : sortedOtherColumns
}

/**
 * 根据页面类型获取默认显示的字段（按顺序排列）
 * @param {string} pageType 页面类型
 * @returns {Array} 默认字段列表
 */
export function getPageDefaultColumns(pageType) {
  const options = getPageColumnOptions(pageType)
  const sortedOptions = sortColumnsByOrder(options)

  // 根据页面类型返回默认显示的字段（按标准顺序）
  switch (pageType) {
    case 'overview':
      // 总览页面：显示核心字段
      return [
        'id', 'item_name', 'specification', 'unit', 'budget_quantity', 'budget_unit_price',
        'budget_total_amount', 'requester_name', 'hierarchy_path', 'created_at',
        'purchase_type', 'status', 'action'
      ].filter(key => sortedOptions.find(opt => opt.key === key))

    case 'requirement':
      // 需求提报页面：显示基础信息和需求相关字段（操作列固定显示，不在筛选配置中）
      return [
        'id', 'item_name', 'specification', 'unit', 'budget_quantity', 'budget_unit_price',
        'budget_total_amount', 'requester_name', 'hierarchy_path', 'created_at',
        'purchase_type', 'remarks', 'status'
      ].filter(key => sortedOptions.find(opt => opt.key === key))

    case 'approval':
      // 审批页面：显示基础信息、需求信息和审批相关字段
      return [
        'id', 'item_name', 'specification', 'unit', 'budget_quantity', 'budget_unit_price',
        'budget_total_amount', 'requester_name', 'hierarchy_path', 'created_at',
        'purchase_type', 'remarks', 'submission_date', 'approver_name', 'approved_at',
        'status', 'action'
      ].filter(key => sortedOptions.find(opt => opt.key === key))

    case 'purchase':
      // 采购页面：显示基础信息、需求信息、审批信息和采购相关字段
      return [
        'id', 'item_name', 'specification', 'unit', 'budget_quantity', 'budget_unit_price',
        'budget_total_amount', 'requester_name', 'hierarchy_path', 'created_at',
        'purchase_type', 'remarks', 'submission_date', 'approver_name', 'approved_at',
        'purchase_unit_price', 'purchase_quantity', 'supplier_name', 'purchaser_name',
        'purchase_date', 'status', 'action'
      ].filter(key => sortedOptions.find(opt => opt.key === key))

    case 'acceptance':
      // 验收页面：显示基础信息、需求信息、审批信息、采购信息和验收相关字段
      return [
        'id', 'item_name', 'specification', 'unit', 'budget_quantity', 'budget_unit_price',
        'budget_total_amount', 'requester_name', 'hierarchy_path', 'created_at',
        'purchase_type', 'remarks', 'submission_date', 'approver_name', 'approved_at',
        'purchase_unit_price', 'purchase_quantity', 'supplier_name', 'purchaser_name',
        'purchase_date', 'acceptance_quantity', 'acceptor_name', 'acceptance_date',
        'status', 'action'
      ].filter(key => sortedOptions.find(opt => opt.key === key))

    case 'settlement':
      // 结算页面：显示所有相关字段
      return [
        'id', 'item_name', 'specification', 'unit', 'budget_quantity', 'budget_unit_price',
        'budget_total_amount', 'requester_name', 'hierarchy_path', 'created_at',
        'purchase_type', 'remarks', 'submission_date', 'approver_name', 'approved_at',
        'purchase_unit_price', 'purchase_quantity', 'supplier_name', 'purchaser_name',
        'purchase_date', 'acceptance_quantity', 'acceptor_name', 'acceptance_date',
        'settlement_amount', 'reimbursement_person', 'reimbursement_date',
        'transaction_number', 'payee_name', 'settlement_remarks', 'status', 'action'
      ].filter(key => sortedOptions.find(opt => opt.key === key))

    default:
      // 默认显示基础字段
      return [
        'id', 'item_name', 'specification', 'unit', 'budget_total_amount',
        'requester_name', 'hierarchy_path', 'created_at', 'status', 'action'
      ].filter(key => sortedOptions.find(opt => opt.key === key))
  }
}

/**
 * 根据页面类型获取预设配置
 * @param {string} pageType 页面类型
 * @returns {Object} 预设配置
 */
export function getPageColumnPresets(pageType) {
  // 基础预设配置（按标准顺序）
  const basePresets = {
    basic: ['id', 'item_name', 'specification', 'budget_total_amount', 'purchase_type', 'status', 'action'],
    detailed: ['id', 'item_name', 'specification', 'unit', 'budget_quantity', 'budget_unit_price', 'budget_total_amount', 'requester_name', 'hierarchy_path', 'created_at', 'purchase_type', 'remarks', 'status', 'action']
  }

  switch (pageType) {
    case 'overview':
      return {
        ...basePresets,
        requirement: ['requester_name', 'hierarchy_path', 'created_at'],
        approval: ['approver_name', 'approved_at'],
        purchase: ['purchase_quantity', 'purchase_unit_price', 'supplier_name'],
        acceptance: ['acceptance_quantity', 'acceptor_name', 'acceptance_date'],
        settlement: ['settlement_amount', 'reimburser_name', 'reimbursement_date']
      }

    case 'requirement':
      return {
        ...basePresets,
        requirement: ['requester_name', 'hierarchy_path', 'created_at', 'remarks']
      }

    case 'approval':
      return {
        ...basePresets,
        requirement: ['requester_name', 'hierarchy_path', 'created_at'],
        approval: ['submission_date', 'approver_name', 'approved_at', 'approval_comment']
      }

    case 'purchase':
      return {
        ...basePresets,
        requirement: ['requester_name', 'hierarchy_path'],
        approval: ['submission_date', 'approver_name', 'approved_at'],
        purchase: ['purchase_quantity', 'purchase_unit_price', 'supplier_name', 'purchaser_name', 'purchase_date']
      }

    case 'acceptance':
      return {
        ...basePresets,
        requirement: ['id', 'item_name', 'specification', 'budget_quantity', 'budget_unit_price', 'requester_name', 'hierarchy_path', 'created_at', 'status', 'action'],
        approval: ['id', 'item_name', 'specification', 'budget_quantity', 'budget_unit_price', 'requester_name', 'hierarchy_path', 'submission_date', 'approver_name', 'approved_at', 'status', 'action'],
        purchase: ['id', 'item_name', 'specification', 'budget_quantity', 'budget_unit_price', 'purchase_quantity', 'purchase_unit_price', 'supplier_name', 'purchaser_name', 'purchase_date', 'status', 'action'],
        acceptance: ['id', 'item_name', 'specification', 'budget_quantity', 'budget_unit_price', 'purchase_quantity', 'purchase_unit_price', 'acceptance_quantity', 'supplier_name', 'acceptor_name', 'acceptance_date', 'status', 'action']
      }

    case 'settlement':
      return {
        ...basePresets,
        requirement: ['requester_name', 'hierarchy_path'],
        approval: ['approver_name', 'approved_at'],
        purchase: ['purchase_quantity', 'purchase_unit_price', 'supplier_name'],
        acceptance: ['acceptance_quantity', 'acceptor_name', 'acceptance_date'],
        settlement: ['settlement_amount', 'reimburser_name', 'reimbursement_date', 'transaction_number']
      }

    default:
      return basePresets
  }
}

/**
 * 根据页面类型获取字段分类配置
 * @param {string} pageType 页面类型
 * @returns {Array} 字段分类配置
 */
export function getPageFieldCategories(pageType) {
  const baseCategories = [
    { key: 'basic', title: '基础信息' },
  ]

  switch (pageType) {
    case 'overview':
      return [
        { key: 'basic', title: '基础信息' },
        { key: 'requirement', title: '需求信息' },
        { key: 'organization', title: '组织和人员' },
        { key: 'approval', title: '审批信息' },
        { key: 'purchase', title: '采购信息' },
        { key: 'acceptance', title: '验收信息' },
        { key: 'settlement', title: '结算信息' }
      ]

    case 'requirement':
      return [
        { key: 'basic', title: '基础信息' },
        { key: 'requirement', title: '需求信息' },
        { key: 'organization', title: '组织和人员' }
      ]

    case 'approval':
      return [
        { key: 'basic', title: '基础信息' },
        { key: 'requirement', title: '需求信息' },
        { key: 'organization', title: '组织和人员' },
        { key: 'approval', title: '审批信息' }
      ]

    case 'purchase':
      return [
        { key: 'basic', title: '基础信息' },
        { key: 'requirement', title: '需求信息' },
        { key: 'organization', title: '组织和人员' },
        { key: 'approval', title: '审批信息' },
        { key: 'purchase', title: '采购信息' },
        { key: 'action', title: '操作' }
      ]

    case 'acceptance':
      return [
        { key: 'basic', title: '基础信息' },
        { key: 'requirement', title: '需求信息' },
        { key: 'organization', title: '组织和人员' },
        { key: 'approval', title: '审批信息' },
        { key: 'purchase', title: '采购信息' },
        { key: 'acceptance', title: '验收信息' }
      ]

    case 'settlement':
      return [
        { key: 'basic', title: '基础信息' },
        { key: 'requirement', title: '需求信息' },
        { key: 'organization', title: '组织和人员' },
        { key: 'approval', title: '审批信息' },
        { key: 'purchase', title: '采购信息' },
        { key: 'acceptance', title: '验收信息' },
        { key: 'settlement', title: '结算信息' }
      ]

    default:
      return baseCategories
  }
}

/**
 * 根据页面类型获取预设菜单选项
 * @param {string} pageType 页面类型
 * @returns {Array} 预设菜单选项
 */
export function getPagePresetMenuItems(pageType) {
  const baseItems = [
    { key: 'basic', title: '基础信息' },
    { key: 'detailed', title: '详细信息' }
  ]

  switch (pageType) {
    case 'overview':
      return [
        ...baseItems,
        { type: 'divider' },
        { key: 'requirement', title: '需求信息' },
        { key: 'approval', title: '审批信息' },
        { key: 'purchase', title: '采购信息' },
        { key: 'acceptance', title: '验收信息' },
        { key: 'settlement', title: '结算信息' }
      ]

    case 'requirement':
      return [
        ...baseItems,
        { type: 'divider' },
        { key: 'requirement', title: '需求信息' }
      ]

    case 'approval':
      return [
        ...baseItems,
        { type: 'divider' },
        { key: 'requirement', title: '需求信息' },
        { key: 'approval', title: '审批信息' }
      ]

    case 'purchase':
      return [
        ...baseItems,
        { type: 'divider' },
        { key: 'requirement', title: '需求详情' },
        { key: 'approval', title: '审批状态' },
        { key: 'purchase', title: '采购信息' },
        { key: 'supplier', title: '供应商信息' },
        { type: 'divider' }
      ]

    case 'acceptance':
      return [
        ...baseItems,
        { type: 'divider' },
        { key: 'requirement', title: '需求信息' },
        { key: 'approval', title: '审批信息' },
        { key: 'purchase', title: '采购信息' },
        { key: 'acceptance', title: '验收信息' }
      ]

    case 'settlement':
      return [
        ...baseItems,
        { type: 'divider' },
        { key: 'requirement', title: '需求信息' },
        { key: 'approval', title: '审批信息' },
        { key: 'purchase', title: '采购信息' },
        { key: 'acceptance', title: '验收信息' },
        { key: 'settlement', title: '结算信息' }
      ]

    default:
      return baseItems
  }
}

/**
 * 根据页面类型获取导出字段选项
 * @param {string} pageType 页面类型
 * @returns {Array} 导出字段选项
 */
export function getPageExportFieldOptions(pageType) {
  const baseOptions = [
    // 基础需求信息字段
    { label: 'ID', value: 'id', category: 'basic', required: true },
    { label: '物品种类', value: 'item_category', category: 'basic', required: false },
    { label: '物品名称', value: 'item_name', category: 'basic', required: true },
    { label: '规格型号', value: 'specification', category: 'basic', required: true },
    { label: '计量单位', value: 'unit', category: 'basic', required: false },
    { label: '采购类型', value: 'purchase_type', category: 'basic', required: false },
    { label: '状态', value: 'status', category: 'basic', required: true },
    { label: '创建时间', value: 'created_at', category: 'basic', required: true },

    // 需求信息
    { label: '需求数量', value: 'budget_quantity', category: 'requirement', required: true },
    { label: '预算单价', value: 'budget_unit_price', category: 'requirement', required: false },
    { label: '预算金额', value: 'budget_total_amount', category: 'requirement', required: false },
    { label: '需求来源', value: 'requirement_source', category: 'requirement', required: false },
    { label: '采购方式', value: 'procurement_method', category: 'requirement', required: false },
    { label: '经费项目', value: 'fund_project', category: 'requirement', required: false },
    { label: '需求备注', value: 'remarks', category: 'requirement', required: false },

    // 组织和人员字段
    { label: '部门层级路径', value: 'hierarchy_path', category: 'organization', required: true },
    { label: '申请人姓名', value: 'requester_name', category: 'organization', required: true },
    { label: '审批人姓名', value: 'approver_name', category: 'organization', required: false },
    { label: '采购员姓名', value: 'purchaser_name', category: 'organization', required: false },
    { label: '验收人姓名', value: 'acceptor_name', category: 'organization', required: false },
    { label: '报销人姓名', value: 'reimburser_name', category: 'organization', required: false }
  ]

  switch (pageType) {
    case 'requirement':
      // 需求提报阶段：显示基础信息、需求信息、组织和人员信息
      return baseOptions.filter(opt => ['basic', 'requirement', 'organization'].includes(opt.category))

    case 'approval':
      // 审批阶段：显示基础信息、需求信息、组织和人员信息、审批信息
      return [
        ...baseOptions.filter(opt => ['basic', 'requirement', 'organization'].includes(opt.category)),
        // 审批相关字段
        { label: '提交时间', value: 'submission_date', category: 'approval' },
        { label: '审批时间', value: 'approved_at', category: 'approval' },
        { label: '审批意见', value: 'approval_comment', category: 'approval' },
        { label: '驳回原因', value: 'rejection_reason', category: 'approval' }
      ]

    case 'purchase':
      // 采购阶段：显示基础信息、需求信息、组织和人员信息、审批信息、采购信息
      return [
        ...baseOptions.filter(opt => ['basic', 'requirement', 'organization'].includes(opt.category)),
        // 审批相关字段
        { label: '提交时间', value: 'submission_date', category: 'approval' },
        { label: '审批时间', value: 'approved_at', category: 'approval' },
        // 采购相关字段
        { label: '采购数量', value: 'purchase_quantity', category: 'purchase' },
        { label: '采购单价', value: 'purchase_unit_price', category: 'purchase' },
        { label: '采购金额', value: 'purchase_total_amount', category: 'purchase' },
        { label: '供应商名称', value: 'supplier_name', category: 'purchase' },
        { label: '采购时间', value: 'purchase_date', category: 'purchase' },
        { label: '采购备注', value: 'purchase_remarks', category: 'purchase' }
      ]

    case 'acceptance':
      // 验收阶段：显示基础信息、需求信息、组织和人员信息、审批信息、采购信息、验收信息
      return [
        ...baseOptions.filter(opt => ['basic', 'requirement', 'organization'].includes(opt.category)),
        // 审批相关字段
        { label: '提交时间', value: 'submission_date', category: 'approval' },
        { label: '审批时间', value: 'approved_at', category: 'approval' },
        // 采购相关字段
        { label: '采购数量', value: 'purchase_quantity', category: 'purchase' },
        { label: '采购单价', value: 'purchase_unit_price', category: 'purchase' },
        { label: '供应商名称', value: 'supplier_name', category: 'purchase' },
        { label: '采购时间', value: 'purchase_date', category: 'purchase' },
        // 验收相关字段
        { label: '验收数量', value: 'acceptance_quantity', category: 'acceptance' },
        { label: '验收时间', value: 'acceptance_date', category: 'acceptance' },
        { label: '验收备注', value: 'acceptance_remarks', category: 'acceptance' },
        { label: '快递公司', value: 'courier_company', category: 'acceptance' },
        { label: '快递单号', value: 'tracking_number', category: 'acceptance' },
        { label: '商品发货地', value: 'shipping_origin', category: 'acceptance' }
      ]

    case 'overview':
      // 总览页面：显示所有字段
      return [
        ...baseOptions.filter(opt => ['basic', 'requirement', 'organization'].includes(opt.category)),
        // 审批相关字段
        { label: '提交时间', value: 'submission_date', category: 'approval' },
        { label: '审批时间', value: 'approved_at', category: 'approval' },
        // 采购相关字段
        { label: '采购数量', value: 'purchase_quantity', category: 'purchase' },
        { label: '采购单价', value: 'purchase_unit_price', category: 'purchase' },
        { label: '采购金额', value: 'purchase_total_amount', category: 'purchase' },
        { label: '供应商名称', value: 'supplier_name', category: 'purchase' },
        { label: '采购时间', value: 'purchase_date', category: 'purchase' },
        { label: '采购备注', value: 'purchase_remarks', category: 'purchase' },
        // 验收相关字段
        { label: '验收数量', value: 'acceptance_quantity', category: 'acceptance' },
        { label: '验收时间', value: 'acceptance_date', category: 'acceptance' },
        { label: '验收备注', value: 'acceptance_remarks', category: 'acceptance' },
        // 结算相关字段
        { label: '结算金额', value: 'settlement_amount', category: 'settlement' },
        { label: '结算时间', value: 'reimbursement_date', category: 'settlement' },
        { label: '报销凭证号', value: 'reimbursement_voucher_no', category: 'settlement' },
        { label: '结算备注', value: 'settlement_remarks', category: 'settlement' }
      ]

    case 'settlement':
      // 结算阶段：显示所有字段
      return [
        ...baseOptions.filter(opt => ['basic', 'requirement', 'organization'].includes(opt.category)),
        // 审批相关字段
        { label: '提交时间', value: 'submission_date', category: 'approval' },
        { label: '审批时间', value: 'approved_at', category: 'approval' },
        // 采购相关字段
        { label: '采购数量', value: 'purchase_quantity', category: 'purchase' },
        { label: '采购单价', value: 'purchase_unit_price', category: 'purchase' },
        { label: '供应商名称', value: 'supplier_name', category: 'purchase' },
        { label: '采购时间', value: 'purchase_date', category: 'purchase' },
        // 验收相关字段
        { label: '验收数量', value: 'acceptance_quantity', category: 'acceptance' },
        { label: '验收时间', value: 'acceptance_date', category: 'acceptance' },
        { label: '快递公司', value: 'courier_company', category: 'acceptance' },
        { label: '快递单号', value: 'tracking_number', category: 'acceptance' },
        { label: '商品发货地', value: 'shipping_origin', category: 'acceptance' },
        // 结算相关字段
        { label: '结算金额', value: 'settlement_amount', category: 'settlement' },
        { label: '结算时间', value: 'reimbursement_date', category: 'settlement' },
        { label: '报销凭证号', value: 'reimbursement_voucher_no', category: 'settlement' },
        { label: '交易流水号', value: 'transaction_number', category: 'settlement' },
        { label: '收款人', value: 'payee_name', category: 'settlement' },
        { label: '收款账号', value: 'payee_account', category: 'settlement' },
        { label: '结算备注', value: 'settlement_remarks', category: 'settlement' }
      ]

    default:
      return baseOptions
  }
}

/**
 * 根据页面类型获取默认导出字段
 * @param {string} pageType 页面类型
 * @returns {Array} 默认导出字段
 */
export function getPageDefaultExportFields(pageType) {
  switch (pageType) {
    case 'overview':
      return ['id', 'item_name', 'specification', 'budget_total_amount', 'requester_name', 'hierarchy_path', 'status', 'created_at']

    case 'requirement':
      return ['id', 'item_name', 'specification', 'budget_total_amount', 'requester_name', 'hierarchy_path', 'status', 'created_at']

    case 'approval':
      return ['item_name', 'specification', 'budget_total_amount', 'requester_name', 'status', 'submission_date', 'approver_name']

    case 'purchase':
      return ['item_name', 'specification', 'budget_total_amount', 'purchase_quantity', 'purchase_unit_price', 'supplier_name', 'status']

    case 'acceptance':
      return ['item_name', 'specification', 'acceptance_quantity', 'acceptor_name', 'courier_company', 'tracking_number', 'acceptance_date', 'status']

    case 'settlement':
      return ['id', 'item_name', 'specification', 'settlement_amount', 'reimburser_name', 'reimbursement_date', 'transaction_number', 'status']

    default:
      return ['item_name', 'specification', 'status', 'created_at']
  }
}
