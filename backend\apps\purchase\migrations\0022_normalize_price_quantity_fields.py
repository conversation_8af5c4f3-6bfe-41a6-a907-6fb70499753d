# Generated by Django 5.2.3 on 2025-06-27 05:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0021_alter_purchaserequest_approved_at'),
    ]

    operations = [
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='quantity',
            new_name='budget_quantity',
        ),
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='unit_price',
            new_name='budget_unit_price',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='accepted_quantity',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='actual_quantity',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='actual_total_amount',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='actual_unit_price',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='total_amount',
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='acceptance_quantity',
            field=models.PositiveIntegerField(default=0, help_text='实际验收确认的数量', verbose_name='验收数量'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='budget_total_amount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='预算单价 × 需求数量', max_digits=15, verbose_name='预算金额'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='purchase_quantity',
            field=models.PositiveIntegerField(default=0, help_text='实际采购的数量', verbose_name='采购数量'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='purchase_total_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='采购单价 × 采购数量', max_digits=15, null=True, verbose_name='采购金额'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='purchase_unit_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='实际采购单价', max_digits=12, null=True, verbose_name='采购单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_avg_price',
            field=models.DecimalField(decimal_places=2, default=0, help_text='同部门同物品的历史平均采购单价', max_digits=12, verbose_name='历史平均采购单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_max_price',
            field=models.DecimalField(decimal_places=2, default=0, help_text='历史最高采购单价', max_digits=12, verbose_name='历史最高采购单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_min_price',
            field=models.DecimalField(decimal_places=2, default=0, help_text='历史最低采购单价', max_digits=12, verbose_name='历史最低采购单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='settlement_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='采购单价 × 验收数量', max_digits=15, null=True, verbose_name='结算金额'),
        ),
    ]
