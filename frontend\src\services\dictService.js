/**
 * 统一字典服务
 * 提供字典数据的获取、缓存和转换功能
 */

import api from '@/api'



class DictService {
  constructor() {
    // 内存缓存
    this.cache = new Map()
    // 缓存过期时间（5分钟）
    this.cacheExpiry = 5 * 60 * 1000
    // 正在加载的Promise，避免重复请求
    this.loadingPromises = new Map()
  }



  /**
   * 获取字典数据
   * @param {string} dictType - 字典类型
   * @param {boolean} enableCache - 是否启用缓存
   * @returns {Promise<Array>} 字典数据数组
   */
  async getDict(dictType, enableCache = true) {
    const cacheKey = `dict_${dictType}`
    
    // 检查缓存
    if (enableCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)
      if (Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data
      }
      // 缓存过期，删除
      this.cache.delete(cacheKey)
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)
    }

    // 创建加载Promise
    const loadPromise = this._loadDict(dictType)
    this.loadingPromises.set(cacheKey, loadPromise)

    try {
      const data = await loadPromise
      
      // 缓存数据
      if (enableCache) {
        this.cache.set(cacheKey, {
          data,
          timestamp: Date.now()
        })
      }
      
      return data
    } finally {
      // 清除加载Promise
      this.loadingPromises.delete(cacheKey)
    }
  }

  /**
   * 实际加载字典数据
   * @param {string} dictType - 字典类型
   * @returns {Promise<Array>} 字典数据数组
   */
  async _loadDict(dictType) {
    try {
      const response = await api.dicts.getDict(dictType, true)
      if (response.code === 200 && response.data && response.data.items) {
        return response.data.items
      }
      // 获取字典数据失败：返回空数组
      return []
    } catch (error) {
      // 加载字典数据失败：返回空数组
      return []
    }
  }

  /**
   * 获取字典映射对象
   * @param {string} dictType - 字典类型
   * @returns {Promise<Object>} 字典映射对象 {code: name}
   */
  async getDictMap(dictType) {
    const items = await this.getDict(dictType)
    const map = {}
    items.forEach(item => {
      map[item.code] = item.name
    })
    return map
  }

  /**
   * 字典值转换
   * @param {string} dictType - 字典类型
   * @param {string} code - 字典编码
   * @param {string} defaultValue - 默认值
   * @returns {Promise<string>} 转换后的显示值
   */
  async getText(dictType, code, defaultValue = '-') {
    if (!code) return defaultValue
    
    try {
      const map = await this.getDictMap(dictType)
      return map[code] || code || defaultValue
    } catch (error) {
      // 字典转换失败：返回原始code或默认值
      return code || defaultValue
    }
  }

  /**
   * 批量获取字典数据
   * @param {Array<string>} dictTypes - 字典类型数组
   * @returns {Promise<Object>} 字典数据对象 {dictType: items}
   */
  async getBatchDicts(dictTypes) {
    const promises = dictTypes.map(type => 
      this.getDict(type).then(data => ({ type, data }))
    )
    
    const results = await Promise.all(promises)
    const batchData = {}
    results.forEach(({ type, data }) => {
      batchData[type] = data
    })
    
    return batchData
  }

  /**
   * 清除缓存
   * @param {string} dictType - 字典类型，不传则清除所有缓存
   */
  clearCache(dictType = null) {
    if (dictType) {
      const cacheKey = `dict_${dictType}`
      this.cache.delete(cacheKey)
      this.loadingPromises.delete(cacheKey)
    } else {
      this.cache.clear()
      this.loadingPromises.clear()
    }
  }

  /**
   * 刷新字典数据
   * @param {string} dictType - 字典类型
   * @returns {Promise<Array>} 刷新后的字典数据
   */
  async refreshDict(dictType) {
    this.clearCache(dictType)
    return this.getDict(dictType, false)
  }

  // 预定义的字典转换函数，保持向后兼容
  async getStatusText(status) {
    return this.getText('status', status)
  }

  async getPurchaseTypeText(type) {
    return this.getText('purchase_type', type)
  }

  async getProcurementMethodText(method) {
    return this.getText('procurement_method', method)
  }

  async getFundProjectText(project) {
    return this.getText('fund_project', project)
  }

  async getUnitText(unit) {
    return this.getText('unit', unit)
  }

  async getItemCategoryText(category) {
    return this.getText('item_category', category)
  }



  async getUrgencyLevelText(level) {
    return this.getText('urgency_level', level)
  }

  /**
   * 获取字典选项（用于下拉框等组件）
   * @param {string} dictType - 字典类型
   * @returns {Promise<Array>} 选项数组 [{label, value}]
   */
  async getDictOptions(dictType) {
    const items = await this.getDict(dictType)
    return items.map(item => ({
      label: item.name,
      value: item.code,
      ...item
    }))
  }
}

// 创建单例实例
const dictService = new DictService()

export default dictService

// 导出常用方法，方便直接使用
export const {
  getDict,
  getDictMap,
  getText,
  getBatchDicts,
  clearCache,
  refreshDict,
  getStatusText,
  getPurchaseTypeText,
  getProcurementMethodText,
  getFundProjectText,
  getUnitText,
  getItemCategoryText,
  getUrgencyLevelText,
  getDictOptions
} = dictService
