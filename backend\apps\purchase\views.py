"""
采购管理系统 - 视图控制器

功能说明：
1. 提供采购需求的完整CRUD操作接口
2. 实现业务流程控制和状态转换
3. 提供数据统计和分析接口
4. 支持批量操作和文件导入导出

技术特点：
- 基于Django REST Framework的API设计
- 完善的权限控制和数据验证
- 高性能的数据库查询优化
- 统一的错误处理和日志记录

<AUTHOR>
@version 1.0.0
@since 2025-01-08
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.utils import timezone
from django.db import models
from django.db.models import Q, Avg, Count, Sum, F
from django.shortcuts import get_object_or_404
import logging
import os
from django.core.cache import cache

# 初始化logger
logger = logging.getLogger(__name__)
from django.core.paginator import Paginator
from django.contrib.auth.models import User
import logging
from datetime import datetime

logger = logging.getLogger(__name__)
from .models import PurchaseRequest, Dictionary
from .serializers import (
    PurchaseRequestSerializer, PurchaseRequestCreateSerializer,
    PurchaseRequestUpdateSerializer, PurchaseRequestApprovalSerializer,
    PurchaseRequestAcceptanceSerializer, PurchaseRequestReimbursementSerializer
)
from .filters import PurchaseRequestFilter
from .utils import generate_purchase_template, import_purchase_requests, export_purchase_requests
from apps.common.permissions import IsOwnerOrReadOnly, IsApproverOrReadOnly, IsAcceptorOrReadOnly, IsFinanceOrReadOnly


class PurchaseRequestListCreateView(generics.ListCreateAPIView):
    """
    采购需求列表和创建接口（整合验收和报销功能）
    GET /api/requests - 获取采购需求列表
    POST /api/requests - 创建采购需求
    """
    serializer_class = PurchaseRequestSerializer
    permission_classes = [permissions.IsAuthenticated]  # 只要求用户认证
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = PurchaseRequestFilter
    search_fields = ['item_name', 'specification', 'hierarchy_path']
    ordering_fields = ['created_at', 'submission_date', 'budget_total_amount']
    ordering = ['-created_at']

    # 高性能分页类
    class CustomPagination(PageNumberPagination):
        page_size = 20  # 增加默认页面大小
        page_size_query_param = 'page_size'
        max_page_size = 100  # 限制最大页面大小，防止性能问题

    pagination_class = CustomPagination

    def get_queryset(self):
        """
        根据用户角色返回不同的查询集，优化查询性能
        """
        user = self.request.user

        if not user.is_authenticated:
            return PurchaseRequest.objects.none()

        # 高性能基础查询集 - 根据请求参数优化
        fields_param = self.request.GET.get('fields')
        if fields_param:
            # 如果指定了字段，需要过滤出实际的模型字段，排除序列化器计算字段
            requested_fields = [f.strip() for f in fields_param.split(',')]

            # 定义序列化器计算字段（不是模型字段）
            computed_fields = {
                'requester_name', 'purchaser_name', 'acceptor_name',
                'reimburser_name', 'approver_name', 'exception_approver_name',
                'status_display', 'item_category_display', 'unit_display',
                'procurement_method_display', 'fund_project_display', 'purchase_type_display',
                'status_code', 'purchase_type_code', 'acceptance_photos',
                'is_price_warning', 'can_auto_approve_acceptance',
                'quantity', 'unit_price', 'total_amount', 'actual_quantity',
                'actual_unit_price', 'actual_total_amount', 'accepted_quantity'
            }

            # 过滤出实际的模型字段
            model_fields = [f for f in requested_fields if f not in computed_fields]

            # 添加必要的关联字段以支持计算字段
            required_relations = set()
            if any(f in requested_fields for f in ['requester_name']):
                required_relations.add('requester')
            if any(f in requested_fields for f in ['purchaser_name']):
                required_relations.add('purchaser')
            if any(f in requested_fields for f in ['acceptor_name']):
                required_relations.add('acceptor')
            if any(f in requested_fields for f in ['reimburser_name']):
                required_relations.add('reimburser')
            if any(f in requested_fields for f in ['approver_name']):
                required_relations.add('approver')
            if any(f in requested_fields for f in ['exception_approver_name']):
                required_relations.add('exception_approver')

            # 将关联字段添加到模型字段列表
            model_fields.extend(required_relations)

            # 确保包含基本字段
            essential_fields = ['id', 'created_at']
            for field in essential_fields:
                if field not in model_fields:
                    model_fields.append(field)

            base_queryset = PurchaseRequest.objects.only(*model_fields).select_related(
                *required_relations
            )
        else:
            # 默认查询集，使用 select_related 优化外键查询
            base_queryset = PurchaseRequest.objects.select_related(
                'requester', 'purchaser', 'acceptor', 'reimburser', 'approver', 'exception_approver'
            )

        # 检查是否是导出配置模式（用于物资采购页面的导出配置模态框）
        export_config_mode = self.request.GET.get('export_config_mode', 'false').lower() == 'true'

        # 检查是否是结算报销导出模式
        reimbursement_export = self.request.GET.get('reimbursement_export', 'false').lower() == 'true'

        if export_config_mode:
            # 导出配置模式：只显示待采购、已采购、已退回三种状态的数据
            base_queryset = base_queryset.filter(status__in=['pending_purchase', 'purchased', 'returned'])
        elif reimbursement_export:
            # 结算报销导出模式：只显示待结算、已结算状态的数据
            base_queryset = base_queryset.filter(status__in=['pending_reimbursement', 'settled'])

        base_queryset = base_queryset.order_by('-created_at')

        # 管理员可以看到所有记录
        if user.is_superuser or getattr(user, 'role', '') == 'admin':
            return base_queryset

        # 审批员可以看到所有记录（用于审批）
        if getattr(user, 'role', '') in ['approver']:
            return base_queryset

        # 验收员可以看到已审批的记录
        if getattr(user, 'role', '') in ['acceptor']:
            return base_queryset.filter(status__in=['approved', 'purchased', 'pending_acceptance', 'accepted'])

        # 财务员可以看到已验收的记录
        if getattr(user, 'role', '') in ['finance']:
            return base_queryset.filter(status__in=['accepted', 'pending_reimbursement', 'settled'])

        # 普通用户只能看到自己创建的记录
        return base_queryset.filter(requester=user)

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return PurchaseRequestCreateSerializer
        return PurchaseRequestSerializer

    def list(self, request, *args, **kwargs):
        # 处理前端可能发送的无效参数
        query_params = request.GET.copy()

        # 移除无效的dateRange参数（前端应该使用具体的日期字段）
        if 'dateRange' in query_params:
            del query_params['dateRange']

        # 更新request.GET
        request.GET = query_params

        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_response = self.get_paginated_response(serializer.data)
            return Response({
                'code': 200,
                'message': 'success',
                'data': {
                    'results': serializer.data,
                    'count': paginated_response.data['count'],
                    'next': paginated_response.data['next'],
                    'previous': paginated_response.data['previous']
                }
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'results': serializer.data,
                'count': queryset.count()
            }
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            purchase_request = serializer.save(requester=request.user)

            # 记录创建日志
            from apps.system.logging_service import LoggingService
            LoggingService.log_user_action(
                user=request.user.id,
                log_type='create',
                action=f'创建采购需求: {purchase_request.item_name}',
                target_model='PurchaseRequest',
                target_id=purchase_request.id,
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_data=request.data
            )

            return Response({
                'code': 200,
                'message': '采购需求创建成功',
                'data': PurchaseRequestSerializer(purchase_request).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'code': 400,
            'message': '数据验证失败',
            'data': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PurchaseRequestDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    采购需求详情、更新和删除接口
    GET /api/requests/{id} - 获取采购需求详情
    PUT /api/requests/{id} - 更新采购需求
    DELETE /api/requests/{id} - 删除采购需求
    """
    serializer_class = PurchaseRequestSerializer

    def get_queryset(self):
        """
        获取查询集，优化查询性能
        """
        return PurchaseRequest.objects.select_related(
            'requester', 'purchaser', 'acceptor', 'reimburser'
        )

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return PurchaseRequestUpdateSerializer
        return PurchaseRequestSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        logger.info(f"更新采购需求 {instance.id}, 当前状态: {instance.status}")
        logger.info(f"请求数据: {request.data}")

        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            purchase_request = serializer.save()
            return Response({
                'code': 200,
                'message': '采购需求更新成功',
                'data': PurchaseRequestSerializer(purchase_request).data
            })

        logger.error(f"数据验证失败: {serializer.errors}")
        return Response({
            'code': 400,
            'message': '数据验证失败',
            'data': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.status != 'draft':
            return Response({
                'code': 400,
                'message': '只能删除草稿状态的采购需求',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        instance.delete()
        return Response({
            'code': 200,
            'message': '采购需求删除成功',
            'data': {}
        })


# 状态转换接口
@api_view(['POST'])
def submit_request(request, pk):
    """
    提交采购需求（草稿→待审批）
    POST /api/requests/{id}/submit
    """
    try:
        purchase_request = PurchaseRequest.objects.get(pk=pk)

        if purchase_request.status != 'draft':
            return Response({
                'code': 400,
                'message': '只能提交草稿状态的需求',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        purchase_request.submit()

        return Response({
            'code': 200,
            'message': '提交成功',
            'data': {'status': purchase_request.status}
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'提交失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def approve_request(request, pk):
    """
    审批采购需求（待审批→已审批）
    POST /api/requests/{id}/approve
    需要权限：purchase:approval:approve
    """
    # 权限检查
    from apps.system.permissions import has_permission
    if not has_permission(request.user, 'purchase:approval:approve'):
        return Response({
            'code': 403,
            'message': '权限不足，无法执行审批操作',
            'data': {
                'required_permission': 'purchase:approval:approve',
                'user_role': getattr(request.user, 'role', None)
            }
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        purchase_request = PurchaseRequest.objects.get(pk=pk)

        if purchase_request.status != 'pending_approval':
            return Response({
                'code': 400,
                'message': '只能审批待审批状态的需求',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = PurchaseRequestApprovalSerializer(data=request.data)
        if serializer.is_valid():
            purchase_request.approve(
                approver_id=request.user.id,
                comment=serializer.validated_data.get('comment', '')
            )

            return Response({
                'code': 200,
                'message': '审批成功',
                'data': {'status': purchase_request.status}
            })

        return Response({
            'code': 400,
            'message': '数据验证失败',
            'data': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'审批失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def reject_request(request, pk):
    """
    驳回采购需求（待审批→已驳回）
    POST /api/requests/{id}/reject
    需要权限：purchase:approval:reject
    """
    # 权限检查
    from apps.system.permissions import has_permission
    if not has_permission(request.user, 'purchase:approval:reject'):
        return Response({
            'code': 403,
            'message': '权限不足，无法执行驳回操作',
            'data': {
                'required_permission': 'purchase:approval:reject',
                'user_role': getattr(request.user, 'role', None)
            }
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        purchase_request = PurchaseRequest.objects.get(pk=pk)

        if purchase_request.status != 'pending_approval':
            return Response({
                'code': 400,
                'message': '只能驳回待审批状态的需求',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        reason = request.data.get('reason', '')
        purchase_request.reject(request.user.id, reason)

        return Response({
            'code': 200,
            'message': '驳回成功',
            'data': {'status': purchase_request.status}
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'驳回失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def purchase_request_func(request, pk):
    """
    执行采购（待采购→已采购）
    POST /api/requests/{id}/purchase
    """

@api_view(['POST'])
def return_request(request, pk):
    """
    退回采购需求（已审批→已退回）
    POST /api/requests/{id}/return
    """
    try:
        purchase_request = get_object_or_404(PurchaseRequest, pk=pk)

        # 检查状态
        if purchase_request.status not in ['approved', 'pending_purchase']:
            return Response({
                'code': 400,
                'message': '只有已审批或待采购状态的需求可以退回',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取退回理由
        reason = request.data.get('reason', '')
        if not reason:
            return Response({
                'code': 400,
                'message': '请填写退回理由',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 执行退回操作
        purchase_request.return_to_draft(request.user.id, reason)

        return Response({
            'code': 200,
            'message': '退回成功',
            'data': {
                'id': purchase_request.id,
                'status': purchase_request.status,
                'return_reason': purchase_request.return_reason,
                'return_date': purchase_request.return_date
            }
        })

    except Exception as e:
        logger.error(f"退回采购需求失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'退回失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def finance_return_to_acceptance(request, pk):
    """
    财务退回到待验收（待结算→待验收）
    POST /api/requests/{id}/finance_return_to_acceptance
    """
    try:
        purchase_request = get_object_or_404(PurchaseRequest, pk=pk)

        # 检查状态
        if purchase_request.status != 'pending_reimbursement':
            return Response({
                'code': 400,
                'message': '只有待结算状态的记录可以退回重新验收',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查权限 - 只有财务人员可以执行此操作
        if not hasattr(request.user, 'role') or request.user.role not in ['admin', 'finance']:
            return Response({
                'code': 403,
                'message': '只有财务人员可以执行此操作',
                'data': {}
            }, status=status.HTTP_403_FORBIDDEN)

        # 获取退回理由
        reason = request.data.get('reason', '')
        if not reason:
            return Response({
                'code': 400,
                'message': '请填写财务退回理由',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 执行财务退回操作
        purchase_request.finance_return_to_acceptance(request.user, reason)

        return Response({
            'code': 200,
            'message': '财务退回成功，已要求重新验收',
            'data': {
                'status': purchase_request.status,
                're_acceptance_required': purchase_request.re_acceptance_required,
                're_acceptance_count': purchase_request.re_acceptance_count
            }
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'财务退回失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def accept_request(request, pk):
    """
    验收采购需求（待验收→已验收）
    POST /api/requests/{id}/accept
    """
    try:
        purchase_request_obj = PurchaseRequest.objects.get(pk=pk)

        if purchase_request_obj.status != 'pending_acceptance':
            return Response({
                'code': 400,
                'message': '只能验收待验收状态的需求',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取验收信息参数
        data = request.data
        acceptance_quantity = data.get('acceptance_quantity')
        courier_company = data.get('courier_company')
        tracking_number = data.get('tracking_number')
        shipping_origin = data.get('shipping_origin')
        exception_reason = data.get('exception_reason')
        acceptance_remarks = data.get('acceptance_remarks')

        # 更新验收信息
        if acceptance_quantity is not None:
            purchase_request_obj.acceptance_quantity = acceptance_quantity
        if courier_company:
            purchase_request_obj.courier_company = courier_company
        if tracking_number:
            purchase_request_obj.tracking_number = tracking_number
        if shipping_origin:
            purchase_request_obj.shipping_origin = shipping_origin
        if exception_reason:
            purchase_request_obj.exception_reason = exception_reason
        if acceptance_remarks:
            purchase_request_obj.acceptance_remarks = acceptance_remarks

        # 如果提供了status参数，则完成验收操作
        if data.get('status') == 'accepted':
            # 准备传递给accept方法的参数
            accept_kwargs = {}
            if acceptance_quantity is not None:
                accept_kwargs['accepted_quantity'] = acceptance_quantity
            if courier_company:
                accept_kwargs['courier_company'] = courier_company
            if tracking_number:
                accept_kwargs['tracking_number'] = tracking_number
            if shipping_origin:
                accept_kwargs['shipping_origin'] = shipping_origin
            if acceptance_remarks:
                accept_kwargs['acceptance_remarks'] = acceptance_remarks
            if exception_reason:
                accept_kwargs['exception_reason'] = exception_reason

            purchase_request_obj.accept(request.user.id, **accept_kwargs)
            message = '验收完成'
        else:
            # 只保存验收信息，不改变状态
            purchase_request_obj.save()
            message = '验收信息保存成功'

        return Response({
            'code': 200,
            'message': message,
            'data': {'status': purchase_request_obj.status}
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'验收失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def reimburse_request(request, pk):
    """
    结算报销（待结算→已结算）
    POST /api/requests/{id}/reimburse
    """
    try:
        purchase_request_obj = PurchaseRequest.objects.get(pk=pk)

        if purchase_request_obj.status != 'pending_reimbursement':
            return Response({
                'code': 400,
                'message': '只能结算待报销审批状态的需求',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取结算信息参数
        data = request.data
        settlement_amount = data.get('settlement_amount')
        transaction_number = data.get('transaction_number')
        payee_account = data.get('payee_account')
        payee_name = data.get('payee_name')

        settlement_remark = data.get('settlement_remarks')

        # 更新结算信息
        if settlement_amount is not None:
            purchase_request_obj.settlement_amount = settlement_amount
        if data.get('transaction_number'):
            purchase_request_obj.transaction_number = data.get('transaction_number')
        if payee_account:
            purchase_request_obj.payee_account = payee_account
        if payee_name:
            purchase_request_obj.payee_name = payee_name

        if settlement_remark:
            purchase_request_obj.settlement_remarks = settlement_remark

        # 如果提供了status参数，则完成结算操作
        if data.get('status') == 'settled':
            purchase_request_obj.settle(request.user.id, **data)
            message = '结算完成'
        else:
            # 只保存结算信息，不改变状态
            purchase_request_obj.save()
            message = '结算信息保存成功'

        return Response({
            'code': 200,
            'message': message,
            'data': {'status': purchase_request_obj.status}
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'结算失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def upload_photo(request, pk):
    """
    上传照片
    POST /api/requests/{id}/upload_photo/
    """
    try:
        purchase_request = PurchaseRequest.objects.get(pk=pk)

        if 'file' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要上传的文件',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']
        # 修复：统一使用photo_type参数名
        photo_type = request.data.get('photo_type', request.data.get('type', 'item'))
        description = request.data.get('description', '')

        # 验证文件类型
        allowed_types = ['image/jpeg', 'image/png', 'image/jpg']
        if file.content_type not in allowed_types:
            return Response({
                'code': 400,
                'message': '只支持JPG、PNG格式的图片',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证文件大小（10MB）
        if file.size > 10 * 1024 * 1024:
            return Response({
                'code': 400,
                'message': '图片大小不能超过10MB',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 使用新的多照片模型保存
        from .models import AcceptancePhoto
        import os

        # 简化照片类型处理：直接使用前端传入的类型
        # 支持的照片类型：front(正面), side(侧面), overall(整体)
        valid_types = ['front', 'side', 'overall']
        if photo_type not in valid_types:
            # 兼容旧的类型映射
            type_mapping = {
                'item': 'front',
                'package': 'side',
                'courier': 'overall'
            }
            photo_type = type_mapping.get(photo_type, 'front')

        # 删除同类型的旧照片（确保每种类型只有一张照片）
        old_photos = AcceptancePhoto.objects.filter(
            purchase_request=purchase_request,
            photo_type=photo_type
        )
        for old_photo in old_photos:
            # 删除物理文件
            if old_photo.photo:
                try:
                    # 获取文件路径
                    file_path = old_photo.photo.path
                    # 删除数据库记录中的文件引用
                    old_photo.photo.delete(save=False)
                    # 删除物理文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"已删除旧照片文件: {file_path}")
                except Exception as e:
                    print(f"删除旧照片文件失败: {str(e)}")
            # 删除数据库记录
            old_photo.delete()

        # 创建新照片记录
        photo = AcceptancePhoto.objects.create(
            purchase_request=purchase_request,
            photo=file,
            photo_type=photo_type,
            description=description,
            uploader=request.user
        )

        return Response({
            'code': 200,
            'message': '照片上传成功',
            'data': {
                'url': photo.photo.url,
                'id': photo.id,
                'type': photo.photo_type,
                'description': photo.description
            }
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'照片上传失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 新增状态转换接口
@api_view(['POST'])
def generate_purchase_order_single(request, pk):
    """
    生成采购单（已审批→待采购）
    POST /api/requests/{id}/generate_purchase_order
    """
    try:
        purchase_request = PurchaseRequest.objects.get(pk=pk)

        if purchase_request.status != 'approved':
            return Response({
                'code': 400,
                'message': '只有已审批状态的记录才能生成采购单',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 使用模型的方法转为待采购状态
        purchase_request.to_pending_purchase()

        return Response({
            'code': 200,
            'message': '生成采购单成功',
            'data': {'status': purchase_request.status}
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'生成采购单失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def purchase_request(request, pk):
    """
    采购操作（待采购→已采购）
    POST /api/requests/{id}/purchase/
    """
    try:
        purchase_request_obj = PurchaseRequest.objects.get(pk=pk)

        if purchase_request_obj.status != 'pending_purchase':
            return Response({
                'code': 400,
                'message': '只有待采购状态的记录才能进行采购操作',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取采购信息参数
        data = request.data
        logger.info(f"采购API接收到的数据: {data}")
        purchase_quantity = data.get('purchase_quantity') or data.get('actual_quantity')  # 兼容旧字段名
        purchase_unit_price = data.get('purchase_unit_price') or data.get('actual_unit_price')  # 兼容旧字段名
        purchase_method = data.get('purchase_method')
        supplier = data.get('supplier_name')
        purchase_remark = data.get('purchase_remarks')
        logger.info(f"解析后的采购参数: quantity={purchase_quantity}, price={purchase_unit_price}, supplier={supplier}")

        # 更新采购信息
        if purchase_quantity is not None:
            purchase_request_obj.purchase_quantity = purchase_quantity
        if purchase_unit_price is not None:
            purchase_request_obj.purchase_unit_price = purchase_unit_price
        if purchase_method:
            purchase_request_obj.procurement_method = purchase_method
        if supplier:
            purchase_request_obj.supplier_name = supplier
        if purchase_remark:
            purchase_request_obj.purchase_remarks = purchase_remark

        # 如果提供了status参数，则完成采购操作
        if data.get('status') == 'purchased':
            try:
                # 传递采购信息给purchase方法
                purchase_kwargs = {}
                if purchase_quantity is not None:
                    purchase_kwargs['actual_quantity'] = purchase_quantity
                if purchase_unit_price is not None:
                    purchase_kwargs['actual_unit_price'] = purchase_unit_price
                if supplier:
                    purchase_kwargs['supplier_name'] = supplier
                if purchase_remark:
                    purchase_kwargs['purchase_remarks'] = purchase_remark

                logger.info(f"调用purchase方法，参数: {purchase_kwargs}")
                purchase_request_obj.purchase(request.user.id, **purchase_kwargs)
                message = '采购完成'
            except Exception as e:
                logger.error(f"采购操作失败: {str(e)}")
                logger.error(f"错误类型: {type(e)}")
                import traceback
                logger.error(f"错误堆栈: {traceback.format_exc()}")
                raise e
        else:
            # 只保存采购信息，不改变状态
            purchase_request_obj.save()
            message = '采购信息保存成功'

        return Response({
            'code': 200,
            'message': message,
            'data': {'status': purchase_request_obj.status}
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'采购操作失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def notify_shipping(request, pk):
    """
    发货通知（已采购→待验收）
    POST /api/requests/{id}/notify_shipping
    """
    try:
        purchase_request = PurchaseRequest.objects.get(pk=pk)

        if purchase_request.status != 'purchased':
            return Response({
                'code': 400,
                'message': '只有已采购状态的记录才能发货通知',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 更新状态为待验收
        purchase_request.status = 'pending_acceptance'
        purchase_request.shipping_date = timezone.now()
        purchase_request.save()

        return Response({
            'code': 200,
            'message': '发货通知成功',
            'data': {'status': purchase_request.status}
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'发货通知失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def generate_reimbursement(request, pk):
    """
    生成报销单（已验收→待结算）
    POST /api/requests/{id}/generate_reimbursement
    """
    try:
        purchase_request = PurchaseRequest.objects.get(pk=pk)

        if purchase_request.status != 'accepted':
            return Response({
                'code': 400,
                'message': '只有已验收状态的记录才能生成报销单',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 使用模型的方法生成报销单
        purchase_request.generate_reimbursement()

        return Response({
            'code': 200,
            'message': '生成报销单成功',
            'data': {'status': purchase_request.status}
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'生成报销单失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_historical_average_price(request):
    """
    获取指定物品规格的历史平均价格
    GET /api/requests/historical_average_price/?item_name=xxx&specification=xxx
    """
    try:
        item_name = request.GET.get('item_name')
        spec = request.GET.get('specification', '')

        if not item_name:
            return Response({
                'code': 400,
                'message': '物品名称不能为空',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 查询历史采购记录，只包含已采购且有采购单价的记录
        # 历史价格最小、最大值及平均价都采用采购单价计算
        historical_records = PurchaseRequest.objects.filter(
            item_name=item_name,
            specification=spec,
            status__in=['purchased', 'pending_acceptance', 'accepted', 'pending_reimbursement', 'settled'],
            purchase_unit_price__isnull=False,
            purchase_unit_price__gt=0
        ).values_list('purchase_unit_price', flat=True)

        if historical_records:
            # 计算平均价格
            total_price = sum(historical_records)
            average_price = total_price / len(historical_records)

            return Response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'average_price': float(average_price),
                    'record_count': len(historical_records),
                    'item_name': item_name,
                    'specification': spec
                }
            })
        else:
            return Response({
                'code': 200,
                'message': '暂无历史采购记录',
                'data': {
                    'average_price': 0,
                    'record_count': 0,
                    'item_name': item_name,
                    'specification': spec
                }
            })

    except Exception as e:
        logger.error(f"获取历史平均价格失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取历史平均价格失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_history_data(request):
    """
    获取历史采购数据（兼容前端需求提报页面和采购信息模态框）
    GET /api/requests/history/?item_name=xxx&specification=xxx&dept_id=xxx
    """
    try:
        item_name = request.GET.get('item_name')
        specification = request.GET.get('specification', '')
        dept_id = request.GET.get('dept_id')

        if not item_name:
            return Response({
                'code': 400,
                'message': '物品名称不能为空',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 构建查询条件
        filter_conditions = {
            'item_name': item_name,
            'specification': specification,
            'status__in': ['purchased', 'pending_acceptance', 'accepted', 'pending_reimbursement', 'settled'],
            'purchase_unit_price__isnull': False,
            'purchase_unit_price__gt': 0,
            'purchase_quantity__isnull': False,
            'purchase_quantity__gt': 0
        }

        # 如果提供了部门ID，添加部门筛选条件
        if dept_id:
            try:
                from apps.system.models import Department
                dept = Department.objects.get(id=dept_id)
                filter_conditions['hierarchy_path__icontains'] = dept.hierarchy_path
            except Department.DoesNotExist:
                pass

        # 查询历史采购记录
        queryset = PurchaseRequest.objects.filter(**filter_conditions).order_by('-purchase_date')

        # 获取最近一次购买记录
        last_purchase = queryset.first()
        last_purchase_data = None
        if last_purchase:
            last_purchase_data = {
                'purchase_quantity': last_purchase.purchase_quantity,
                'purchase_unit_price': float(last_purchase.purchase_unit_price),
                'purchase_total_amount': float(last_purchase.purchase_total_amount),
                'purchase_date': last_purchase.purchase_date.strftime('%Y-%m-%d') if last_purchase.purchase_date else None
            }

        # 计算历史购买汇总数据
        total_purchase_data = None
        statistics_data = None
        if queryset.exists():
            from django.db.models import Sum, Avg, Count, Min, Max
            aggregated = queryset.aggregate(
                total_quantity=Sum('purchase_quantity'),
                average_unit_price=Avg('purchase_unit_price'),
                min_unit_price=Min('purchase_unit_price'),
                max_unit_price=Max('purchase_unit_price'),
                total_amount=Sum('purchase_total_amount'),
                purchase_count=Count('id')
            )

            total_purchase_data = {
                'total_quantity': aggregated['total_quantity'] or 0,
                'average_unit_price': float(aggregated['average_unit_price'] or 0),
                'total_amount': float(aggregated['total_amount'] or 0),
                'purchase_count': aggregated['purchase_count'] or 0
            }

            # 为前端需求提报页面提供兼容的数据结构
            statistics_data = {
                'avg_price': float(aggregated['average_unit_price'] or 0),
                'min_price': float(aggregated['min_unit_price'] or 0),
                'max_price': float(aggregated['max_unit_price'] or 0),
                'purchase_count': aggregated['purchase_count'] or 0
            }

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'last_purchase': last_purchase_data,
                'total_purchase': total_purchase_data,
                'statistics': statistics_data,  # 为前端需求提报页面提供兼容数据
                'item_name': item_name,
                'specification': specification
            }
        })

    except Exception as e:
        logger.error(f"获取历史数据失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取历史数据失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_time_warnings(request):
    """
    获取时间预警信息
    GET /api/requests/time_warnings/
    """
    try:
        from datetime import datetime, timedelta
        from django.utils import timezone

        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)

        warnings = []

        # 1. 检查采购完成后30天内未验收的记录
        overdue_acceptance = PurchaseRequest.objects.filter(
            status='purchased',
            purchase_date__lt=thirty_days_ago,
            purchase_date__isnull=False
        ).select_related('requester', 'purchaser')

        for record in overdue_acceptance:
            days_overdue = (now - record.purchase_date).days - 30
            warnings.append({
                'id': record.id,
                'type': 'acceptance_overdue',
                'title': f'验收超期预警',
                'message': f'物品"{record.item_name}"采购完成已超过30天，仍未验收',
                'details': {
                    'item_name': record.item_name,
                    'specification': record.specification,
                    'requester_name': record.requester.username if record.requester else '',
                    'purchaser_name': record.purchaser.username if record.purchaser else '',
                    'purchase_date': record.purchase_date.strftime('%Y-%m-%d') if record.purchase_date else '',
                    'days_overdue': days_overdue,
                    'hierarchy_path': record.hierarchy_path
                },
                'priority': 'high' if days_overdue > 10 else 'medium',
                'created_at': now.isoformat()
            })

        # 2. 检查验收完成后30天内未结算的记录
        overdue_settlement = PurchaseRequest.objects.filter(
            status='accepted',
            acceptance_date__lt=thirty_days_ago,
            acceptance_date__isnull=False
        ).select_related('requester', 'purchaser', 'acceptor')

        for record in overdue_settlement:
            days_overdue = (now - record.acceptance_date).days - 30
            warnings.append({
                'id': record.id,
                'type': 'settlement_overdue',
                'title': f'结算超期预警',
                'message': f'物品"{record.item_name}"验收完成已超过30天，仍未结算',
                'details': {
                    'item_name': record.item_name,
                    'specification': record.specification,
                    'requester_name': record.requester.username if record.requester else '',
                    'purchaser_name': record.purchaser.username if record.purchaser else '',
                    'acceptor_name': record.acceptor.username if record.acceptor else '',
                    'acceptance_date': record.acceptance_date.strftime('%Y-%m-%d') if record.acceptance_date else '',
                    'days_overdue': days_overdue,
                    'hierarchy_path': record.hierarchy_path,
                    'total_amount': str(record.budget_total_amount)
                },
                'priority': 'high' if days_overdue > 10 else 'medium',
                'created_at': now.isoformat()
            })

        # 3. 检查即将到期的记录（25-30天）
        warning_period_start = now - timedelta(days=30)
        warning_period_end = now - timedelta(days=25)

        # 即将到期的验收
        upcoming_acceptance = PurchaseRequest.objects.filter(
            status='purchased',
            purchase_date__gte=warning_period_start,
            purchase_date__lt=warning_period_end,
            purchase_date__isnull=False
        ).select_related('requester', 'purchaser')

        for record in upcoming_acceptance:
            days_remaining = 30 - (now - record.purchase_date).days
            warnings.append({
                'id': record.id,
                'type': 'acceptance_warning',
                'title': f'验收即将到期',
                'message': f'物品"{record.item_name}"采购完成已{30-days_remaining}天，请及时验收',
                'details': {
                    'item_name': record.item_name,
                    'specification': record.specification,
                    'requester_name': record.requester.username if record.requester else '',
                    'purchaser_name': record.purchaser.username if record.purchaser else '',
                    'purchase_date': record.purchase_date.strftime('%Y-%m-%d') if record.purchase_date else '',
                    'days_remaining': days_remaining,
                    'hierarchy_path': record.hierarchy_path
                },
                'priority': 'medium',
                'created_at': now.isoformat()
            })

        # 即将到期的结算
        upcoming_settlement = PurchaseRequest.objects.filter(
            status='accepted',
            acceptance_date__gte=warning_period_start,
            acceptance_date__lt=warning_period_end,
            acceptance_date__isnull=False
        ).select_related('requester', 'purchaser', 'acceptor')

        for record in upcoming_settlement:
            days_remaining = 30 - (now - record.acceptance_date).days
            warnings.append({
                'id': record.id,
                'type': 'settlement_warning',
                'title': f'结算即将到期',
                'message': f'物品"{record.item_name}"验收完成已{30-days_remaining}天，请及时结算',
                'details': {
                    'item_name': record.item_name,
                    'specification': record.specification,
                    'requester_name': record.requester.username if record.requester else '',
                    'purchaser_name': record.purchaser.username if record.purchaser else '',
                    'acceptor_name': record.acceptor.username if record.acceptor else '',
                    'acceptance_date': record.acceptance_date.strftime('%Y-%m-%d') if record.acceptance_date else '',
                    'days_remaining': days_remaining,
                    'hierarchy_path': record.hierarchy_path,
                    'total_amount': str(record.budget_total_amount)
                },
                'priority': 'medium',
                'created_at': now.isoformat()
            })

        # 按优先级和时间排序
        warnings.sort(key=lambda x: (
            0 if x['priority'] == 'high' else 1,  # 高优先级在前
            x['created_at']
        ))

        return Response({
            'code': 200,
            'message': '获取预警信息成功',
            'data': {
                'warnings': warnings,
                'total_count': len(warnings),
                'high_priority_count': len([w for w in warnings if w['priority'] == 'high']),
                'medium_priority_count': len([w for w in warnings if w['priority'] == 'medium']),
                'generated_at': now.isoformat()
            }
        })

    except Exception as e:
        logger.error(f"获取预警信息失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取预警信息失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





@api_view(['GET'])
def print_acceptance(request, pk):
    """
    打印验收单
    GET /api/requests/{id}/print_acceptance
    """
    try:
        purchase_request = PurchaseRequest.objects.get(pk=pk)

        if purchase_request.status != 'accepted':
            return Response({
                'code': 400,
                'message': '只有已验收状态的记录才能打印验收单',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 返回打印所需的数据
        print_data = {
            'request_id': purchase_request.id,
            'item_category': purchase_request.item_category,
            'item_name': purchase_request.item_name,
            'specification': purchase_request.specification,
            'quantity': purchase_request.quantity,
            'unit_price': str(purchase_request.unit_price),
            'total_amount': str(purchase_request.budget_total_amount),
            'hierarchy_path': purchase_request.hierarchy_path,
            'requester_name': purchase_request.requester.username if purchase_request.requester else '',
            'purchaser_name': purchase_request.purchaser.username if purchase_request.purchaser else '',
            'acceptor_name': purchase_request.acceptor.username if purchase_request.acceptor else '',
            'acceptance_date': purchase_request.acceptance_date.strftime('%Y-%m-%d') if purchase_request.acceptance_date else '',
            'courier_company': purchase_request.courier_company or '',
            'tracking_number': purchase_request.tracking_number or '',
        }

        return Response({
            'code': 200,
            'message': '获取打印数据成功',
            'data': print_data
        })

    except PurchaseRequest.DoesNotExist:
        return Response({
            'code': 404,
            'message': '采购需求不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取打印数据失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 批量操作接口
@api_view(['POST'])
def batch_submit_requests(request):
    """
    批量提交采购需求
    POST /api/requests/batch_submit/
    """
    try:
        request_ids = request.data.get('request_ids', [])
        if not request_ids:
            return Response({
                'code': 400,
                'message': '请提供要提交的需求ID列表',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        error_messages = []

        for request_id in request_ids:
            try:
                purchase_request = PurchaseRequest.objects.get(pk=request_id)
                if purchase_request.status == 'draft':
                    purchase_request.submit()
                    success_count += 1
                else:
                    error_messages.append(f'ID {request_id}: 只能提交草稿状态的需求')
            except PurchaseRequest.DoesNotExist:
                error_messages.append(f'ID {request_id}: 采购需求不存在')
            except Exception as e:
                error_messages.append(f'ID {request_id}: {str(e)}')

        return Response({
            'code': 200,
            'message': f'批量提交完成，成功 {success_count} 个',
            'data': {
                'success_count': success_count,
                'error_messages': error_messages
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量提交失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def batch_apply_purchase(request):
    """
    批量申请采购 - 将已审批状态变更为待采购
    POST /api/requests/batch_apply_purchase/
    """
    try:
        request_ids = request.data.get('request_ids', [])
        if not request_ids:
            return Response({
                'code': 400,
                'message': '请提供要申请采购的需求ID列表',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        error_messages = []

        for request_id in request_ids:
            try:
                purchase_request = PurchaseRequest.objects.get(pk=request_id)
                if purchase_request.status == 'approved':
                    purchase_request.status = 'pending_purchase'
                    purchase_request.save()
                    success_count += 1
                else:
                    error_messages.append(f'ID {request_id}: 只能对已审批状态的需求申请采购')
            except PurchaseRequest.DoesNotExist:
                error_messages.append(f'ID {request_id}: 采购需求不存在')
            except Exception as e:
                error_messages.append(f'ID {request_id}: {str(e)}')

        return Response({
            'code': 200,
            'message': f'批量申请采购完成，成功 {success_count} 个',
            'data': {
                'success_count': success_count,
                'error_messages': error_messages
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量申请采购失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def batch_approve_requests(request):
    """
    批量审批采购需求
    POST /api/requests/batch_approve/
    需要权限：purchase:approval:batch
    """
    # 权限检查
    from apps.system.permissions import has_permission
    if not has_permission(request.user, 'purchase:approval:batch'):
        return Response({
            'code': 403,
            'message': '权限不足，无法执行批量审批操作',
            'data': {
                'required_permission': 'purchase:approval:batch',
                'user_role': getattr(request.user, 'role', None)
            }
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        request_ids = request.data.get('request_ids', [])
        if not request_ids:
            return Response({
                'code': 400,
                'message': '请提供要审批的需求ID列表',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        error_messages = []

        for request_id in request_ids:
            try:
                purchase_request = PurchaseRequest.objects.get(pk=request_id)
                if purchase_request.status == 'pending_approval':
                    purchase_request.approve(
                        approver_id=request.user.id,
                        comment='批量审批通过'
                    )
                    success_count += 1
                else:
                    error_messages.append(f'ID {request_id}: 只能审批待审批状态的需求')
            except PurchaseRequest.DoesNotExist:
                error_messages.append(f'ID {request_id}: 采购需求不存在')
            except Exception as e:
                error_messages.append(f'ID {request_id}: {str(e)}')

        return Response({
            'code': 200,
            'message': f'批量审批完成，成功 {success_count} 个',
            'data': {
                'success_count': success_count,
                'error_messages': error_messages
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量审批失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def batch_generate_purchase_order(request):
    """
    批量生成采购单
    POST /api/requests/batch_generate_purchase_order/
    """
    try:
        request_ids = request.data.get('request_ids', [])
        if not request_ids:
            return Response({
                'code': 400,
                'message': '请提供要生成采购单的需求ID列表',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        error_messages = []

        for request_id in request_ids:
            try:
                purchase_request = PurchaseRequest.objects.get(pk=request_id)
                if purchase_request.status == 'approved':
                    purchase_request.to_pending_purchase()
                    success_count += 1
                else:
                    error_messages.append(f'ID {request_id}: 只能对已审批状态的需求生成采购单')
            except PurchaseRequest.DoesNotExist:
                error_messages.append(f'ID {request_id}: 采购需求不存在')
            except Exception as e:
                error_messages.append(f'ID {request_id}: {str(e)}')

        return Response({
            'code': 200,
            'message': f'批量生成采购单完成，成功 {success_count} 个',
            'data': {
                'success_count': success_count,
                'error_messages': error_messages
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量生成采购单失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def batch_purchase_requests(request):
    """
    批量采购
    POST /api/requests/batch_purchase/
    """
    try:
        request_ids = request.data.get('request_ids', [])
        if not request_ids:
            return Response({
                'code': 400,
                'message': '请提供要采购的需求ID列表',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        error_messages = []

        for request_id in request_ids:
            try:
                purchase_request = PurchaseRequest.objects.get(pk=request_id)
                if purchase_request.status == 'pending_purchase':
                    purchase_request.purchase(request.user.id)
                    success_count += 1
                else:
                    error_messages.append(f'ID {request_id}: 只能对待采购状态的需求进行采购操作')
            except PurchaseRequest.DoesNotExist:
                error_messages.append(f'ID {request_id}: 采购需求不存在')
            except Exception as e:
                error_messages.append(f'ID {request_id}: {str(e)}')

        return Response({
            'code': 200,
            'message': f'批量采购完成，成功 {success_count} 个',
            'data': {
                'success_count': success_count,
                'error_messages': error_messages
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量采购失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def batch_notify_shipping(request):
    """
    批量发货通知
    POST /api/requests/batch_notify_shipping/
    """
    try:
        request_ids = request.data.get('request_ids', [])
        if not request_ids:
            return Response({
                'code': 400,
                'message': '请提供要发货通知的需求ID列表',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        error_messages = []

        for request_id in request_ids:
            try:
                purchase_request = PurchaseRequest.objects.get(pk=request_id)
                if purchase_request.status == 'purchased':
                    purchase_request.status = 'pending_acceptance'
                    purchase_request.shipping_date = timezone.now()
                    purchase_request.save()
                    success_count += 1
                else:
                    error_messages.append(f'ID {request_id}: 只能对已采购状态的需求发货通知')
            except PurchaseRequest.DoesNotExist:
                error_messages.append(f'ID {request_id}: 采购需求不存在')
            except Exception as e:
                error_messages.append(f'ID {request_id}: {str(e)}')

        return Response({
            'code': 200,
            'message': f'批量发货通知完成，成功 {success_count} 个',
            'data': {
                'success_count': success_count,
                'error_messages': error_messages
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量发货通知失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def batch_generate_reimbursement(request):
    """
    批量生成报销单
    POST /api/requests/batch_generate_reimbursement/
    """
    try:
        request_ids = request.data.get('request_ids', [])
        if not request_ids:
            return Response({
                'code': 400,
                'message': '请提供要生成报销单的需求ID列表',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        error_messages = []

        for request_id in request_ids:
            try:
                purchase_request = PurchaseRequest.objects.get(pk=request_id)
                if purchase_request.status == 'accepted':
                    purchase_request.generate_reimbursement()
                    success_count += 1
                else:
                    error_messages.append(f'ID {request_id}: 只能对已验收状态的需求生成报销单')
            except PurchaseRequest.DoesNotExist:
                error_messages.append(f'ID {request_id}: 采购需求不存在')
            except Exception as e:
                error_messages.append(f'ID {request_id}: {str(e)}')

        return Response({
            'code': 200,
            'message': f'批量生成报销单完成，成功 {success_count} 个',
            'data': {
                'success_count': success_count,
                'error_messages': error_messages
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量生成报销单失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 仪表板统计 API（优化版本）
@api_view(['GET'])
def dashboard_statistics(request):
    """
    获取仪表板统计数据（优化版本）
    GET /api/dashboard/statistics/?days=30
    GET /api/dashboard/statistics/?start_date=2025-06-01&end_date=2025-06-30
    """
    from django.core.cache import cache
    from django.db.models import Prefetch
    import hashlib

    try:
        # 支持自定义日期范围
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        if start_date_str and end_date_str:
            # 使用自定义日期范围
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            # 设置为当天结束时间
            end_date = end_date.replace(hour=23, minute=59, second=59)
            start_date = timezone.make_aware(start_date)
            end_date = timezone.make_aware(end_date)
            days = (end_date - start_date).days
            cache_key = f"dashboard_stats_{start_date_str}_{end_date_str}"
        else:
            # 使用天数计算日期范围
            days = int(request.GET.get('days', 30))
            end_date = timezone.now()
            start_date = end_date - timezone.timedelta(days=days)
            cache_key = f"dashboard_stats_days_{days}"

        # 尝试从缓存获取数据
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)

        # 优化：使用单个聚合查询获取所有统计数据
        from django.db.models import Case, When, IntegerField

        # 全部统计和时间范围统计合并查询
        all_stats = PurchaseRequest.objects.aggregate(
            # 全部统计
            total_requests=Count('id'),
            pending_requests=Count('id', filter=Q(status='pending_approval')),
            approved_requests=Count('id', filter=Q(status='approved')),
            completed_requests=Count('id', filter=Q(status='settled')),

            # 时间范围内统计
            period_total=Count('id', filter=Q(created_at__gte=start_date, created_at__lte=end_date)),
            period_amount=Sum('budget_total_amount', filter=Q(created_at__gte=start_date, created_at__lte=end_date)),

            # 状态分布统计
            draft_count=Count('id', filter=Q(status='draft')),
            pending_approval_count=Count('id', filter=Q(status='pending_approval')),
            approved_count=Count('id', filter=Q(status='approved')),
            rejected_count=Count('id', filter=Q(status='rejected')),
            pending_purchase_count=Count('id', filter=Q(status='pending_purchase')),
            purchased_count=Count('id', filter=Q(status='purchased')),
            pending_acceptance_count=Count('id', filter=Q(status='pending_acceptance')),
            accepted_count=Count('id', filter=Q(status='accepted')),
            pending_reimbursement_count=Count('id', filter=Q(status='pending_reimbursement')),
            settled_count=Count('id', filter=Q(status='settled'))
        )

        # 状态分布数据
        status_distribution = {}
        for status, label in PurchaseRequest.STATUS_CHOICES:
            count_key = f"{status}_count"
            status_distribution[status] = {
                'label': label,
                'count': all_stats.get(count_key, 0)
            }

        # 部门统计（前5名）- 限制查询字段
        department_stats = PurchaseRequest.objects.values('hierarchy_path').annotate(
            count=Count('id'),
            total_amount=Sum('budget_total_amount')
        ).order_by('-total_amount')[:5]

        response_data = {
            'code': 200,
            'message': 'success',
            'data': {
                'overview': {
                    'total_requests': all_stats['total_requests'],
                    'pending_requests': all_stats['pending_requests'],
                    'approved_requests': all_stats['approved_requests'],
                    'completed_requests': all_stats['completed_requests'],
                    'period_total': all_stats['period_total'],
                    'period_amount': float(all_stats['period_amount'] or 0)
                },
                'status_distribution': status_distribution,
                'department_stats': list(department_stats),
                'period_days': days
            }
        }

        # 缓存数据5分钟
        cache.set(cache_key, response_data, 300)

        return Response(response_data)

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取统计数据失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])  # 临时允许匿名访问用于测试
def dashboard_trend(request):
    """
    获取采购趋势数据
    GET /api/dashboard/trend/?days=30
    GET /api/dashboard/trend/?start_date=2025-06-01&end_date=2025-06-30
    """
    from django.core.cache import cache

    try:
        # 支持自定义日期范围
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        group_by = request.GET.get('group_by', 'day')  # 支持按天或按月聚合

        if start_date_str and end_date_str:
            # 使用自定义日期范围
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        else:
            # 使用天数计算日期范围
            days = int(request.GET.get('days', 30))
            end_date = timezone.now().date()
            start_date = end_date - timezone.timedelta(days=days)

            # 如果是一年的数据，自动按月聚合
            if days >= 365:
                group_by = 'month'

        # 根据聚合方式统计数据
        trend_data = []

        if group_by == 'month':
            # 按月聚合
            from django.db.models import Extract
            from collections import defaultdict

            # 获取所有数据
            requests = PurchaseRequest.objects.filter(
                reimbursement_date__gte=start_date,
                reimbursement_date__lte=end_date,
                status='settled'
            ).values('reimbursement_date', 'settlement_amount')

            # 按月分组
            monthly_data = defaultdict(lambda: {'count': 0, 'amount': 0})
            for req in requests:
                if req['reimbursement_date']:
                    month_key = req['reimbursement_date'].strftime('%Y-%m')
                    monthly_data[month_key]['count'] += 1
                    monthly_data[month_key]['amount'] += float(req['settlement_amount'] or 0)

            # 生成完整的月份序列
            current_date = start_date.replace(day=1)
            while current_date <= end_date:
                month_key = current_date.strftime('%Y-%m')
                data = monthly_data[month_key]
                trend_data.append({
                    'date': month_key + '-01',
                    'count': data['count'],
                    'total_amount': data['amount']
                })

                # 移动到下个月
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
        else:
            # 按日聚合
            current_date = start_date
            while current_date <= end_date:
                # 使用日期范围查询避免时区问题
                start_datetime = timezone.datetime.combine(current_date, timezone.datetime.min.time())
                end_datetime = timezone.datetime.combine(current_date, timezone.datetime.max.time())

                # 确保使用UTC时区
                if timezone.is_aware(timezone.now()):
                    start_datetime = timezone.make_aware(start_datetime)
                    end_datetime = timezone.make_aware(end_datetime)

                # 使用reimbursement_date字段统计趋势数据（已结算的采购需求）
                day_requests = PurchaseRequest.objects.filter(
                    reimbursement_date__gte=start_datetime,
                    reimbursement_date__lte=end_datetime,
                    status='settled'  # 只统计已结算的记录
                )

                day_count = day_requests.count()
                day_amount = day_requests.aggregate(
                    total=Sum('settlement_amount')
                )['total'] or 0

                trend_data.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'count': day_count,
                    'total_amount': float(day_amount)
                })

                current_date += timezone.timedelta(days=1)

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'trend_data': trend_data,
                'period_days': days
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取趋势数据失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def dashboard_price_warnings(request):
    """
    获取价格预警数据
    GET /api/dashboard/price-warnings/
    """
    try:
        # 查找价格异常的采购需求（预算单价超过历史平均价格20%）
        warning_requests = PurchaseRequest.objects.filter(
            budget_unit_price__gt=F('history_avg_price') * 1.2,
            history_avg_price__gt=0
        ).order_by('-budget_unit_price')[:10]

        warning_data = []
        for request in warning_requests:
            if request.history_avg_price > 0:
                price_increase = ((request.budget_unit_price - request.history_avg_price) / request.history_avg_price) * 100
                warning_data.append({
                    'id': request.id,
                    'item_name': request.item_name,
                    'current_price': float(request.budget_unit_price),
                    'history_avg_price': float(request.history_avg_price),
                    'price_increase': round(price_increase, 2),
                    'hierarchy_path': request.hierarchy_path,
                    'requester_name': request.requester.username if request.requester else '',
                    'created_at': request.created_at.strftime('%Y-%m-%d') if request.created_at else ''
                })

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'warning_requests': warning_data,
                'total_warnings': len(warning_data)
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取价格预警失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 统计API - 优化版本
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_overview_statistics(request):
    """
    获取采购总览统计数据 - 优化版本，支持筛选条件
    """
    try:
        # 构建基础查询条件
        queryset = PurchaseRequest.objects.all()

        # 应用筛选条件
        status_filter = request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(item_name__icontains=search) |
                Q(specification__icontains=search)
            )

        hierarchy_path = request.GET.get('hierarchy_path__icontains')
        if hierarchy_path:
            queryset = queryset.filter(hierarchy_path__icontains=hierarchy_path)

        requester_filter = request.GET.get('requester__username__icontains')
        if requester_filter:
            queryset = queryset.filter(requester__username__icontains=requester_filter)

        purchase_type = request.GET.get('purchase_type')
        if purchase_type:
            queryset = queryset.filter(purchase_type=purchase_type)

        # 时间范围筛选
        created_at_gte = request.GET.get('created_at__gte')
        created_at_lte = request.GET.get('created_at__lte')
        if created_at_gte:
            queryset = queryset.filter(created_at__gte=created_at_gte)
        if created_at_lte:
            queryset = queryset.filter(created_at__lte=created_at_lte)

        # 使用数据库聚合函数进行高效统计
        stats = queryset.aggregate(
            total_count=Count('id'),
            total_amount=Sum('budget_total_amount'),
            pending_count=Count('id', filter=Q(status__in=['pending_approval', 'pending_purchase', 'pending_acceptance', 'pending_reimbursement'])),
            completed_count=Count('id', filter=Q(status='settled')),
            draft_count=Count('id', filter=Q(status='draft')),
            approved_count=Count('id', filter=Q(status='approved')),
            rejected_count=Count('id', filter=Q(status='rejected'))
        )

        # 状态分布统计
        status_distribution = queryset.values('status').annotate(
            count=Count('id'),
            amount=Sum('budget_total_amount')
        ).order_by('status')

        # 月度趋势数据（基于结算日期）
        monthly_trend = queryset.filter(
            status='settled',
            reimbursement_date__isnull=False
        ).extra(
            select={'month': "DATE_FORMAT(reimbursement_date, '%%Y-%%m')"}
        ).values('month').annotate(
            count=Count('id'),
            amount=Sum('settlement_amount')
        ).order_by('month')

        return Response({
            'code': 200,
            'message': '获取统计数据成功',
            'data': {
                'overview': {
                    'total_count': stats['total_count'] or 0,
                    'pending_count': stats['pending_count'] or 0,
                    'completed_count': stats['completed_count'] or 0,
                    'total_amount': float(stats['total_amount'] or 0)
                },
                'status_distribution': list(status_distribution),
                'monthly_trend': list(monthly_trend)
            }
        })
    except Exception as e:
        logger.error(f"获取统计数据失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取统计数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_overview_charts(request):
    """
    获取采购总览图表数据 - 专门的图表数据API
    """
    try:
        # 构建基础查询条件
        queryset = PurchaseRequest.objects.all()

        # 应用筛选条件
        status_filter = request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(item_name__icontains=search) |
                Q(specification__icontains=search)
            )

        hierarchy_path = request.GET.get('hierarchy_path__icontains')
        if hierarchy_path:
            queryset = queryset.filter(hierarchy_path__icontains=hierarchy_path)

        requester_filter = request.GET.get('requester__username__icontains')
        if requester_filter:
            queryset = queryset.filter(requester__username__icontains=requester_filter)

        purchase_type = request.GET.get('purchase_type')
        if purchase_type:
            queryset = queryset.filter(purchase_type=purchase_type)

        # 时间范围筛选
        created_at_gte = request.GET.get('created_at__gte')
        created_at_lte = request.GET.get('created_at__lte')
        if created_at_gte:
            queryset = queryset.filter(created_at__gte=created_at_gte)
        if created_at_lte:
            queryset = queryset.filter(created_at__lte=created_at_lte)

        # 状态分布统计
        status_distribution = queryset.values('status').annotate(
            count=Count('id'),
            amount=Sum('budget_total_amount')
        ).order_by('status')

        # 采购类型分布统计
        purchase_type_distribution = queryset.values('purchase_type').annotate(
            count=Count('id'),
            amount=Sum('budget_total_amount')
        ).order_by('purchase_type')

        # 采购趋势数据（基于结算日期，只统计已结算的记录）
        trend_queryset = queryset.filter(
            status='settled',
            reimbursement_date__isnull=False
        )

        # 按月统计趋势数据
        monthly_trend = trend_queryset.extra(
            select={'month': "DATE_FORMAT(reimbursement_date, '%%Y-%%m')"}
        ).values('month').annotate(
            count=Count('id'),
            amount=Sum('settlement_amount')
        ).order_by('month')

        return Response({
            'code': 200,
            'message': '获取图表数据成功',
            'data': {
                'status_distribution': list(status_distribution),
                'purchase_type_distribution': list(purchase_type_distribution),
                'monthly_trend': list(monthly_trend)
            }
        })
    except Exception as e:
        logger.error(f"获取图表数据失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取图表数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_optimized_list(request):
    """
    优化的列表API - 支持不同页面的数据需求
    """
    try:
        page_type = request.GET.get('page_type', 'all')  # all, requests, approval, procurement, acceptance, reimbursement
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 20)), 100)  # 限制最大页面大小

        # 基础查询
        queryset = PurchaseRequest.objects.select_related(
            'requester', 'approver', 'purchaser', 'acceptor', 'reimburser'
        ).prefetch_related('department')

        # 应用状态筛选条件
        status_filter = request.GET.get('status')
        status_in = request.GET.get('status_in')

        if status_filter:
            # 如果有具体状态筛选，使用具体状态
            queryset = queryset.filter(status=status_filter)
        elif status_in:
            # 如果有多状态筛选，使用多状态
            status_list = [s.strip() for s in status_in.split(',') if s.strip()]
            if status_list:
                queryset = queryset.filter(status__in=status_list)
        else:
            # 否则根据页面类型筛选状态
            if page_type == 'requests':
                # 需求提报页面：草稿、待审批、已审批、已驳回、已退回
                queryset = queryset.filter(status__in=['draft', 'pending_approval', 'approved', 'rejected', 'returned'])
            elif page_type == 'approval':
                # 需求审核页面：待审批、已审批、已驳回
                queryset = queryset.filter(status__in=['pending_approval', 'approved', 'rejected'])
            elif page_type == 'procurement':
                # 物资采购页面：待采购、已采购、已退回
                queryset = queryset.filter(status__in=['pending_purchase', 'purchased', 'returned'])
            elif page_type == 'acceptance':
                # 物资验收页面：待验收、已验收
                queryset = queryset.filter(status__in=['pending_acceptance', 'accepted'])
            elif page_type == 'reimbursement':
                # 结算报销页面：待结算、已结算
                queryset = queryset.filter(status__in=['pending_reimbursement', 'settled'])

        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(item_name__icontains=search) |
                Q(specification__icontains=search) |
                Q(request_number__icontains=search)
            )

        department_id = request.GET.get('department_id')
        if department_id:
            queryset = queryset.filter(dept_id=department_id)

        requester_id = request.GET.get('requester_id')
        if requester_id:
            queryset = queryset.filter(requester_id=requester_id)

        # 时间范围筛选
        created_at_gte = request.GET.get('created_at__gte')
        created_at_lte = request.GET.get('created_at__lte')
        if created_at_gte:
            queryset = queryset.filter(created_at__gte=created_at_gte)
        if created_at_lte:
            queryset = queryset.filter(created_at__lte=created_at_lte)

        # 排序
        ordering = request.GET.get('ordering', '-created_at')
        queryset = queryset.order_by(ordering)

        # 调试日志
        logger.info(f"优化列表API - page_type: {page_type}, 查询条件: {request.GET.dict()}")
        logger.info(f"优化列表API - 查询结果数量: {queryset.count()}")

        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # 序列化数据
        serializer = PurchaseRequestSerializer(page_obj.object_list, many=True)

        return Response({
            'code': 200,
            'message': '获取数据成功',
            'data': {
                'results': serializer.data,
                'count': paginator.count,
                'current_page': page,
                'total_pages': paginator.num_pages,
                'page_size': page_size,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        })
    except Exception as e:
        logger.error(f"获取优化列表失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_filter_options(request):
    """
    获取筛选选项数据 - 优化版本
    """
    try:
        # 使用缓存避免重复查询
        cache_key = 'filter_options_v1'
        cached_data = cache.get(cache_key)

        if cached_data:
            return Response({
                'code': 200,
                'message': '获取筛选选项成功',
                'data': cached_data
            })

        # 获取部门列表（只获取有采购记录的部门）
        departments = Department.objects.filter(
            purchaserequest__isnull=False
        ).distinct().values('id', 'dept_name', 'parent_id')

        # 获取用户列表（只获取相关用户）
        users = User.objects.filter(
            Q(requested_purchases__isnull=False) |
            Q(approved_purchases__isnull=False) |
            Q(purchased_items__isnull=False) |
            Q(accepted_items__isnull=False) |
            Q(settled_items__isnull=False)
        ).distinct().values('id', 'username', 'first_name')

        # 获取常用的物品类别和供应商（从现有记录中提取）
        item_categories = PurchaseRequest.objects.values_list(
            'item_category', flat=True
        ).distinct().exclude(item_category__isnull=True).exclude(item_category='')

        suppliers = PurchaseRequest.objects.values_list(
            'supplier_name', flat=True
        ).distinct().exclude(supplier_name__isnull=True).exclude(supplier_name='')

        filter_data = {
            'departments': list(departments),
            'users': list(users),
            'item_categories': list(item_categories)[:50],  # 限制数量
            'suppliers': list(suppliers)[:100],  # 限制数量
            'status_options': [
                {'value': 'draft', 'label': '草稿'},
                {'value': 'pending_approval', 'label': '待审批'},
                {'value': 'approved', 'label': '已审批'},
                {'value': 'rejected', 'label': '已驳回'},
                {'value': 'pending_purchase', 'label': '待采购'},
                {'value': 'purchased', 'label': '已采购'},
                {'value': 'returned', 'label': '已退回'},
                {'value': 'pending_acceptance', 'label': '待验收'},
                {'value': 'accepted', 'label': '已验收'},
                {'value': 'pending_reimbursement', 'label': '待结算'},
                {'value': 'settled', 'label': '已结算'}
            ]
        }

        # 缓存5分钟
        cache.set(cache_key, filter_data, 300)

        return Response({
            'code': 200,
            'message': '获取筛选选项成功',
            'data': filter_data
        })
    except Exception as e:
        logger.error(f"获取筛选选项失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取筛选选项失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_procurement_optimized(request):
    """
    物资采购页面专用优化API - 超高性能数据获取
    使用数据库视图和索引优化，实现无感刷新
    """
    try:
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 20)), 100)

        # 使用优化的ORM查询，避免原生SQL的复杂性
        queryset = PurchaseRequest.objects.select_related().only(
            'id', 'item_name', 'specification', 'budget_quantity', 'unit',
            'budget_unit_price', 'budget_total_amount', 'purchase_quantity',
            'purchase_unit_price', 'purchase_total_amount', 'status', 'created_at',
            'purchase_date', 'supplier_name', 'procurement_method', 'purchase_type',
            'hierarchy_path', 'requirement_source', 'fund_project', 'item_category',
            'purchase_remarks'
        )

        # 状态筛选 - 物资采购页面只显示相关状态
        status_in = request.GET.get('status_in', 'pending_purchase,purchased,returned')
        if status_in:
            status_list = [s.strip() for s in status_in.split(',')]
            queryset = queryset.filter(status__in=status_list)

        # 其他筛选条件
        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(item_name__icontains=search) | Q(specification__icontains=search)
            )

        hierarchy_path = request.GET.get('hierarchy_path__icontains')
        if hierarchy_path:
            queryset = queryset.filter(hierarchy_path__icontains=hierarchy_path)

        procurement_method = request.GET.get('procurement_method__icontains')
        if procurement_method:
            queryset = queryset.filter(procurement_method__icontains=procurement_method)

        # 添加更多筛选条件
        for field in ['purchase_type', 'item_category', 'requirement_source', 'fund_project']:
            value = request.GET.get(field)
            if value:
                queryset = queryset.filter(**{f'{field}__icontains': value})

        # 日期范围筛选
        date_gte = request.GET.get('created_at__gte')
        date_lte = request.GET.get('created_at__lte')
        if date_gte:
            queryset = queryset.filter(created_at__gte=date_gte)
        if date_lte:
            queryset = queryset.filter(created_at__lte=date_lte)

        # 排序
        ordering = request.GET.get('ordering', '-created_at')
        queryset = queryset.order_by(ordering)

        # 使用Django的Paginator进行高效分页
        from django.core.paginator import Paginator
        paginator = Paginator(queryset, page_size)

        try:
            page_obj = paginator.page(page)
        except:
            page_obj = paginator.page(1)

        # 序列化数据 - 使用简化的序列化
        results = []
        for obj in page_obj.object_list:
            results.append({
                'id': obj.id,
                'item_name': obj.item_name,
                'specification': obj.specification,
                'budget_quantity': obj.budget_quantity,
                'unit': obj.unit,
                'budget_unit_price': float(obj.budget_unit_price) if obj.budget_unit_price else 0,
                'budget_total_amount': float(obj.budget_total_amount) if obj.budget_total_amount else 0,
                'purchase_quantity': obj.purchase_quantity,
                'purchase_unit_price': float(obj.purchase_unit_price) if obj.purchase_unit_price else 0,
                'purchase_total_amount': float(obj.purchase_total_amount) if obj.purchase_total_amount else 0,
                'status': obj.status,
                'created_at': obj.created_at.isoformat() if obj.created_at else None,
                'purchase_date': obj.purchase_date.isoformat() if obj.purchase_date else None,
                'supplier_name': obj.supplier_name,
                'procurement_method': obj.procurement_method,
                'purchase_type': obj.purchase_type,
                'hierarchy_path': obj.hierarchy_path,
                'requirement_source': obj.requirement_source,
                'fund_project': obj.fund_project,
                'item_category': obj.item_category,
                'purchase_remarks': obj.purchase_remarks
            })

        return Response({
            'code': 200,
            'message': '获取数据成功',
            'data': {
                'results': results,
                'count': paginator.count,
                'current_page': page,
                'total_pages': paginator.num_pages,
                'page_size': page_size,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        })

    except Exception as e:
        logger.error(f"获取采购数据失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取数据失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_fast_list(request):
    """
    快速列表API - 专门优化的高性能数据获取
    """
    try:
        page_type = request.GET.get('page_type', 'all')
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 20)), 50)  # 限制最大页面大小为50

        # 基础查询 - 只选择必要字段
        base_fields = [
            'id', 'request_number', 'item_name', 'specification', 'quantity',
            'unit', 'unit_price', 'total_amount', 'status', 'created_at',
            'requester__username', 'requester__first_name',
            'hierarchy_path', 'urgency_level'
        ]

        queryset = PurchaseRequest.objects.select_related(
            'requester'
        ).only(*base_fields)

        # 根据页面类型筛选状态
        status_filters = {
            'requests': ['draft', 'pending_approval', 'approved', 'rejected', 'returned'],
            'approval': ['pending_approval', 'approved', 'rejected'],
            'procurement': ['pending_purchase', 'purchased', 'returned'],
            'acceptance': ['pending_acceptance', 'accepted'],
            'reimbursement': ['pending_reimbursement', 'settled']
        }

        if page_type in status_filters:
            queryset = queryset.filter(status__in=status_filters[page_type])

        # 应用其他筛选条件
        status_filter = request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(item_name__icontains=search) |
                Q(specification__icontains=search) |
                Q(request_number__icontains=search)
            )

        department_id = request.GET.get('department_id')
        if department_id:
            queryset = queryset.filter(dept_id=department_id)

        # 排序
        ordering = request.GET.get('ordering', '-created_at')
        queryset = queryset.order_by(ordering)

        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # 手动构建响应数据以提高性能
        results = []
        for obj in page_obj.object_list:
            results.append({
                'id': obj.id,
                'request_number': obj.request_number,
                'item_name': obj.item_name,
                'specification': obj.specification,
                'quantity': obj.quantity,
                'unit': obj.unit,
                'unit_price': str(obj.unit_price) if obj.unit_price else None,
                'total_amount': str(obj.total_amount) if obj.total_amount else None,
                'status': obj.status,
                'created_at': obj.created_at.isoformat() if obj.created_at else None,
                'requester_name': obj.requester.first_name or obj.requester.username if obj.requester else None,
                'department_name': obj.department.dept_name if obj.department else None,
                'urgency_level': obj.urgency_level,
            })

        return Response({
            'code': 200,
            'message': '获取数据成功',
            'data': {
                'results': results,
                'count': paginator.count,
                'current_page': page,
                'total_pages': paginator.num_pages,
                'page_size': page_size,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        })
    except Exception as e:
        logger.error(f"快速列表API失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_requirements_optimized(request):
    """
    需求提报页面专用优化API - 高性能数据获取
    """
    try:
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 20)), 50)

        # 只选择需求提报页面需要的字段
        required_fields = [
            'id', 'request_number', 'item_name', 'specification', 'quantity', 'unit',
            'unit_price', 'total_amount', 'status', 'created_at', 'item_category',
            'purchase_type', 'remarks', 'urgency_level',
            'requester__username', 'requester__first_name',
            'hierarchy_path'
        ]

        # 优化查询 - 只查询需要的字段和关联
        queryset = PurchaseRequest.objects.select_related(
            'requester'
        ).only(*required_fields)

        # 需求提报页面状态筛选
        status_filter = ['draft', 'pending_approval', 'approved', 'rejected', 'returned']
        queryset = queryset.filter(status__in=status_filter)

        # 应用其他筛选条件
        status = request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(item_name__icontains=search) |
                Q(specification__icontains=search) |
                Q(request_number__icontains=search)
            )

        department_id = request.GET.get('department_id')
        if department_id:
            queryset = queryset.filter(dept_id=department_id)

        # 排序优化
        ordering = request.GET.get('ordering', '-created_at')
        queryset = queryset.order_by(ordering)

        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # 手动构建响应数据以提高性能
        results = []
        for obj in page_obj.object_list:
            # 构建层级路径
            hierarchy_path = obj.department.dept_name if obj.department else '未知单位'

            results.append({
                'id': obj.id,
                'request_number': obj.request_number,
                'item_name': obj.item_name,
                'item_category': obj.item_category,
                'specification': obj.specification,
                'quantity': obj.quantity,
                'unit': obj.unit,
                'unit_price': str(obj.unit_price) if obj.unit_price else None,
                'total_amount': str(obj.total_amount) if obj.total_amount else None,
                'status': obj.status,
                'created_at': obj.created_at.isoformat() if obj.created_at else None,
                'purchase_type': obj.purchase_type,
                'remarks': obj.remarks,
                'urgency_level': obj.urgency_level,
                'requester_name': obj.requester.first_name or obj.requester.username if obj.requester else None,
                'hierarchy_path': hierarchy_path,
            })

        return Response({
            'code': 200,
            'message': '获取数据成功',
            'data': {
                'results': results,
                'count': paginator.count,
                'current_page': page,
                'total_pages': paginator.num_pages,
                'page_size': page_size,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        })
    except Exception as e:
        logger.error(f"需求提报优化API失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取数据失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 导入导出功能模块 ====================

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def export_requests(request):
    """
    导出采购需求到Excel文件
    需要权限：purchase:approval:export

    功能说明：
    1. 根据筛选条件查询采购需求数据
    2. 支持多种筛选方式（状态、ID列表、日期范围等）
    3. 将查询结果导出为Excel格式文件
    4. 返回可下载的文件响应

    请求参数：
    - status: 单个状态筛选
    - status_in: 多状态筛选（逗号分隔）
    - ids: ID列表筛选（逗号分隔）
    - fields: 导出字段列表（逗号分隔）
    - 其他筛选参数：部门、日期范围等

    返回：
    - 成功：Excel文件流
    - 失败：错误信息JSON

    @param request: HTTP请求对象
    @return: HttpResponse Excel文件或错误响应
    """
    # 权限检查
    from apps.system.permissions import has_permission
    if not has_permission(request.user, 'purchase:approval:export'):
        return Response({
            'code': 403,
            'message': '权限不足，无法执行导出操作',
            'data': {
                'required_permission': 'purchase:approval:export',
                'user_role': getattr(request.user, 'role', None)
            }
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # 步骤1：初始化查询集，优化外键查询
        queryset = PurchaseRequest.objects.select_related(
            'requester', 'approver', 'purchaser', 'acceptor', 'reimburser'
        )

        # 检查是否是导出配置模式（用于物资采购页面的导出配置模态框）
        export_config_mode = request.GET.get('export_config_mode', 'false').lower() == 'true'

        # 检查是否是结算报销导出模式
        reimbursement_export = request.GET.get('reimbursement_export', 'false').lower() == 'true'

        if export_config_mode:
            # 导出配置模式：只显示待采购、已采购、已退回三种状态的数据
            queryset = queryset.filter(status__in=['pending_purchase', 'purchased', 'returned'])
        elif reimbursement_export:
            # 结算报销导出模式：只显示待结算、已结算状态的数据
            queryset = queryset.filter(status__in=['pending_reimbursement', 'settled'])

        # 步骤2：应用单状态筛选条件
        status_filter = request.GET.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # 步骤3：应用多状态筛选条件
        status_in_filter = request.GET.get('status_in')
        if status_in_filter:
            status_list = status_in_filter.split(',')

            # 如果是结算报销导出模式，确保状态在允许的范围内
            if reimbursement_export:
                allowed_statuses = ['pending_reimbursement', 'settled']
                status_list = [status for status in status_list if status in allowed_statuses]

            # 如果是导出配置模式，确保状态在允许的范围内
            elif export_config_mode:
                allowed_statuses = ['pending_purchase', 'purchased', 'returned']
                status_list = [status for status in status_list if status in allowed_statuses]

            if status_list:  # 只有在有有效状态时才应用筛选
                queryset = queryset.filter(status__in=status_list)

        # 步骤4：应用ID列表筛选条件
        ids_filter = request.GET.get('ids')
        if ids_filter:
            id_list = [int(id.strip()) for id in ids_filter.split(',') if id.strip().isdigit()]
            if id_list:
                queryset = queryset.filter(id__in=id_list)

        # 步骤5：应用部门筛选条件
        department_filter = request.GET.get('department')
        if department_filter:
            queryset = queryset.filter(dept_id=department_filter)

        # 步骤5.1：应用多部门筛选条件
        department_in_filter = request.GET.get('department_in')
        if department_in_filter:
            dept_ids = [int(dept_id.strip()) for dept_id in department_in_filter.split(',') if dept_id.strip().isdigit()]
            if dept_ids:
                queryset = queryset.filter(dept_id__in=dept_ids)

        # 步骤6：应用验收人筛选条件
        acceptor_filter = request.GET.get('acceptor')
        if acceptor_filter:
            queryset = queryset.filter(acceptor__username=acceptor_filter)

        # 步骤7：应用采购方式筛选条件
        purchase_type_filter = request.GET.get('purchase_type')
        if purchase_type_filter:
            queryset = queryset.filter(purchase_type=purchase_type_filter)

        # 步骤8：应用日期范围筛选条件
        start_date = request.GET.get('start_date')
        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)

        end_date = request.GET.get('end_date')
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)

        # 步骤9：应用关键字搜索筛选
        search_filter = request.GET.get('search')
        if search_filter:
            queryset = queryset.filter(
                Q(item_name__icontains=search_filter) |
                Q(specification__icontains=search_filter)
            )

        # 步骤10：应用层级路径筛选
        hierarchy_filter = request.GET.get('hierarchy_path__icontains')
        if hierarchy_filter:
            queryset = queryset.filter(hierarchy_path__icontains=hierarchy_filter)

        # 步骤11：应用申请人筛选
        requester_filter = request.GET.get('requester__username__icontains')
        if requester_filter:
            queryset = queryset.filter(requester__username__icontains=requester_filter)

        # 步骤12：应用创建时间范围筛选
        date_gte = request.GET.get('created_at__gte')
        if date_gte:
            queryset = queryset.filter(created_at__gte=date_gte)

        date_lte = request.GET.get('created_at__lte')
        if date_lte:
            queryset = queryset.filter(created_at__lte=date_lte)

        # 步骤13：应用其他筛选条件
        procurement_method_filter = request.GET.get('procurement_method__icontains')
        if procurement_method_filter:
            queryset = queryset.filter(procurement_method__icontains=procurement_method_filter)

        requirement_source_filter = request.GET.get('requirement_source__icontains')
        if requirement_source_filter:
            queryset = queryset.filter(requirement_source__icontains=requirement_source_filter)

        fund_project_filter = request.GET.get('fund_project__icontains')
        if fund_project_filter:
            queryset = queryset.filter(fund_project__icontains=fund_project_filter)

        unit_filter = request.GET.get('unit__icontains')
        if unit_filter:
            queryset = queryset.filter(unit__icontains=unit_filter)

        item_category_filter = request.GET.get('item_category__icontains')
        if item_category_filter:
            queryset = queryset.filter(item_category__icontains=item_category_filter)

        remarks_filter = request.GET.get('remarks__icontains')
        if remarks_filter:
            queryset = queryset.filter(remarks__icontains=remarks_filter)

        # 步骤14：应用金额范围筛选
        budget_gte = request.GET.get('budget_total_amount__gte')
        if budget_gte:
            try:
                queryset = queryset.filter(budget_total_amount__gte=float(budget_gte))
            except ValueError:
                pass

        budget_lte = request.GET.get('budget_total_amount__lte')
        if budget_lte:
            try:
                queryset = queryset.filter(budget_total_amount__lte=float(budget_lte))
            except ValueError:
                pass

        # 获取字段列表参数
        fields_param = request.GET.get('fields')
        selected_fields = None
        if fields_param:
            selected_fields = [field.strip() for field in fields_param.split(',') if field.strip()]

        # 添加调试日志
        logger.info(f'导出请求参数: {dict(request.GET)}')
        logger.info(f'查询结果数量: {queryset.count()}')
        logger.info(f'选择的字段: {selected_fields}')

        # 调用导出工具函数
        response = export_purchase_requests(queryset, selected_fields)
        return response

    except Exception as e:
        logger.error(f'导出失败: {str(e)}')
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def download_template(request):
    """
    下载导入模板
    GET /api/requests/template/
    """
    try:
        response = generate_purchase_template()
        return response
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'下载模板失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def import_requests(request):
    """
    从Excel导入采购需求
    POST /api/requests/import/
    """
    try:
        if 'file' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要导入的文件',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']
        skip_errors = request.data.get('skip_errors', False)
        result = import_purchase_requests(file, request.user, skip_errors)

        # 根据导入结果返回相应的状态和消息
        if result.get('success', False):
            success_count = result.get('success_count', 0)
            error_count = result.get('error_count', 0)

            if error_count == 0:
                message = f'导入成功，共导入 {success_count} 条记录'
            else:
                message = f'导入完成，成功导入 {success_count} 条，失败 {error_count} 条'

            return Response({
                'code': 200,
                'message': message,
                'data': result
            })
        else:
            return Response({
                'code': 400,
                'message': result.get('message', '导入失败'),
                'data': result
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导入失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def status_distribution(request):
    """
    获取状态分布统计数据（简化版本）
    """
    try:
        # 简化：直接使用基础查询，避免复杂的聚合
        status_counts = {}

        # 统计各状态的数量
        status_counts['pending'] = PurchaseRequest.objects.filter(status='pending_approval').count()
        status_counts['pending_purchase'] = PurchaseRequest.objects.filter(status='pending_purchase').count()
        status_counts['pending_acceptance'] = PurchaseRequest.objects.filter(status='pending_acceptance').count()
        status_counts['pending_reimbursement'] = PurchaseRequest.objects.filter(status='pending_reimbursement').count()
        status_counts['rejected'] = PurchaseRequest.objects.filter(status='rejected').count()
        status_counts['returned'] = PurchaseRequest.objects.filter(status='returned').count()
        status_counts['draft'] = PurchaseRequest.objects.filter(status='draft').count()
        status_counts['approved'] = PurchaseRequest.objects.filter(status='approved').count()
        status_counts['purchased'] = PurchaseRequest.objects.filter(status='purchased').count()
        status_counts['accepted'] = PurchaseRequest.objects.filter(status='accepted').count()
        status_counts['settled'] = PurchaseRequest.objects.filter(status='settled').count()

        return Response({
            'code': 200,
            'message': '获取状态分布成功',
            'data': status_counts
        })

    except Exception as e:
        logger.error(f"获取状态分布失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取状态分布失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])  # 临时允许匿名访问用于测试
def department_status_distribution(request):
    """
    获取各部门状态分布统计 - 显示所有一、二级单位数据，二级单位数据不纳入一级单位统计
    支持时间范围参数
    GET /api/dashboard/department-status/?days=30
    GET /api/dashboard/department-status/?start_date=2025-06-01&end_date=2025-06-30
    """
    try:
        from django.db.models import Count, Q
        from apps.system.models import Department

        # 支持自定义日期范围
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        if start_date_str and end_date_str:
            # 使用自定义日期范围
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            # 设置为当天结束时间
            end_date = end_date.replace(hour=23, minute=59, second=59)
            start_date = timezone.make_aware(start_date)
            end_date = timezone.make_aware(end_date)
            cache_key = f"dept_status_distribution_{start_date_str}_{end_date_str}"
        else:
            # 使用天数计算日期范围
            days = int(request.GET.get('days', 30))
            end_date = timezone.now()
            start_date = end_date - timezone.timedelta(days=days)
            cache_key = f"dept_status_distribution_days_{days}"

        # 尝试从缓存获取数据
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)

        # 优化版本：先获取所有部门，再统计数据，确保显示所有部门
        from django.db.models import Sum, Case, When, IntegerField
        from apps.system.models import Department

        # 1. 获取所有启用的部门
        all_departments = Department.objects.filter(status=True).order_by('dept_name')

        # 2. 获取时间范围内的采购请求统计数据
        stats_queryset = PurchaseRequest.objects.filter(
            submission_date__range=[start_date, end_date]
        ).values('hierarchy_path').annotate(
            # 统计各状态数量 - 修复：使用正确的状态值
            pending_count=Count('id', filter=Q(status='pending_approval')),
            pending_purchase_count=Count('id', filter=Q(status='pending_purchase')),
            pending_acceptance_count=Count('id', filter=Q(status='pending_acceptance')),
            pending_reimbursement_count=Count('id', filter=Q(status='pending_reimbursement')),

            # 统计采购类型
            unified_count=Count('id', filter=Q(purchase_type='unified')),
            self_count=Count('id', filter=Q(purchase_type='self')),

            # 统计金额
            unified_amount=Sum('budget_total_amount', filter=Q(purchase_type='unified')),
            self_amount=Sum('budget_total_amount', filter=Q(purchase_type='self')),

            # 总计
            total_count=Count('id'),
            total_amount=Sum('budget_total_amount')
        ).filter(
            hierarchy_path__isnull=False
        )

        # 3. 创建统计数据映射
        stats_map = {}
        for item in stats_queryset:
            hierarchy_path = item['hierarchy_path']
            if hierarchy_path:
                stats_map[hierarchy_path] = item

        # 4. 构建扁平化结果，包含所有部门（一级和二级部门都平铺显示）
        result = []

        # 扁平化处理所有部门
        for dept in all_departments:
            if dept.parent_id is None:
                # 一级部门
                dept_name = dept.dept_name
                hierarchy_path = dept_name
                display_name = dept_name
                level = 1
                parent_name = None
            else:
                # 二级部门
                try:
                    parent_dept = all_departments.get(id=dept.parent_id)
                    parent_name = parent_dept.dept_name
                    dept_name = dept.dept_name
                    hierarchy_path = f"{parent_name}-{dept_name}"
                    display_name = f"{parent_name}-{dept_name}"  # 扁平化显示完整路径
                    level = 2
                except Department.DoesNotExist:
                    # 如果找不到父部门，按一级部门处理
                    dept_name = dept.dept_name
                    hierarchy_path = dept_name
                    display_name = dept_name
                    level = 1
                    parent_name = None

            # 获取统计数据，如果没有则为0
            stats = stats_map.get(hierarchy_path, {})

            result.append({
                'name': hierarchy_path,  # 用于数据匹配
                'dept_name': dept_name,  # 部门名称
                'display_name': display_name,  # 显示名称（扁平化）
                'pending': stats.get('pending_count', 0) or 0,
                'pending_purchase': stats.get('pending_purchase_count', 0) or 0,
                'pending_acceptance': stats.get('pending_acceptance_count', 0) or 0,
                'pending_reimbursement': stats.get('pending_reimbursement_count', 0) or 0,
                'unified_count': stats.get('unified_count', 0) or 0,
                'self_count': stats.get('self_count', 0) or 0,
                'unified_amount': float(stats.get('unified_amount', 0) or 0),
                'self_amount': float(stats.get('self_amount', 0) or 0),
                'total_count': stats.get('total_count', 0) or 0,
                'total_amount': float(stats.get('total_amount', 0) or 0),
                'level': level,
                'parent_name': parent_name
            })

        # 按部门名称排序，确保显示顺序一致
        result.sort(key=lambda x: (x['level'], x['display_name']))



        response_data = {
            'code': 200,
            'message': '获取部门状态分布成功',
            'data': result
        }

        # 缓存数据3分钟
        cache.set(cache_key, response_data, 180)

        return Response(response_data)

    except Exception as e:
        logger.error(f"获取部门状态分布失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取部门状态分布失败: {str(e)}',
            'data': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def budget_statistics(request):
    """
    获取经费项目统计
    """
    try:
        from django.db.models import Sum, Count

        # 按经费项目统计 - 使用预算金额统计
        budget_stats = PurchaseRequest.objects.values('fund_project').annotate(
            amount=Sum('budget_total_amount'),
            count=Count('id')
        ).filter(
            fund_project__isnull=False,
            amount__gt=0
        ).order_by('-amount')[:10]  # 取前10个经费项目

        result = []
        for item in budget_stats:
            result.append({
                'name': item['fund_project'] or '未分类',
                'amount': float(item['amount'] or 0),
                'count': item['count']
            })

        return Response({
            'code': 200,
            'message': '获取经费项目统计成功',
            'data': result
        })

    except Exception as e:
        logger.error(f"获取经费项目统计失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取经费项目统计失败: {str(e)}',
            'data': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
@api_view(['GET'])
def method_statistics(request):
    """
    获取采购方式统计 - 优化版本，显示字典表中的所有采购方式
    GET /api/dashboard/method-statistics/?days=30
    GET /api/dashboard/method-statistics/?start_date=2025-06-01&end_date=2025-06-30
    """
    try:
        from django.db.models import Sum, Count
        from apps.purchase.services.dict_service import DictService

        # 支持自定义日期范围
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        queryset = PurchaseRequest.objects.all()

        if start_date_str and end_date_str:
            # 使用自定义日期范围
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            # 设置为当天结束时间
            end_date = end_date.replace(hour=23, minute=59, second=59)
            start_date = timezone.make_aware(start_date)
            end_date = timezone.make_aware(end_date)
            queryset = queryset.filter(submission_date__range=[start_date, end_date])
        else:
            # 使用天数计算日期范围
            days = int(request.GET.get('days', 30))
            end_date = timezone.now()
            start_date = end_date - timezone.timedelta(days=days)
            queryset = queryset.filter(submission_date__range=[start_date, end_date])

        # 获取字典表中的所有采购方式
        dict_methods = DictService.get_dict_items('procurement_method', enabled_only=True)
        valid_method_codes = [item['code'] for item in dict_methods]

        # 只统计字典表中存在的采购方式，排除无效数据
        method_stats = queryset.filter(
            procurement_method__in=valid_method_codes,
            budget_total_amount__gt=0
        ).values('procurement_method').annotate(
            amount=Sum('budget_total_amount'),
            count=Count('id')
        ).order_by('-amount')

        # 创建统计数据映射
        stats_map = {item['procurement_method']: item for item in method_stats}

        # 计算总数用于百分比计算（只计算有效数据）
        total_count = sum(item['count'] for item in method_stats)
        total_amount = sum(float(item['amount'] or 0) for item in method_stats)

        # 构建结果，包含所有字典中的采购方式
        result = []
        for dict_item in dict_methods:
            code = dict_item['code']
            name = dict_item['name']

            # 获取统计数据，如果没有则为0
            stats = stats_map.get(code, {'count': 0, 'amount': 0})
            count = stats['count']
            amount = float(stats['amount'] or 0)

            # 计算百分比
            percentage = (count / total_count * 100) if total_count > 0 else 0
            amount_percentage = (amount / total_amount * 100) if total_amount > 0 else 0

            result.append({
                'method_name': name,
                'name': name,
                'code': code,
                'amount': amount,
                'count': count,
                'percentage': round(percentage, 2),
                'amount_percentage': round(amount_percentage, 2),
                'order': dict_item.get('order', 999)
            })

        # 按字典顺序排序
        result.sort(key=lambda x: x['order'])

        return Response({
            'code': 200,
            'message': '获取采购方式统计成功',
            'data': {'methods': result}
        })

    except Exception as e:
        logger.error(f"获取采购方式统计失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取采购方式统计失败: {str(e)}',
            'data': {'methods': []}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])  # 临时允许匿名访问用于测试
def department_statistics(request):
    """
    获取部门统计 - 返回所有一、二级单位数据，二级单位数据不纳入一级单位统计
    """
    try:
        from django.db.models import Sum, Count
        from apps.system.models import Department

        result = []
        total_amount = 0

        # 获取所有启用的部门
        all_departments = Department.objects.filter(status=True)

        # 分别处理一级部门和二级部门
        first_level_depts = all_departments.filter(parent_id__isnull=True)
        second_level_depts = all_departments.filter(parent_id__isnull=False)

        # 处理一级部门（只统计直接属于该部门的数据，不包含子部门）
        for dept in first_level_depts:
            # 总体统计 - 使用预算金额统计
            dept_stats = PurchaseRequest.objects.filter(
                hierarchy_path=dept.dept_name  # 使用hierarchy_path匹配
            ).aggregate(
                total_amount=Sum('budget_total_amount'),
                total_count=Count('id')
            )

            # 统一采购统计
            unified_stats = PurchaseRequest.objects.filter(
                hierarchy_path=dept.dept_name,
                purchase_type='unified'
            ).aggregate(
                unified_amount=Sum('budget_total_amount'),
                unified_count=Count('id')
            )

            # 自行采购统计
            self_stats = PurchaseRequest.objects.filter(
                hierarchy_path=dept.dept_name,
                purchase_type='self'
            ).aggregate(
                self_amount=Sum('budget_total_amount'),
                self_count=Count('id')
            )

            amount = float(dept_stats['total_amount'] or 0)
            count = dept_stats['total_count'] or 0
            unified_amount = float(unified_stats['unified_amount'] or 0)
            unified_count = unified_stats['unified_count'] or 0
            self_amount = float(self_stats['self_amount'] or 0)
            self_count = self_stats['self_count'] or 0
            total_amount += amount

            result.append({
                'dept_name': dept.dept_name,
                'name': dept.dept_name,
                'amount': amount,
                'count': count,
                'unified_amount': unified_amount,
                'unified_count': unified_count,
                'self_amount': self_amount,
                'self_count': self_count,
                'parent_id': None,
                'level': 1
            })

        # 处理二级部门
        for dept in second_level_depts:
            # 获取父部门名称
            parent_name = ''
            if dept.parent_id:
                try:
                    parent_dept = Department.objects.get(id=dept.parent_id)
                    parent_name = parent_dept.dept_name
                except Department.DoesNotExist:
                    parent_name = ''

            # 构建完整的层级路径
            full_hierarchy_path = f"{parent_name}-{dept.dept_name}" if parent_name else dept.dept_name

            # 总体统计 - 使用预算金额统计
            dept_stats = PurchaseRequest.objects.filter(
                hierarchy_path=full_hierarchy_path  # 使用hierarchy_path匹配
            ).aggregate(
                total_amount=Sum('budget_total_amount'),
                total_count=Count('id')
            )

            # 统一采购统计
            unified_stats = PurchaseRequest.objects.filter(
                hierarchy_path=full_hierarchy_path,
                purchase_type='unified'
            ).aggregate(
                unified_amount=Sum('budget_total_amount'),
                unified_count=Count('id')
            )

            # 自行采购统计
            self_stats = PurchaseRequest.objects.filter(
                hierarchy_path=full_hierarchy_path,
                purchase_type='self'
            ).aggregate(
                self_amount=Sum('budget_total_amount'),
                self_count=Count('id')
            )

            amount = float(dept_stats['total_amount'] or 0)
            count = dept_stats['total_count'] or 0
            unified_amount = float(unified_stats['unified_amount'] or 0)
            unified_count = unified_stats['unified_count'] or 0
            self_amount = float(self_stats['self_amount'] or 0)
            self_count = self_stats['self_count'] or 0
            total_amount += amount

            result.append({
                'dept_name': dept.dept_name,
                'name': dept.dept_name,
                'full_name': full_hierarchy_path,
                'amount': amount,
                'count': count,
                'unified_amount': unified_amount,
                'unified_count': unified_count,
                'self_amount': self_amount,
                'self_count': self_count,
                'parent_id': dept.parent_id,
                'level': 2
            })

        # 计算百分比
        for item in result:
            if total_amount > 0:
                item['percentage'] = round((item['amount'] / total_amount) * 100, 2)
            else:
                item['percentage'] = 0

        return Response({
            'code': 200,
            'message': '获取部门统计成功',
            'data': {'departments': result}
        })

    except Exception as e:
        logger.error(f"获取部门统计失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取部门统计失败: {str(e)}',
            'data': {'departments': []}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def purchase_type_statistics(request):
    """
    获取采购类型统计
    GET /api/dashboard/purchase-type-statistics/?days=30
    GET /api/dashboard/purchase-type-statistics/?start_date=2025-06-01&end_date=2025-06-30
    """
    try:
        from django.db.models import Sum, Count

        # 支持自定义日期范围
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        queryset = PurchaseRequest.objects.all()

        if start_date_str and end_date_str:
            # 使用自定义日期范围
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            # 设置为当天结束时间
            end_date = end_date.replace(hour=23, minute=59, second=59)
            start_date = timezone.make_aware(start_date)
            end_date = timezone.make_aware(end_date)
            queryset = queryset.filter(submission_date__range=[start_date, end_date])
        else:
            # 使用天数计算日期范围
            days = int(request.GET.get('days', 30))
            end_date = timezone.now()
            start_date = end_date - timezone.timedelta(days=days)
            queryset = queryset.filter(submission_date__range=[start_date, end_date])

        # 按采购类型统计 - 使用预算金额统计
        type_stats = queryset.values('purchase_type').annotate(
            amount=Sum('budget_total_amount'),
            count=Count('id')
        ).filter(
            purchase_type__isnull=False,
            amount__gt=0
        ).order_by('-amount')

        # 计算总数用于百分比计算
        total_count = sum(item['count'] for item in type_stats)
        total_amount = sum(float(item['amount'] or 0) for item in type_stats)

        # 导入字典服务
        from apps.purchase.services.dict_service import DictService

        result = []
        for item in type_stats:
            percentage = (item['count'] / total_count * 100) if total_count > 0 else 0
            purchase_type_code = item['purchase_type'] or 'unified'
            # 使用字典服务转换采购类型编码为中文名称
            purchase_type_name = DictService.get_text('purchase_type', purchase_type_code, '统一采购')

            result.append({
                'type': purchase_type_code,
                'name': purchase_type_name,
                'amount': float(item['amount'] or 0),
                'count': item['count'],
                'percentage': round(percentage, 2)
            })

        return Response({
            'code': 200,
            'message': '获取采购类型统计成功',
            'data': {
                'total_count': total_count,
                'total_amount': total_amount,
                'types': result
            }
        })

    except Exception as e:
        logger.error(f"获取采购类型统计失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取采购类型统计失败: {str(e)}',
            'data': {
                'total_count': 0,
                'total_amount': 0,
                'types': []
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_reimbursement_statistics(request):
    """
    获取结算报销页面的统计数据
    GET /api/purchase-requests/reimbursement-statistics/
    """
    try:
        from django.db.models import Sum, Count, Q

        # 获取结算报销相关的状态数据
        # 结算报销页面主要关注：pending_reimbursement（待结算）和 settled（已结算）状态
        reimbursement_queryset = PurchaseRequest.objects.filter(
            status__in=['pending_reimbursement', 'settled']
        )

        # 基础统计
        stats = reimbursement_queryset.aggregate(
            total_count=Count('id'),
            pending_count=Count('id', filter=Q(status='pending_reimbursement')),
            settled_count=Count('id', filter=Q(status='settled')),
            total_amount=Sum('budget_total_amount'),
            settled_amount=Sum('settlement_amount', filter=Q(status='settled'))
        )

        # 部门统计
        department_stats = reimbursement_queryset.values('hierarchy_path').annotate(
            count=Count('id'),
            amount=Sum('budget_total_amount')
        ).filter(
            hierarchy_path__isnull=False
        )

        # 计算涉及的一级部门数量
        department_set = set()
        for item in department_stats:
            hierarchy_path = item['hierarchy_path']
            if hierarchy_path:
                first_level_dept = hierarchy_path.split('-')[0].strip()
                if first_level_dept:
                    department_set.add(first_level_dept)

        return Response({
            'code': 200,
            'message': '获取结算报销统计成功',
            'data': {
                'total_count': stats['total_count'] or 0,
                'pending_count': stats['pending_count'] or 0,
                'settled_count': stats['settled_count'] or 0,
                'total_amount': float(stats['total_amount'] or 0),
                'settled_amount': float(stats['settled_amount'] or 0),
                'department_count': len(department_set)
            }
        })

    except Exception as e:
        logger.error(f"获取结算报销统计失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'获取结算报销统计失败: {str(e)}',
            'data': {
                'total_count': 0,
                'pending_count': 0,
                'settled_count': 0,
                'total_amount': 0,
                'settled_amount': 0,
                'department_count': 0
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def acceptances_redirect_view(request):
    """
    验收接口重定向视图
    将 /acceptances/ 请求重定向到 /requests/ 并添加验收状态筛选
    """
    # 获取原始查询参数
    params = request.GET.copy()

    # 移除无效的dateRange参数
    if 'dateRange' in params:
        del params['dateRange']

    # 添加验收状态筛选
    if 'status' not in params and 'status_in' not in params:
        params['status_in'] = 'pending_acceptance,accepted'

    # 构建重定向URL
    redirect_url = '/api/requests/'
    if params:
        redirect_url += '?' + params.urlencode()

    return Response({
        'code': 301,
        'message': '请使用 /api/requests/ 接口并添加适当的状态筛选',
        'data': {
            'redirect_url': redirect_url,
            'suggested_params': {
                'status_in': 'pending_acceptance,accepted',
                'note': '验收相关数据请通过采购需求接口获取'
            }
        }
    }, status=status.HTTP_301_MOVED_PERMANENTLY)