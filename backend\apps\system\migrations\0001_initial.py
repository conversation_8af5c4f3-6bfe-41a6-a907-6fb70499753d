# Generated by Django 5.2.1 on 2025-06-12 14:03

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dept_name', models.CharField(max_length=50, verbose_name='部门名称')),
                ('parent_id', models.IntegerField(blank=True, null=True, verbose_name='上级部门ID')),
                ('dept_code', models.CharField(max_length=20, unique=True, verbose_name='部门编码')),
                ('hierarchy_path', models.Char<PERSON>ield(help_text='例：分公司-办事处', max_length=200, verbose_name='部门层级路径')),
                ('status', models.BooleanField(default=True, verbose_name='启用状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '部门',
                'verbose_name_plural': '部门管理',
                'db_table': 'sys_department',
            },
        ),
        migrations.CreateModel(
            name='DictData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(help_text='采购方式/经费来源/物品分类等', max_length=50, verbose_name='字典类型')),
                ('code', models.CharField(max_length=50, verbose_name='字典编码')),
                ('name', models.CharField(max_length=100, verbose_name='字典名称')),
                ('description', models.TextField(blank=True, verbose_name='描述信息')),
                ('order', models.IntegerField(default=0, verbose_name='排序值')),
                ('status', models.BooleanField(default=True, help_text='启用/停用', verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='最后更新时间')),
            ],
            options={
                'verbose_name': '数据字典',
                'verbose_name_plural': '数据字典管理',
                'db_table': 'purchase_dict_data',
                'ordering': ['type', 'order', 'code'],
                'unique_together': {('type', 'code')},
            },
        ),
    ]
