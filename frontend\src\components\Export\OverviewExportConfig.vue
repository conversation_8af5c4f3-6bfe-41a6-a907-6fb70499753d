<template>
  <a-modal :open="visible" title="导出Excel配置" width="80%" :footer="null" @cancel="$emit('cancel')" :resizable="true">
    <div class="export-config-container">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <h4>筛选条件</h4>
        <div class="filter-form-container">
          <a-form :model="filters" :label-col="{ style: { width: '70px', textAlign: 'right' } }">
            <a-row :gutter="[12, 6]">
              <a-col :span="4">
                <a-form-item label="需求单位">
                  <a-select v-model:value="filters.department" placeholder="选择需求单位" allowClear show-search
                    :filter-option="filterOption" @change="debouncedSearch" size="small">
                    <a-select-option v-for="dept in departmentOptions" :key="dept.value" :value="dept.value"
                      :label="dept.label">
                      {{ dept.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="状态">
                  <a-select v-model:value="filters.status" placeholder="选择状态" allowClear @change="debouncedSearch"
                    size="small">
                    <a-select-option v-for="status in statusOptions" :key="status.value" :value="status.value">
                      {{ status.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="采购类型">
                  <a-select v-model:value="filters.purchase_type" placeholder="选择采购类型" allowClear
                    @change="debouncedSearch" size="small">
                    <a-select-option v-for="type in purchaseTypeOptions" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="日期范围">
                  <a-range-picker v-model:value="filters.dateRange" style="width: 100%" @change="debouncedSearch"
                    size="small" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="采购方式">
                  <a-select v-model:value="filters.procurementMethod" placeholder="选择采购方式" allowClear
                    @change="debouncedSearch" size="small">
                    <a-select-option v-for="method in procurementMethodOptions" :key="method.value"
                      :value="method.value">
                      {{ method.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item>
                  <a-space>
                    <a-button @click="resetFilters" size="small">重置</a-button>
                    <a-button type="primary" @click="searchRecords" size="small">搜索</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>



      <!-- 导出字段配置 -->
      <div class="fields-section">
        <a-collapse v-model:activeKey="fieldsCollapseKey" size="small">
          <a-collapse-panel key="1" header="导出字段配置">
            <template #extra>
              <a-space>
                <a-button @click.stop="selectAllExportFields" size="small">全选</a-button>
                <a-button @click.stop="resetExportFields" size="small">重置</a-button>
                <a-button @click.stop="selectRequiredExportFields" size="small">仅必选</a-button>
              </a-space>
            </template>
            <a-checkbox-group v-model:value="selectedFields" @change="onFieldsChange">
              <div class="export-fields-container">
                <div v-for="category in fieldCategories" :key="category.key" class="export-field-category">
                  <div class="export-category-header">
                    <h5 class="export-category-title">{{ category.title }}</h5>
                  </div>
                  <div class="export-category-fields">
                    <div v-for="field in fieldOptions.filter(opt => opt.category === category.key)" :key="field.value"
                      class="export-field-option">
                      <a-checkbox :value="field.value" :disabled="field.required">
                        <span class="field-title">{{ field.label }}</span>
                        <a-tag v-if="field.required" size="small" color="blue">必选</a-tag>
                      </a-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </a-checkbox-group>
          </a-collapse-panel>
        </a-collapse>
      </div>

      <!-- 选择导出记录 -->
      <div class="records-section">
        <div class="section-header">
          <h4>选择导出记录</h4>
          <div class="section-actions">
            <a-space>
              <span class="record-count">共 {{ records.length }} 条记录，已选择 {{ selectedRecords.length }} 条</span>
              <a-select v-model:value="recordPageSize" size="small" style="width: 100px"
                @change="onRecordPageSizeChange">
                <a-select-option :value="10">10条/页</a-select-option>
                <a-select-option :value="20">20条/页</a-select-option>
                <a-select-option :value="50">50条/页</a-select-option>
                <a-select-option :value="100">100条/页</a-select-option>
              </a-select>
            </a-space>
          </div>
        </div>

        <a-table :columns="recordColumns" :data-source="records" :row-selection="recordRowSelection" :pagination="{
          current: recordPagination.current,
          pageSize: recordPageSize,
          total: recordPagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          onChange: onRecordPageChange,
          onShowSizeChange: onRecordPageSizeChange
        }" :loading="searchLoading" size="small" :scroll="{ x: 1200 }" row-key="id" bordered>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status_display || record.status || '-' }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'purchase_type'">
              {{ record.purchase_type_display || record.purchase_type || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'procurement_method'">
              {{ record.procurement_method_display || record.procurement_method || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'requirement_source'">
              {{ record.requirement_source }}
            </template>
            <template v-else-if="['amount', 'purchase_amount', 'settlement_amount'].includes(column.dataIndex)">
              ¥{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '0.00' }}
            </template>
            <template
              v-else-if="['created_at', 'purchase_time', 'acceptance_time', 'settlement_time'].includes(column.dataIndex)">
              {{ record[column.dataIndex] ? formatDateToYMD(record[column.dataIndex]) : '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'quantity'">
              {{ record.quantity || 0 }}
            </template>
            <template v-else>
              {{ record[column.dataIndex] || '-' }}
            </template>
          </template>
        </a-table>
      </div>

      <!-- 导出预览和操作 -->
      <div class="actions-section">
        <a-space>
          <a-button @click="previewExport" :disabled="selectedRecords.length === 0">
            <EyeOutlined />
            预览 ({{ selectedRecords.length }}条)
          </a-button>
          <a-button type="primary" @click="executeExport" :disabled="selectedRecords.length === 0"
            :loading="exportLoading">
            <DownloadOutlined />
            导出Excel ({{ selectedRecords.length }}条)
          </a-button>
          <a-button @click="handleCancel">取消</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>

  <!-- 预览模态框 -->
  <a-modal :open="showPreviewModal" title="导出预览" width="90%" height="80vh" :footer="null" :centered="true"
    :mask-closable="true" :destroy-on-close="true" wrap-class-name="preview-modal-wrapper"
    @cancel="showPreviewModal = false">
    <div class="preview-container">
      <div class="preview-header">
        <a-space>
          <span>预览数据 (共{{ previewTotal }}条记录)</span>
          <a-button size="small" @click="loadPreviewData" :loading="previewLoading">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>

      <div class="preview-table-container">
        <a-table :columns="previewColumns" :data-source="previewData" :loading="previewLoading" :pagination="{
          current: previewPagination.current,
          pageSize: previewPagination.pageSize,
          total: previewTotal,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ['20', '50', '100'],
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          onChange: onPreviewPageChange,
          onShowSizeChange: onPreviewPageSizeChange
        }" size="small" :scroll="{ x: 1200, y: 'calc(80vh - 200px)' }" row-key="id" bordered>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status_display || record.status || '-' }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'purchase_type'">
              {{ record.purchase_type_display || record.purchase_type || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'procurement_method'">
              {{ record.procurement_method_display || record.procurement_method || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'requirement_source'">
              {{ record.requirement_source || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'item_category'">
              {{ record.item_category_display || record.item_category || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'unit'">
              {{ record.unit_display || record.unit || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'fund_project'">
              {{ record.fund_project_display || record.fund_project || '-' }}
            </template>
            <template v-else-if="['amount', 'purchase_amount', 'settlement_amount'].includes(column.dataIndex)">
              ¥{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '0.00' }}
            </template>
            <template
              v-else-if="['created_at', 'purchase_time', 'acceptance_time', 'settlement_time'].includes(column.dataIndex)">
              {{ record[column.dataIndex] ? formatDateToYMD(record[column.dataIndex]) : '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'quantity'">
              {{ record.quantity || 0 }}
            </template>
            <template v-else>
              {{ record[column.dataIndex] || '-' }}
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  DownloadOutlined,
  EyeOutlined,
} from '@ant-design/icons-vue'
import api from '@/api'
import { getPageExportFieldOptions, getPageFieldCategories, getPageDefaultExportFields } from '@/utils/validation'
import { useDictMixin } from '@/mixins/dictMixin'
import { debounce } from 'lodash'

export default {
  name: 'OverviewExportConfig',
  components: {
    // SearchOutlined,
    ReloadOutlined,
    EyeOutlined,
    DownloadOutlined,
    // UpOutlined,
    // DownOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['cancel', 'export-complete'],
  setup(props, { emit }) {
    // 使用字典混入 - 只保留必要的方法
    const {
      getStatusColor,
      formatDateToYMD
    } = useDictMixin()

    // 基础数据
    const departmentOptions = ref([])
    const statusOptions = ref([])
    const purchaseTypeOptions = ref([])
    const procurementMethodOptions = ref([])
    const fundProjectOptions = ref([])
    const unitOptions = ref([])
    const itemCategoryOptions = ref([])
    const exportLoading = ref(false)
    const previewLoading = ref(false)
    const previewData = ref([])
    const previewTotal = ref(0)
    const showPreviewModal = ref(false)
    // 移除showFilters，不再需要展开/折叠功能
    const previewPagination = ref({
      current: 1,
      pageSize: 50
    })

    // 记录选择相关
    const records = ref([])
    const selectedRecords = ref([])
    const selectedRecordData = ref([])
    const searchLoading = ref(false)
    const recordPageSize = ref(20)
    const recordPagination = ref({
      current: 1,
      total: 0
    })

    // 字段配置
    const exportFieldOptions = getPageExportFieldOptions('overview')
    const rawFieldCategories = getPageFieldCategories('overview')

    // 初始化选中字段，确保必选字段被包含
    const initializeSelectedFields = () => {
      const defaultFields = getPageDefaultExportFields('overview')
      const requiredFields = exportFieldOptions.filter(field => field.required).map(field => field.value)
      // 合并默认字段和必选字段，去重
      const allFields = [...new Set([...defaultFields, ...requiredFields])]
      return allFields
    }

    const selectedFields = ref(initializeSelectedFields())
    const fieldsCollapseKey = ref([]) // 默认折叠状态
    const fieldOptions = exportFieldOptions
    const fieldCategories = rawFieldCategories

    // 筛选条件
    const filters = reactive({
      department: undefined,
      status: undefined,
      purchase_type: undefined,
      dateRange: [],
      procurementMethod: '',
      requirementSource: '',
      fundProject: '',
      unit: '',
      itemCategory: '',
      remarks: '',
      itemName: '',
      minAmount: undefined,
      maxAmount: undefined
    })

    // 字段选择状态
    const fieldsIndeterminate = computed(() => {
      return selectedFields.value.length > 0 && selectedFields.value.length < exportFieldOptions.length
    })

    const fieldsCheckAll = computed(() => {
      return selectedFields.value.length === exportFieldOptions.length
    })

    // 字段中文标题映射 - 与validation.js中的字段配置保持一致
    const getFieldTitle = (fieldKey) => {
      const titleMap = {
        // 基础信息字段
        'id': 'ID',
        'item_category': '物品种类',
        'item_name': '物品名称',
        'specification': '规格型号',
        'unit': '计量单位',
        'purchase_type': '采购类型',
        'status': '状态',
        'created_at': '创建时间',

        // 需求信息字段
        'budget_quantity': '需求数量',
        'budget_unit_price': '预算单价',
        'budget_total_amount': '预算金额',
        'requirement_source': '需求来源',
        'procurement_method': '采购方式',
        'fund_project': '经费项目',
        'remarks': '需求备注',

        // 组织和人员字段
        'hierarchy_path': '部门层级路径',
        'requester_name': '申请人姓名',
        'approver_name': '审批人姓名',
        'purchaser_name': '采购员姓名',
        'acceptor_name': '验收人姓名',
        'reimburser_name': '报销人姓名',

        // 审批相关字段
        'submission_date': '提交时间',
        'approved_at': '审批时间',
        'approval_comment': '审批意见',
        'rejection_reason': '驳回原因',

        // 采购相关字段
        'purchase_quantity': '采购数量',
        'purchase_unit_price': '采购单价',
        'purchase_total_amount': '采购金额',
        'supplier_name': '供应商名称',
        'purchase_date': '采购时间',
        'purchase_remarks': '采购备注',

        // 验收相关字段
        'acceptance_quantity': '验收数量',
        'acceptance_date': '验收时间',
        'acceptance_remarks': '验收备注',
        'courier_company': '快递公司',
        'tracking_number': '快递单号',

        // 结算相关字段
        'settlement_amount': '结算金额',
        'reimbursement_date': '结算时间',
        'reimbursement_voucher_no': '报销凭证号',
        'settlement_remarks': '结算备注',

        // 兼容旧字段名
        'quantity': '数量',
        'amount': '预算金额',
        'dept_name': '需求单位',
        'requester': '申请人',
        'purchase_time': '采购时间',
        'acceptance_time': '验收时间',
        'settlement_time': '结算时间',
        'payee_name': '收款人户名',
        'payee_account': '收款人账号'
      }
      return titleMap[fieldKey] || fieldKey
    }

    // 记录表格列 - 根据选中字段动态生成，ID列始终在第一列
    const recordColumns = computed(() => {
      // 确保ID列始终在第一列
      let columns = []

      // 如果选中字段包含ID，先添加ID列
      if (selectedFields.value.includes('id')) {
        columns.push({
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          ellipsis: true,
          align: 'center',
          resizable: true,
          fixed: 'left',
          sorter: true
        })
      }

      // 添加其他列（除了ID）
      const otherColumns = selectedFields.value
        .filter(fieldKey => fieldKey !== 'id')
        .map(fieldKey => {
          // 从fieldOptions中查找对应的label，如果找不到则使用getFieldTitle
          const fieldOption = fieldOptions.find(opt => opt.value === fieldKey)
          const title = fieldOption ? fieldOption.label : getFieldTitle(fieldKey)

          return {
            title: title,
            dataIndex: fieldKey,
            width: getColumnWidth(fieldKey),
            ellipsis: true,
            align: 'center',
            resizable: true,
            sorter: ['created_at', 'amount', 'purchase_amount', 'settlement_amount'].includes(fieldKey)
          }
        })

      columns = columns.concat(otherColumns)
      return columns
    })

    // 获取列宽度
    const getColumnWidth = (fieldKey) => {
      const widthMap = {
        // 基础信息字段
        'id': 80,
        'item_category': 100,
        'item_name': 150,
        'specification': 120,
        'unit': 80,
        'purchase_type': 100,
        'status': 100,
        'created_at': 120,

        // 需求信息字段
        'budget_quantity': 80,
        'budget_unit_price': 100,
        'budget_total_amount': 120,
        'requirement_source': 120,
        'procurement_method': 120,
        'fund_project': 120,
        'remarks': 150,

        // 组织和人员字段
        'hierarchy_path': 200,
        'requester_name': 100,
        'approver_name': 100,
        'purchaser_name': 100,
        'acceptor_name': 100,
        'reimburser_name': 100,

        // 审批相关字段
        'submission_date': 120,
        'approved_at': 120,
        'approval_comment': 150,
        'rejection_reason': 150,

        // 采购相关字段
        'purchase_quantity': 80,
        'purchase_unit_price': 100,
        'purchase_total_amount': 120,
        'supplier_name': 120,
        'purchase_date': 120,
        'purchase_remarks': 150,

        // 验收相关字段
        'acceptance_quantity': 80,
        'acceptance_date': 120,
        'acceptance_remarks': 150,
        'courier_company': 100,
        'tracking_number': 120,

        // 结算相关字段
        'settlement_amount': 120,
        'reimbursement_date': 120,
        'reimbursement_voucher_no': 150,
        'settlement_remarks': 150,

        // 兼容旧字段名
        'quantity': 80,
        'amount': 100,
        'dept_name': 120
      }
      return widthMap[fieldKey] || 100
    }

    // 记录行选择
    const recordRowSelection = computed(() => ({
      selectedRowKeys: selectedRecords.value,
      onChange: (selectedRowKeys, selectedRows) => {
        selectedRecords.value = [...selectedRowKeys]
        selectedRecordData.value = [...selectedRows]
      }
    }))

    // 预览表格列 - 确保ID列在第一列，使用中文标题
    const previewColumns = computed(() => {
      let columns = []

      // 如果选中字段包含ID，先添加ID列
      if (selectedFields.value.includes('id')) {
        columns.push({
          title: 'ID',
          dataIndex: 'id',
          key: 'id',
          width: 80,
          ellipsis: true,
          align: 'center',
          fixed: 'left'
        })
      }

      // 添加其他列（除了ID）
      const otherColumns = selectedFields.value
        .filter(field => field !== 'id')
        .map(field => {
          // 从fieldOptions中查找对应的label，如果找不到则使用getFieldTitle
          const fieldOption = fieldOptions.find(opt => opt.value === field)
          const title = fieldOption ? fieldOption.label : getFieldTitle(field)

          return {
            title: title,
            dataIndex: field,
            key: field,
            width: 120,
            ellipsis: true,
            align: 'center'
          }
        })

      columns = columns.concat(otherColumns)
      return columns
    })

    // 筛选选项过滤
    const filterOption = (input, option) => {
      return option.label.toLowerCase().includes(input.toLowerCase())
    }

    // 初始化部门选项 - 用于下拉选择，使用专门的无分页接口
    const initDepartmentOptions = async () => {
      try {
        const response = await api.departments.getAll()
        console.log('Department response:', response)
        if (response.code === 200) {
          const departments = response.data || []
          departmentOptions.value = departments.map(dept => ({
            value: dept.id,
            label: dept.dept_name || dept.name
          }))
          console.log('Department options loaded:', departmentOptions.value)
        }
      } catch (error) {
        console.error('获取部门列表失败:', error)
        // 如果API失败，清空选项并显示错误提示
        departmentOptions.value = []
        message.error('获取部门列表失败，请刷新页面重试')
      }
    }



    // 初始化字典选项
    const initDictOptions = async () => {
      try {
        // 获取状态字典
        const statusResponse = await api.dicts.getDict('status')
        if (statusResponse.code === 200) {
          statusOptions.value = (statusResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }

        // 获取采购类型字典
        const purchaseTypeResponse = await api.dicts.getDict('purchase_type')
        if (purchaseTypeResponse.code === 200) {
          purchaseTypeOptions.value = (purchaseTypeResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }

        // 获取采购方式字典
        const procurementMethodResponse = await api.dicts.getDict('procurement_method')
        if (procurementMethodResponse.code === 200) {
          procurementMethodOptions.value = (procurementMethodResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }

        // 获取经费项目字典
        const fundProjectResponse = await api.dicts.getDict('fund_project')
        if (fundProjectResponse.code === 200) {
          fundProjectOptions.value = (fundProjectResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }

        // 获取计量单位字典
        const unitResponse = await api.dicts.getDict('unit')
        if (unitResponse.code === 200) {
          unitOptions.value = (unitResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }

        // 获取物品种类字典
        const itemCategoryResponse = await api.dicts.getDict('item_category')
        if (itemCategoryResponse.code === 200) {
          itemCategoryOptions.value = (itemCategoryResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }
      } catch (error) {
        console.error('获取字典数据失败:', error)
        message.error('获取字典数据失败，部分筛选功能可能不可用')

        // 清空所有字典选项
        statusOptions.value = []
        purchaseTypeOptions.value = []
        procurementMethodOptions.value = []
        fundProjectOptions.value = []
        unitOptions.value = []
        itemCategoryOptions.value = []
      }
    }

    // 移除toggleFilters函数，不再需要展开/折叠功能

    // 重置筛选条件
    const resetFilters = () => {
      Object.assign(filters, {
        department: undefined,
        status: undefined,
        purchase_type: undefined,
        dateRange: [],
        procurementMethod: '',
        requirementSource: '',
        fundProject: '',
        unit: '',
        itemCategory: '',
        remarks: '',
        itemName: '',
        minAmount: undefined,
        maxAmount: undefined
      })
      searchRecords()
    }

    // 字段选择相关
    const onFieldsCheckAllChange = (e) => {
      if (e.target.checked) {
        selectedFields.value = exportFieldOptions.map(option => option.value)
      } else {
        selectedFields.value = []
      }
    }

    const onFieldsChange = (checkedList) => {
      selectedFields.value = checkedList
    }

    const resetFields = () => {
      selectedFields.value = initializeSelectedFields()
    }

    // 全选导出字段
    const selectAllExportFields = () => {
      selectedFields.value = fieldOptions.map(field => field.value)
      console.log('全选字段:', selectedFields.value)
    }

    // 重置导出字段
    const resetExportFields = () => {
      selectedFields.value = initializeSelectedFields()
      console.log('重置字段:', selectedFields.value)
    }

    // 仅选择必选字段
    const selectRequiredExportFields = () => {
      const requiredFields = fieldOptions.filter(field => field.required)
      console.log('必选字段:', requiredFields)
      selectedFields.value = requiredFields.map(field => field.value)
      // 如果没有必选字段，至少选择基础字段
      if (selectedFields.value.length === 0) {
        selectedFields.value = ['id', 'item_name', 'specification', 'budget_quantity', 'status']
      }
      console.log('选择必选字段:', selectedFields.value)
    }

    // 预览导出数据
    const previewExportData = () => {
      loadPreviewData()
    }



    // 加载预览数据
    const loadPreviewData = async () => {
      if (selectedFields.value.length === 0) {
        message.warning('请至少选择一个导出字段')
        return
      }

      previewLoading.value = true
      try {
        const params = {
          page: previewPagination.value.current,
          page_size: previewPagination.value.pageSize,
          fields: selectedFields.value.join(',')
        }

        // 添加筛选条件
        if (filters.department) params.hierarchy_path__icontains = filters.department
        if (filters.status) params.status = filters.status
        if (filters.purchase_type) params.purchase_type = filters.purchase_type
        if (filters.itemName) params.item_name__icontains = filters.itemName
        if (filters.minAmount) params.budget_total_amount__gte = filters.minAmount
        if (filters.maxAmount) params.budget_total_amount__lte = filters.maxAmount

        // 多选字典字段的处理
        if (filters.procurementMethod && filters.procurementMethod.length > 0) {
          params.procurement_method__in = filters.procurementMethod.join(',')
        }
        if (filters.fundProject && filters.fundProject.length > 0) {
          params.fund_project__in = filters.fundProject.join(',')
        }
        if (filters.unit && filters.unit.length > 0) {
          params.unit__in = filters.unit.join(',')
        }
        if (filters.itemCategory && filters.itemCategory.length > 0) {
          params.item_category__in = filters.itemCategory.join(',')
        }

        // 文本字段
        if (filters.requirementSource) params.requirement_source__icontains = filters.requirementSource
        if (filters.remarks) params.remarks__icontains = filters.remarks

        if (filters.dateRange && filters.dateRange.length === 2) {
          params.created_at__gte = filters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = filters.dateRange[1].format('YYYY-MM-DD')
        }

        // 使用已选择的记录数据进行预览，直接使用后端提供的display字段
        previewData.value = selectedRecordData.value.map(record => {
          const previewRecord = {}
          selectedFields.value.forEach(field => {
            // 优先使用后端提供的display字段，如果没有则使用原值
            const displayField = field + '_display'
            previewRecord[field] = record[displayField] || record[field] || '-'
          })
          return previewRecord
        })
        previewTotal.value = previewData.value.length
      } catch (error) {
        console.error('加载预览数据失败:', error)
        message.error('加载预览数据失败')
      } finally {
        previewLoading.value = false
      }
    }

    // 使用dictMixin中的统一方法

    // 记录分页相关
    const onRecordPageSizeChange = (_, size) => {
      recordPageSize.value = size
      recordPagination.value.current = 1 // 重置到第一页
      searchRecords() // 重新搜索数据
    }

    const onRecordPageChange = (page) => {
      recordPagination.value.current = page
      searchRecords() // 重新搜索数据
    }

    // 预览分页相关
    const onPreviewPageChange = (page) => {
      previewPagination.value.current = page
      loadPreviewData()
    }

    const onPreviewPageSizeChange = (_, size) => {
      previewPagination.value.pageSize = size
      previewPagination.value.current = 1
      loadPreviewData()
    }

    // 防抖搜索
    const debouncedSearch = debounce(() => {
      recordPagination.value.current = 1 // 重置到第一页
      searchRecords()
    }, 300)

    // 搜索记录
    const searchRecords = async () => {
      searchLoading.value = true
      try {
        const params = {
          page: recordPagination.value.current,
          page_size: recordPageSize.value
        }

        // 添加筛选条件
        if (filters.department) params.hierarchy_path__icontains = filters.department
        if (filters.status) params.status = filters.status
        if (filters.purchase_type) params.purchase_type = filters.purchase_type
        if (filters.itemName) params.item_name__icontains = filters.itemName
        if (filters.minAmount) params.budget_total_amount__gte = filters.minAmount
        if (filters.maxAmount) params.budget_total_amount__lte = filters.maxAmount

        // 文本字段 - 使用模糊匹配
        if (filters.procurementMethod) params.procurement_method__icontains = filters.procurementMethod
        if (filters.fundProject) params.fund_project__icontains = filters.fundProject
        if (filters.unit) params.unit__icontains = filters.unit
        if (filters.itemCategory) params.item_category__icontains = filters.itemCategory
        if (filters.requirementSource) params.requirement_source__icontains = filters.requirementSource
        if (filters.remarks) params.remarks__icontains = filters.remarks

        if (filters.dateRange && filters.dateRange.length === 2) {
          params.created_at__gte = filters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = filters.dateRange[1].format('YYYY-MM-DD')
        }

        const response = await api.purchaseRequests.getList(params)
        if (response.code === 200) {
          const rawRecords = response.data.results || response.data || []

          // 转换字典编码为显示名称，同时保留原始编码用于筛选
          records.value = rawRecords.map(record => {
            return {
              ...record,
              // 保留原始编码用于筛选（转换为大写）
              status_code: record.status_code || record.status?.toUpperCase() || record.status,
              purchase_type_code: record.purchase_type_code || record.purchase_type?.toUpperCase() || record.purchase_type,

              // 显示字段使用display值或原始值
              status: record.status_display || record.status || '-',
              item_category: record.item_category_display || record.item_category || '-',
              unit: record.unit_display || record.unit || '-',
              procurement_method: record.procurement_method_display || record.procurement_method || '-',
              fund_project: record.fund_project_display || record.fund_project || '-',
              purchase_type: record.purchase_type_display || record.purchase_type || '-'
            }
          })

          recordPagination.value.total = response.data.count || records.value.length
        }
      } catch (error) {
        console.error('搜索记录失败:', error)
        message.error('搜索记录失败')
      } finally {
        searchLoading.value = false
      }
    }

    // 预览导出
    const previewExport = () => {
      if (selectedRecords.value.length === 0) {
        message.warning('请先选择要导出的记录')
        return
      }
      if (selectedFields.value.length === 0) {
        message.warning('请至少选择一个导出字段')
        return
      }
      showPreviewModal.value = true
      loadPreviewData()
    }

    // 重置配置
    const resetConfig = () => {
      resetFilters()
      resetFields()
      records.value = []
      selectedRecords.value = []
      selectedRecordData.value = []
      previewData.value = []
      previewTotal.value = 0
    }

    // 处理取消操作
    const handleCancel = () => {
      resetConfig()
      emit('cancel')
    }

    // 执行导出
    const executeExport = async () => {
      if (selectedRecords.value.length === 0) {
        message.warning('请先选择要导出的记录')
        return
      }

      exportLoading.value = true
      try {
        const params = {
          fields: selectedFields.value.join(','),
          export_format: 'excel',
          ids: selectedRecords.value.join(',')
        }

        // 添加筛选条件
        if (filters.department) params.hierarchy_path__icontains = filters.department
        if (filters.status) params.status = filters.status
        if (filters.purchase_type) params.purchase_type = filters.purchase_type
        if (filters.dateRange && filters.dateRange.length === 2) {
          params.created_at__gte = filters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = filters.dateRange[1].format('YYYY-MM-DD')
        }

        const response = await api.purchaseRequests.exportToExcel(params)

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.download = `采购总览_${formatDateToYMD(new Date())}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        message.success(`导出成功，共导出 ${selectedRecords.value.length} 条记录`)
        emit('export-complete', selectedRecordData.value)
        emit('cancel')
      } catch (error) {
        console.error('导出失败:', error)
        message.error(`导出失败: ${error.message}`)
      } finally {
        exportLoading.value = false
      }
    }

    // 原有的导出函数（保持兼容性）
    const exportData = async () => {
      if (selectedFields.value.length === 0) {
        message.warning('请至少选择一个导出字段')
        return
      }

      exportLoading.value = true
      try {
        const params = {
          fields: selectedFields.value.join(','),
          export_format: 'excel'
        }

        // 添加筛选条件
        if (filters.department) params.hierarchy_path__icontains = filters.department
        if (filters.status) params.status = filters.status
        if (filters.purchase_type) params.purchase_type = filters.purchase_type
        if (filters.itemName) params.item_name__icontains = filters.itemName
        if (filters.minAmount) params.budget_total_amount__gte = filters.minAmount
        if (filters.maxAmount) params.budget_total_amount__lte = filters.maxAmount
        if (filters.procurementMethod) params.procurement_method__icontains = filters.procurementMethod
        if (filters.requirementSource) params.requirement_source__icontains = filters.requirementSource
        if (filters.fundProject) params.fund_project__icontains = filters.fundProject
        if (filters.unit) params.unit__icontains = filters.unit
        if (filters.itemCategory) params.item_category__icontains = filters.itemCategory
        if (filters.remarks) params.remarks__icontains = filters.remarks

        if (filters.dateRange && filters.dateRange.length === 2) {
          params.created_at__gte = filters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = filters.dateRange[1].format('YYYY-MM-DD')
        }

        const response = await api.purchaseRequests.exportToExcel(params)

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.download = `采购总览_${formatDateToYMD(new Date())}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        message.success('导出成功')
        emit('export-complete')
        emit('cancel')
      } catch (error) {
        console.error('导出失败:', error)
        message.error('导出失败')
      } finally {
        exportLoading.value = false
      }
    }

    // 监听visible变化
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        initDepartmentOptions()
        initDictOptions()
        resetConfig()
        searchRecords()
      }
    })

    // 组件挂载时初始化
    onMounted(() => {
      if (props.visible) {
        initDepartmentOptions()
        initDictOptions()
        searchRecords()
      }
    })

    return {
      // 基础数据
      departmentOptions,
      statusOptions,
      purchaseTypeOptions,
      procurementMethodOptions,
      fundProjectOptions,
      unitOptions,
      itemCategoryOptions,
      exportLoading,
      previewLoading,
      previewData,
      previewTotal,
      showPreviewModal,
      // 移除showFilters
      previewPagination,

      // 记录选择相关
      records,
      selectedRecords,
      selectedRecordData,
      searchLoading,
      recordPageSize,
      recordPagination,
      recordColumns,
      recordRowSelection,

      // 字段配置
      fieldCategories,
      selectedFields,
      fieldsIndeterminate,
      fieldsCheckAll,
      previewColumns,
      fieldsCollapseKey,
      fieldOptions,

      // 筛选条件
      filters,
      filterOption,

      // 状态转换函数
      getStatusColor,
      formatDateToYMD,

      // 操作函数
      searchRecords,
      debouncedSearch,
      resetFilters,
      // 移除toggleFilters
      onFieldsCheckAllChange,
      onFieldsChange,
      resetFields,
      loadPreviewData,
      resetConfig,
      executeExport,
      selectAllExportFields,
      resetExportFields,
      selectRequiredExportFields,
      previewExportData,
      exportData,
      previewExport,
      handleCancel,
      onRecordPageSizeChange,
      onRecordPageChange,
      onPreviewPageChange,
      onPreviewPageSizeChange
    }
  }
}
</script>

<style scoped>
.export-config-container {
  max-height: 70vh;
  overflow-y: auto;
}

.filter-section,
.fields-section,
.preview-section,
.actions-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-section h4,
.fields-section h4,
.preview-section h4 {
  margin-bottom: 16px;
  color: #1890ff;
  font-weight: 600;
}

.fields-controls {
  margin-bottom: 16px;
}

/* 导出字段选择样式 - 与字段筛选保持一致 */
.export-field-category {
  margin-bottom: 12px;
}

.export-category-title {
  margin: 0 0 6px 0;
  font-weight: 600;
  font-size: 13px;
  color: #666;
}

.export-category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.export-field-option {
  display: flex;
  align-items: center;
}

.actions-section {
  border-bottom: none;
  text-align: right;
  padding: 16px 0 0 0;
  /* border-top: 1px solid #f0f0f0; */
}

/* 新增样式 */
.export-config-container {
  padding: 0;
}

.filter-section {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  padding-bottom: 0;
  border-bottom: 0px solid #1890ff;
}

/* 筛选头部flex布局 */
.filter-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0px;
}

.filter-header-flex h4 {
  margin: 0;

}

/* 导出筛选区域样式 */
.detailed-filters-export {
  padding: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 6px;
  margin-top: 12px;
  border: 1px solid #e5e7eb;
}

.detailed-filters-export .filter-row {
  margin-bottom: 6px;
}

.detailed-filters-export .filter-row:last-child {
  margin-bottom: 0;
}

.detailed-filters-export .filter-select,
.detailed-filters-export .filter-input {
  width: 100%;
  height: 32px;
}

/* 多选下拉框特殊样式 */
.detailed-filters-export .multiple-select {
  min-height: 32px;
  height: auto;
}

.detailed-filters-export .multiple-select .ant-select-selector {
  min-height: 32px;
  height: auto;
  padding: 2px 4px;
}

.detailed-filters-export .multiple-select .ant-select-selection-item {
  margin: 2px 4px 2px 0;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  border-radius: 4px;
  background: #f0f2f5;
  border: 1px solid #d9d9d9;
}

.detailed-filters-export .filter-select .ant-select-selector,
.detailed-filters-export .filter-input {
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.detailed-filters-export .filter-select .ant-select-selector:hover,
.detailed-filters-export .filter-input:hover {
  border-color: #3b82f6;
}

.detailed-filters-export .filter-select .ant-select-focused .ant-select-selector,
.detailed-filters-export .filter-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 带标签的筛选项样式 */
.filter-item-with-label {
  display: flex;
  align-items: center;
  width: 100%;
}

.filter-item-with-label .filter-label {
  white-space: nowrap;
  margin-right: 8px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 60px;
}

.filter-item-with-label .filter-date-picker,
.filter-item-with-label .budget-range-input,
.filter-item-with-label .filter-select,
.filter-item-with-label .filter-input {
  flex: 1;
  min-width: 0;
}

.budget-range-input {
  display: flex;
  width: 100%;
}

.budget-range-input .ant-input-number {
  border-radius: 4px 0 0 4px;
}

.budget-range-input .ant-input-number:last-child {
  border-radius: 0 4px 4px 0;
  border-left: 0;
}

.budget-range-input .ant-input-number:focus {
  z-index: 1;
}

.fields-section {
  margin-bottom: 16px;
}

/* 导出字段配置样式 - 与验收页面保持一致 */
.export-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
}

.export-field-category {
  flex: 1;
  min-width: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  overflow: hidden;
}

.export-category-header {
  background-color: #e6f7ff;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 12px;
}

.export-category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.export-category-fields {
  padding: 12px;
}

.export-field-option {
  margin-bottom: 8px;
}

.export-field-option:last-child {
  margin-bottom: 0;
}

.field-title {
  margin-right: 8px;
}

/* 记录选择部分样式 */
.records-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-count {
  font-size: 13px;
  color: #666;
}

/* 预览模态框样式 */
:deep(.preview-modal-wrapper .ant-modal) {
  height: 80vh;
  top: 10vh;
  margin: 0;
}

:deep(.preview-modal-wrapper .ant-modal-content) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.preview-modal-wrapper .ant-modal-body) {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.preview-table-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-table-container .ant-table-wrapper {
  flex: 1;
  overflow: hidden;
}

.preview-table-container .ant-table {
  height: 100%;
}

.preview-table-container .ant-table-container {
  height: calc(100% - 64px);
  /* 减去分页器高度 */
}

.preview-table-container .ant-table-body {
  overflow-y: auto !important;
  max-height: calc(80vh - 200px);
}

:deep(.preview-container .ant-table-wrapper) {
  flex: 1;
  overflow: hidden;
}

:deep(.preview-container .ant-table-tbody) {
  overflow-y: auto;
}
</style>
