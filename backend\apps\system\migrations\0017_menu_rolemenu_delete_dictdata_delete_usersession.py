# Generated by Django 5.2.4 on 2025-07-24 02:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("roles", "0002_alter_role_permissions"),
        ("system", "0016_remove_usersession"),
    ]

    operations = [
        migrations.CreateModel(
            name="Menu",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("menu_name", models.Char<PERSON>ield(max_length=50, verbose_name="菜单名称")),
                (
                    "menu_code",
                    models.Char<PERSON>ield(
                        max_length=50, unique=True, verbose_name="菜单编码"
                    ),
                ),
                (
                    "menu_type",
                    models.CharField(
                        choices=[("menu", "菜单"), ("button", "按钮")],
                        default="menu",
                        max_length=10,
                        verbose_name="菜单类型",
                    ),
                ),
                (
                    "route_path",
                    models.Char<PERSON>ield(
                        blank=True, max_length=200, null=True, verbose_name="路由路径"
                    ),
                ),
                (
                    "component_path",
                    models.CharField(
                        blank=True, max_length=200, null=True, verbose_name="组件路径"
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="菜单图标"
                    ),
                ),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                (
                    "is_visible",
                    models.BooleanField(default=True, verbose_name="是否显示"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "permission_code",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="权限标识"
                    ),
                ),
                (
                    "business_status",
                    models.CharField(
                        blank=True,
                        help_text="采购流程专用，如：draft,pending_approval",
                        max_length=50,
                        null=True,
                        verbose_name="关联的业务状态",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="system.menu",
                        verbose_name="父菜单",
                    ),
                ),
            ],
            options={
                "verbose_name": "系统菜单",
                "verbose_name_plural": "系统菜单",
                "db_table": "sys_menu",
                "ordering": ["sort_order", "id"],
            },
        ),
        migrations.CreateModel(
            name="RoleMenu",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "menu",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="system.menu",
                        verbose_name="菜单",
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="roles.role",
                        verbose_name="角色",
                    ),
                ),
            ],
            options={
                "verbose_name": "角色菜单关联",
                "verbose_name_plural": "角色菜单关联",
                "db_table": "sys_role_menu",
                "unique_together": {("role", "menu")},
            },
        ),
        migrations.DeleteModel(
            name="DictData",
        ),
        migrations.DeleteModel(
            name="UserSession",
        ),
    ]
