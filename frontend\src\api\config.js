/**
 * API配置文件
 * 包含请求配置、拦截器等
 */
import axios from 'axios'
import { message } from 'ant-design-vue'
import router from '@/router'

// API基础配置
export const API_CONFIG = {
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
}

// 创建axios实例
export const apiClient = axios.create(API_CONFIG)

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    // 请求拦截器错误：静默处理
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 统一处理响应数据
    const { data } = response
    
    // 如果是文件下载，直接返回response
    if (response.headers['content-type']?.includes('application/octet-stream') ||
        response.headers['content-type']?.includes('application/vnd.ms-excel') ||
        response.headers['content-type']?.includes('application/vnd.openxmlformats')) {
      return response
    }
    
    // 正常的JSON响应
    if (data.code === 200) {
      return data
    } else {
      // 业务错误
      const errorMessage = data.message || '请求失败'
      message.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    }
  },
  (error) => {
    // HTTP错误处理
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token')
          router.push('/login')
          message.error('登录已过期，请重新登录')
          break
        case 403:
          message.error('没有权限访问该资源')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error('服务器内部错误')
          break
        default: {
          const errorMessage = data?.message || error.message || '网络错误'
          message.error(errorMessage)
          break
        }
      }
    } else if (error.request) {
      // 网络错误
      message.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      message.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// URL构建工具函数
export function buildUrlWithParams(url, params = {}) {
  if (!params || Object.keys(params).length === 0) {
    return url
  }
  
  const searchParams = new URLSearchParams()
  
  Object.keys(params).forEach(key => {
    const value = params[key]
    if (value !== null && value !== undefined && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, item))
      } else {
        searchParams.append(key, value)
      }
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `${url}?${queryString}` : url
}

// 文件上传配置
export const UPLOAD_CONFIG = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  headers: {
    'Content-Type': 'multipart/form-data'
  }
}

// 导出配置
export default {
  API_CONFIG,
  apiClient,
  buildUrlWithParams,
  UPLOAD_CONFIG
}
