/**
 * 统一筛选样式组件
 * 为所有页面提供一致的筛选区域样式
 */

/* ==================== 筛选区域容器样式 ==================== */

/* 全宽筛选区域 - 标准样式（与采购总览页面完全一致） */
.unified-filter-container,
.detailed-filters-fullwidth {
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid var(--border-light, #e5e7eb);
  /* border-radius: 0; */
  margin-bottom: 0;
}

/* 筛选行容器（与采购总览页面完全一致） */
.unified-filter-row,
.filter-row {
  margin-bottom: 16px;
}

.unified-filter-row:last-child,
.filter-row:last-child {
  margin-bottom: 0;
}

/* ==================== 筛选控件样式 ==================== */

/* 统一的筛选下拉选择器样式（与采购总览页面完全一致） */
.unified-filter-select,
.filter-select {
  width: 100%;
  height: 36px;
  font-size: 14px;
}

.unified-filter-select .ant-select-selector,
.filter-select .ant-select-selector {
  border: 1px solid var(--border-light, #e5e7eb);
  border-radius: 6px;
  transition: all 0.3s ease;
  height: 36px;
  padding: 0 11px;
}

.unified-filter-select .ant-select-selector:hover,
.filter-select .ant-select-selector:hover {
  border-color: var(--primary-light, #60a5fa);
}

.unified-filter-select.ant-select-focused .ant-select-selector,
.filter-select.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 统一的筛选输入框样式（与采购总览页面完全一致） */
.unified-filter-input,
.filter-input {
  width: 100%;
  height: 36px;
  font-size: 14px;
  border: 1px solid var(--border-light, #e5e7eb);
  border-radius: 6px;
  transition: all 0.3s ease;
  padding: 0 11px;
}

.unified-filter-input:hover,
.filter-input:hover {
  border-color: var(--primary-light, #60a5fa);
}

.unified-filter-input:focus,
.filter-input:focus {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* 统一的筛选日期选择器样式（与采购总览页面完全一致） */
.unified-filter-date-picker,
.filter-date-picker {
  width: 100%;
  height: 36px;
  font-size: 14px;
}

.unified-filter-date-picker .ant-picker,
.filter-date-picker .ant-picker {
  border: 1px solid var(--border-light, #e5e7eb);
  border-radius: 6px;
  transition: all 0.3s ease;
  height: 36px;
  width: 100%;
}

.unified-filter-date-picker .ant-picker:hover,
.filter-date-picker .ant-picker:hover {
  border-color: var(--primary-light, #60a5fa);
}

.unified-filter-date-picker .ant-picker.ant-picker-focused,
.filter-date-picker .ant-picker.ant-picker-focused {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 统一的日期选择器样式 */
.unified-filter-date-picker {
  width: 100%;
  height: 36px;
}

.unified-filter-date-picker .ant-picker {
  border: 1px solid var(--border-light, #e5e7eb);
  border-radius: 6px;
  transition: all 0.3s ease;
  height: 36px;
}

.unified-filter-date-picker .ant-picker:hover {
  border-color: var(--primary-light, #60a5fa);
}

.unified-filter-date-picker .ant-picker:focus {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* ==================== 响应式布局 ==================== */

/* 标准响应式断点 */
.unified-filter-col-xs { flex: 0 0 50%; max-width: 50%; }
.unified-filter-col-sm { flex: 0 0 33.333333%; max-width: 33.333333%; }
.unified-filter-col-md { flex: 0 0 25%; max-width: 25%; }
.unified-filter-col-lg { flex: 0 0 20%; max-width: 20%; }
.unified-filter-col-xl { flex: 0 0 12.5%; max-width: 12.5%; }

/* ==================== 带标签的筛选项样式 ==================== */

.unified-filter-item-with-label {
  display: flex;
  align-items: center;
  width: 100%;
}

.unified-filter-item-with-label .filter-label {
  white-space: nowrap;
  margin-right: 8px;
  font-size: 14px;
  color: var(--text-secondary, #6b7280);
  min-width: 60px;
  text-align: right;
}

.unified-filter-item-with-label .filter-control {
  flex: 1;
}

/* ==================== 筛选按钮样式 ==================== */

.unified-filter-action-btn {
  height: 36px;
  line-height: 34px;
  padding: 0 16px;
  border: 1px solid var(--border-light, #e5e7eb);
  border-radius: 6px;
  background: white;
  color: var(--text-primary, #374151);
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.unified-filter-action-btn:hover {
  border-color: var(--primary-light, #60a5fa);
  color: var(--primary-color, #3b82f6);
}

.unified-filter-action-btn:active {
  transform: translateY(1px);
}

.unified-filter-action-btn.primary {
  background: var(--primary-color, #3b82f6);
  border-color: var(--primary-color, #3b82f6);
  color: white;
}

.unified-filter-action-btn.primary:hover {
  background: var(--primary-dark, #2563eb);
  border-color: var(--primary-dark, #2563eb);
}

/* 移除移动端适配，保持固定布局 */

@media (max-width: 576px) {
  .unified-filter-container {
    padding: 12px;
  }
  
  .unified-filter-col-xs { flex: 0 0 100%; max-width: 100%; }
  
  .unified-filter-item-with-label {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .unified-filter-item-with-label .filter-label {
    margin-right: 0;
    margin-bottom: 4px;
    min-width: auto;
    text-align: left;
  }
  
  .unified-filter-item-with-label .filter-control {
    width: 100%;
  }
}

/* ==================== 特殊状态样式 ==================== */

/* 筛选项禁用状态 */
.unified-filter-select.disabled,
.unified-filter-input:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.unified-filter-select.disabled .ant-select-selector {
  background-color: #f9fafb;
  color: #9ca3af;
}

/* 筛选项错误状态 */
.unified-filter-select.error .ant-select-selector,
.unified-filter-input.error {
  border-color: #ef4444;
}

.unified-filter-select.error.ant-select-focused .ant-select-selector,
.unified-filter-input.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

/* ==================== 动画效果 ==================== */

.unified-filter-container {
  animation: filterSlideIn 0.3s ease-out;
}

@keyframes filterSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 筛选控件聚焦动画 */
.unified-filter-select .ant-select-selector,
.unified-filter-input,
.unified-filter-date-picker .ant-picker {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ==================== CSS变量定义 ==================== */

:root {
  --filter-bg-primary: #f8fafc;
  --filter-bg-secondary: #f1f5f9;
  --filter-border-light: #e5e7eb;
  --filter-border-focus: #3b82f6;
  --filter-border-hover: #60a5fa;
  --filter-text-primary: #374151;
  --filter-text-secondary: #6b7280;
  --filter-shadow-focus: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --filter-bg-primary: #1f2937;
    --filter-bg-secondary: #374151;
    --filter-border-light: #4b5563;
    --filter-text-primary: #f9fafb;
    --filter-text-secondary: #d1d5db;
  }
  
  .unified-filter-container {
    background: linear-gradient(135deg, var(--filter-bg-primary) 0%, var(--filter-bg-secondary) 100%);
  }
  
  .unified-filter-select .ant-select-selector,
  .unified-filter-input,
  .unified-filter-date-picker .ant-picker {
    background-color: var(--filter-bg-secondary);
    border-color: var(--filter-border-light);
    color: var(--filter-text-primary);
  }
}
