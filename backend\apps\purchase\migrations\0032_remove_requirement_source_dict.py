# Generated by Django 5.2.4 on 2025-07-17 13:04

from django.db import migrations


def remove_requirement_source_dict(apps, schema_editor):
    """删除requirement_source类型的字典数据"""
    Dictionary = apps.get_model('purchase', 'Dictionary')

    # 删除requirement_source类型的字典记录
    deleted_count = Dictionary.objects.filter(type='requirement_source').delete()[0]
    print(f"删除了 {deleted_count} 条requirement_source类型的字典记录")


def reverse_remove_requirement_source_dict(apps, schema_editor):
    """回滚操作 - 重新创建requirement_source类型的字典数据"""
    Dictionary = apps.get_model('purchase', 'Dictionary')

    # 重新创建基本的requirement_source字典数据
    requirement_sources = [
        {'code': 'teaching', 'name': '教学需求', 'order': 1},
        {'code': 'research', 'name': '科研需求', 'order': 2},
        {'code': 'administration', 'name': '行政需求', 'order': 3},
        {'code': 'infrastructure', 'name': '基础设施', 'order': 4},
        {'code': 'maintenance', 'name': '维护需求', 'order': 5},
        {'code': 'emergency', 'name': '应急需求', 'order': 6},
    ]

    for item in requirement_sources:
        Dictionary.objects.create(
            type='requirement_source',
            type_display='需求来源',
            code=item['code'],
            name=item['name'],
            order=item['order'],
            status=True
        )


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0031_convert_purchase_request_dict_fields"),
    ]

    operations = [
        migrations.RunPython(
            remove_requirement_source_dict,
            reverse_remove_requirement_source_dict
        ),
    ]
