<template>
  <div v-if="hasAuth">
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { hasPermission, hasRole } from '@/utils/permission';

const props = defineProps({
  // 权限标识
  permission: {
    type: String,
    default: null
  },
  // 角色标识
  role: {
    type: String,
    default: null
  }
});

// 计算是否有权限
const hasAuth = computed(() => {
  // 如果没有配置权限和角色要求，默认有权限
  if (!props.permission && !props.role) {
    return true;
  }
  
  let permissionAuth = true;
  let roleAuth = true;
  
  // 检查权限
  if (props.permission) {
    permissionAuth = hasPermission(props.permission);
  }
  
  // 检查角色
  if (props.role) {
    roleAuth = hasRole(props.role);
  }
  
  // 权限和角色都需要满足
  return permissionAuth && roleAuth;
});
</script>

<style scoped>
/* 权限检查组件样式 */
</style>
