// 创建LineChart组件
export default {
  name: 'LineChart',
  props: {
    data: {
      type: Object,
      required: true
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const chartRef = ref(null);
    let chartInstance = null;
    
    // 创建图表
    const createChart = () => {
      if (!chartRef.value) return;
      
      // 销毁现有图表
      if (chartInstance) {
        chartInstance.destroy();
      }
      
      // 创建新图表
      chartInstance = new Chart(chartRef.value, {
        type: 'line',
        data: props.data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          ...props.options
        }
      });
    };
    
    // 监听数据变化
    watchEffect(() => {
      if (chartInstance) {
        chartInstance.data = props.data;
        chartInstance.options = {
          responsive: true,
          maintainAspectRatio: false,
          ...props.options
        };
        chartInstance.update();
      } else {
        createChart();
      }
    });
    
    // 页面卸载时销毁图表
    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.destroy();
      }
    });
    
    return { chartRef };
  }
};