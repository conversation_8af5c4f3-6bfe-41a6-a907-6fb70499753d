# Generated by Django 5.2.3 on 2025-06-26 13:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0019_remove_purchaserequest_idx_purchase_approved_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='purchaserequest',
            options={'ordering': ['-created_at'], 'verbose_name': '采购需求', 'verbose_name_plural': '采购需求管理'},
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_item_dept',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_purchased',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_accepted',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_reimbursed',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_status_dept',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_status_time',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_path_status',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_price',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_avg_price',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_order_no',
        ),
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_approved',
        ),
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='acceptance_remark',
            new_name='acceptance_remarks',
        ),
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='financial_serial_no',
            new_name='financial_serial',
        ),
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='purchase_remark',
            new_name='purchase_remarks',
        ),
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='quantity_difference',
            new_name='quantity_variance',
        ),
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='attachment',
            new_name='requirement_attachment',
        ),
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='supplier',
            new_name='supplier_name',
        ),
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='reimbursement_voucher_no',
            new_name='voucher_number',
        ),
        migrations.RenameIndex(
            model_name='purchaserequest',
            new_name='idx_status',
            old_name='idx_purchase_status',
        ),
        migrations.RenameIndex(
            model_name='purchaserequest',
            new_name='idx_dept_id',
            old_name='idx_purchase_dept',
        ),
        migrations.RenameIndex(
            model_name='purchaserequest',
            new_name='idx_requester_id',
            old_name='idx_purchase_requester',
        ),
        migrations.RenameIndex(
            model_name='purchaserequest',
            new_name='idx_created_at',
            old_name='idx_purchase_created',
        ),
        migrations.RenameIndex(
            model_name='purchaserequest',
            new_name='idx_submission_date',
            old_name='idx_purchase_submitted',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='acceptance_quantity',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='difference_rate',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='exception_approval_date',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='fund_project_name',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='internal_approval',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='purchase_amount',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='purchase_method',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='reimbursement_generated_date',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='reimbursement_remarks',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='settlement_exception_reason',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='settlement_remark',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='spec',
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='accepted_quantity',
            field=models.PositiveIntegerField(default=0, help_text='实际验收确认的数量', verbose_name='验收确认数量'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='actual_total_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='实际单价 × 实际数量', max_digits=15, null=True, verbose_name='实际采购总金额'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='exception_approved_at',
            field=models.DateTimeField(blank=True, help_text='异常审批完成时间', null=True, verbose_name='异常审批时间'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='fund_project',
            field=models.CharField(default='未指定', help_text='资金来源项目名称', max_length=100, verbose_name='经费项目'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='settlement_remarks',
            field=models.TextField(blank=True, help_text='结算相关的补充说明', verbose_name='结算备注'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='specification',
            field=models.CharField(blank=True, default='', help_text='物品的详细规格', max_length=200, verbose_name='规格型号'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='预算单价 × 需求数量', max_digits=15, verbose_name='预算总金额'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='variance_rate',
            field=models.DecimalField(decimal_places=2, default=0, help_text='数量差异的百分比', max_digits=5, verbose_name='差异率百分比'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='acceptance_date',
            field=models.DateTimeField(blank=True, help_text='验收完成的时间', null=True, verbose_name='验收完成时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='approval_attachment',
            field=models.FileField(blank=True, help_text='审批相关的附件文档', null=True, upload_to='approvals/%Y/%m/', verbose_name='审批附件'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='courier_receipt_photo',
            field=models.ImageField(blank=True, help_text='快递单据照片', null=True, upload_to='receipts/%Y/%m/', verbose_name='快递单照片'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='dept_id',
            field=models.PositiveIntegerField(help_text='申请部门的ID', verbose_name='申请部门ID'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='exception_approved',
            field=models.BooleanField(default=False, help_text='异常是否已经审批', verbose_name='异常已审批'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='exception_approver',
            field=models.ForeignKey(blank=True, help_text='处理异常的审批人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='exception_approved_requests', to=settings.AUTH_USER_MODEL, verbose_name='异常审批人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='hierarchy_path',
            field=models.CharField(help_text='例：分公司-办事处-科室', max_length=200, verbose_name='部门层级路径'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_max_price',
            field=models.DecimalField(decimal_places=2, default=0, help_text='历史最高单价', max_digits=12, verbose_name='历史最高单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_min_price',
            field=models.DecimalField(decimal_places=2, default=0, help_text='历史最低单价', max_digits=12, verbose_name='历史最低单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='item_category',
            field=models.CharField(help_text='物品分类', max_length=50, verbose_name='物品种类'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='item_name',
            field=models.CharField(help_text='具体物品名称', max_length=100, verbose_name='物品名称'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='item_photo',
            field=models.ImageField(blank=True, help_text='验收物品照片', null=True, upload_to='items/%Y/%m/', verbose_name='物品照片'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='order_number',
            field=models.CharField(blank=True, help_text='审批通过后自动生成', max_length=50, unique=True, verbose_name='采购订单号'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='procurement_method',
            field=models.CharField(help_text='如：公开招标、询价采购、直接采购等', max_length=50, verbose_name='采购方式'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='purchase_date',
            field=models.DateTimeField(blank=True, help_text='采购完成的时间', null=True, verbose_name='采购完成时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='purchase_type',
            field=models.CharField(choices=[('unified', '统一采购'), ('self', '自行采购')], default='unified', help_text='统一采购或自行采购', max_length=20, verbose_name='采购类型'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='quantity',
            field=models.PositiveIntegerField(help_text='申请采购的数量', verbose_name='需求数量'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='reimbursement_date',
            field=models.DateTimeField(blank=True, help_text='结算完成的时间', null=True, verbose_name='结算完成时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='requester',
            field=models.ForeignKey(help_text='需求提报人', on_delete=django.db.models.deletion.RESTRICT, related_name='purchase_requests', to=settings.AUTH_USER_MODEL, verbose_name='申请人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='return_date',
            field=models.DateTimeField(blank=True, help_text='退回操作的时间', null=True, verbose_name='退回时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='shipping_date',
            field=models.DateTimeField(blank=True, help_text='供应商发货时间', null=True, verbose_name='发货时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='status',
            field=models.CharField(choices=[('draft', '草稿'), ('pending_approval', '待审批'), ('approved', '已审批'), ('rejected', '已驳回'), ('pending_purchase', '待采购'), ('purchased', '已采购'), ('returned', '已退回'), ('pending_acceptance', '待验收'), ('accepted', '已验收'), ('pending_reimbursement', '待结算'), ('settled', '已结算')], default='draft', help_text='当前业务状态', max_length=30, verbose_name='当前状态'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='unit',
            field=models.CharField(help_text='如：个、台、套、箱等', max_length=20, verbose_name='计量单位'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='unit_price',
            field=models.DecimalField(decimal_places=2, help_text='预估的单价（元）', max_digits=12, verbose_name='预算单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='最后更新时间'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['item_name'], name='idx_item_name'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['dept_id', 'status'], name='idx_dept_status'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['requester', 'status'], name='idx_requester_status'),
        ),
        migrations.AddConstraint(
            model_name='purchaserequest',
            constraint=models.UniqueConstraint(condition=models.Q(('order_number__isnull', False), models.Q(('order_number', ''), _negated=True)), fields=('order_number',), name='unique_order_number'),
        ),
    ]
