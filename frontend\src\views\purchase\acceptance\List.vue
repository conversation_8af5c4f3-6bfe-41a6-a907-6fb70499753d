<template>
  <div>
    <div class="page-container">
      <!-- 页面标题区域 -->
      <div class="page-header business-card">
        <div class="header-content">
          <h1 class="page-title">
            <CheckCircleOutlined />
            物资验收
          </h1>
          <p class="page-subtitle">验收已采购物资，确保采购物品质量和数量符合要求</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-number">{{ totalCount }}</span>
            <span class="stat-label">验收总数</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ pendingCount }}</span>
            <span class="stat-label">待验收</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ acceptedCount }}</span>
            <span class="stat-label">已验收</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ departmentCount }}</span>
            <span class="stat-label">涉及部门</span>
          </div>
        </div>
      </div>

      <!-- 同一个面板内包括上方按钮区、下方表格区 -->
      <div class="department-tables business-card">


        <!-- 表格区域 -->
        <div class="table-section">
          <!-- 表格标题和按钮区域 - 使用flex布局，两端对齐 -->
          <div class="table-header-flex">
            <div class="table-title-section">
              <div class="table-title">
                <span>物资验收列表</span>
                <span class="table-subtitle">共 {{ totalCount }} 条记录</span>
              </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="table-actions">
              <a-space :size="8">
                <!-- 主要业务操作按钮 -->
                <PermissionButton v-if="selectedRowKeys.length > 0 && hasAcceptedRequests"
                  permission="purchase:acceptance:generate_reimbursement" type="primary"
                  @click="batchGenerateReimbursement" :disabled="selectedRowKeys.length === 0" size="small"
                  class="compact-action-btn" style="background: #52c41a; border-color: #52c41a;">
                  <DollarOutlined />
                  生成报销单 ({{ selectedRowKeys.length }})
                </PermissionButton>

                <!-- 分隔线 -->
                <a-divider v-if="selectedRowKeys.length > 0 && hasAcceptedRequests" type="vertical" />

                <!-- 筛选和视图操作按钮 -->
                <a-button @click="toggleFilters" size="small" class="compact-action-btn">
                  <template #icon>
                    <component :is="showFilters ? 'UpOutlined' : 'DownOutlined'" />
                  </template>
                  {{ showFilters ? '收起筛选' : '展开筛选' }}
                </a-button>
                <a-button @click="resetFilters" size="small" class="compact-action-btn">重置筛选</a-button>
                <PermissionButton permission="purchase:acceptance:export" @click="showExportDialog" size="small" class="compact-action-btn">
                  <DownloadOutlined />
                  导出
                </PermissionButton>
                <a-button @click="showPrintModal = true" size="small" class="compact-action-btn" type="primary">
                  <PrinterOutlined />
                  打印
                </a-button>
                <a-button size="small" class="compact-action-btn" @click="showColumnFilter = true">
                  <template #icon>
                    <SettingOutlined />
                  </template>
                  字段筛选
                  <a-badge :count="selectedColumns.length"
                    :number-style="{ backgroundColor: '#52c41a', fontSize: '10px' }" />
                </a-button>

                <!-- 字段筛选模态框 -->
                <a-modal v-model:open="showColumnFilter" title="字段筛选配置" :footer="null" :width="'auto'" :centered="true"
                  :mask-closable="true" :destroy-on-close="true" wrap-class-name="column-filter-modal">
                  <template #closeIcon>
                    <CloseOutlined />
                  </template>
                  <div class="column-filter-panel">
                    <div class="preset-section">
                      <div class="preset-controls">
                        <a-dropdown v-model:open="presetDropdownOpen" :trigger="['click']" placement="bottomRight"
                          :overlay-style="{ zIndex: 9999 }" @click.stop>
                          <a-button class="preset-trigger" @click.stop>
                            <template #icon>
                              <SettingOutlined />
                            </template>
                            预设配置
                            <DownOutlined />
                          </a-button>
                          <template #overlay>
                            <a-menu @click="handlePresetClick" @click.stop>
                              <template v-for="item in presetMenuItems" :key="item.key || 'divider'">
                                <a-menu-divider v-if="item.type === 'divider'" />
                                <a-menu-item v-else :key="item.key">{{ item.title }}</a-menu-item>
                              </template>
                            </a-menu>
                          </template>
                        </a-dropdown>
                        <a-button type="primary" size="small" @click="handleSelectAll"
                          style="margin-left: 12px;">全选</a-button>
                        <a-button size="small" @click="handleReset" style="margin-left: 8px;">重置</a-button>
                      </div>
                    </div>

                    <div class="filter-tip">
                      <span>已选择 {{ selectedColumns.length }} / {{ columnOptions.length }} 个字段</span>
                    </div>

                    <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnChange">
                      <!-- 动态字段分类 - 使用flex横向布局 -->
                      <div class="field-categories-container">
                        <div v-for="category in fieldCategories" :key="category.key" class="field-category-section">
                          <h5 class="category-title">{{ category.title }}</h5>
                          <div class="category-fields">
                            <div v-for="option in columnOptions.filter(opt => opt.category === category.key)"
                              :key="option.key" class="column-option">
                              <a-checkbox :value="option.key" :disabled="option.required" @click.stop>
                                <span class="column-title">{{ option.title }}</span>
                                <a-tag v-if="option.required" size="small" color="blue">必选</a-tag>
                              </a-checkbox>
                            </div>
                          </div>
                        </div>
                      </div>
                    </a-checkbox-group>
                  </div>
                </a-modal>
              </a-space>
            </div>
          </div>

          <!-- 详细筛选控件区域 - 分两行布局，与页面同宽 -->
          <div v-show="showFilters" class="detailed-filters-fullwidth">
            <!-- 第一行：基础筛选项 -->
            <div class="filter-row">
              <a-row :gutter="[16, 12]" align="middle">
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.status" placeholder="状态" allowClear @change="handleFilterChange"
                    class="filter-select">
                    <a-select-option value="">全部状态</a-select-option>
                    <a-select-option :value="STATUS.PENDING_ACCEPTANCE">待验收</a-select-option>
                    <a-select-option :value="STATUS.ACCEPTED">已验收</a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.purchaseType" placeholder="采购类型" allowClear
                    @change="handleFilterChange" class="filter-select">
                    <a-select-option value="">全部类型</a-select-option>
                    <a-select-option v-for="type in purchaseTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.department" placeholder="需求单位" allowClear
                    @change="handleFilterChange" class="filter-select" show-search :filter-option="filterOption">
                    <a-select-option value="">全部需求单位</a-select-option>
                    <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                      {{ dept.dept_name }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.acceptor" placeholder="验收人" allowClear @change="handleFilterChange"
                    class="filter-select" show-search :filter-option="filterOption">
                    <a-select-option value="">全部验收人</a-select-option>
                    <a-select-option v-for="user in acceptors" :key="user.id" :value="user.id">
                      {{ user.username }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.minSettlementAmount" placeholder="最小金额" @input="debouncedFilterChange"
                    class="filter-input" type="number" :min="0">
                    <template #prefix>
                      ¥
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.maxSettlementAmount" placeholder="最大金额" @input="debouncedFilterChange"
                    class="filter-input" type="number" :min="0">
                    <template #prefix>
                      ¥
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-range-picker v-model:value="filters.acceptanceTimeRange" :placeholder="['开始时间', '结束时间']"
                    @change="handleFilterChange" class="filter-date-picker" />
                </a-col>
              </a-row>
            </div>

            <!-- 第二行：搜索和其他筛选项 -->
            <div class="filter-row">
              <a-row :gutter="[16, 12]" align="middle">
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.id" placeholder="ID" @input="debouncedFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.itemName" placeholder="搜索物品名称" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.specification" placeholder="搜索规格型号" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.procurementMethod" placeholder="采购方式" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.requirementSource" placeholder="需求来源" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.fundProject" placeholder="经费项目" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.unit" placeholder="计量单位" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.itemCategory" placeholder="物品种类" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.remarks" placeholder="需求备注" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>

        <div class="table-container">
          <a-table :columns="filteredColumns" :data-source="acceptances" :row-key="record => record.id"
            :row-selection="rowSelection" :pagination="{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: (page, pageSize) => handleTableChange({ current: page, pageSize }),
              onShowSizeChange: (current, size) => handleTableChange({ current, pageSize: size })
            }" :loading="loading" @change="handleTableChange" :scroll="{ x: 'max-content', y: 'calc(100vh - 400px)' }"
            :virtual="false" :row-height="54" bordered class="unified-table">
            <template #bodyCell="{ column, record }">
              <!-- 状态标签 -->
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>

              <!-- 照片显示 -->
              <template v-if="column.dataIndex === 'photos'">
                <div class="photo-preview">
                  <a-image v-for="(photo, index) in record.acceptance_photos" :key="index"
                    :src="photo && photo.url ? getPhotoUrl(photo.url) : ''" :width="40" :height="40"
                    style="margin-right: 8px" :preview="photo && photo.url ? true : false" />
                  <span v-if="!record.acceptance_photos || record.acceptance_photos.length === 0"
                    class="no-photos">暂无照片</span>
                </div>
              </template>


              <!-- 操作列 -->
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <PermissionButton permission="purchase:acceptance:view" type="link" size="small" @click="viewDetail(record)">
                    详情
                  </PermissionButton>

                  <PermissionButton permission="purchase:acceptance:accept" type="primary" size="small" @click="openAcceptanceModal(record)"
                    v-if="record.status === STATUS.PENDING_ACCEPTANCE">
                    验收信息
                  </PermissionButton>
                  <PermissionButton permission="purchase:acceptance:generate_reimbursement" type="primary" size="small" @click="generateReimbursement(record)"
                    v-if="record.status === STATUS.ACCEPTED">
                    生成报销单
                  </PermissionButton>

                </a-space>
              </template>

            </template>
          </a-table>
        </div>
      </div>
    </div>



    <!-- 验收信息模态框 -->
    <a-modal v-model:open="showAcceptanceModal" title="验收信息" width="50%" :footer="null" :destroy-on-close="true">
      <div class="acceptance-form">
        <a-form ref="acceptanceFormRef" :model="acceptanceForm" :rules="acceptanceFormRules" layout="vertical">
          <!-- 基本信息展示 -->
          <div class="form-section">
            <h4>需求信息</h4>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="物品名称">
                  <a-input :value="acceptanceForm.item_name" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="规格型号">
                  <a-input :value="acceptanceForm.specification" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="需求单位">
                  <a-input :value="acceptanceForm.hierarchy_path" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="申请人">
                  <a-input :value="acceptanceForm.requester_name" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="采购数量">
                  <a-input :value="acceptanceForm.purchase_quantity || acceptanceForm.quantity" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="采购单价">
                  <a-input :value="formatCurrency(acceptanceForm.actual_unit_price || acceptanceForm.budget_unit_price || acceptanceForm.unit_price)" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="采购金额">
                  <a-input :value="formatCurrency(acceptanceForm.actual_total_price || acceptanceForm.budget_total_amount || acceptanceForm.total_amount)" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="物品种类">
                  <a-input :value="getDictText('物品种类', acceptanceForm.item_category) || acceptanceForm.item_category_display" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="采购类型">
                  <a-input :value="getDictText('采购类型', acceptanceForm.purchase_type) || acceptanceForm.purchase_type_display" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="单位">
                  <a-input :value="getDictText('单位', acceptanceForm.unit) || acceptanceForm.unit_display" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="供应商">
                  <a-input :value="acceptanceForm.supplier_name" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="采购日期">
                  <a-input :value="formatDate(acceptanceForm.purchase_date)" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 验收信息填报 -->
          <div class="form-section">
            <h4>验收信息</h4>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="验收数量" name="acceptance_quantity" required>
                  <a-input-number v-model:value="acceptanceForm.acceptance_quantity" :min="0" style="width: 100%"
                    placeholder="请输入验收数量" />
                  <div class="form-hint">采购数量：{{ acceptanceForm.purchase_quantity }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="数量差异">
                  <a-input :value="quantityDifference" disabled style="width: 100%" />
                  <div class="form-hint">差异率：{{ differenceRate }}%</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="验收状态">
                  <a-tag :color="hasException ? 'error' : 'success'">
                    {{ hasException ? '异常' : '正常' }}
                  </a-tag>
                  <div class="form-hint" v-if="hasException">差异率≥5%需要异常处理</div>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 物流信息 -->
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="快递公司" name="courier_company">
                  <a-input v-model:value="acceptanceForm.courier_company" placeholder="请输入快递公司名称" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="快递单号" name="tracking_number">
                  <a-input v-model:value="acceptanceForm.tracking_number" placeholder="请输入快递单号" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="商品发货地" name="shipping_origin">
                  <a-input v-model:value="acceptanceForm.shipping_origin" placeholder="请输入商品发货地" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16" v-if="hasException">
              <a-col :span="24">
                <a-form-item label="异常原因说明" name="exception_reason">
                  <a-textarea v-model:value="acceptanceForm.exception_reason" :rows="3" placeholder="请说明数量差异的原因" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item label="验收备注" name="acceptance_remarks">
                  <a-textarea v-model:value="acceptanceForm.acceptance_remarks" :rows="3" placeholder="请输入验收备注信息" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 照片上传 -->
          <div class="form-section">
            <AcceptancePhotoUpload
              v-model="acceptancePhotos"
              :record-id="acceptanceForm.id"
              :upload-api="uploadPhotoForComponent"
              @change="handlePhotoChange"
            />
          </div>
        </a-form>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <a-space>
            <a-button @click="showAcceptanceModal = false">取消</a-button>
            <PermissionButton permission="purchase:acceptance:save" @click="saveAcceptanceInfo" :loading="acceptanceLoading">保存</PermissionButton>
            <PermissionButton permission="purchase:acceptance:accept" type="primary" @click="submitAcceptanceInfo" :loading="acceptanceLoading">
              提交验收
            </PermissionButton>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 导出Excel配置 -->
    <AcceptanceExportConfig v-model:visible="showExportModal" @export-complete="handleExportComplete"
      @cancel="showExportModal = false" />

    <!-- 打印验收单配置 -->
    <AcceptancePrintConfig :open="showPrintModal" :user-department="userDepartment"
      @print-complete="handlePrintComplete" @cancel="showPrintModal = false" />

  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { debounce } from 'lodash-es'
import {
  PlusOutlined,
  CheckCircleOutlined,
  DownloadOutlined,
  FileAddOutlined,
  ReloadOutlined,
  PrinterOutlined,
  SettingOutlined,
  DownOutlined,
  UpOutlined,
  SearchOutlined,
  DollarOutlined
} from '@ant-design/icons-vue'
import api from '@/api'
import dayjs from 'dayjs'
import { getStatusConfig } from '@/utils/status'
import { getPhotoUrl } from '@/utils/photo'
import AcceptancePrintConfig from '@/components/Print/AcceptancePrintConfig.vue'
import AcceptanceExportConfig from '@/components/Export/AcceptanceExportConfig.vue'
import AcceptancePhotoUpload from '@/components/Upload/AcceptancePhotoUpload.vue'
import PermissionButton from '@/components/Permission/PermissionButton.vue'
import { getPageColumnOptions, getPageColumnPresets, getPageFieldCategories, getPagePresetMenuItems, getPageExportFieldOptions, getPageDefaultExportFields, getPageDefaultColumns, sortColumnsByOrder } from '@/utils/validation'

export default {
  name: 'AcceptanceListView',
  components: {
    PlusOutlined,
    CheckCircleOutlined,
    DownloadOutlined,
    FileAddOutlined,
    ReloadOutlined,
    PrinterOutlined,
    SettingOutlined,
    DownOutlined,
    UpOutlined,
    SearchOutlined,
    DollarOutlined,
    AcceptancePrintConfig,
    AcceptanceExportConfig,
    AcceptancePhotoUpload,
    PermissionButton
  },
  setup() {
    const router = useRouter()

    // 状态常量
    const STATUS = {
      PENDING_ACCEPTANCE: 'pending_acceptance',
      ACCEPTED: 'accepted',
      DRAFT: 'draft',
      PENDING_APPROVAL: 'pending_approval',
      APPROVED: 'approved',
      REJECTED: 'rejected',
      PENDING_PURCHASE: 'pending_purchase',
      PURCHASED: 'purchased',
      RETURNED: 'returned',
      PENDING_REIMBURSEMENT: 'pending_reimbursement',
      SETTLED: 'settled'
    }

    const loading = ref(false)
    const acceptances = ref([])
    const selectedRowKeys = ref([])
    const departments = ref([])

    // 导出相关状态
    const showExportModal = ref(false)
    const exportActiveTab = ref('fields')
    const exportLoading = ref(false)
    const previewLoading = ref(false)
    const previewData = ref([])
    const previewTotal = ref(0)

    // 打印相关状态
    const showPrintModal = ref(false)
    const userDepartment = ref({})





    // 基于业务流程的导出字段配置（验收阶段）
    const exportFieldOptions = getPageExportFieldOptions('acceptance')
    const exportFieldCategories = getPageFieldCategories('acceptance')
    const selectedExportFields = ref(getPageDefaultExportFields('acceptance'))

    // 导出筛选条件
    const exportFilters = reactive({
      status: [],
      department: [],
      itemName: '',
      requester: '',
      courierCompany: '',
      dateRange: [],
      // 新增导出筛选字段
      procurementMethod: '',
      requirementSource: '',
      fundProject: '',
      unit: '',
      itemCategory: '',
      remarks: ''
    })

    // 字段选择相关计算属性
    const exportFieldsIndeterminate = computed(() => {
      return selectedExportFields.value.length > 0 && selectedExportFields.value.length < exportFieldOptions.length
    })

    const exportFieldsCheckAll = computed(() => {
      return selectedExportFields.value.length === exportFieldOptions.length
    })

    // 预览表格列
    const previewColumns = computed(() => {
      return selectedExportFields.value.map(field => {
        const option = exportFieldOptions.find(opt => opt.value === field)
        return {
          title: option?.label || field,
          dataIndex: field,
          key: field,
          width: 120,
          ellipsis: true
        }
      })
    })

    // 筛选选项数据源
    const purchaseTypes = ref([
      { value: 'unified', label: '统一采购' },
      { value: 'self', label: '自行采购' }
    ])
    const acceptors = ref([])
    const suppliers = ref([])

    // 筛选控件显示状态
    const showFilters = ref(false)
    const presetDropdownOpen = ref(false)
    const showColumnFilter = ref(false)

    // 筛选条件
    const filters = reactive({
      status: '',
      id: '',
      itemName: '',
      specification: '',
      department: '',
      acceptor: '',
      supplier_name: '',
      purchaseType: '',
      acceptanceTimeRange: [],
      minPurchaseAmount: '',
      maxPurchaseAmount: '',
      minSettlementAmount: '',
      maxSettlementAmount: '',
      // 新增缺失的筛选字段
      procurementMethod: '',     // 采购方式
      requirementSource: '',     // 需求来源
      fundProject: '',          // 经费项目
      unit: '',                 // 计量单位
      itemCategory: '',         // 物品种类
      remarks: ''               // 需求备注
    })

    // 从本地存储读取字段配置
    const getStoredColumns = () => {
      const COLUMNS_VERSION = '2.2' // 版本号，更新时清除旧缓存
      const STORAGE_KEY = `acceptances-columns-v${COLUMNS_VERSION}`

      try {
        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const parsed = JSON.parse(stored)
          // 确保必选字段始终被包含
          const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)
          return [...new Set([...parsed, ...requiredColumns])]
        }
      } catch (error) {
        console.warn('读取字段配置失败:', error)
      }
      // 使用默认字段配置（按顺序排列）
      const defaultColumns = getPageDefaultColumns('acceptance')
      console.log('getPageDefaultColumns result:', defaultColumns)
      return defaultColumns // 默认配置已包含action列
    }

    // 保存字段配置到本地存储
    const saveColumnsToStorage = (columns) => {
      const COLUMNS_VERSION = '2.2'
      const STORAGE_KEY = `acceptances-columns-v${COLUMNS_VERSION}`
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(columns))
      } catch (error) {
        console.warn('保存字段配置失败:', error)
      }
    }

    // 基于业务流程的字段筛选配置（验收阶段，排除操作列）
    const columnOptions = getPageColumnOptions('acceptance').filter(opt => opt.key !== 'action')

    // 初始化选中的字段
    const selectedColumns = ref(getStoredColumns())
    console.log('Acceptances selectedColumns:', selectedColumns.value)

    // 基于业务流程的预设配置（验收阶段）
    const columnPresets = getPageColumnPresets('acceptance')

    // 基于业务流程的字段分类配置（验收阶段）
    const fieldCategories = getPageFieldCategories('acceptance')

    // 基于业务流程的预设菜单选项（验收阶段）
    const presetMenuItems = getPagePresetMenuItems('acceptance')

    // 验收信息填报相关
    const showAcceptanceModal = ref(false)
    const acceptanceLoading = ref(false)
    const acceptanceFormRef = ref(null)
    const acceptanceForm = reactive({
      id: null,
      item_name: '',
      specification: '',
      hierarchy_path: '',
      requester_name: '',
      purchase_quantity: 0,
      budget_unit_price: 0,
      budget_total_amount: 0,
      actual_unit_price: 0,
      actual_total_price: 0,
      acceptance_quantity: 0,
      courier_company: '',
      tracking_number: '',
      shipping_origin: '',
      exception_reason: '',
      acceptance_remarks: '',
      item_category: '',
      item_category_display: '',
      purchase_type: '',
      purchase_type_display: '',
      unit: '',
      unit_display: '',
      supplier_name: '',
      purchase_date: null,
      quantity: 0,
      unit_price: 0,
      total_amount: 0
    })

    // 验收表单验证规则
    const acceptanceFormRules = {
      acceptance_quantity: [
        { required: true, message: '请输入验收数量', trigger: 'blur' },
        { type: 'number', min: 0, message: '数量不能为负数', trigger: 'blur' }
      ]
    }

    // 验收照片上传 - 使用新的照片组件
    const acceptancePhotos = ref([])

    // 计算数量差异
    const quantityDifference = computed(() => {
      return (acceptanceForm.acceptance_quantity || 0) - (acceptanceForm.purchase_quantity || 0)
    })

    // 照片相关计算属性 - 基于新的照片组件
    const hasUploadedPhotos = computed(() => {
      return acceptancePhotos.value && acceptancePhotos.value.length > 0
    })

    // 计算差异率
    const differenceRate = computed(() => {
      if (acceptanceForm.quantity > 0) {
        return Math.abs(quantityDifference.value) / acceptanceForm.quantity * 100
      }
      return 0
    })

    // 判断是否有异常
    const hasException = computed(() => {
      return differenceRate.value >= 5
    })



    // 行选择配置 - 只允许选择已验收状态的记录进行批量生成报销单
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (selectedKeys) => {
        selectedRowKeys.value = selectedKeys
      },
      getCheckboxProps: (record) => ({
        disabled: record.status !== STATUS.ACCEPTED
      })
    }
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
      pageSizeOptions: ['10', '20', '50', '100']
    })

    // 计算属性

    const pendingCount = computed(() => {
      return acceptances.value.filter(item => item.status === STATUS.PENDING_ACCEPTANCE).length
    })

    const acceptedCount = computed(() => {
      return acceptances.value.filter(item => item.status === STATUS.ACCEPTED).length
    })



    const departmentCount = computed(() => {
      const deptSet = new Set(acceptances.value.map(item => {
        let hierarchyPath = item?.hierarchy_path || '未知单位'
        hierarchyPath = hierarchyPath.trim()
        if (!hierarchyPath || hierarchyPath === '') {
          return '未知单位'
        }
        let firstLevelDept = hierarchyPath.split('-')[0]?.trim() || '未知单位'
        if (!firstLevelDept || firstLevelDept === '') {
          firstLevelDept = '未知单位'
        }
        return firstLevelDept
      }))
      return deptSet.size
    })

    // 动态生成表格列定义
    const allColumns = computed(() => {
      const columnMap = {
        id: {
          title: 'ID',
          dataIndex: 'id',
          key: 'id',
          width: 80,
          sorter: true,
          align: 'center'
        },
        item_name: {
          title: '物品名称',
          dataIndex: 'item_name',
          key: 'item_name',
          width: 150,
          sorter: true,
          align: 'center',
          customRender: ({ record }) => {
            return record?.item_name || '未知物品'
          }
        },
        specification: {
          title: '规格型号',
          dataIndex: 'specification',
          key: 'specification',
          width: 200,
          sorter: true,
          align: 'center',
          customRender: ({ record }) => {
            return record?.specification || '无规格'
          }
        },
        unit: {
          title: '计量单位',
          dataIndex: 'unit',
          key: 'unit',
          width: 100,
          sorter: true,
          align: 'center',
          customRender: ({ record }) => record.unit_display || record.unit || '-'
        },
        budget_quantity: {
          title: '预算数量',
          dataIndex: 'budget_quantity',
          key: 'budget_quantity',
          width: 100,
          align: 'center',
          sorter: true
        },
        budget_unit_price: {
          title: '预算单价',
          dataIndex: 'budget_unit_price',
          key: 'budget_unit_price',
          width: 120,
          align: 'center',
          sorter: true,
          customRender: ({ text }) => {
            return text ? `¥${parseFloat(text).toFixed(2)}` : '-'
          }
        },
        budget_total_amount: {
          title: '预算金额',
          dataIndex: 'budget_total_amount',
          key: 'budget_total_amount',
          width: 120,
          sorter: true,
          align: 'right',
          customRender: ({ text }) => {
            return text ? `¥${parseFloat(text).toFixed(2)}` : '-'
          }
        },
        requester_name: {
          title: '申请人',
          dataIndex: 'requester_name',
          key: 'requester_name',
          width: 100,
          sorter: true,
          customRender: ({ record }) => {
            return record?.requester_name || '未知'
          }
        },
        hierarchy_path: {
          title: '需求单位',
          dataIndex: 'hierarchy_path',
          key: 'hierarchy_path',
          width: 150,
          sorter: true,
          customRender: ({ record }) => {
            return record?.hierarchy_path || '未知单位'
          }
        },
        created_at: {
          title: '创建时间',
          dataIndex: 'created_at',
          key: 'created_at',
          width: 150,
          sorter: true,
          customRender: ({ text }) => {
            return text ? formatDateToYMD(text) : '-'
          }
        },
        purchase_type: {
          title: '采购类型',
          dataIndex: 'purchase_type',
          key: 'purchase_type',
          width: 100,
          sorter: true,
          customRender: ({ record }) => record.purchase_type_display || record.purchase_type || '-'
        },
        remarks: {
          title: '备注',
          dataIndex: 'remarks',
          key: 'remarks',
          width: 200,
          sorter: true,
          ellipsis: true
        },
        submission_date: {
          title: '提交时间',
          dataIndex: 'submission_date',
          key: 'submission_date',
          width: 150,
          sorter: true,
          customRender: ({ text }) => {
            return text ? formatDateToYMD(text) : '-'
          }
        },
        approver_name: {
          title: '审批人',
          dataIndex: 'approver_name',
          key: 'approver_name',
          width: 100,
          sorter: true
        },
        approved_at: {
          title: '审批时间',
          dataIndex: 'approved_at',
          key: 'approved_at',
          width: 150,
          sorter: true,
          customRender: ({ text }) => {
            return text ? formatDateToYMD(text) : '-'
          }
        },
        purchase_unit_price: {
          title: '采购单价',
          dataIndex: 'purchase_unit_price',
          key: 'purchase_unit_price',
          width: 120,
          align: 'right',
          customRender: ({ text }) => {
            return text ? `¥${parseFloat(text).toFixed(2)}` : '-'
          }
        },
        purchase_quantity: {
          title: '采购数量',
          dataIndex: 'purchase_quantity',
          key: 'purchase_quantity',
          width: 100,
          align: 'right'
        },
        supplier_name: {
          title: '供应商',
          dataIndex: 'supplier_name',
          key: 'supplier_name',
          width: 150
        },
        purchaser_name: {
          title: '采购员',
          dataIndex: 'purchaser_name',
          key: 'purchaser_name',
          width: 100,
          customRender: ({ record }) => {
            return record?.purchaser_name || '未知'
          }
        },
        purchase_date: {
          title: '采购时间',
          dataIndex: 'purchase_date',
          key: 'purchase_date',
          width: 150,
          customRender: ({ text }) => {
            return text ? formatDateToYMD(text) : '-'
          }
        },
        acceptance_quantity: {
          title: '验收数量',
          dataIndex: 'acceptance_quantity',
          key: 'acceptance_quantity',
          width: 100,
          align: 'right'
        },
        acceptor_name: {
          title: '验收员',
          dataIndex: 'acceptor_name',
          key: 'acceptor_name',
          width: 100
        },
        acceptance_date: {
          title: '验收时间',
          dataIndex: 'acceptance_date',
          key: 'acceptance_date',
          width: 150,
          customRender: ({ text }) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
        },
        settlement_amount: {
          title: '结算金额',
          dataIndex: 'settlement_amount',
          key: 'settlement_amount',
          width: 120,
          align: 'right',
          customRender: ({ text }) => {
            return text ? `¥${parseFloat(text).toFixed(2)}` : '-'
          }
        },
        reimbursement_person: {
          title: '报销人',
          dataIndex: 'reimbursement_person',
          key: 'reimbursement_person',
          width: 100
        },
        reimbursement_date: {
          title: '结算时间',
          dataIndex: 'reimbursement_date',
          key: 'reimbursement_date',
          width: 150,
          customRender: ({ text }) => {
            return text ? formatDateToYMD(text) : '-'
          }
        },
        transaction_number: {
          title: '交易流水号',
          dataIndex: 'transaction_number',
          key: 'transaction_number',
          width: 150
        },
        payee_name: {
          title: '收款人',
          dataIndex: 'payee_name',
          key: 'payee_name',
          width: 100
        },
        settlement_remarks: {
          title: '结算备注',
          dataIndex: 'settlement_remarks',
          key: 'settlement_remarks',
          width: 200,
          ellipsis: true
        },
        status: {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 120,
          customRender: ({ record }) => {
            const statusConfig = getStatusConfig(record.status)
            return h('span', {
              style: {
                color: statusConfig.color,
                backgroundColor: statusConfig.bgColor,
                padding: '2px 8px',
                borderRadius: '4px',
                fontSize: '12px'
              }
            }, statusConfig.text)
          }
        },
        action: {
          title: '操作',
          key: 'action',
          dataIndex: 'action',
          width: 200,
          fixed: 'right',
          className: 'action-column'
        }
      }

      // 根据选中的列生成表格列配置（排除操作列）
      const selectedCols = selectedColumns.value
        .filter(columnKey => columnKey !== 'action') // 过滤掉操作列
        .map(columnKey => {
          return columnMap[columnKey]
        }).filter(Boolean)

      // 添加固定的操作列到最右侧
      if (columnMap.action) {
        selectedCols.push(columnMap.action)
      }

      // 为所有列添加居中对齐（如果还没有的话）
      selectedCols.forEach(column => {
        if (!column.align) {
          column.align = 'center'
        }
      })

      return selectedCols
    })


    // 动态筛选后的列（allColumns已经是computed，直接使用）
    const filteredColumns = allColumns



    // 状态颜色映射
    const getStatusColor = (status) => {
      return getStatusConfig(status).color
    }

    // 状态文本映射
    const getStatusText = (status) => {
      return getStatusConfig(status).text
    }

    // 时间格式化为年/月/日
    const formatDateToYMD = (dateString) => {
      if (!dateString) return '-'
      try {
        const date = new Date(dateString)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}/${month}/${day}`
      } catch (error) {
        console.error('日期格式化错误:', error)
        return '-'
      }
    }

    // 验收记录
    const acceptRecord = async (record) => {
      try {
        const response = await api.purchaseRequests.accept(record.id)
        if (response.code === 200) {
          message.success('验收成功')
          loadData()
        }
      } catch (error) {
        console.error('验收失败:', error)
        message.error('验收失败')
      }
    }

    // 生成报销单
    const generateReimbursement = async (record) => {
      try {
        const response = await api.purchaseRequests.generateReimbursement(record.id)

        if (response.code === 200) {
          message.success('生成报销单成功')
          loadData()
        }
      } catch (error) {
        console.error('生成报销单失败:', error)
        message.error('生成报销单失败')
      }
    }





    // 批量生成报销单
    const batchGenerateReimbursement = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要生成报销单的记录')
        return
      }

      const acceptedRequests = acceptances.value.filter(item =>
        selectedRowKeys.value.includes(item.id) && item.status === STATUS.ACCEPTED
      )

      if (acceptedRequests.length === 0) {
        message.warning('所选项目中没有已验收的记录')
        return
      }

      Modal.confirm({
        title: '批量生成报销单确认',
        content: `确定要对选中的 ${acceptedRequests.length} 个已验收记录生成报销单吗？`,
        okText: "确定",
        cancelText: "取消",
        onOk: async () => {
          try {
            const requestIds = acceptedRequests.map(req => req.id)
            const response = await api.purchaseRequests.batchGenerateReimbursement(requestIds)
            if (response.code === 200) {
              message.success(`批量生成报销单成功，共处理 ${acceptedRequests.length} 条记录`)
              selectedRowKeys.value = []
              loadData()
            }
          } catch (error) {
            console.error('批量生成报销单失败:', error)
            message.error('批量生成报销单失败')
          }
        }
      })
    }



    // 判断是否有已验收的记录
    const hasAcceptedRequests = computed(() => {
      return selectedRowKeys.value.some(id => {
        const record = acceptances.value.find(r => r.id === id)
        return record && record.status === STATUS.ACCEPTED
      })
    })



    // 获取部门列表 - 用于筛选选项，使用专门的无分页接口
    const getDepartments = async () => {
      try {
        const response = await api.departments.getAll()
        if (response.code === 200) {
          departments.value = response.data || []
        }
      } catch (error) {
        console.error('获取部门列表失败:', error)
      }
    }

    // 构建查询参数的辅助函数
    const buildQueryParams = () => {
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        // 验收页面默认状态
        status_in: filters.status || `${STATUS.PENDING_ACCEPTANCE},${STATUS.ACCEPTED}`
      }

      // 优化筛选参数构建
      const filterMappings = {
        itemName: 'search',
        requester: 'requester__username__icontains',
        purchaseType: 'purchase_type'
      }

      // 批量处理简单筛选参数
      Object.entries(filterMappings).forEach(([key, mapping]) => {
        if (filters[key]) {
          params[mapping] = filters[key]
        }
      })

      // 处理特殊筛选参数
      if (filters.id) {
        const idNum = parseInt(filters.id)
        if (!isNaN(idNum)) params.id = idNum
      }

      if (filters.minSettlementAmount) {
        const minAmount = parseFloat(filters.minSettlementAmount)
        if (!isNaN(minAmount)) params.settlement_amount__gte = minAmount
      }

      if (filters.maxSettlementAmount) {
        const maxAmount = parseFloat(filters.maxSettlementAmount)
        if (!isNaN(maxAmount)) params.settlement_amount__lte = maxAmount
      }

      // 处理部门筛选
      if (filters.department) {
        const selectedDept = departments.value.find(d => d.id == filters.department)
        if (selectedDept) {
          params.hierarchy_path__icontains = selectedDept.parent_id
            ? selectedDept.hierarchy_path
            : selectedDept.dept_name
        }
      }

      // 处理排序
      if (sortField.value && sortOrder.value) {
        params.ordering = `${sortOrder.value === 'descend' ? '-' : ''}${sortField.value}`
      }

      return params
    }

    // 加载数据 - 优化版本
    const loadData = async () => {
      // 防止重复请求
      if (loading.value) return

      loading.value = true
      try {
        const params = buildQueryParams()

        // 添加额外的筛选参数
        const additionalFilters = {
          procurementMethod: 'procurement_method__icontains',
          requirementSource: 'requirement_source__icontains',
          fundProject: 'fund_project__icontains',
          unit: 'unit__icontains',
          itemCategory: 'item_category__icontains',
          remarks: 'remarks__icontains'
        }

        Object.entries(additionalFilters).forEach(([key, mapping]) => {
          if (filters[key]) {
            params[mapping] = filters[key]
          }
        })

        // 添加验收页面专用字段优化 - 只包含数据库实际字段
        params.fields = 'id,item_name,specification,unit,acceptance_quantity,purchase_type,status,hierarchy_path,courier_company,tracking_number,created_at,acceptance_date,purchase_quantity,budget_total_amount,supplier_name,acceptance_remarks,acceptor'

        const response = await api.acceptances.getList(params)
        if (response.code === 200) {
          acceptances.value = response.data.results || []
          pagination.total = response.data.count || 0
        } else {
          throw new Error(`API返回错误: ${response.message}`)
        }
      } catch (error) {
        console.error('获取验收数据失败:', error)
        message.error(error.message || '获取数据失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 筛选变化处理
    const handleFilterChange = () => {
      pagination.current = 1
      loadData()
    }

    // 防抖筛选变化处理
    const debouncedFilterChange = debounce(handleFilterChange, 500)

    // 重置筛选
    const resetFilters = () => {
      Object.assign(filters, {
        status: '',
        id: '',
        itemName: '',
        specification: '',
        department: '',
        acceptor: '',
        supplier_name: '',
        purchaseType: '',
        acceptanceTimeRange: [],
        minPurchaseAmount: '',
        maxPurchaseAmount: '',
        minSettlementAmount: '',
        maxSettlementAmount: '',
        // 重置新增的筛选字段
        procurementMethod: '',
        requirementSource: '',
        fundProject: '',
        unit: '',
        itemCategory: '',
        remarks: ''
      })
      handleFilterChange()
    }

    // 获取筛选选项数据
    const getFilterOptions = async () => {
      try {
        // 获取用户列表（验收员）- 优化分页大小
        const usersResponse = await api.users.getList({ page_size: 20 })
        if (usersResponse.code === 200) {
          const users = usersResponse.data.results || usersResponse.data
          acceptors.value = users.filter(user => user.role_name && user.role_name.includes('验收'))

          // 如果没有找到验收员，取前10个用户
          if (acceptors.value.length === 0) {
            acceptors.value = users.slice(0, 10)
          }
        }

        // 去除供应商列表请求 - 这个请求影响性能且不是必需的
        // 供应商可以通过输入框手动输入，不需要预加载所有供应商
        suppliers.value = []
      } catch (error) {
        console.error('获取筛选选项失败:', error)
      }
    }

    // 筛选选项过滤方法
    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 展开/收起筛选控件
    const toggleFilters = () => {
      showFilters.value = !showFilters.value
    }

    // 字段筛选方法
    const onColumnChange = (checkedValues) => {
      // 确保必选字段始终被选中
      const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)
      const newSelectedColumns = [...new Set([...checkedValues, ...requiredColumns])]
      selectedColumns.value = newSelectedColumns
      saveColumnsToStorage(newSelectedColumns)
    }

    const selectAllColumns = () => {
      const sortedOptions = sortColumnsByOrder(columnOptions)
      const allColumns = sortedOptions.map(opt => opt.key)
      selectedColumns.value = allColumns
      saveColumnsToStorage(allColumns)
    }

    const resetColumns = () => {
      // 重置到系统默认配置，排除操作列
      const defaultColumns = getPageDefaultColumns('acceptance').filter(col => col !== 'action')
      selectedColumns.value = defaultColumns
      saveColumnsToStorage(defaultColumns)
    }

    // 应用预设
    const applyPreset = ({ key }) => {
      const presetColumns = columnPresets[key]
      if (presetColumns) {
        // 确保必选字段始终被包含
        const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)

        // 合并预设字段和必选字段
        const combinedColumns = [...new Set([...presetColumns, ...requiredColumns])]

        // 按照标准顺序排列字段
        const sortedColumns = sortColumnsByOrder(combinedColumns, columnOptions)

        selectedColumns.value = sortedColumns
        saveColumnsToStorage(sortedColumns)
      }
    }

    // 处理字段变更（阻止面板关闭）
    const handleColumnChange = (checkedValues) => {
      onColumnChange(checkedValues)
    }

    // 处理预设点击（阻止面板关闭）
    const handlePresetClick = ({ key }) => {
      applyPreset({ key })
      // 保持dropdown打开状态
      presetDropdownOpen.value = true
    }

    // 处理全选按钮点击
    const handleSelectAll = (e) => {
      e.stopPropagation()
      selectAllColumns()
    }

    // 处理重置按钮点击
    const handleReset = (e) => {
      e.stopPropagation()
      resetColumns()
    }









    // 显示导出对话框
    const showExportDialog = () => {
      showExportModal.value = true
      resetExportConfig()
    }

    // 字段选择全选/取消全选
    const onExportFieldsCheckAllChange = (e) => {
      if (e.target.checked) {
        selectedExportFields.value = exportFieldOptions.map(option => option.value)
      } else {
        selectedExportFields.value = []
      }
    }

    // 字段选择变化
    const onExportFieldsChange = (checkedList) => {
      selectedExportFields.value = checkedList
    }

    // 重置导出配置
    const resetExportConfig = () => {
      selectedExportFields.value = getPageDefaultExportFields('acceptance')
      Object.assign(exportFilters, {
        status: [],
        department: [],
        itemName: '',
        requester: '',
        courierCompany: '',
        dateRange: [],
        // 重置新增的导出筛选字段
        procurementMethod: '',
        requirementSource: '',
        fundProject: '',
        unit: '',
        itemCategory: '',
        remarks: ''
      })
      exportActiveTab.value = 'fields'
      previewData.value = []
      previewTotal.value = 0
    }

    // 加载预览数据
    const loadPreviewData = async () => {
      if (selectedExportFields.value.length === 0) {
        message.warning('请至少选择一个导出字段')
        return
      }

      previewLoading.value = true
      try {
        const params = {
          page: 1,
          page_size: 10,
          fields: selectedExportFields.value.join(','),
          status_in: `${STATUS.PENDING_ACCEPTANCE},${STATUS.ACCEPTED}`
        }

        // 应用导出筛选条件
        if (exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(',')
        }
        if (exportFilters.department.length > 0) {
          params.department_in = exportFilters.department.join(',')
        }
        if (exportFilters.itemName) {
          params.search = exportFilters.itemName
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester
        }
        if (exportFilters.courierCompany) {
          params.courier_company__icontains = exportFilters.courierCompany
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.acceptance_date__gte = exportFilters.dateRange[0].format('YYYY-MM-DD')
          params.acceptance_date__lte = exportFilters.dateRange[1].format('YYYY-MM-DD')
        }
        // 新增导出筛选参数
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains = exportFilters.procurementMethod
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains = exportFilters.requirementSource
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks
        }

        const response = await api.purchaseRequests.getList(params)
        if (response.code === 200) {
          previewData.value = response.data.results || response.data || []
          previewTotal.value = response.data.count || previewData.value.length
        }
      } catch (error) {
        console.error('加载预览数据失败:', error)
        message.error('加载预览数据失败')
      } finally {
        previewLoading.value = false
      }
    }

    // 执行导出
    const executeExport = async () => {
      if (selectedExportFields.value.length === 0) {
        message.warning('请至少选择一个导出字段')
        return
      }

      exportLoading.value = true
      try {
        const params = {
          fields: selectedExportFields.value.join(','),
          status_in: `${STATUS.PENDING_ACCEPTANCE},${STATUS.ACCEPTED}`
        }

        // 应用导出筛选条件
        if (exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(',')
        }
        if (exportFilters.department.length > 0) {
          params.department_in = exportFilters.department.join(',')
        }
        if (exportFilters.itemName) {
          params.search = exportFilters.itemName
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester
        }
        if (exportFilters.courierCompany) {
          params.courier_company__icontains = exportFilters.courierCompany
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.acceptance_date__gte = exportFilters.dateRange[0].format('YYYY-MM-DD')
          params.acceptance_date__lte = exportFilters.dateRange[1].format('YYYY-MM-DD')
        }
        // 新增导出筛选参数
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains = exportFilters.procurementMethod
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains = exportFilters.requirementSource
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks
        }

        const response = await api.purchaseRequests.exportToExcel(params)

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.download = `物资验收清单_${new Date().toLocaleDateString()}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        message.success('导出成功')
        showExportModal.value = false
      } catch (error) {
        console.error('导出失败:', error)
        message.error('导出失败')
      } finally {
        exportLoading.value = false
      }
    }



    // 获取用户部门信息
    const getUserDepartment = async () => {
      try {
        const response = await api.users.getCurrentUser()
        if (response.data && response.data.department) {
          userDepartment.value = response.data.department
        }
      } catch (error) {
        console.error('获取用户部门信息失败:', error)
      }
    }

    // 打印完成处理
    const handlePrintComplete = (printedRecords) => {
      message.success(`成功打印 ${printedRecords.length} 条验收单`)
      // 可以在这里添加其他处理逻辑，比如记录打印日志等
    }

    // 导出完成处理
    const handleExportComplete = (exportedRecords) => {
      message.success(`成功导出 ${exportedRecords.length} 条验收记录`)
      // 可以在这里添加其他处理逻辑，比如记录导出日志等
    }



    // 打开验收信息填报模态框
    const openAcceptanceModal = async (record) => {
      try {
        // 获取详细信息
        const response = await api.purchaseRequests.getDetail(record.id)
        if (response.code === 200) {
          const data = response.data

          // 填充表单数据
          Object.assign(acceptanceForm, {
            id: data.id,
            item_name: data.item_name,
            specification: data.specification || '',
            hierarchy_path: data.hierarchy_path || '',
            requester_name: data.requester_name || '',
            purchase_quantity: data.purchase_quantity || data.quantity,
            budget_unit_price: data.budget_unit_price || data.unit_price,
            budget_total_amount: data.budget_total_amount || data.total_amount,
            actual_unit_price: data.actual_unit_price || 0,
            actual_total_price: data.actual_total_price || 0,
            acceptance_quantity: data.acceptance_quantity || data.purchase_quantity || data.quantity,
            courier_company: data.courier_company || '',
            tracking_number: data.tracking_number || '',
            shipping_origin: data.shipping_origin || '',
            exception_reason: data.exception_reason || '',
            acceptance_remarks: data.acceptance_remarks || '',
            item_category: data.item_category || '',
            item_category_display: data.item_category_display || '',
            purchase_type: data.purchase_type || '',
            purchase_type_display: data.purchase_type_display || '',
            unit: data.unit || '',
            unit_display: data.unit_display || '',
            supplier_name: data.supplier_name || '',
            purchase_date: data.purchase_date || null,
            quantity: data.quantity || 0,
            unit_price: data.unit_price || 0,
            total_amount: data.total_amount || 0
          })

          // 重置照片数据
          acceptancePhotos.value = []

          // 加载现有验收照片 - 使用新的照片组件格式
          if (data.acceptance_photos && Array.isArray(data.acceptance_photos)) {
            console.log('🔄 加载现有验收照片:', data.acceptance_photos)

            // 直接设置照片数据给新组件
            acceptancePhotos.value = data.acceptance_photos.map(photo => ({
              url: photo.url,
              type: photo.type || photo.photo_type,
              order: photo.order || 0,
              uid: photo.id || photo.uid,
              description: photo.description
            }))

            console.log('✅ 照片数据已设置:', acceptancePhotos.value)
          } else {
            acceptancePhotos.value = []
          }

          showAcceptanceModal.value = true
        }
      } catch (error) {
        console.error('获取验收详情失败:', error)
        message.error('获取验收详情失败')
      }
    }

    // 保存验收信息（不改变状态）
    const saveAcceptanceInfo = async () => {
      try {
        await acceptanceFormRef.value.validate()

        acceptanceLoading.value = true
        const params = {
          acceptance_quantity: acceptanceForm.acceptance_quantity,
          courier_company: acceptanceForm.courier_company,
          tracking_number: acceptanceForm.tracking_number,
          shipping_origin: acceptanceForm.shipping_origin,
          exception_reason: acceptanceForm.exception_reason,
          acceptance_remarks: acceptanceForm.acceptance_remarks
        }

        const response = await api.purchaseRequests.updateAcceptanceInfo(acceptanceForm.id, params)
        if (response.code === 200) {
          message.success('验收信息保存成功')

          // 刷新数据
          loadData()
        }
      } catch (error) {
        console.error('保存验收信息失败:', error)
        message.error('保存验收信息失败')
      } finally {
        acceptanceLoading.value = false
      }
    }

    // 提交验收信息（改变状态为已验收）
    const submitAcceptanceInfo = async () => {
      try {
        await acceptanceFormRef.value.validate()

        // 如果有异常但没有填写异常原因，提示用户
        if (hasException.value && !acceptanceForm.exception_reason.trim()) {
          message.warning('检测到数量异常，请填写异常原因说明')
          return
        }

        acceptanceLoading.value = true
        const params = {
          acceptance_quantity: acceptanceForm.acceptance_quantity,
          courier_company: acceptanceForm.courier_company,
          tracking_number: acceptanceForm.tracking_number,
          shipping_origin: acceptanceForm.shipping_origin,
          exception_reason: acceptanceForm.exception_reason,
          acceptance_remarks: acceptanceForm.acceptance_remarks,
          status: STATUS.ACCEPTED
        }

        const response = await api.purchaseRequests.completeAcceptance(acceptanceForm.id, params)
        if (response.code === 200) {
          message.success('验收完成')

          showAcceptanceModal.value = false
          loadData()
        }
      } catch (error) {
        console.error('提交验收信息失败:', error)
        message.error('提交验收信息失败')
      } finally {
        acceptanceLoading.value = false
      }
    }





    // 照片变化处理
    const handlePhotoChange = (photos) => {
      console.log('=== 验收照片变化 ===')
      console.log('photos:', photos)
      acceptancePhotos.value = photos

      // 验证是否上传了必需的三张照片
      const hasAllRequiredPhotos = photos.length === 3 &&
        photos.some(p => p.type === 'front') &&
        photos.some(p => p.type === 'side') &&
        photos.some(p => p.type === 'overall')

      if (hasAllRequiredPhotos) {
        message.success('验收照片已完整上传')
      }
    }

    // 为新组件创建上传API包装函数
    const uploadPhotoForComponent = async (recordId, formData) => {
      console.log('🔄 组件上传API调用:', { recordId, formData })
      return await api.purchaseRequests.uploadPhoto(recordId, formData)
    }

    // 照片类型标签
    const getPhotoTypeLabel = (type) => {
      const labels = {
        front: '正面照片',
        side: '侧面照片',
        overall: '整体照片'
      }
      return labels[type] || type
    }

    // 旧的照片上传函数已被新的照片组件替代

    // 格式化货币
    const formatCurrency = (value) => {
      if (!value || value === 0) return '¥0.00'
      return `¥${parseFloat(value).toFixed(2)}`
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 排序状态
    const sortField = ref('')
    const sortOrder = ref('')

    // 表格变化处理 - 支持分页和排序
    const handleTableChange = (pag, _filters, sorter) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize

      // 处理排序
      if (sorter && sorter.field) {
        sortField.value = sorter.field
        sortOrder.value = sorter.order
      } else {
        sortField.value = ''
        sortOrder.value = ''
      }

      loadData()
    }

    // 查看详情
    const viewDetail = (record) => {
      router.push(`/purchase/requests/${record.id}`)
    }



    // 计算总记录数
    const totalCount = computed(() => {
      return pagination.total
    })

    onMounted(async () => {
      // 优化初始化：先加载主要数据，其他数据按需加载
      await loadData()

      // 延迟加载筛选选项，避免阻塞主要数据
      setTimeout(() => {
        getDepartments()
        getFilterOptions()
        getUserDepartment()
      }, 100)
    })

    return {
      loading,
      acceptances,
      selectedRowKeys,
      departments,
      purchaseTypes,
      acceptors,
      suppliers,
      showFilters,
      presetDropdownOpen,
      showColumnFilter,
      filters,
      columnOptions,
      selectedColumns,
      fieldCategories,
      presetMenuItems,
      pagination,
      rowSelection,
      filteredColumns,
      totalCount,
      pendingCount,
      acceptedCount,
      departmentCount,
      getDepartments,
      getFilterOptions,
      filterOption,
      toggleFilters,
      onColumnChange,
      handleColumnChange,
      handlePresetClick,
      handleSelectAll,
      handleReset,
      selectAllColumns,
      resetColumns,
      applyPreset,
      loadData,
      handleFilterChange,
      debouncedFilterChange,
      resetFilters,
      handleTableChange,
      sortField,
      sortOrder,
      viewDetail,
      getPhotoUrl,
      acceptRecord,
      generateReimbursement,
      batchGenerateReimbursement,
      getStatusColor,
      getStatusText,
      formatDateToYMD,
      hasAcceptedRequests,
      // 导出相关
      showExportModal,
      exportActiveTab,
      exportLoading,
      previewLoading,
      previewData,
      previewTotal,
      selectedExportFields,
      exportFieldOptions,
      exportFieldCategories,
      exportFilters,
      exportFieldsIndeterminate,
      exportFieldsCheckAll,
      previewColumns,
      showExportDialog,
      onExportFieldsCheckAllChange,
      onExportFieldsChange,
      resetExportConfig,
      loadPreviewData,
      executeExport,
      // 打印相关
      showPrintModal,
      userDepartment,
      getUserDepartment,
      handlePrintComplete,
      handleExportComplete,
      // 验收信息填报相关
      showAcceptanceModal,
      acceptanceLoading,
      acceptanceFormRef,
      acceptanceForm,
      acceptanceFormRules,
      acceptancePhotos,
      hasUploadedPhotos,
      quantityDifference,
      differenceRate,
      hasException,
      openAcceptanceModal,
      saveAcceptanceInfo,
      submitAcceptanceInfo,
      uploadPhotoForComponent,
      handlePhotoChange,
      getPhotoTypeLabel,
      formatCurrency,
      formatDate,
      STATUS
    }
  }
}
</script>

<style scoped>
@import '@/styles/business-panels.css';

/* 页面容器样式 */
.page-container {
  padding: 0;
}

/* 表格头部flex布局 - 与采购总览页面保持一致 */
.table-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-light);
  gap: 24px;
}

.table-title-section {
  flex-shrink: 0;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: 13px;
  font-weight: 400;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .table-header-flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .table-actions .ant-space {
    width: 100%;
    flex-wrap: wrap;
  }
}


.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .anticon {
  font-size: var(--text-2xl);
  color: rgba(255, 255, 255, 0.9);
}

.page-subtitle {
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 24px;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-inverse);
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 验收表格区域样式 */
.acceptance-tables {
  margin-top: var(--space-xl);
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 表格标题样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
}

.table-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.table-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
  justify-content: flex-start;
}

.table-title .anticon {
  color: var(--primary-color);
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.table-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
  word-break: break-word;
}

/* 统一筛选区域样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

.filter-result-text {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  margin-left: var(--space-lg);
}

/* 操作按钮区域样式 */
.action-buttons-section {
  padding: var(--space-xl) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  text-align: center;
}

/* 主要操作按钮样式 */
.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 48px;
  padding: 0 var(--space-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-sm);
  min-width: 140px;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
  color: var(--text-inverse);
}

.primary-action-btn:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  transform: none;
  box-shadow: none;
}

/* 次要操作按钮样式 */
.secondary-action-btn {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 48px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 140px;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
  transform: translateY(-1px);
}

/* 说明区域样式 */
.info-section {
  margin-bottom: var(--space-lg);
  padding: var(--space-md);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

/* 验收表单样式 */
.acceptance-form {
  padding: 20px;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #1890ff;
  font-weight: 600;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.form-hint {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.form-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 部门表格区域样式 */
.department-tables {
  margin-top: var(--space-xl);
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 标签页样式 */
.department-tabs {
  padding: var(--space-lg);
  width: 100%;
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav) {
  margin-bottom: var(--space-lg);
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav-wrap) {
  overflow: hidden;
}

:deep(.department-tabs .ant-tabs-nav-list) {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  scrollbar-width: thin;
  scrollbar-color: var(--border-light) transparent;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar) {
  height: 4px;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-thumb) {
  background: var(--border-light);
  border-radius: 2px;
}

:deep(.department-tabs .ant-tabs-nav-list::-webkit-scrollbar-thumb:hover) {
  background: var(--primary-color);
}

:deep(.department-tabs .ant-tabs-tab) {
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  transition: var(--transition-normal);
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 120px;
  text-align: center;
}

:deep(.department-tabs .ant-tabs-tab-active) {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

:deep(.department-tabs .ant-tabs-tab-active .ant-tabs-tab-btn) {
  color: var(--text-inverse);
}

:deep(.department-tabs .ant-tabs-content-holder) {
  background: var(--bg-primary);
  border-radius: 0 var(--radius-sm) var(--radius-sm) var(--radius-sm);
  border: 1px solid var(--border-light);
  padding: var(--space-lg);
  width: 100%;
  overflow-x: auto;
}

/* 照片预览 - 页面特有样式 */
.photo-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.photo-preview .ant-image) {
  border-radius: var(--radius-sm);
  overflow: hidden;
  border: 2px solid var(--border-light);
  transition: var(--transition-normal);
}

:deep(.photo-preview .ant-image:hover) {
  border-color: var(--success-color);
  transform: scale(1.05);
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .acceptance-tables {
    margin-top: var(--space-lg);
  }

  .table-header {
    padding: var(--space-md) var(--space-lg);
  }

  .table-title {
    font-size: var(--text-md);
    gap: var(--space-xs);
  }

  .table-title .anticon {
    font-size: var(--text-md);
  }

  .table-subtitle {
    font-size: var(--text-xs);
  }

  .unified-filter-section {
    padding: var(--space-md) var(--space-lg);
  }

  .action-buttons-section {
    padding: var(--space-lg) var(--space-lg);
  }

  .action-buttons-section .ant-space {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .acceptance-tables {
    margin-top: var(--space-md);
  }

  .table-header {
    padding: var(--space-sm) var(--space-md);
    text-align: center;
  }

  .table-title {
    font-size: var(--text-sm);
    justify-content: center;
    gap: var(--space-xs);
  }

  .table-title .anticon {
    font-size: var(--text-sm);
  }

  .table-subtitle {
    font-size: 11px;
    line-height: 1.4;
    margin-top: var(--space-xs);
  }

  .unified-filter-section {
    padding: var(--space-sm) var(--space-md);
  }

  .unified-filter-section .ant-row {
    flex-direction: column;
  }

  .unified-filter-section .ant-col {
    width: 100% !important;
    margin-bottom: var(--space-sm);
  }

  .action-buttons-section {
    padding: var(--space-md) var(--space-md);
  }

  .primary-action-btn,
  .secondary-action-btn {
    width: 100%;
    margin-bottom: var(--space-sm);
    min-width: auto;
  }

  .action-buttons-section .ant-space {
    width: 100%;
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .table-header {
    padding: var(--space-xs) var(--space-sm);
    text-align: center;
  }

  .table-title {
    font-size: 12px;
    justify-content: center;
    flex-direction: column;
    gap: 2px;
  }

  .table-title .anticon {
    font-size: 14px;
  }

  .table-subtitle {
    font-size: 10px;
    line-height: 1.3;
    margin-top: 2px;
    padding: 0 var(--space-xs);
  }

  .primary-action-btn,
  .secondary-action-btn {
    height: 40px;
    font-size: var(--text-xs);
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .table-header {
    padding: 6px 8px;
  }

  .table-title {
    font-size: 11px;
    gap: 1px;
  }

  .table-title .anticon {
    font-size: 12px;
  }

  .table-subtitle {
    font-size: 9px;
    line-height: 1.2;
  }

  .unified-filter-section {
    padding: 6px 8px;
  }

  .action-buttons-section {
    padding: 8px;
  }
}



/* 筛选区按钮高度对齐 */
.unified-filter-section .secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-filter-section .secondary-action-btn:hover {
  border-color: var(--primary-light);
}

/* 导出配置对话框样式 */
.export-config {
  padding: 16px 0;
}

.export-fields h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 600;
}

.export-filters .ant-form-item {
  margin-bottom: 16px;
}

.export-preview {
  min-height: 300px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-weight: 600;
}

.preview-summary {
  margin-top: 16px;
}

.export-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
  text-align: right;
}

/* 打印配置对话框样式 */
.print-config {
  padding: 16px 0;
}

.print-fields h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 600;
}

.print-filters h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 600;
}

.print-filters .ant-form-item {
  margin-bottom: 16px;
}

.print-preview {
  min-height: 300px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-weight: 600;
}

.preview-summary {
  margin-top: 16px;
}

.print-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
  text-align: right;
}

.photo-upload-section {
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.photo-upload-item h5 {
  margin-bottom: 12px;
  color: #333;
  font-weight: 600;
}

.upload-hint {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

/* 新的照片上传样式 */
.upload-description {
  margin: 8px 0 16px 0;
  color: #8c8c8c;
  font-size: 14px;
}

.photo-upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.photo-upload-item {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8e8e8;
}

.photo-label {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.label-text {
  color: #262626;
  font-weight: 500;
  font-size: 14px;
}

.required-mark {
  color: #ff4d4f;
  margin-left: 4px;
}

.photo-order-section {
  margin-top: 20px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.photo-order-section h5 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.photo-order-list {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.photo-order-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #d4edda;
}

.order-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
}

.order-photo {
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.photo-type {
  color: #262626;
  font-size: 12px;
  font-weight: 500;
}

.order-actions {
  display: flex;
  gap: 4px;
}

.upload-tips {
  margin-top: 16px;
  padding: 12px;
  background: #f0f8ff;
  border-radius: 6px;
  border: 1px solid #d4edda;
}

.upload-tips p {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 14px;
}

.upload-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #595959;
  font-size: 13px;
}

.upload-tips li {
  margin-bottom: 4px;
}

.upload-tips li:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .photo-upload-grid {
    grid-template-columns: 1fr;
  }

  .photo-order-list {
    flex-direction: column;
    align-items: flex-start;
  }

  .photo-order-item {
    width: 100%;
  }
}

/* 表格标题区域样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.record-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-filters {
  margin-top: 8px;
  padding: 12px 0;
  border-top: 1px solid var(--border-light);
}

.table-actions {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

/* 详细筛选控件样式 */
.detailed-filters {
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.filter-select,
.filter-input,
.filter-date-picker {
  height: 32px;
  font-size: var(--text-sm);
}

.budget-range-input {
  width: 200px;
}

.budget-range-input .ant-input {
  height: 32px;
  font-size: var(--text-sm);
}



/* 紧凑型操作按钮样式 */
.compact-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .table-actions .ant-row {
    width: 100%;
  }

  .detailed-filters .ant-row {
    flex-wrap: wrap;
  }

  .detailed-filters .ant-col {
    margin-bottom: 8px;
  }

  .detailed-filters .filter-select,
  .detailed-filters .filter-input,
  .detailed-filters .filter-date-picker {
    width: 100% !important;
    min-width: 150px;
  }

  .budget-range-input {
    width: 100% !important;
  }

  .column-filter-panel {
    width: auto;
    min-width: 400px;
    max-width: 90vw;
    padding: 12px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .preset-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .preset-trigger {
    flex: 1;
    margin-right: 8px;
  }

  .preset-actions {
    display: flex;
    gap: 4px;
  }

  .filter-tip {
    margin-bottom: 12px;
    padding: 6px 8px;
    background: transparent;
    font-size: 12px;
    color: #666;
  }

  .field-categories-container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 16px;
    align-items: flex-start;
  }

  .field-category-section {
    flex: 0 0 auto;
    min-width: 120px;
  }

  .category-title {
    margin: 0 0 6px 0;
    font-size: 13px;
    font-weight: 600;
    color: #666;
  }

  .category-fields {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .column-option {
    display: flex;
    align-items: center;
  }

  .column-title {
    font-size: 12px;
  }

  /* 导出字段选择样式 - 与字段筛选保持一致 */
  .export-field-category {
    margin-bottom: 12px;
  }

  .export-category-title {
    margin: 0 0 6px 0;
    font-weight: 600;
    font-size: 13px;
    color: #666;
  }

  .export-category-fields {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 6px;
  }

  .export-field-option {
    display: flex;
    align-items: center;
  }
}

.table-container {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 统一表格样式 - 基于需求提报页面 */
.unified-table :deep(.ant-table-tbody > tr > td) {
  white-space: nowrap !important;
  text-align: center !important;
  padding: var(--space-md) var(--space-lg) !important;
  border-bottom: 1px solid var(--border-light);
  font-size: var(--text-sm);
  color: var(--text-primary);
  height: 52px;
  vertical-align: middle;
}

.unified-table :deep(.ant-table-thead > tr > th) {
  text-align: center !important;
  white-space: nowrap !important;
  padding: var(--space-md) var(--space-lg) !important;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-bottom: 2px solid var(--border-light);
  color: var(--text-secondary);
  font-weight: 600;
  font-size: var(--text-sm);
  height: 48px;
}

.unified-table :deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5 !important;
}

/* 操作列固定样式 - 修复透视问题 */
.unified-table :deep(.ant-table-cell-fix-right) {
  background: #fff !important;
  z-index: 2 !important;
  position: sticky !important;
  right: 0 !important;
  border-left: 1px solid var(--border-light) !important;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1) !important;
}

.unified-table :deep(.ant-table-thead .ant-table-cell-fix-right) {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%) !important;
  z-index: 3 !important;
  border-left: 1px solid var(--border-light) !important;
}

.unified-table :deep(.ant-table-tbody > tr:hover > td.ant-table-cell-fix-right) {
  background: #f5f5f5 !important;
  z-index: 2 !important;
}

.unified-table :deep(.action-column) {
  border-left: 1px solid var(--border-light) !important;
}

/* 强化操作列透视修复 */
.unified-table :deep(.ant-table-cell-fix-right::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  z-index: -1;
}

.unified-table :deep(.ant-table-tbody > tr > td.ant-table-cell-fix-right) {
  position: relative;
  overflow: hidden;
}

/* 自适应列宽 */
.unified-table :deep(.ant-table-column-title) {
  white-space: nowrap;
}

.unified-table :deep(.ant-table) {
  table-layout: auto !important;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.unified-table :deep(.ant-table-thead > tr > th) {
  text-align: center !important;
  white-space: nowrap !important;
  padding: var(--space-md) var(--space-lg) !important;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-bottom: 2px solid var(--border-light);
  color: var(--text-secondary);
  font-weight: 600;
  font-size: var(--text-sm);
  min-width: fit-content;
  width: auto;
}

.unified-table :deep(.ant-table-tbody > tr > td) {
  min-width: fit-content;
  width: auto;
}


</style>
