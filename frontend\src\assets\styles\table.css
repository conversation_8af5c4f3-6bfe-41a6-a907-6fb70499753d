/* 统一表格样式 */

/* 页面容器 */
.page-container {
  padding: 0;
  background: transparent;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  letter-spacing: 0.5px;
}

.page-title .anticon {
  font-size: 32px;
}

.page-subtitle {
  font-size: var(--text-base);
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
  padding: var(--space-lg) var(--space-xl);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.9;
  font-weight: 500;
}

/* 搜索和筛选区域 */
.filter-section {
  padding: var(--space-xl) var(--space-2xl);
  margin-bottom: var(--space-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.search-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: var(--space-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--text-base);
  z-index: 1;
}

/* 搜索输入框样式 */
.search-input {
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.search-input .ant-input {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: var(--transition-normal);
  padding-left: 48px;
}

.search-input .ant-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.search-input .ant-input:hover {
  border-color: var(--primary-light);
}

/* 筛选表单样式 */
.filter-form {
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.filter-form .ant-form-item {
  margin-bottom: 0;
}

.filter-form .ant-form-item-label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* 下拉选择框样式 */
.filter-select .ant-select-selector {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.filter-select .ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.filter-select .ant-select-selector:hover {
  border-color: var(--primary-light);
}

/* 金额输入框样式 */
.amount-input .ant-input-number {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.amount-input .ant-input-number:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.amount-input .ant-input-number:hover {
  border-color: var(--primary-light);
}

.amount-range {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.range-separator .ant-input {
  border: 2px solid var(--border-light);
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  text-align: center;
  width: 40px;
  border-radius: var(--radius-sm);
}

/* 日期选择器样式 */
.date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-2xl);
  margin-bottom: var(--space-xl);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.action-left {
  display: flex;
  gap: var(--space-md);
}

.action-right {
  display: flex;
  gap: var(--space-md);
}

/* 按钮样式 */
.btn-primary {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
  color: var(--text-inverse);
}

.btn-secondary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.btn-secondary:hover {
  border-color: var(--primary-dark);
  color: var(--primary-dark);
  background: rgba(30, 58, 138, 0.05);
  transform: translateY(-1px);
}

.btn-default {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.btn-default:hover {
  border-color: var(--border-medium);
  color: var(--text-primary);
  background: var(--bg-secondary);
}

.btn-danger {
  border: 2px solid var(--error-color);
  color: var(--error-color);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.btn-danger:hover {
  border-color: var(--error-dark);
  color: var(--error-dark);
  background: rgba(239, 68, 68, 0.05);
  transform: translateY(-1px);
}

/* 表格区域 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--bg-primary);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

/* 表格样式 */
.unified-table,
.business-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.unified-table .ant-table-thead > tr > th,
.business-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-bottom: 2px solid var(--border-light);
  color: var(--text-secondary);
  font-weight: 600;
  font-size: var(--text-sm);
  letter-spacing: 0.3px;
  padding: var(--space-md) var(--space-lg);
  position: relative;
  height: 48px;
}

.unified-table .ant-table-thead > tr > th::before,
.business-table .ant-table-thead > tr > th::before {
  display: none;
}

.unified-table .ant-table-tbody > tr,
.business-table .ant-table-tbody > tr {
  transition: var(--transition-normal);
}

.unified-table .ant-table-tbody > tr:hover > td,
.business-table .ant-table-tbody > tr:hover > td,
.ant-table-tbody > tr:hover > td {
  background: var(--bg-hover-light) !important;
}

.unified-table .ant-table-tbody > tr > td,
.business-table .ant-table-tbody > tr > td {
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  font-size: var(--text-sm);
  color: var(--text-primary);
  height: 52px;
  vertical-align: middle;
}

/* 紧凑表格样式 - 用于系统设置页面 */
.compact-table .ant-table-thead > tr > th {
  padding: var(--space-sm) var(--space-md);
  height: 40px;
  font-size: var(--text-xs);
}

.compact-table .ant-table-tbody > tr > td {
  padding: var(--space-sm) var(--space-md);
  height: 44px;
  font-size: var(--text-xs);
}

/* 操作列按钮样式优化 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  align-items: center;
  justify-content: flex-start;
}

.action-buttons .ant-btn {
  height: 28px;
  padding: 0 var(--space-sm);
  font-size: var(--text-xs);
  line-height: 1.2;
  border-radius: var(--radius-sm);
}

.action-buttons .ant-btn-link {
  padding: 0 var(--space-xs);
  height: auto;
  min-height: 24px;
}

.action-buttons .ant-popconfirm .ant-btn {
  color: #ff4d4f;
}

.action-buttons .ant-popconfirm .ant-btn:hover {
  color: #ff7875;
  background: rgba(255, 77, 79, 0.1);
}

/* 分页样式 */
.unified-pagination,
.ant-pagination {
  margin: var(--space-2xl) 0 0 0;
  text-align: center;
}

.unified-pagination .ant-pagination-item,
.ant-pagination .ant-pagination-item {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-pagination .ant-pagination-item:hover,
.ant-pagination .ant-pagination-item:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.unified-pagination .ant-pagination-item-active,
.ant-pagination .ant-pagination-item-active {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
}

.unified-pagination .ant-pagination-item-active a,
.ant-pagination .ant-pagination-item-active a {
  color: var(--text-inverse);
}

/* 状态标签样式 */
.status-tag {
  border-radius: 20px;
  font-weight: 500;
  font-size: var(--text-xs);
  padding: 4px 12px;
  border: none;
  letter-spacing: 0.3px;
}

/* 操作按钮样式 */
.action-btn,
.ant-btn-link {
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition-normal);
  border-radius: var(--radius-sm);
  padding: 4px 8px;
  font-size: var(--text-sm);
}

.action-btn:hover,
.ant-btn-link:hover {
  color: var(--primary-dark);
  background: var(--bg-hover-light);
}

.action-btn.danger,
.ant-btn-link.ant-btn-dangerous {
  color: var(--error-color);
}

.action-btn.danger:hover,
.ant-btn-link.ant-btn-dangerous:hover {
  color: var(--error-dark);
  background: rgba(239, 68, 68, 0.1);
}
