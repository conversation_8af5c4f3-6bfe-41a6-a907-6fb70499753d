<template>
  <div class="role-management">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <SafetyCertificateOutlined />
          角色管理
        </h1>
        <p class="page-subtitle">
          管理系统角色，配置权限和用户关联，控制系统访问权限
        </p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalCount }}</span>
          <span class="stat-label">角色总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ activeCount }}</span>
          <span class="stat-label">启用角色</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ systemCount }}</span>
          <span class="stat-label">系统角色</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ customCount }}</span>
          <span class="stat-label">自定义角色</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ userCount }}</span>
          <span class="stat-label">关联用户</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="unified-filter-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-input
            v-model:value="searchTerm"
            placeholder="角色名称、编码、描述"
            allow-clear
            @pressEnter="searchRoles"
            class="filter-input"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select
            v-model:value="typeFilter"
            placeholder="选择类型"
            allowClear
            @change="handleTypeFilterChange"
            class="filter-select"
          >
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option :value="true">系统角色</a-select-option>
            <a-select-option :value="false">自定义角色</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select
            v-model:value="statusFilter"
            placeholder="选择状态"
            allowClear
            @change="handleStatusFilterChange"
            class="filter-select"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">停用</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="searchRoles" class="secondary-action-btn">
            <SearchOutlined />
            搜索
          </a-button>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="resetSearch" class="secondary-action-btn">
            <ReloadOutlined />
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 操作栏 -->
    <div class="action-buttons-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col>
          <a-button
            type="primary"
            @click="showAddModal"
            class="primary-action-btn"
          >
            <PlusOutlined />
            新增角色
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="exportRoles" class="secondary-action-btn">
            <DownloadOutlined />
            导出角色
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="showImportModal" class="secondary-action-btn">
            <UploadOutlined />
            批量导入
          </a-button>
        </a-col>
        <!-- 批量操作按钮 -->
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button
            type="primary"
            @click="batchEnable"
            class="primary-action-btn"
          >
            <CheckCircleOutlined />
            批量启用 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button @click="batchDisable" class="secondary-action-btn">
            <StopOutlined />
            批量停用 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button danger @click="batchDelete" class="danger-action-btn">
            <DeleteOutlined />
            批量删除 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 角色列表 -->
    <div class="table-section business-card">
      <a-table
        :columns="columns"
        :data-source="roles"
        :row-key="
          (record) => {
            return record.id;
          }
        "
        :row-selection="rowSelection"
        :pagination="paginationConfig"
        @change="handleTableChange"
        class="unified-table"
        size="large"
        :scroll="{ x: 1450 }"
      >
        <template #bodyCell="{ column, record }">
          <!-- 角色编码显示 -->
          <template v-if="column.dataIndex === 'code'">
            <code class="role-code">{{ record.code }}</code>
          </template>

          <!-- 角色类型显示 -->
          <template v-if="column.dataIndex === 'is_system'">
            <a-tag :color="record.is_system ? 'blue' : 'green'">
              <SafetyCertificateOutlined v-if="record.is_system" />
              <UserOutlined v-else />
              {{ record.is_system ? "系统角色" : "自定义角色" }}
            </a-tag>
          </template>

          <!-- 权限数量显示 -->
          <template v-if="column.dataIndex === 'permission_count'">
            <a-badge
              :count="record.permission_count || 0"
              :number-style="{ backgroundColor: '#1890ff' }"
            />
          </template>

          <!-- 用户数量显示 -->
          <template v-if="column.dataIndex === 'user_count'">
            <a-badge
              :count="record.user_count || 0"
              :number-style="{ backgroundColor: '#52c41a' }"
            />
          </template>

          <!-- 状态显示 -->
          <template v-if="column.dataIndex === 'is_active'">
            <a-tag :color="record.is_active ? 'success' : 'error'">
              <CheckCircleOutlined v-if="record.is_active" />
              <StopOutlined v-else />
              {{ record.is_active ? "启用" : "停用" }}
            </a-tag>
          </template>

          <!-- 创建时间显示 -->
          <template v-if="column.dataIndex === 'created_at'">
            <span v-if="record.created_at" class="created-time">
              <ClockCircleOutlined />
              {{ formatDateTime(record.created_at) }}
            </span>
            <span v-else class="no-time">未知</span>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" @click="viewDetail(record)" size="small">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="link" @click="editRole(record)" size="small">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="link" @click="manageUsers(record)" size="small">
                <UserOutlined />
                管理用户
              </a-button>
              <a-popconfirm
                title="确定要删除这个角色吗？"
                @confirm="deleteRole(record)"
                ok-text="确定"
                cancel-text="取消"
                :disabled="record.is_system"
              >
                <a-button
                  type="link"
                  danger
                  size="small"
                  :disabled="record.is_system"
                >
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增/编辑角色模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEditMode ? '编辑角色' : '新增角色'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      ok-text="确定"
      cancel-text="取消"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="角色名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入角色名称" />
        </a-form-item>
        <a-form-item label="角色编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入角色编码" />
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入描述信息"
            rows="3"
          />
        </a-form-item>
        <a-form-item label="是否系统角色" name="is_system">
          <a-switch v-model:checked="formData.is_system" />
          <div class="form-help-text">系统角色不能删除</div>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formData.status" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量导入模态框 -->
    <a-modal
      v-model:open="importModalVisible"
      title="批量导入角色"
      @ok="handleImport"
      @cancel="handleImportCancel"
      width="600px"
      :confirm-loading="importLoading"
      ok-text="导入"
      cancel-text="取消"
    >
      <div class="import-section">
        <a-alert
          message="导入说明"
          description="请下载模板文件，按照模板格式填写角色信息后上传。支持Excel格式(.xlsx)文件。"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        />

        <div style="margin-bottom: 16px">
          <a-button @click="downloadTemplate" type="link">
            <DownloadOutlined />
            下载角色导入模板
          </a-button>
        </div>

        <a-upload-dragger
          v-model:fileList="importFileList"
          :before-upload="beforeUpload"
          :remove="handleRemoveFile"
          accept=".xlsx,.xls"
          :multiple="false"
        >
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持单个文件上传，仅支持Excel格式(.xlsx, .xls)
          </p>
        </a-upload-dragger>

        <div v-if="importResult" class="import-result" style="margin-top: 16px">
          <a-alert
            :message="importResult.success ? '导入成功' : '导入失败'"
            :description="importResult.message"
            :type="importResult.success ? 'success' : 'error'"
            show-icon
          />
        </div>
      </div>
    </a-modal>

    <!-- 角色详情模态框 -->
    <a-modal
      v-model:open="detailVisible"
      title="角色详情"
      :footer="null"
      width="800px"
      class="role-detail-modal"
    >
      <div class="role-detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="角色名称">
            <span class="detail-value">{{ currentRole.name }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="角色编码">
            <code class="role-code">{{ currentRole.code }}</code>
          </a-descriptions-item>
          <a-descriptions-item label="角色类型">
            <a-tag :color="currentRole.is_system ? 'blue' : 'green'">
              <SafetyCertificateOutlined v-if="currentRole.is_system" />
              <UserOutlined v-else />
              {{ currentRole.is_system ? "系统角色" : "自定义角色" }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="currentRole.is_active ? 'success' : 'error'">
              <CheckCircleOutlined v-if="currentRole.is_active" />
              <StopOutlined v-else />
              {{ currentRole.is_active ? "启用" : "停用" }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="权限数量">
            <a-badge
              :count="currentRole.permission_count || 0"
              :number-style="{ backgroundColor: '#1890ff' }"
            />
          </a-descriptions-item>
          <a-descriptions-item label="关联用户">
            <a-badge
              :count="currentRole.user_count || 0"
              :number-style="{ backgroundColor: '#52c41a' }"
            />
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            <span v-if="currentRole.created_at" class="detail-value">
              {{ formatDateTime(currentRole.created_at) }}
            </span>
            <span v-else class="detail-empty">未知</span>
          </a-descriptions-item>
          <a-descriptions-item label="最后更新">
            <span v-if="currentRole.updated_at" class="detail-value">
              {{ formatDateTime(currentRole.updated_at) }}
            </span>
            <span v-else class="detail-empty">未知</span>
          </a-descriptions-item>
          <a-descriptions-item label="描述" :span="2">
            <span class="detail-value">{{
              currentRole.description || "无描述"
            }}</span>
          </a-descriptions-item>
        </a-descriptions>

        <div class="detail-actions">
          <a-space>
            <a-button @click="editRoleFromDetail(currentRole)" type="primary">
              <EditOutlined />
              编辑角色
            </a-button>
            <a-button @click="manageUsers(currentRole)">
              <UserOutlined />
              用户管理
            </a-button>
            <a-button @click="detailVisible = false"> 关闭 </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 用户管理模态框 -->
    <a-modal
      v-model:open="userManageVisible"
      :title="userManageTitle"
      @ok="handleUserManageSubmit"
      @cancel="userManageVisible = false"
      width="900px"
      class="user-manage-modal"
      ok-text="确定"
      cancel-text="取消"
    >
      <div class="user-manage-content">
        <a-row :gutter="16">
          <a-col :span="12">
            <h4>已关联用户</h4>
            <a-list :data-source="roleUsers" size="small" class="user-list">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.real_name"
                    :description="item.username"
                  >
                    <template #avatar>
                      <a-avatar>
                        <UserOutlined />
                      </a-avatar>
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-button
                      type="link"
                      danger
                      size="small"
                      @click="removeUserFromRole(item)"
                    >
                      移除
                    </a-button>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </a-col>
          <a-col :span="12">
            <h4>可添加用户</h4>
            <a-transfer
              v-model:target-keys="selectedUsers"
              :data-source="availableUsers"
              :titles="['可选用户', '待添加用户']"
              :render="(item) => item.title"
              :list-style="{ width: '100%', height: '300px' }"
              show-search
              :filter-option="
                (inputValue, option) =>
                  option.title.toLowerCase().includes(inputValue.toLowerCase())
              "
            />
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { message, Modal } from "ant-design-vue";
import {
  SafetyCertificateOutlined,
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  InboxOutlined,
  UserOutlined,
  CheckCircleOutlined,
  StopOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons-vue";
import api from "@/api";

// 搜索关键词
const searchTerm = ref("");
// 类型筛选
const typeFilter = ref("");
// 状态筛选
const statusFilter = ref("");

// 批量操作相关
const selectedRowKeys = ref([]);

// 导入相关
const importModalVisible = ref(false);
const importLoading = ref(false);
const importFileList = ref([]);
const importResult = ref(null);

// 表单引用
const formRef = ref(null);

// 模态框控制
const modalVisible = ref(false);
// 是否为编辑模式
const isEditMode = ref(false);

// 表单数据
const formData = ref({
  id: null,
  name: "",
  code: "",
  description: "",
  status: true,
  is_system: false,
});

// 角色列表
const roles = ref([]);

// 计算属性
const totalCount = computed(() => {
  return roles.value.length;
});

const activeCount = computed(() => {
  return roles.value.filter((role) => role.is_active).length;
});

const systemCount = computed(() => {
  return roles.value.filter((role) => role.is_system).length;
});

const customCount = computed(() => {
  return roles.value.filter((role) => !role.is_system).length;
});

const userCount = computed(() => {
  return roles.value.reduce((total, role) => total + (role.user_count || 0), 0);
});

const userManageTitle = computed(() => {
  return `管理角色 "${currentRole.value.name || ""}" 的用户`;
});

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: (record) => ({
    disabled: record.is_system, // 系统角色不能批量操作
  }),
};

// 表格列定义
const columns = [
  {
    title: "序号",
    key: "index",
    width: 60,
    customRender: ({ index }) =>
      pagination.pageSize * (pagination.current - 1) + index + 1,
  },
  {
    title: "角色名称",
    dataIndex: "name",
    width: 140,
    sorter: true,
  },
  {
    title: "角色编码",
    dataIndex: "code",
    width: 120,
    sorter: true,
  },
  {
    title: "角色类型",
    dataIndex: "is_system",
    width: 110,
    align: "center",
  },
  {
    title: "描述",
    dataIndex: "description",
    width: 180,
    ellipsis: true,
  },
  {
    title: "权限数量",
    dataIndex: "permission_count",
    width: 90,
    align: "center",
  },
  {
    title: "用户数量",
    dataIndex: "user_count",
    width: 90,
    align: "center",
  },
  {
    title: "状态",
    dataIndex: "is_active",
    width: 80,
    align: "center",
  },
  {
    title: "创建时间",
    dataIndex: "created_at",
    width: 140,
    sorter: true,
  },
  {
    title: "操作",
    dataIndex: "action",
    width: 300,
    fixed: "right",
    align: "center",
  },
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
});

const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: pagination.showSizeChanger,
  showQuickJumper: pagination.showQuickJumper,
  showTotal: pagination.showTotal,
  pageSizeOptions: ["10", "20", "50", "100"],
}));

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入角色名称" }],
  code: [{ required: true, message: "请输入角色编码" }],
};

// 获取角色列表
const getRoles = async () => {
  try {
    const params = {
      page: pagination.current,
      page_size: pagination.pageSize,
    };

    // 添加搜索参数
    if (searchTerm.value) {
      params.search = searchTerm.value;
    }

    // 添加类型筛选参数
    if (typeFilter.value !== "") {
      params.is_system = typeFilter.value;
    }

    // 添加状态筛选参数
    if (statusFilter.value !== "") {
      params.is_active = statusFilter.value;
    }

    const response = await api.roles.getList(params);

    if (response.code === 200) {
      const roleData = response.data.results || response.data || [];
      roles.value = roleData;
      pagination.total = response.data.count || 0;
    }
  } catch (error) {
    console.error("获取角色列表失败:", error);
    message.error("获取角色列表失败");
  }
};

// 显示新增角色模态框
const showAddModal = () => {
  isEditMode.value = false;
  formData.value = {
    id: null,
    name: "",
    code: "",
    description: "",
    status: true,
    is_system: false,
    menuPermissions: [],
    apiPermissions: [],
  };
  modalVisible.value = true;
};

// 编辑角色
const editRole = (record) => {
  if (!record) {
    message.error("角色信息为空，请刷新页面重试");
    return;
  }

  // 确保使用数字ID而不是字符串
  let roleId = record.id;

  // 如果ID是字符串，尝试转换为数字
  if (typeof roleId === "string" && !isNaN(roleId)) {
    roleId = parseInt(roleId, 10);
  }

  if (!roleId) {
    message.error("角色ID无效，请刷新页面重试");
    return;
  }

  isEditMode.value = true;
  formData.value = {
    id: roleId,
    name: record.name,
    code: record.code,
    description: record.description,
    status: record.is_active !== undefined ? record.is_active : record.status,
    is_system: record.is_system || false,
  };
  modalVisible.value = true;
};

// 从详情页面编辑角色
const editRoleFromDetail = (record) => {
  // 先关闭详情弹出框
  detailVisible.value = false;
  // 然后打开编辑弹出框
  setTimeout(() => {
    editRole(record);
  }, 100);
};

// 提交表单
const handleSubmit = async () => {
  try {
    const values = await formRef.value.validateFields();
    let response;
    if (isEditMode.value) {
      if (!formData.value.id) {
        message.error("角色ID不能为空，请重新选择角色进行编辑");
        return;
      }
      // 更新角色信息
      response = await api.roles.update(formData.value.id, values);
    } else {
      // 创建新角色
      response = await api.roles.create(values);
    }

    if (response.code === 200) {
      message.success(isEditMode.value ? "更新成功" : "创建成功");
      modalVisible.value = false;
      getRoles();
    }
  } catch (error) {
    message.error("操作失败");
  }
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  getRoles();
};

// 搜索角色
const searchRoles = () => {
  pagination.current = 1;
  getRoles();
};

// 重置搜索
const resetSearch = () => {
  searchTerm.value = "";
  typeFilter.value = "";
  statusFilter.value = "";
  pagination.current = 1;
  getRoles();
};

// 类型筛选变化处理
const handleTypeFilterChange = () => {
  pagination.current = 1;
  getRoles();
};

// 状态筛选变化处理
const handleStatusFilterChange = () => {
  pagination.current = 1;
  getRoles();
};

// 导出角色
const exportRoles = async () => {
  try {
    const params = {};

    // 添加搜索参数
    if (searchTerm.value) {
      params.search = searchTerm.value;
    }

    // 添加类型筛选参数
    if (typeFilter.value !== "") {
      params.type = typeFilter.value === true ? "system" : "custom";
    }

    // 添加状态筛选参数
    if (statusFilter.value !== "") {
      params.status = statusFilter.value === true ? "active" : "inactive";
    }

    const response = await api.roles.export(params);

    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `角色数据_${new Date().toLocaleDateString()}.xlsx`
    );
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    message.success("导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    message.error("导出失败");
  }
};

// 显示导入模态框
const showImportModal = () => {
  importModalVisible.value = true;
  importFileList.value = [];
  importResult.value = null;
};

// 下载角色导入模板
const downloadTemplate = async () => {
  try {
    const response = await api.roles.downloadTemplate();

    // 检查响应数据
    if (!response || !response.data) {
      throw new Error("响应数据为空");
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob =
      response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error("下载的文件为空");
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "角色导入模板.xlsx");
    document.body.appendChild(link);
    link.click();
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    message.success("模板下载成功");
  } catch (error) {
    console.error("下载模板失败:", error);
    message.error(`下载模板失败: ${error.message || "未知错误"}`);
  }
};

// 文件上传前检查
const beforeUpload = (file) => {
  // 如果已经有文件，先清除
  if (importFileList.value.length > 0) {
    importFileList.value = [];
  }

  const isExcel =
    file.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    file.type === "application/vnd.ms-excel";
  if (!isExcel) {
    message.error("只能上传Excel文件!");
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error("文件大小不能超过10MB!");
    return false;
  }
  return false; // 阻止自动上传
};

// 移除文件
const handleRemoveFile = () => {
  importFileList.value = [];
  importResult.value = null;
};

// 处理导入
const handleImport = async () => {
  if (importFileList.value.length === 0) {
    message.warning("请选择要导入的文件");
    return;
  }

  importLoading.value = true;
  try {
    const file =
      importFileList.value[0].originFileObj || importFileList.value[0];
    const response = await api.roles.import(file);
    if (response.code === 200) {
      importResult.value = {
        success: true,
        message: `导入成功！共导入 ${response.data.success_count} 条记录`,
      };
      message.success("导入成功");
      getRoles(); // 刷新列表
      setTimeout(() => {
        importModalVisible.value = false;
      }, 2000);
    } else {
      importResult.value = {
        success: false,
        message: response.message || "导入失败",
      };
    }
  } catch (error) {
    console.error("导入失败:", error);
    importResult.value = {
      success: false,
      message: "导入失败，请检查文件格式",
    };
    message.error("导入失败");
  } finally {
    importLoading.value = false;
  }
};

// 取消导入
const handleImportCancel = () => {
  importModalVisible.value = false;
  importFileList.value = [];
  importResult.value = null;
};

// 角色详情模态框控制
const detailVisible = ref(false);
const currentRole = ref({});

// 查看详情
const viewDetail = (record) => {
  currentRole.value = { ...record };
  detailVisible.value = true;
};

// 用户管理模态框控制
const userManageVisible = ref(false);
const roleUsers = ref([]);
const availableUsers = ref([]);
const selectedUsers = ref([]);

// 管理用户
const manageUsers = async (record) => {
  currentRole.value = { ...record };
  userManageVisible.value = true;

  try {
    // 获取角色关联的用户
    const roleUsersResponse = await api.roles.getUsers(record.id);
    if (roleUsersResponse.code === 200) {
      roleUsers.value = roleUsersResponse.data || [];
    }

    // 获取所有可用用户
    const allUsersResponse = await api.users.getList({ page_size: 1000 });
    if (allUsersResponse.code === 200) {
      const allUsers =
        allUsersResponse.data.results || allUsersResponse.data || [];

      // 过滤出未关联到当前角色的用户，并转换为Transfer组件需要的格式
      const roleUserIds = roleUsers.value.map((user) => user.id);
      availableUsers.value = allUsers
        .filter((user) => !roleUserIds.includes(user.id))
        .map((user) => ({
          key: user.id,
          title: `${user.real_name} (${user.username})`,
          description: user.phone || user.email || "",
          ...user,
        }));

      // 清空已选择的用户
      selectedUsers.value = [];
    }
  } catch (error) {
    console.error("获取用户数据失败:", error);
    message.error("获取用户数据失败");
  }
};

// 批量启用角色
const batchEnable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要启用的角色");
    return;
  }

  try {
    const promises = selectedRowKeys.value.map((id) => {
      const role = roles.value.find((r) => r.id === id);
      return api.roles.update(id, { ...role, is_active: true });
    });

    await Promise.all(promises);
    message.success(`成功启用 ${selectedRowKeys.value.length} 个角色`);
    selectedRowKeys.value = [];
    getRoles();
  } catch (error) {
    console.error("批量启用失败:", error);
    message.error("批量启用失败");
  }
};

// 批量停用角色
const batchDisable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要停用的角色");
    return;
  }

  try {
    const promises = selectedRowKeys.value.map((id) => {
      const role = roles.value.find((r) => r.id === id);
      return api.roles.update(id, { ...role, is_active: false });
    });

    await Promise.all(promises);
    message.success(`成功停用 ${selectedRowKeys.value.length} 个角色`);
    selectedRowKeys.value = [];
    getRoles();
  } catch (error) {
    console.error("批量停用失败:", error);
    message.error("批量停用失败");
  }
};

// 批量删除角色
const batchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要删除的角色");
    return;
  }

  Modal.confirm({
    title: "批量删除确认",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个角色吗？此操作不可恢复。`,
    okText: "确定",
    cancelText: "取消",
    onOk: async () => {
      try {
        const promises = selectedRowKeys.value.map((id) =>
          api.roles.delete(id)
        );
        await Promise.all(promises);
        message.success(`成功删除 ${selectedRowKeys.value.length} 个角色`);
        selectedRowKeys.value = [];
        getRoles();
      } catch (error) {
        console.error("批量删除失败:", error);
        message.error("批量删除失败");
      }
    },
  });
};

// 删除角色
const deleteRole = async (record) => {
  Modal.confirm({
    title: "确认删除",
    content: `确定删除角色 ${record.name} 吗？`,
    okText: "确定",
    cancelText: "取消",
    okType: "danger",
    onOk: async () => {
      try {
        const response = await api.roles.delete(record.id);
        if (response.code === 200) {
          message.success("删除成功");
          getRoles();
        }
      } catch (error) {
        console.error("删除失败:", error);
        message.error("删除失败");
      }
    }
  });
};

// Removed unused getStatusColor function

// 从角色中移除用户
const removeUserFromRole = async (user) => {
  try {
    const response = await api.roles.removeUser(currentRole.value.id, user.id);
    if (response.code === 200) {
      message.success(`已将用户 ${user.real_name} 从角色中移除`);
      // 重新获取角色用户列表
      const roleUsersResponse = await api.roles.getUsers(currentRole.value.id);
      if (roleUsersResponse.code === 200) {
        roleUsers.value = roleUsersResponse.data || [];
      }
    } else {
      message.error(response.message || "移除用户失败");
    }
  } catch (error) {
    console.error("移除用户失败:", error);
    message.error("移除用户失败");
  }
};

// 处理用户管理提交
const handleUserManageSubmit = async () => {
  try {
    if (selectedUsers.value.length > 0) {
      const response = await api.roles.addUsers(
        currentRole.value.id,
        selectedUsers.value
      );
      if (response.code === 200) {
        message.success(`成功添加 ${selectedUsers.value.length} 个用户到角色`);
        selectedUsers.value = [];
        userManageVisible.value = false;
        // 重新获取角色列表以更新用户数量
        getRoles();
      } else {
        message.error(response.message || "添加用户失败");
      }
    } else {
      userManageVisible.value = false;
    }
  } catch (error) {
    console.error("添加用户失败:", error);
    message.error("添加用户失败");
  }
};

// 格式化时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return "";
  const date = new Date(dateTime);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 页面加载时初始化
onMounted(async () => {
  await getRoles();
});
</script>

<style scoped>
.role-management {
  padding: 0;
  background: transparent;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  letter-spacing: 0.5px;
}

.page-title .anticon {
  font-size: var(--text-3xl);
}

.page-subtitle {
  font-size: var(--text-base);
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: var(--space-xl);
}

.stat-item {
  text-align: center;
  padding: var(--space-md) var(--space-lg);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.9;
  font-weight: 500;
}

/* 搜索和筛选区域 */
.filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
}

.filter-form {
  gap: var(--space-lg);
}

:deep(.filter-form .ant-form-item) {
  margin-bottom: 0;
}

:deep(.filter-form .ant-form-item-label) {
  font-weight: 500;
  color: var(--text-secondary);
}

/* 搜索输入框样式 */
.search-input {
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.search-input .ant-input) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: all 0.3s;
}

:deep(.search-input .ant-input:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

:deep(.search-input .ant-input:hover) {
  border-color: var(--primary-light);
}

/* 按钮样式 */
.search-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.search-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.reset-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  background: var(--bg-primary);
  transition: all 0.3s;
}

.reset-btn:hover {
  border-color: var(--border-dark);
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
}

.action-left {
  display: flex;
  gap: var(--space-md);
}

.primary-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.secondary-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.secondary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-overlay);
}

/* 表格区域 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* 表格样式已在统一样式文件中定义 */

/* 角色编码样式 */
.role-code {
  background: var(--bg-tertiary);
  color: var(--primary-color);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-family: "Courier New", monospace;
  font-size: var(--text-xs);
  font-weight: 600;
  border: 1px solid var(--border-light);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

:deep(.action-buttons .ant-btn-link) {
  color: var(--primary-color);
  font-weight: 500;
  transition: all 0.3s;
  border-radius: var(--radius-sm);
  padding: var(--space-xs) var(--space-sm);
  height: auto;
  font-size: var(--text-sm);
}

:deep(.action-buttons .ant-btn-link:hover) {
  color: var(--primary-dark);
  background: var(--bg-overlay);
}

:deep(.action-buttons .ant-btn-link.ant-btn-dangerous) {
  color: var(--error-color);
}

:deep(.action-buttons .ant-btn-link.ant-btn-dangerous:hover) {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 状态标签优化 */
:deep(.ant-tag) {
  border-radius: 20px;
  font-weight: 500;
  font-size: var(--text-sm);
  padding: 4px var(--space-md);
  border: none;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-lg);
  }

  .header-stats {
    justify-content: center;
  }
}

/* 统一筛选区样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    var(--bg-primary) 100%
  );
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

.unified-filter-section .filter-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select {
  height: 32px;
}

.unified-filter-section .filter-select .ant-select-selector {
  height: 32px !important;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: all 0.3s;
}

.unified-filter-section .filter-select:hover .ant-select-selector {
  border-color: var(--primary-light);
}

.unified-filter-section .filter-select.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* 统一按钮样式 */
.action-buttons-section {
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.primary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: white;
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.secondary-action-btn:hover {
  border-color: var(--primary-light);
  color: var(--primary-color);
}

.danger-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--error-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--error-color);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.danger-action-btn:hover {
  border-color: #dc2626;
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 角色详情模态框样式 */
.role-detail-modal .role-detail-content {
  padding: var(--space-md) 0;
}

.detail-value {
  font-weight: 500;
  color: var(--text-primary);
}

.detail-empty {
  color: var(--text-tertiary);
  font-style: italic;
}

.detail-actions {
  margin-top: var(--space-lg);
  padding-top: var(--space-md);
  border-top: 1px solid var(--border-light);
  text-align: right;
}

:deep(.role-detail-modal .ant-descriptions-item-label) {
  font-weight: 600;
  color: var(--text-secondary);
  background: var(--bg-secondary);
}

:deep(.role-detail-modal .ant-descriptions-item-content) {
  background: var(--bg-primary);
}

/* 用户管理模态框样式 */
.user-manage-modal .user-manage-content {
  padding: var(--space-md) 0;
}

.user-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
}

.user-list .ant-list-item {
  padding: var(--space-sm) var(--space-md);
}

.user-list .ant-list-item:hover {
  background: var(--bg-secondary);
}

/* 创建时间显示样式 */
.created-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* 分页样式 - 参考日志管理页面 */
:deep(.ant-pagination) {
  margin: var(--space-lg) 0 0 0;
  text-align: right;
  padding: var(--space-md) 0;
}

:deep(.ant-pagination .ant-pagination-item) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s;
  margin: 0 4px;
}

:deep(.ant-pagination .ant-pagination-item:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

:deep(.ant-pagination .ant-pagination-item-active) {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

:deep(.ant-pagination .ant-pagination-item-active a) {
  color: var(--text-inverse);
  font-weight: 600;
}

:deep(.ant-pagination .ant-pagination-prev),
:deep(.ant-pagination .ant-pagination-next) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s;
}

:deep(.ant-pagination .ant-pagination-prev:hover),
:deep(.ant-pagination .ant-pagination-next:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

:deep(.ant-pagination .ant-pagination-options) {
  margin-left: var(--space-md);
}

:deep(.ant-pagination .ant-pagination-options .ant-select) {
  margin-right: var(--space-sm);
}

:deep(.ant-pagination .ant-pagination-total-text) {
  margin-right: var(--space-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.no-time {
  color: var(--text-tertiary);
  font-style: italic;
}
</style>