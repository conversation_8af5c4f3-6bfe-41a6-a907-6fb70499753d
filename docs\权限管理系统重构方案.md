# 采购管理系统权限管理重构方案

## 📋 方案概述

### 核心目标
基于当前采购管理系统的实际业务需求，实现一套完整的用户角色权限管理方案：

1. **统一采购业务模块**: 将需求提报→审批→采购→验收→报销作为一个完整的业务流程
2. **基于实际角色**: 使用数据库中的真实角色（super_admin、approver、requester等）
3. **用户-角色一对一**: 简化权限模型，一个用户对应一个角色
4. **动态菜单系统**: 移除硬编码菜单，实现基于权限的动态菜单
5. **数据库驱动**: 所有权限配置通过数据库管理

### 实际数据库角色
- 超级管理员 (super_admin)
- 采购人 (requester) 
- 审批人 (approver)
- 验收人 (acceptor)
- 财务 (finance)
- 申请人 (purchaser)
- 使用管理员 (user_admin)
- 一级部门审批人 (manager_approver)
- 管理员 (admin)

### 采购流程状态
```
draft → pending_approval → approved → purchased → accepted → settled
草稿     待审批           已审批      已采购      已验收      已结算
```

## 🏗️ 技术架构

### 权限模型设计
```
用户 (sys_user) 
    ↓ (一对一)
角色 (sys_role)
    ↓ (一对多)
    ├── 角色权限关联 (sys_role_permission) → 权限 (auth_permission)
    └── 角色菜单关联 (sys_role_menu) → 菜单 (sys_menu)
```

### 业务模块划分
1. **系统管理模块**: 用户管理、角色管理、权限管理
2. **采购管理模块**: 统一的采购业务流程
   - 采购概览（全流程数据）
   - 需求提报（draft→pending_approval）
   - 需求审批（pending_approval→approved）
   - 物资采购（approved→purchased）
   - 物资验收（purchased→accepted）
   - 结算报销（accepted→settled）
3. **个人中心模块**: 个人信息管理

## 📊 数据库设计

### 新增表结构

#### 1. 菜单表 (sys_menu)
```sql
CREATE TABLE `sys_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `parent_id` bigint DEFAULT NULL COMMENT '父菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `menu_code` varchar(50) NOT NULL COMMENT '菜单编码',
  `menu_type` varchar(10) NOT NULL DEFAULT 'menu' COMMENT '菜单类型：menu=菜单,button=按钮',
  `route_path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `component_path` varchar(200) DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `is_visible` tinyint(1) DEFAULT 1 COMMENT '是否显示',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `permission_code` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `business_status` varchar(50) DEFAULT NULL COMMENT '关联的业务状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_menu_code` (`menu_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_business_status` (`business_status`)
) COMMENT='系统菜单表';
```

#### 2. 角色菜单关联表 (sys_role_menu)
```sql
CREATE TABLE `sys_role_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_menu` (`role_id`, `menu_id`),
  CONSTRAINT `fk_role_menu_role` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_role_menu_menu` FOREIGN KEY (`menu_id`) REFERENCES `sys_menu` (`id`) ON DELETE CASCADE
) COMMENT='角色菜单关联表';
```

### 角色权限分配

#### 基于实际角色的权限设计
- **超级管理员**: 所有权限
- **审批人**: 采购审批权限 + 数据查看
- **采购人**: 需求提报权限 + 数据查看
- **申请人**: 物资采购权限 + 数据查看
- **验收人**: 物资验收权限 + 数据查看
- **财务**: 结算报销权限 + 数据查看
- **使用管理员**: 用户管理权限 + 数据查看

## 🔧 技术实现

### 后端实现要点
1. **权限验证中间件**: 基于角色的API权限控制
2. **菜单管理API**: 动态菜单数据获取
3. **状态权限控制**: 基于采购流程状态的权限验证
4. **缓存优化**: 权限查询缓存机制

### 前端实现要点
1. **动态菜单组件**: 基于权限的菜单渲染
2. **路由权限守卫**: 页面级权限验证
3. **权限控制组件**: 按钮级权限控制
4. **状态管理**: 用户权限和菜单状态管理

## 📅 实施计划

### 阶段划分
1. **阶段一**: 数据库结构优化 (1天)
2. **阶段二**: 后端权限系统重构 (2天)
3. **阶段三**: 前端动态菜单系统 (2天)
4. **阶段四**: 权限管理界面重构 (2天)
5. **阶段五**: 按钮级权限控制 (1天)
6. **阶段六**: 测试和优化 (1天)

### 风险控制
1. **数据备份**: 每个阶段前备份关键数据
2. **渐进式实施**: 保持现有功能可用
3. **回滚方案**: 每个阶段都有完整回滚策略
4. **功能验证**: 每步完成后验证功能完整性

## ✅ 质量保证

### 回滚安全措施
- 不修改现有核心表结构
- 保留原有API接口兼容性
- 前端功能开关控制
- 完整的数据备份策略

### 性能优化
- 基于角色的快速权限查询
- 菜单数据缓存机制
- 最小化数据库查询
- 前端组件懒加载

### 功能验证清单
- [ ] 用户登录和权限获取
- [ ] 菜单显示和路由跳转
- [ ] 页面权限验证
- [ ] 按钮权限控制
- [ ] 采购流程权限控制
- [ ] 系统管理功能
- [ ] 数据导入导出
- [ ] 性能表现

## 🎯 预期效果

1. **管理便捷**: 通过界面管理菜单和权限
2. **安全可靠**: 完整的权限验证体系
3. **用户友好**: 根据权限显示相应功能
4. **易于维护**: 清晰的代码结构和文档
5. **性能优良**: 优化的权限查询和缓存

---

**注意事项**:
- 严格按照阶段顺序实施
- 每个阶段完成后进行功能验证
- 遇到问题及时回滚到上一个稳定状态
- 保持与业务团队的沟通，确保需求理解正确
