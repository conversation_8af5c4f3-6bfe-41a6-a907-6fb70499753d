<template>
  <a-tree-select
    v-model:value="selectedDept"
    :tree-data="treeData"
    :loading="loading"
    placeholder="请选择部门"
    tree-default-expand-all
    :size="size"
    :style="style"
    :allow-clear="allowClear"
    :show-search="showSearch"
    :filter-tree-node="filterTreeNode"
    :tree-node-filter-prop="treeNodeFilterProp"
    :disabled="loading"
    @change="handleChange"
  >
    <template #title="{ dept_name, dept_code }">
      <div class="dept-tree-node">
        <span class="dept-name">{{ dept_name }}</span>
        <span class="dept-code">{{ dept_code }}</span>
      </div>
    </template>
  </a-tree-select>
</template>

<!-- eslint-disable vue/no-unused-vars -->
<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { message } from "ant-design-vue";
import api from "@/api";

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null,
  },
  size: {
    type: String,
    default: "middle",
  },
  style: {
    type: [String, Object],
    default: () => ({}),
  },
  allowClear: {
    type: Boolean,
    default: true,
  },
  showSearch: {
    type: Boolean,
    default: true,
  },
  // 是否返回层级路径而不是ID
  returnPath: {
    type: Boolean,
    default: false,
  },
  // 是否只能选择叶子节点
  leafOnly: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:modelValue", "change"]);

// 状态
const loading = ref(false);
const departments = ref([]);
const treeData = ref([]);

// 双向绑定
const selectedDept = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 树节点过滤属性
const treeNodeFilterProp = "title";

// 获取部门树数据
const getDepartmentTree = async () => {
  try {
    loading.value = true;
    const response = await api.departments.getTree();
    if (response.code === 200) {
      departments.value = response.data || [];
      treeData.value = buildTreeData(departments.value);
    }
  } catch (error) {
    console.error("获取部门树失败:", error);
    message.error("获取部门树失败");
  } finally {
    loading.value = false;
  }
};

// 构建树形数据
const buildTreeData = (depts) => {
  return depts.map((dept) => {
    const nodeValue = props.returnPath ? dept.hierarchy_path : dept.id;
    const hasChildren = dept.children && dept.children.length > 0;

    return {
      title: dept.dept_name,
      value: nodeValue,
      key: nodeValue, // 确保 key 和 value 一致
      dept_name: dept.dept_name,
      dept_code: dept.dept_code,
      hierarchy_path: dept.hierarchy_path,
      disabled: props.leafOnly && hasChildren, // 如果只能选择叶子节点且有子节点，则禁用
      children: hasChildren ? buildTreeData(dept.children) : [],
    };
  });
};

// 树节点过滤
const filterTreeNode = (inputValue, treeNode) => {
  return (
    treeNode.dept_name.toLowerCase().includes(inputValue.toLowerCase()) ||
    treeNode.dept_code.toLowerCase().includes(inputValue.toLowerCase()) ||
    treeNode.hierarchy_path.toLowerCase().includes(inputValue.toLowerCase())
  );
};

// 处理选择变化
const handleChange = (value, label, extra) => {
  const selectedNode = extra.triggerNode?.dataRef;
  emit("change", value, selectedNode);
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== selectedDept.value) {
      selectedDept.value = newValue;
    }
  }
);

onMounted(() => {
  getDepartmentTree();
});
</script>

<style scoped>
.dept-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dept-name {
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
}

.dept-code {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: "Courier New", monospace;
  margin-left: 8px;
}

:deep(.ant-select-tree-title) {
  width: 100%;
}
</style>
