<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>

    <!-- 全局错误处理脚本 -->
    <script>
      // 最强力的 ResizeObserver 错误抑制
      (function() {
        // 1. 重写 console.error
        const originalConsoleError = console.error;
        console.error = function(...args) {
          const message = args[0];
          if (typeof message === 'string' && message.includes('ResizeObserver loop completed')) {
            return; // 完全忽略
          }
          originalConsoleError.apply(console, args);
        };

        // 2. 重写 console.warn
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
          const message = args[0];
          if (typeof message === 'string' && message.includes('ResizeObserver loop completed')) {
            return; // 完全忽略
          }
          originalConsoleWarn.apply(console, args);
        };

        // 3. 全局错误事件处理
        window.addEventListener('error', function(e) {
          if (e.message && e.message.includes('ResizeObserver loop completed')) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            return false;
          }
        }, true);

        // 4. Promise 错误处理
        window.addEventListener('unhandledrejection', function(e) {
          if (e.reason && e.reason.message && e.reason.message.includes('ResizeObserver')) {
            e.preventDefault();
            return false;
          }
        });

        // 5. 重写 ResizeObserver
        if (window.ResizeObserver) {
          const OriginalResizeObserver = window.ResizeObserver;
          window.ResizeObserver = class extends OriginalResizeObserver {
            constructor(callback) {
              const wrappedCallback = (...args) => {
                try {
                  return callback(...args);
                } catch (e) {
                  if (e.message && e.message.includes('ResizeObserver loop completed')) {
                    return; // 忽略错误
                  }
                  throw e;
                }
              };
              super(wrappedCallback);
            }
          };
        }

        // 6. 拦截 webpack 错误处理
        if (window.__webpack_require__ && window.__webpack_require__.oe) {
          const originalOe = window.__webpack_require__.oe;
          window.__webpack_require__.oe = function(err) {
            if (err && err.message && err.message.includes('ResizeObserver loop completed')) {
              return; // 忽略错误
            }
            return originalOe(err);
          };
        }
      })();
    </script>

    <!-- built files will be auto injected -->
  </body>
</html>
