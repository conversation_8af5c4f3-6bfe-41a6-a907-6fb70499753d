# Generated manually to remove UserSession model

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0015_usersession_delete_dictdata_alter_systemlog_log_type_and_more'),
    ]

    operations = [
        # 删除UserSession表（MySQL语法）
        migrations.RunSQL(
            "DROP TABLE IF EXISTS user_session;",
            reverse_sql="-- 无法恢复表结构"
        ),
    ]
