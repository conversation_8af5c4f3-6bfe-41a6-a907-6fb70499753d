<template>
  <div class="purchase-overview">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <ShoppingCartOutlined />
          采购总览
        </h1>
        <p class="page-subtitle">查看本部门所有采购记录和统计数据</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalCount }}</span>
          <span class="stat-label">总记录数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ pendingCount }}</span>
          <span class="stat-label">待处理</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">¥{{ totalAmount }}</span>
          <span class="stat-label">总金额</span>
        </div>
      </div>
    </div>

    <!-- 统计图表区域 -->
    <div class="charts-section">
      <a-row :gutter="24">
        <a-col :span="8">
          <div class="chart-card business-card">
            <h3>状态分布</h3>
            <div id="statusChart" style="height: 300px;"></div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="chart-card business-card">
            <h3>采购类型</h3>
            <div id="purchaseTypeChart" style="height: 300px;"></div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="chart-card business-card">
            <h3>采购趋势（已结算）</h3>
            <div id="trendChart" style="height: 300px;"></div>
          </div>
        </a-col>
      </a-row>
    </div>



    <!-- 采购记录表格 -->
    <div class="table-section business-card">
      <!-- 表格标题和按钮区域 - 使用flex布局，两端对齐 -->
      <div class="table-header-flex">
        <div class="table-title-section">
          <div class="table-title">
            <span>采购记录列表</span>
            <span class="table-subtitle">共 {{ totalCount }} 条记录</span>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="table-actions">
          <a-space :size="8">
            <a-button @click="toggleFilters" size="small" class="compact-action-btn">
              <template #icon>
                <component :is="showFilters ? 'UpOutlined' : 'DownOutlined'" />
              </template>
              {{ showFilters ? '收起筛选' : '展开筛选' }}
            </a-button>
            <a-button @click="resetFilters" size="small" class="compact-action-btn">重置筛选</a-button>
            <a-button @click="showExportDialog" size="small" class="compact-action-btn">
              <DownloadOutlined />
              导出
            </a-button>
            <a-button size="small" class="compact-action-btn" @click="showColumnFilter = true">
              <template #icon>
                <SettingOutlined />
              </template>
              字段筛选
              <a-badge :count="selectedColumns.length"
                :number-style="{ backgroundColor: '#52c41a', fontSize: '10px' }" />
            </a-button>

            <!-- 字段筛选模态框 -->
            <a-modal v-model:open="showColumnFilter" title="字段筛选配置" :footer="null" width="auto" :centered="true"
              :mask-closable="true" :destroy-on-close="true" wrap-class-name="column-filter-modal">
              <template #closeIcon>
                <CloseOutlined />
              </template>
              <div class="column-filter-panel">
                <div class="preset-section"> <!-- 预设配置区域 -->
                  <a-dropdown v-model:open="presetDropdownOpen" :trigger="['click']" placement="bottomRight"
                    :overlay-style="{ zIndex: 9999 }" @click.stop>
                    <a-button class="preset-trigger" @click.stop size="large">
                      <template #icon>
                        <SettingOutlined />
                      </template>
                      预设配置
                      <DownOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu @click="handlePresetClick" @click.stop>
                        <template v-for="item in presetMenuItems" :key="item.key || 'divider'">
                          <a-menu-divider v-if="item.type === 'divider'" />
                          <a-menu-item v-else :key="item.key">{{ item.title }}</a-menu-item>
                        </template>
                      </a-menu>
                    </template>
                  </a-dropdown>
                  <div>
                    <a-button type="primary" size="large" @click="handleSelectAll"
                      style="margin-left: 12px;">全选</a-button>
                    <a-button size="large" @click="handleReset" style="margin-left: 8px;">重置</a-button>
                  </div>
                </div>

                <div class="filter-tip">
                  <span>已选择 {{ selectedColumns.length }} / {{ columnOptions.length }} 个字段</span>
                </div>

                <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnChange">
                  <!-- 动态字段分类 - 使用flex横向布局 -->
                  <div class="field-categories-container">
                    <div v-for="category in fieldCategories" :key="category.key" class="field-category-section">
                      <h5 class="category-title">{{ category.title }}</h5>
                      <div class="category-fields">
                        <div v-for="option in columnOptions.filter(opt => opt.category === category.key)"
                          :key="option.key" class="column-option">
                          <a-checkbox :value="option.key" :disabled="option.required" @click.stop>
                            <span class="column-title">{{ option.title }}</span>
                            <a-tag v-if="option.required" size="small" color="blue">必选</a-tag>
                          </a-checkbox>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-checkbox-group>
              </div>
            </a-modal>
          </a-space>
        </div>
      </div>

      <!-- 详细筛选控件区域 - 分两行布局，与页面同宽 -->
      <div v-show="showFilters" class="detailed-filters-fullwidth">
        <!-- 第一行：基础筛选项 -->
        <div class="filter-row">
          <a-row :gutter="[16, 12]" align="middle">
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.status" placeholder="状态" allowClear @change="handleFilterChange"
                class="filter-select">
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option v-for="status in statusOptions" :key="status.value" :value="status.value">
                  {{ status.label }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.purchaseType" placeholder="采购类型" allowClear @change="handleFilterChange"
                class="filter-select">
                <a-select-option value="">全部类型</a-select-option>
                <a-select-option v-for="type in purchaseTypes" :key="type.value" :value="type.value">
                  {{ type.label }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.department" placeholder="需求单位" allowClear @change="handleFilterChange"
                class="filter-select" show-search :filter-option="filterOption">
                <a-select-option value="">全部需求单位</a-select-option>
                <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                  {{ dept.dept_name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.applicant" placeholder="申请人" allowClear @change="handleFilterChange"
                class="filter-select" show-search :filter-option="filterOption">
                <a-select-option value="">全部申请人</a-select-option>
                <a-select-option v-for="user in applicants" :key="user.id" :value="user.id">
                  {{ user.real_name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.approver" placeholder="审批人" allowClear @change="handleFilterChange"
                class="filter-select" show-search :filter-option="filterOption">
                <a-select-option value="">全部审批人</a-select-option>
                <a-select-option v-for="user in approvers" :key="user.id" :value="user.id">
                  {{ user.real_name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.purchaser" placeholder="采购人" allowClear @change="handleFilterChange"
                class="filter-select" show-search :filter-option="filterOption">
                <a-select-option value="">全部采购人</a-select-option>
                <a-select-option v-for="user in purchasers" :key="user.id" :value="user.id">
                  {{ user.real_name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.acceptor" placeholder="验收人" allowClear @change="handleFilterChange"
                class="filter-select" show-search :filter-option="filterOption">
                <a-select-option value="">全部验收人</a-select-option>
                <a-select-option v-for="user in acceptors" :key="user.id" :value="user.id">
                  {{ user.real_name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.settler" placeholder="结算人" allowClear @change="handleFilterChange"
                class="filter-select" show-search :filter-option="filterOption">
                <a-select-option value="">全部结算人</a-select-option>
                <a-select-option v-for="user in settlers" :key="user.id" :value="user.id">
                  {{ user.real_name }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </div>

        <!-- 第二行：搜索和其他筛选项 -->
        <div class="filter-row">
          <a-row :gutter="[16, 12]" align="middle">
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.payeeName" placeholder="收款人户名" allowClear @change="handleFilterChange"
                class="filter-select" show-search :filter-option="filterOption">
                <a-select-option value="">全部收款人</a-select-option>
                <a-select-option v-for="payee in payeeNames" :key="payee" :value="payee">
                  {{ payee }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-select v-model:value="filters.payeeAccount" placeholder="收款人账号" allowClear @change="handleFilterChange"
                class="filter-select" show-search :filter-option="filterOption">
                <a-select-option value="">全部账号</a-select-option>
                <a-select-option v-for="account in payeeAccounts" :key="account" :value="account">
                  {{ account }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.id" placeholder="ID" @input="debouncedFilterChange" class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.itemName" placeholder="搜索物品名称" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.spec" placeholder="搜索规格型号" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.procurementMethod" placeholder="采购方式" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.requirementSource" placeholder="需求来源" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.fundProject" placeholder="经费项目" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.unit" placeholder="计量单位" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.itemCategory" placeholder="物品种类" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.remarks" placeholder="需求备注" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.purchaseRemarks" placeholder="采购备注" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.acceptanceRemarks" placeholder="验收备注" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.settlementRemarks" placeholder="结算备注" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <a-input v-model:value="filters.voucherNo" placeholder="报销凭证号" @change="handleFilterChange"
                class="filter-input">
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </a-col>
          </a-row>
        </div>

        <!-- 第三行：金额范围和日期筛选 -->
        <div class="filter-row">
          <a-row :gutter="[16, 12]" align="middle">
            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <div class="filter-item-with-label">
                <span class="filter-label">预算金额:</span>
                <a-input-group compact class="budget-range-input">
                  <a-input v-model:value="filters.minAmount" placeholder="最小金额" @input="debouncedFilterChange"
                    style="width: 50%" type="number" :min="0" />
                  <a-input v-model:value="filters.maxAmount" placeholder="最大金额" @input="debouncedFilterChange"
                    style="width: 50%" type="number" :min="0" />
                </a-input-group>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="5">
              <div class="filter-item-with-label">
                <span class="filter-label">创建时间:</span>
                <a-range-picker v-model:value="filters.dateRange" :placeholder="['开始时间', '结束时间']"
                  @change="handleFilterChange" class="filter-date-picker" />
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="5">
              <div class="filter-item-with-label">
                <span class="filter-label">采购时间:</span>
                <a-range-picker v-model:value="filters.purchaseTimeRange" :placeholder="['开始时间', '结束时间']"
                  @change="handleFilterChange" class="filter-date-picker" />
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="5">
              <div class="filter-item-with-label">
                <span class="filter-label">验收时间:</span>
                <a-range-picker v-model:value="filters.acceptanceTimeRange" :placeholder="['开始时间', '结束时间']"
                  @change="handleFilterChange" class="filter-date-picker" />
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="5">
              <div class="filter-item-with-label">
                <span class="filter-label">结算时间:</span>
                <a-range-picker v-model:value="filters.settlementTimeRange" :placeholder="['开始时间', '结束时间']"
                  @change="handleFilterChange" class="filter-date-picker" />
              </div>
            </a-col>
          </a-row>
        </div>
      </div>

      <div class="table-container">
        <a-table :columns="filteredColumns" :data-source="displayRequests" :row-key="record => record.id" :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: (page, pageSize) => handleTableChange({ current: page, pageSize }),
          onShowSizeChange: (current, size) => handleTableChange({ current, pageSize: size })
        }" :loading="loading" @change="handleTableChange" :scroll="{ x: 1400, y: 'calc(100vh - 400px)' }"
          :virtual="false" :row-height="54" bordered class="unified-table" table-layout="fixed">
          <!-- 空状态模板 -->
          <template #emptyText>
            <a-empty description="暂无采购需求数据" />
          </template>
          <template #bodyCell="{ column, record }">
            <!-- 状态标签 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status_display || record.status }}
              </a-tag>
            </template>

            <!-- 采购方式 -->
            <template v-else-if="column.dataIndex === 'procurement_method'">
              {{ record.procurement_method_display || record.procurement_method || '-' }}
            </template>

            <!-- 采购类型 -->
            <template v-else-if="column.dataIndex === 'purchase_type'">
              {{ record.purchase_type_display || record.purchase_type || '-' }}
            </template>

            <!-- 物品种类 -->
            <template v-else-if="column.dataIndex === 'item_category'">
              {{ record.item_category_display || record.item_category || '-' }}
            </template>

            <!-- 单位 -->
            <template v-else-if="column.dataIndex === 'unit'">
              {{ record.unit_display || record.unit || '-' }}
            </template>

            <!-- 需求来源 -->
            <template v-else-if="column.dataIndex === 'requirement_source'">
              {{ record.requirement_source || '-' }}
            </template>

            <!-- 经费项目 -->
            <template v-else-if="column.dataIndex === 'fund_project'">
              {{ record.fund_project_display || record.fund_project || '-' }}
            </template>

            <!-- 金额显示 -->
            <template v-else-if="column.dataIndex === 'total_amount'">
              ¥{{ record.total_amount }}
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.dataIndex === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  详情
                </a-button>
              </a-space>
            </template>

          </template>
        </a-table>
      </div>
    </div>

    <!-- 导出Excel配置 -->
    <OverviewExportConfig :visible="showExportModal" @cancel="showExportModal = false"
      @export-complete="handleExportComplete" />

  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, nextTick, h } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { debounce } from '@/utils/debounce'

import {
  ShoppingCartOutlined,
  DownloadOutlined,
  ReloadOutlined,
  SettingOutlined,
  DownOutlined,
  UpOutlined,
  SearchOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import api from '@/api'
import OverviewExportConfig from '@/components/Export/OverviewExportConfig.vue'
import { getStatusConfig } from '@/utils/status'
import { getPageColumnOptions, getPageColumnPresets, getPageFieldCategories, getPagePresetMenuItems, getPageDefaultColumns, sortColumnsByOrder } from '@/utils/validation'
import { useDictMixin } from '@/mixins/dictMixin'

export default {
  name: 'PurchaseOverview',
  components: {
    ShoppingCartOutlined,
    DownloadOutlined,
    ReloadOutlined,
    SettingOutlined,
    DownOutlined,
    UpOutlined,
    SearchOutlined,
    CloseOutlined,
    OverviewExportConfig
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const presetDropdownOpen = ref(false)
    const requests = ref([])
    const departments = ref([])

    // 使用字典混入 - 只保留状态颜色功能
    const {
      getStatusColor
    } = useDictMixin()

    // 筛选选项数据源 - 从字典表动态获取
    const statusOptions = ref([])
    const purchaseTypes = ref([])
    const applicants = ref([])
    const approvers = ref([])
    const purchasers = ref([])
    const acceptors = ref([])
    const settlers = ref([])
    const payeeNames = ref([])
    const payeeAccounts = ref([])

    // 筛选控件显示状态
    const showFilters = ref(false)
    const showColumnFilter = ref(false)

    // 导出相关状态
    const showExportModal = ref(false)

    // 筛选条件
    const filters = reactive({
      status: '',
      id: '',
      itemName: '',
      spec: '',
      department: '',
      purchaseType: '',
      applicant: '',
      approver: '',
      purchaser: '',
      acceptor: '',
      settler: '',
      payeeName: '',
      payeeAccount: '',
      purchaseTimeRange: [],
      acceptanceTimeRange: [],
      settlementTimeRange: [],
      minAmount: '',
      maxAmount: '',
      dateRange: [],
      // 新增缺失的筛选字段
      procurementMethod: '',     // 采购方式
      requirementSource: '',     // 需求来源
      fundProject: '',          // 经费项目
      unit: '',                 // 计量单位
      itemCategory: '',         // 物品种类
      remarks: '',              // 需求备注
      purchaseRemarks: '',      // 采购备注
      acceptanceRemarks: '',    // 验收备注
      settlementRemarks: '',    // 结算备注
      voucherNo: ''            // 报销凭证号
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
      pageSizeOptions: ['10', '20', '50', '100']
    })

    // 统计数据 - 使用实际API数据
    const totalCount = ref(0)
    const pendingCount = ref(0)
    const totalAmount = ref('0.00')
    const statusDistribution = ref([])
    const trendData = ref([])
    const purchaseTypeData = ref([])

    // 获取所有图表数据 - 统一的图表数据获取函数
    const getAllChartsData = async () => {
      try {
        // 构建筛选参数
        const params = {}

        // 添加筛选条件
        if (filters.status) {
          params.status = filters.status
        }
        if (filters.itemName) {
          params.search = filters.itemName
        }
        if (filters.department) {
          const selectedDept = departments.value.find(d => d.id == filters.department)
          if (selectedDept) {
            if (!selectedDept.parent_id) {
              // 一级部门，筛选该部门及其子部门
              params.hierarchy_path__icontains = selectedDept.dept_name
            } else {
              // 二级部门，只筛选该部门
              params.hierarchy_path__icontains = selectedDept.hierarchy_path
            }
          }
        }
        if (filters.requester) {
          params.requester__username__icontains = filters.requester
        }
        if (filters.purchaseType) {
          params.purchase_type = filters.purchaseType
        }
        if (filters.dateRange && filters.dateRange.length === 2) {
          params.created_at__gte = filters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = filters.dateRange[1].format('YYYY-MM-DD')
        }

        const response = await api.dashboard.getOverviewCharts(params)
        if (response.code === 200) {
          const data = response.data

          // 更新状态分布数据 - 使用统一的状态配置，显示所有状态
          if (data.status_distribution) {
            // 导入状态配置
            const { getStatusChartColor, getStatusInOrder } = await import('@/utils/status')

            // 获取所有状态配置，按采购流程顺序排序
            const allStatuses = getStatusInOrder()

            // 创建状态数据映射
            const statusDataMap = {}
            data.status_distribution.forEach(item => {
              statusDataMap[item.status] = item.count
            })

            // 构建完整的状态分布数据，包含所有状态
            statusDistribution.value = allStatuses.map(statusConfig => {
              // 从字典选项中查找对应的名称
              const statusOption = statusOptions.value.find(opt => opt.value === statusConfig.status)
              const statusName = statusOption ? statusOption.label : statusConfig.text
              const count = statusDataMap[statusConfig.status] || 0

              return {
                name: statusName,
                value: count,
                color: getStatusChartColor(statusConfig.status),
                status: statusConfig.status,
                order: statusConfig.order,
                hasData: count > 0  // 标记是否有数据，用于图表显示优化
              }
            })
          }

          // 更新采购类型数据
          if (data.purchase_type_distribution) {
            // 使用字典数据获取采购类型名称，如果没有则使用默认颜色映射
            const defaultColors = {
              'UNIFIED': '#1890ff',
              'SELF': '#52c41a'
            }

            // 计算总数用于百分比计算
            const totalRecords = data.purchase_type_distribution.reduce((sum, item) => sum + item.count, 0)

            purchaseTypeData.value = data.purchase_type_distribution
              .map(item => {
                // 从字典选项中查找对应的名称
                const typeOption = purchaseTypes.value.find(opt => opt.value === item.purchase_type)
                const typeName = typeOption ? typeOption.label : (item.purchase_type_display || item.purchase_type)

                return {
                  name: typeName,
                  value: item.count,
                  amount: item.amount || 0,
                  percentage: totalRecords > 0 ? ((item.count / totalRecords) * 100).toFixed(1) : 0,
                  color: defaultColors[item.purchase_type] || '#666666'
                }
              })
              .filter(item => item.value > 0)
          }

          // 更新趋势数据
          if (data.monthly_trend) {
            trendData.value = data.monthly_trend
          }

          // 渲染所有图表 - 添加延迟确保图表容器已初始化
          setTimeout(() => {
            renderStatusChart()
            renderPurchaseTypeChart()
            renderTrendChart()
          }, 200)
        }
      } catch (error) {
        // 静默处理图表数据获取失败
      }
    }

    // 保留原函数以兼容现有调用
    const getStatusDistribution = async () => {
      // 已整合到getAllChartsData中
    }

    // 保留原函数以兼容现有调用
    const getTrendData = async () => {
      // 已整合到getAllChartsData中
    }

    // 保留原函数以兼容现有调用
    const getPurchaseTypeData = async () => {
      // 已整合到getAllChartsData中
    }

    // 获取统计数据 - 使用优化的统计API
    const getOverallStats = async () => {
      try {
        // 构建筛选参数
        const params = {}

        // 添加筛选条件
        if (filters.status) {
          params.status = filters.status
        }
        if (filters.itemName) {
          params.search = filters.itemName
        }
        if (filters.department) {
          const selectedDept = departments.value.find(d => d.id == filters.department)
          if (selectedDept) {
            if (!selectedDept.parent_id) {
              // 一级部门，筛选该部门及其子部门
              params.hierarchy_path__icontains = selectedDept.dept_name
            } else {
              // 二级部门，只筛选该部门
              params.hierarchy_path__icontains = selectedDept.hierarchy_path
            }
          }
        }
        if (filters.requester) {
          params.requester__username__icontains = filters.requester
        }
        if (filters.purchaseType) {
          params.purchase_type = filters.purchaseType
        }
        if (filters.dateRange && filters.dateRange.length === 2) {
          params.created_at__gte = filters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = filters.dateRange[1].format('YYYY-MM-DD')
        }

        const response = await api.dashboard.getOverviewStatistics(params)
        if (response.code === 200 && response.data) {
          const data = response.data
          if (data.overview) {
            totalCount.value = data.overview.total_count || 0
            pendingCount.value = data.overview.pending_count || 0
            totalAmount.value = (data.overview.total_amount || 0).toFixed(2)
          }
        } else {
          // 使用备选方案
          calculateStatsFromCurrentData()
        }
      } catch (error) {
        // 静默处理错误，使用备选方案
        calculateStatsFromCurrentData()
      }
    }

    // 从当前数据计算统计信息的备选方案
    const calculateStatsFromCurrentData = () => {
      totalCount.value = pagination.total || requests.value.length

      // 使用字典数据计算待处理数量，如果字典数据未加载则使用硬编码（统一使用小写编码）
      const pendingStatuses = statusOptions.value.length > 0
        ? statusOptions.value.filter(opt =>
          ['draft', 'pending_approval', 'pending_purchase', 'pending_acceptance', 'pending_reimbursement'].includes(opt.value)
        ).map(opt => opt.value)
        : ['draft', 'pending_approval', 'pending_purchase', 'pending_acceptance', 'pending_reimbursement']

      pendingCount.value = requests.value.filter(item =>
        pendingStatuses.includes(item.status)
      ).length

      const currentTotal = requests.value.reduce((sum, item) => {
        const amount = parseFloat(item.budget_total_amount || item.amount || 0)
        return sum + amount
      }, 0)
      totalAmount.value = currentTotal.toFixed(2)
    }

    // 基于业务流程的字段筛选配置（总览页面显示所有字段，但排除操作列）
    const columnOptions = getPageColumnOptions('overview').filter(opt => opt.key !== 'action')

    // 基于业务流程的字段分类配置（总览页面显示所有分类）
    const fieldCategories = getPageFieldCategories('overview')

    // 基于业务流程的预设菜单选项（总览页面显示所有预设）
    const presetMenuItems = getPagePresetMenuItems('overview')

    // 基于业务流程的预设配置（总览页面显示所有预设）
    const columnPresets = getPageColumnPresets('overview')

    // 从本地存储获取用户的字段选择，如果没有则使用默认值
    const getStoredColumns = () => {
      const COLUMNS_VERSION = '2.2' // 版本号，更新时清除旧缓存
      const STORAGE_KEY = `purchase-overview-columns-v${COLUMNS_VERSION}`

      try {
        // 只在版本升级时清除旧缓存
        const oldKeys = ['purchase-overview-columns', 'purchase-overview-columns-v2.0', 'purchase-overview-columns-v2.1']
        oldKeys.forEach(key => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key)
          }
        })

        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const parsed = JSON.parse(stored)
          // 确保必选字段始终被包含
          const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)
          return [...new Set([...parsed, ...requiredColumns])]
        }
      } catch (error) {
        // 静默处理配置读取失败
      }
      // 使用默认字段配置（按顺序排列）
      const defaultColumns = getPageDefaultColumns('overview')
      return defaultColumns // 默认配置已包含action列
    }

    // 默认选中的列
    const selectedColumns = ref(getStoredColumns())

    // 动态筛选后的列（使用动态列配置）
    const filteredColumns = computed(() => {
      // 构建选中的列（排除操作列）
      const selectedCols = selectedColumns.value.map(columnKey => {
        const option = columnOptions.find(opt => opt.key === columnKey)
        if (!option) {
          return null
        }

        const baseColumn = {
          title: option.title,
          dataIndex: columnKey,
          key: columnKey,
          width: 150, // 增加默认列宽
          ellipsis: true,
          align: 'center', // 所有列文字居中
          resizable: true // 列宽可调节
        }

        // 特殊字段的自定义渲染
        switch (columnKey) {
          case 'id':
            return { ...baseColumn, width: 80, sorter: true, fixed: 'left' }
          case 'budget_unit_price':
          case 'purchase_unit_price':
          case 'settlement_amount':
          case 'budget_total_amount':
          case 'purchase_total_amount':
            return {
              ...baseColumn, align: 'center', sorter: true,
              customRender: ({ text }) => text ? `¥${parseFloat(text).toFixed(2)}` : '-'
            }
          case 'budget_quantity':
          case 'purchase_quantity':
          case 'acceptance_quantity':
            return { ...baseColumn, width: 100, align: 'center', sorter: true }
          case 'purchase_type':
            return {
              ...baseColumn,
              sorter: true,
              customRender: ({ record }) => {
                return record.purchase_type_display || record.purchase_type || '-'
              }
            }
          case 'created_at':
          case 'submission_date':
          case 'approved_at':
          case 'purchase_date':
          case 'acceptance_date':
          case 'reimbursement_date':
            return {
              ...baseColumn,
              width: 150,
              sorter: true,
              customRender: ({ text }) => text ? new Date(text).toLocaleDateString() : '-'
            }
          case 'status':
            return {
              ...baseColumn,
              customRender: ({ record }) => {
                const statusConfig = getStatusConfig(record.status)
                return h('span', {
                  style: {
                    color: statusConfig.color,
                    backgroundColor: statusConfig.bgColor,
                    padding: '2px 8px',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }
                }, statusConfig.text)
              }
            }
          case 'hierarchy_path':
            return {
              ...baseColumn,
              width: 150,
              ellipsis: false,
              customRender: ({ text }) => {
                return h('div', {
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'break-all',
                    lineHeight: '1.4'
                  }
                }, text || '-')
              }
            }
          case 'remarks':
          case 'purchase_remarks':
          case 'acceptance_remarks':
          case 'settlement_remarks':
            return {
              ...baseColumn,
              width: 200,
              ellipsis: false,
              customRender: ({ text }) => {
                return h('div', {
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'break-all',
                    lineHeight: '1.4',
                    maxHeight: '80px',
                    overflow: 'auto'
                  }
                }, text || '-')
              }
            }
          // 需要字典转换的字段
          case 'procurement_method':
            return {
              ...baseColumn,
              sorter: true,
              customRender: ({ record }) => record.procurement_method_display || record.procurement_method
            }
          case 'item_category':
            return {
              ...baseColumn,
              sorter: true,
              customRender: ({ record }) => record.item_category_display || record.item_category
            }
          case 'unit':
            return {
              ...baseColumn,
              sorter: true,
              customRender: ({ record }) => record.unit_display || record.unit
            }
          case 'requirement_source':
            return {
              ...baseColumn,
              sorter: true,
              customRender: ({ record }) => record.requirement_source || '-'
            }
          case 'fund_project':
            return {
              ...baseColumn,
              sorter: true,
              customRender: ({ record }) => record.fund_project_display || record.fund_project
            }
          // 其他字段的排序功能
          case 'specification':
          case 'requester':
          case 'approver':
          case 'item_name':
          case 'department':
            return { ...baseColumn, sorter: true }
          default:
            return baseColumn
        }
      }).filter(Boolean)

      // 添加固定的操作列到最右侧
      const actionColumn = {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 120,
        fixed: 'right',
        align: 'center'
      }

      return [...selectedCols, actionColumn]
    })

    // 确保表格有最小行数的显示数据
    const displayRequests = computed(() => {
      const currentData = [...requests.value]
      return currentData
    })

    // 保存字段配置到本地存储
    const saveColumnsToStorage = (columns) => {
      const COLUMNS_VERSION = '2.2'
      const STORAGE_KEY = `purchase-overview-columns-v${COLUMNS_VERSION}`
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(columns))
      } catch (error) {
        // 静默处理保存失败
      }
    }

    // 字段筛选方法
    const onColumnChange = (checkedValues) => {
      // 确保必选字段始终被选中
      const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)
      const newSelectedColumns = [...new Set([...checkedValues, ...requiredColumns])]
      selectedColumns.value = newSelectedColumns
      saveColumnsToStorage(newSelectedColumns)
    }

    const selectAllColumns = () => {
      const sortedOptions = sortColumnsByOrder(columnOptions)
      const allColumns = sortedOptions.map(opt => opt.key)
      selectedColumns.value = allColumns
      saveColumnsToStorage(allColumns)
    }

    const resetColumns = () => {
      const defaultColumns = getPageDefaultColumns('overview')
      selectedColumns.value = defaultColumns
      saveColumnsToStorage(defaultColumns)
    }



    // 应用预设
    const applyPreset = ({ key }) => {
      const presetColumns = columnPresets[key]
      if (presetColumns) {
        // 确保必选字段始终被包含
        const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)

        // 合并预设字段和必选字段
        const combinedColumns = [...new Set([...presetColumns, ...requiredColumns])]

        // 按照标准顺序排列字段
        const sortedColumns = sortColumnsByOrder(combinedColumns, columnOptions)

        selectedColumns.value = sortedColumns
        saveColumnsToStorage(sortedColumns)
      }
    }

    // 处理字段变更（阻止面板关闭）
    const handleColumnChange = (checkedValues) => {
      onColumnChange(checkedValues)
    }

    // 处理预设点击（阻止面板关闭）
    const handlePresetClick = ({ key }) => {
      applyPreset({ key })
      // 保持dropdown打开状态
      presetDropdownOpen.value = true
    }

    // 处理全选按钮点击
    const handleSelectAll = (e) => {
      e.stopPropagation()
      selectAllColumns()
    }

    // 处理重置按钮点击
    const handleReset = (e) => {
      e.stopPropagation()
      resetColumns()
    }

    // 获取采购记录
    const getPurchaseRequests = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.current,
          page_size: pagination.pageSize
        }

        // 添加筛选参数
        if (filters.status) {
          params.status = filters.status
        }
        if (filters.itemName) {
          params.search = filters.itemName
        }
        if (filters.department) {
          // 根据选择的部门获取部门信息
          const selectedDept = departments.value.find(d => d.id == filters.department)
          if (selectedDept) {
            // 如果是一级部门（没有parent_id），筛选该部门及其所有子部门
            if (!selectedDept.parent_id) {
              // 一级部门：使用部门名称匹配层级路径，确保匹配该部门及其所有子部门
              params.hierarchy_path__icontains = selectedDept.dept_name
            } else {
              // 二级部门：使用层级路径精确匹配
              params.hierarchy_path__icontains = selectedDept.hierarchy_path
            }
          }
        }
        if (filters.purchaseType) {
          params.purchase_type = filters.purchaseType
        }
        if (filters.id) {
          const idNum = parseInt(filters.id)
          if (!isNaN(idNum)) {
            params.id = idNum
          }
        }
        if (filters.minAmount) {
          const minAmount = parseFloat(filters.minAmount)
          if (!isNaN(minAmount)) {
            params.budget_total_amount__gte = minAmount
          }
        }
        if (filters.maxAmount) {
          const maxAmount = parseFloat(filters.maxAmount)
          if (!isNaN(maxAmount)) {
            params.budget_total_amount__lte = maxAmount
          }
        }
        if (filters.itemName) {
          params.item_name__icontains = filters.itemName
        }
        if (filters.spec) {
          params.specification__icontains = filters.spec
        }
        if (filters.dateRange && filters.dateRange.length === 2) {
          params.created_at__gte = filters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = filters.dateRange[1].format('YYYY-MM-DD')
        }
        if (filters.purchaseTimeRange && filters.purchaseTimeRange.length === 2) {
          params.purchase_time__gte = filters.purchaseTimeRange[0].format('YYYY-MM-DD')
          params.purchase_time__lte = filters.purchaseTimeRange[1].format('YYYY-MM-DD')
        }
        if (filters.acceptanceTimeRange && filters.acceptanceTimeRange.length === 2) {
          params.acceptance_time__gte = filters.acceptanceTimeRange[0].format('YYYY-MM-DD')
          params.acceptance_time__lte = filters.acceptanceTimeRange[1].format('YYYY-MM-DD')
        }
        if (filters.settlementTimeRange && filters.settlementTimeRange.length === 2) {
          params.settlement_time__gte = filters.settlementTimeRange[0].format('YYYY-MM-DD')
          params.settlement_time__lte = filters.settlementTimeRange[1].format('YYYY-MM-DD')
        }
        // 新增筛选参数
        if (filters.procurementMethod) {
          params.procurement_method__icontains = filters.procurementMethod
        }
        if (filters.requirementSource) {
          params.requirement_source__icontains = filters.requirementSource
        }
        if (filters.fundProject) {
          params.fund_project__icontains = filters.fundProject
        }
        if (filters.unit) {
          params.unit__icontains = filters.unit
        }
        if (filters.itemCategory) {
          params.item_category__icontains = filters.itemCategory
        }
        if (filters.remarks) {
          params.remarks__icontains = filters.remarks
        }
        if (filters.purchaseRemarks) {
          params.purchase_remarks__icontains = filters.purchaseRemarks
        }
        if (filters.acceptanceRemarks) {
          params.acceptance_remarks__icontains = filters.acceptanceRemarks
        }
        if (filters.settlementRemarks) {
          params.settlement_remarks__icontains = filters.settlementRemarks
        }
        if (filters.voucherNo) {
          params.reimbursement_voucher_no__icontains = filters.voucherNo
        }
        if (filters.applicant) {
          params.requester__real_name__icontains = filters.applicant
        }
        if (filters.approver) {
          params.approver__real_name__icontains = filters.approver
        }
        if (filters.purchaser) {
          params.purchaser__real_name__icontains = filters.purchaser
        }
        if (filters.acceptor) {
          params.acceptor__real_name__icontains = filters.acceptor
        }
        if (filters.settler) {
          params.reimburser__real_name__icontains = filters.settler
        }

        // 添加排序参数
        if (sortField.value && sortOrder.value) {
          const orderPrefix = sortOrder.value === 'descend' ? '-' : ''
          params.ordering = `${orderPrefix}${sortField.value}`
        }

        const response = await api.purchaseRequests.getList(params)

        if (response.code === 200) {
          const rawRequests = response.data.results || response.data || []

          // 使用后端提供的display字段，同时保留原始编码用于逻辑判断
          const convertedRequests = rawRequests.map(request => {
            return {
              ...request,
              // 显示字段使用display值
              status: request.status_display || request.status,
              item_category: request.item_category_display || request.item_category,
              unit: request.unit_display || request.unit,
              procurement_method: request.procurement_method_display || request.procurement_method,
              fund_project: request.fund_project_display || request.fund_project,
              purchase_type: request.purchase_type_display || request.purchase_type,
              // 保存原始编码用于逻辑判断（统一使用小写编码）
              _originalStatus: request.status,
              _originalItemCategory: request.item_category,
              _originalUnit: request.unit,
              _originalProcurementMethod: request.procurement_method,
              _originalFundProject: request.fund_project,
              _originalPurchaseType: request.purchase_type
            }
          })

          requests.value = convertedRequests
          pagination.total = response.data.count || requests.value.length

          // 从当前数据中更新收款人选项
          updatePayeeOptions()

          // 更新统计数据（基于当前筛选条件）
          await getOverallStats()
        }
      } catch (error) {
        message.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }


    // 查看详情
    const viewDetail = (record) => {
      router.push(`/purchase/requests/${record.id}`)
    }

    // 排序状态
    const sortField = ref('')
    const sortOrder = ref('')

    // 表格变化处理 - 支持分页和排序
    const handleTableChange = (pag, _filters, sorter) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize

      // 处理排序
      if (sorter && sorter.field) {
        sortField.value = sorter.field
        sortOrder.value = sorter.order
      } else {
        sortField.value = ''
        sortOrder.value = ''
      }

      // 只获取表格数据，统计数据保持不变（因为它们是全局数据）
      getPurchaseRequests()
    }



    // 获取部门列表 - 用于筛选选项，使用专门的无分页接口
    const getDepartments = async () => {
      try {
        const response = await api.departments.getAll()
        if (response.code === 200) {
          departments.value = response.data || []
        }
      } catch (error) {
        // 静默处理部门列表获取失败
      }
    }



    // 获取字典数据
    const getDictOptions = async () => {
      try {
        // 获取状态字典
        const statusResponse = await api.dicts.getDict('status')
        if (statusResponse.code === 200) {
          statusOptions.value = (statusResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }

        // 获取采购类型字典
        const purchaseTypeResponse = await api.dicts.getDict('purchase_type')
        if (purchaseTypeResponse.code === 200) {
          purchaseTypes.value = (purchaseTypeResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }
      } catch (error) {
        // 静默处理字典数据获取失败
        // 使用备用数据（统一使用小写编码）
        statusOptions.value = [
          { value: 'draft', label: '草稿' },
          { value: 'pending_approval', label: '待审批' },
          { value: 'approved', label: '已审批' },
          { value: 'rejected', label: '已驳回' },
          { value: 'pending_purchase', label: '待采购' },
          { value: 'purchased', label: '已采购' },
          { value: 'pending_acceptance', label: '待验收' },
          { value: 'accepted', label: '已验收' },
          { value: 'pending_reimbursement', label: '待结算' },
          { value: 'settled', label: '已结算' },
          { value: 'returned', label: '已退回' }
        ]
        purchaseTypes.value = [
          { value: 'unified', label: '统一采购' },
          { value: 'self', label: '自行采购' }
        ]
      }
    }

    // 获取筛选选项数据
    const getFilterOptions = async () => {
      try {
        // 获取字典数据
        await getDictOptions()

        // 获取用户列表（申请人、审批人、采购人、验收人、结算人）
        const usersResponse = await api.users.getList()
        if (usersResponse.code === 200) {
          const users = usersResponse.data.results || usersResponse.data
          applicants.value = users // 所有用户都可以是申请人
          approvers.value = users.filter(user => user.role_name && user.role_name.includes('审批'))
          purchasers.value = users.filter(user => user.role_name && user.role_name.includes('采购'))
          acceptors.value = users.filter(user => user.role_name && user.role_name.includes('验收'))
          settlers.value = users.filter(user => user.role_name && user.role_name.includes('财务'))
        }

        // 收款人信息将从当前页面数据中动态提取，避免额外的API请求
        // 这个逻辑移到了updatePayeeOptions函数中
      } catch (error) {
        // 静默处理筛选选项获取失败
      }
    }

    // 从当前数据中更新收款人选项
    const updatePayeeOptions = () => {
      const payeeNameSet = new Set()
      const payeeAccountSet = new Set()

      requests.value.forEach(record => {
        if (record.payee_name) payeeNameSet.add(record.payee_name)
        if (record.payee_account) payeeAccountSet.add(record.payee_account)
      })

      payeeNames.value = Array.from(payeeNameSet)
      payeeAccounts.value = Array.from(payeeAccountSet)
    }

    // 筛选选项过滤方法
    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 展开/收起筛选控件
    const toggleFilters = () => {
      showFilters.value = !showFilters.value
    }

    // 筛选变化处理
    const handleFilterChange = () => {
      pagination.current = 1
      getPurchaseRequests()
      getOverallStats()
      getAllChartsData() // 使用统一的图表数据获取函数
    }

    // 防抖筛选变化处理
    const debouncedFilterChange = debounce(handleFilterChange, 500)

    // 重置筛选
    const resetFilters = () => {
      Object.assign(filters, {
        status: '',
        id: '',
        itemName: '',
        spec: '',
        department: '',
        purchaseType: '',
        applicant: '',
        approver: '',
        purchaser: '',
        acceptor: '',
        settler: '',
        payeeName: '',
        payeeAccount: '',
        purchaseTimeRange: [],
        acceptanceTimeRange: [],
        settlementTimeRange: [],
        minAmount: '',
        maxAmount: '',
        dateRange: [],
        // 重置新增的筛选字段
        procurementMethod: '',
        requirementSource: '',
        fundProject: '',
        unit: '',
        itemCategory: '',
        remarks: '',
        purchaseRemarks: '',
        acceptanceRemarks: '',
        settlementRemarks: '',
        voucherNo: ''
      })
      handleFilterChange()
    }

    // 显示导出对话框
    const showExportDialog = () => {
      showExportModal.value = true
    }

    // 处理导出完成
    const handleExportComplete = () => {
      // 导出完成后可以执行的操作，比如刷新数据
      getPurchaseRequests()
    }







    // 初始化状态分布图表
    const initStatusChart = () => {
      const chartDom = document.getElementById('statusChart')
      if (!chartDom) return

      // 检查是否已经初始化，避免重复初始化
      if (echarts.getInstanceByDom(chartDom)) {
        echarts.dispose(chartDom)
      }

      // 只初始化图表容器，不设置数据
      const myChart = echarts.init(chartDom)

      // 响应式调整
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }

    // 初始化采购趋势图表
    const initTrendChart = () => {
      const chartDom = document.getElementById('trendChart')
      if (!chartDom) return

      // 检查是否已经初始化，避免重复初始化
      if (echarts.getInstanceByDom(chartDom)) {
        echarts.dispose(chartDom)
      }

      // 只初始化图表容器，不设置数据
      const myChart = echarts.init(chartDom)

      // 响应式调整
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }

    // 初始化采购类型图表
    const initPurchaseTypeChart = () => {
      const chartDom = document.getElementById('purchaseTypeChart')
      if (!chartDom) return

      // 检查是否已经初始化，避免重复初始化
      if (echarts.getInstanceByDom(chartDom)) {
        echarts.dispose(chartDom)
      }

      const myChart = echarts.init(chartDom)

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }

    // 初始化图表
    const initCharts = async () => {
      await nextTick()
      // 确保DOM元素已经渲染
      setTimeout(() => {
        initStatusChart()
        initPurchaseTypeChart()
        initTrendChart()
      }, 100)
    }

    // 渲染状态分布图表
    const renderStatusChart = () => {
      nextTick(() => {
        const chartDom = document.getElementById('statusChart')
        if (chartDom && statusDistribution.value.length > 0) {
          // 获取已存在的图表实例，避免重复初始化
          let myChart = echarts.getInstanceByDom(chartDom)
          if (!myChart) {
            myChart = echarts.init(chartDom)
          }

          // 过滤出有数据的项目用于饼图显示，但图例显示所有项目
          const chartData = statusDistribution.value.filter(item => item.hasData)
          const legendData = statusDistribution.value.map(item => item.name)

          const option = {
            tooltip: {
              trigger: 'item',
              formatter: function (params) {
                return `${params.seriesName}<br/>
                        ${params.name}: ${params.value}项 (${params.percent}%)`
              }
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              data: legendData,
              formatter: function (name) {
                const item = statusDistribution.value.find(d => d.name === name)
                const count = item ? item.value : 0
                const hasData = item ? item.hasData : false
                // 对于没有数据的项目，在图例中用灰色显示
                return hasData ? `${name} (${count}项)` : `{inactive|${name} (${count}项)}`
              },
              textStyle: {
                rich: {
                  inactive: {
                    color: '#999'
                  }
                }
              }
            },
            series: [
              {
                name: '状态分布',
                type: 'pie',
                radius: '50%',
                data: chartData.length > 0 ? chartData : [
                  // 如果没有任何数据，显示一个占位项
                  { name: '暂无数据', value: 1, color: '#f0f0f0' }
                ],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                label: {
                  show: chartData.length > 0,
                  formatter: '{d}%'
                }
              }
            ]
          }
          myChart.setOption(option)
        }
      })
    }

    // 渲染趋势图表
    const renderTrendChart = () => {
      nextTick(() => {
        const chartDom = document.getElementById('trendChart')
        if (chartDom && trendData.value.length > 0) {
          // 获取已存在的图表实例，避免重复初始化
          let myChart = echarts.getInstanceByDom(chartDom)
          if (!myChart) {
            myChart = echarts.init(chartDom)
          }
          const option = {
            tooltip: {
              trigger: 'axis',
              formatter: function (params) {
                let result = `<strong>${params[0].name}</strong><br/>`
                params.forEach(param => {
                  if (param.seriesName === '采购数量') {
                    result += `${param.marker} ${param.seriesName}: ${param.value}项<br/>`
                  } else if (param.seriesName === '采购金额') {
                    result += `${param.marker} ${param.seriesName}: ¥${parseFloat(param.value).toFixed(2)}<br/>`
                  }
                })
                return result
              }
            },
            legend: {
              data: ['采购数量', '采购金额']
            },
            xAxis: {
              type: 'category',
              data: trendData.value.map(item => item.month)
            },
            yAxis: [
              {
                type: 'value',
                name: '数量',
                position: 'left'
              },
              {
                type: 'value',
                name: '金额(元)',
                position: 'right',
                axisLabel: {
                  formatter: function (value) {
                    return '¥' + parseFloat(value).toFixed(0)
                  }
                }
              }
            ],
            series: [
              {
                name: '采购数量',
                type: 'line',
                data: trendData.value.map(item => item.count)
              },
              {
                name: '采购金额',
                type: 'line',
                yAxisIndex: 1,
                data: trendData.value.map(item => parseFloat(item.amount).toFixed(2))
              }
            ]
          }
          myChart.setOption(option)
        }
      })
    }

    // 渲染采购类型图表
    const renderPurchaseTypeChart = () => {
      nextTick(() => {
        const chartDom = document.getElementById('purchaseTypeChart')
        if (chartDom && purchaseTypeData.value.length > 0) {
          // 获取已存在的图表实例，避免重复初始化
          let myChart = echarts.getInstanceByDom(chartDom)
          if (!myChart) {
            myChart = echarts.init(chartDom)
          }
          const option = {
            tooltip: {
              trigger: 'item',
              formatter: function (params) {
                const data = params.data
                return `${params.name}<br/>
                        数量: ${params.value}项 (${data.percentage}%)<br/>
                        金额: ¥${data.amount.toLocaleString()}`
              }
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              top: 'center',
              data: purchaseTypeData.value.map(item => item.name),
              formatter: function (name) {
                const item = purchaseTypeData.value.find(d => d.name === name)
                return `${name}\n${item ? item.value : 0}项 | ¥${(item ? item.amount : 0).toLocaleString()}`
              }
            },
            series: [
              {
                name: '采购类型',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['65%', '50%'],
                data: purchaseTypeData.value,
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                label: {
                  show: true,
                  formatter: '{d}%'
                }
              }
            ]
          }
          myChart.setOption(option)
        }
      })
    }

    onMounted(async () => {
      // 并行加载数据以提高性能
      try {
        loading.value = true
        await Promise.all([
          getDepartments(),
          getFilterOptions(),
          getPurchaseRequests()
        ])

        // 初始化图表容器
        await initCharts()

        // 图表数据可以在后台异步加载
        Promise.all([
          getOverallStats(),
          getAllChartsData() // 使用统一的图表数据获取函数
        ]).catch(() => {
          // 静默处理图表数据加载失败
        })
      } catch (error) {
        message.error('页面加载失败')
      } finally {
        loading.value = false
      }
    })

    return {
      loading,
      presetDropdownOpen,
      requests,
      displayRequests,
      departments,
      purchaseTypes,
      applicants,
      approvers,
      purchasers,
      acceptors,
      settlers,
      payeeNames,
      payeeAccounts,
      showFilters,
      showColumnFilter,
      filters,
      pagination,
      filteredColumns,
      columnOptions,
      selectedColumns,
      fieldCategories,
      presetMenuItems,
      columnPresets,
      totalCount,
      pendingCount,
      totalAmount,
      getPurchaseRequests,
      getDepartments,
      getDictOptions,
      statusOptions,
      handleFilterChange,
      debouncedFilterChange,
      resetFilters,
      getFilterOptions,
      updatePayeeOptions,
      filterOption,
      toggleFilters,
      getStatusColor,

      viewDetail,
      handleTableChange,
      sortField,
      sortOrder,
      onColumnChange,
      handleColumnChange,
      handlePresetClick,
      handleSelectAll,
      handleReset,
      selectAllColumns,
      resetColumns,
      applyPreset,
      initCharts,
      getOverallStats,
      getAllChartsData,
      getStatusDistribution,
      getTrendData,
      getPurchaseTypeData,
      renderStatusChart,
      renderTrendChart,
      renderPurchaseTypeChart,
      statusDistribution,
      trendData,
      purchaseTypeData,
      // 导出相关
      showExportModal,
      showExportDialog,
      handleExportComplete
    }
  }
}
</script>

<style scoped>
@import '@/styles/business-panels.css';

.purchase-overview {
  padding: 24px;
}

.table-container {
  /* overflow: hidden; */
  width: 100%;
  min-width: 800px;
  overflow: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
  gap: 24px;
}

.table-title-section {
  flex-shrink: 0;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title>span:first-child {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

/* 详细筛选控件样式 */
.detailed-filters {
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.detailed-filters[v-show] {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding: 0;
  }

  to {
    opacity: 1;
    max-height: 200px;
    padding: 16px 0;
  }
}

.filter-select {
  height: 32px;
  font-size: var(--text-sm);
}

.filter-input {
  height: 32px;
  font-size: var(--text-sm);
}

.filter-date-picker {
  height: 32px;
  font-size: var(--text-sm);
}

.budget-range-input {
  width: 200px;
}

.budget-range-input .ant-input {
  height: 32px;
  font-size: var(--text-sm);
}

/* 紧凑型筛选控件样式 */
.compact-filter-select {
  height: 32px;
  font-size: var(--text-sm);
}

.compact-filter-input {
  height: 32px;
  font-size: var(--text-sm);
}

.compact-action-btn {
  height: 32px;
  font-size: var(--text-sm);
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.column-filter-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.column-filter-btn .ant-badge {
  margin-left: 4px;
}

/* 新的表格头部flex布局 */
.table-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

/* 全宽筛选区域 */
.detailed-filters-fullwidth {
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid var(--border-light);
}

.detailed-filters-fullwidth .filter-row {
  margin-bottom: 16px;
}

.detailed-filters-fullwidth .filter-row:last-child {
  margin-bottom: 0;
}

.detailed-filters-fullwidth .filter-select,
.detailed-filters-fullwidth .filter-input {
  width: 100%;
  height: 36px;
}

.detailed-filters-fullwidth .filter-select .ant-select-selector,
.detailed-filters-fullwidth .filter-input {
  border: 1px solid var(--border-light);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.detailed-filters-fullwidth .filter-select .ant-select-selector:hover,
.detailed-filters-fullwidth .filter-input:hover {
  border-color: var(--primary-light);
}

.detailed-filters-fullwidth .filter-select .ant-select-focused .ant-select-selector,
.detailed-filters-fullwidth .filter-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.1);
}

/* 带标签的筛选项样式 */
.filter-item-with-label {
  display: flex;
  align-items: center;
  width: 100%;
}

.filter-item-with-label .filter-label {
  white-space: nowrap;
  margin-right: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.filter-item-with-label .filter-date-picker,
.filter-item-with-label .budget-range-input {
  flex: 1;
  min-width: 0;
}

.budget-range-input {
  display: flex;
  width: 100%;
}

.budget-range-input .ant-input {
  border-radius: 6px 0 0 6px;
}

.budget-range-input .ant-input:last-child {
  border-radius: 0 6px 6px 0;
  border-left: 0;
}

.budget-range-input .ant-input:focus {
  z-index: 1;
}

.field-category-section {
  margin-bottom: 0px;
}

.category-title {
  margin: 0 0 6px 0;
  font-size: 13px;
  font-weight: 600;
  color: #666;
}

.category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 5px;
}

.column-option {
  display: flex;
  align-items: center;
}

.column-title {
  font-size: 12px;
}

/* 导出字段选择样式 - 与字段筛选保持一致 */
.export-field-category {
  margin-bottom: 12px;
}

.export-category-title {
  margin: 0 0 6px 0;
  font-weight: 600;
  font-size: 13px;
  color: #666;
}

.export-category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.export-field-option {
  display: flex;
  align-items: center;
}

.filter-content {
  max-height: 300px;
}

.charts-section {
  margin: 24px 0;
}

.chart-card {
  padding: 24px;
  height: 380px;
}

/* 统一筛选区域样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

/* 次要操作按钮样式 */
.secondary-action-btn {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 48px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 140px;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
  transform: translateY(-1px);
}

.chart-card h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 筛选区按钮高度对齐 */
.unified-filter-section .secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-filter-section .secondary-action-btn:hover {
  border-color: var(--primary-light);
}

/* 金额范围输入框样式 */
.amount-range-input {
  width: 100%;
}

.amount-range-input .ant-input {
  height: 32px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: var(--transition-normal);
}

.amount-range-input .ant-input:hover {
  border-color: var(--primary-light);
}

.amount-range-input .ant-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.1);
}

.amount-range-input .ant-input:first-child {
  border-right: 1px solid var(--border-light);
  border-radius: var(--radius-sm) 0 0 var(--radius-sm);
}

.amount-range-input .ant-input:last-child {
  border-left: 1px solid var(--border-light);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

/* 导出配置对话框样式 */
.export-config {
  padding: 16px 0;
}

.export-fields h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 600;
}

.field-category-section {
  margin-bottom: 24px;
}

.field-category-section:last-child {
  margin-bottom: 0;
}

.category-title {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8f4ff;
}

.export-filters .ant-form-item {
  margin-bottom: 16px;
}

.export-preview {
  min-height: 300px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-weight: 600;
}

.preview-summary {
  margin-top: 16px;
}

.export-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
  text-align: right;
}

/* 修复操作列透明背景问题 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5 !important;
}

:deep(.ant-table-tbody > tr:hover > td.ant-table-cell-fix-right) {
  background-color: #f5f5f5 !important;
}
</style>
