# Generated by Django 5.2.4 on 2025-07-17 11:34

from django.db import migrations


def fix_dictionary_type_normalization(apps, schema_editor):
    """修复字典类型标准化"""
    Dictionary = apps.get_model('purchase', 'Dictionary')

    # 类型名称标准化映射（将旧的中文类型名称转换为英文）
    type_normalize_mapping = {
        '单位': 'unit',
        '物品分类': 'item_category',
        '经费来源': 'fund_project',
        '采购方式': 'procurement_method',
    }

    # 类型显示名称映射
    type_display_mapping = {
        'status': '状态',
        'purchase_type': '采购类型',
        'procurement_method': '采购方式',
        'fund_project': '经费项目',
        'unit': '计量单位',
        'item_category': '物品种类',
        'requirement_source': '需求来源',
        'urgency_level': '紧急程度',
    }

    # 先标准化类型名称（将中文类型名称转换为英文）
    for old_type, new_type in type_normalize_mapping.items():
        Dictionary.objects.filter(type=old_type).update(type=new_type)

    # 更新type_display字段
    for type_code, display_name in type_display_mapping.items():
        Dictionary.objects.filter(type=type_code).update(type_display=display_name)


def reverse_fix_dictionary_type_normalization(apps, schema_editor):
    """回滚操作"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0029_update_dictionary_type_display"),
    ]

    operations = [
        migrations.RunPython(
            fix_dictionary_type_normalization,
            reverse_fix_dictionary_type_normalization
        ),
    ]
