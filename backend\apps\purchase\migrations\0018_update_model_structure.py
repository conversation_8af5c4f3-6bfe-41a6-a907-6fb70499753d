# Generated migration for model structure updates
# This migration updates the model structure to match the new design

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0017_add_purchase_amount_field'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # 添加新字段（如果不存在）
        migrations.AddField(
            model_name='purchaserequest',
            name='approver',
            field=models.ForeignKey(
                blank=True,
                help_text='最终审批人',
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='approved_requests',
                to=settings.AUTH_USER_MODEL,
                verbose_name='审批人'
            ),
        ),
        
        migrations.AddField(
            model_name='purchaserequest',
            name='exception_approver',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='exception_approved_requests',
                to=settings.AUTH_USER_MODEL,
                verbose_name='异常审批人'
            ),
        ),
        
        migrations.AddField(
            model_name='purchaserequest',
            name='acceptance_quantity',
            field=models.PositiveIntegerField(
                default=0,
                help_text='实际验收确认的数量',
                verbose_name='验收数量'
            ),
        ),
        
        migrations.AddField(
            model_name='purchaserequest',
            name='purchase_method',
            field=models.CharField(
                blank=True,
                help_text='实际采用的采购方式',
                max_length=50,
                verbose_name='采购方式'
            ),
        ),
        
        # 重命名字段
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='final_approval_date',
            new_name='approved_at',
        ),
        
        migrations.RenameField(
            model_name='purchaserequest',
            old_name='final_approval_comment',
            new_name='approval_comment',
        ),
        
        # 更新状态选择
        migrations.AlterField(
            model_name='purchaserequest',
            name='status',
            field=models.CharField(
                choices=[
                    ('draft', '草稿'),
                    ('pending_approval', '待审批'),
                    ('approved', '已审批'),
                    ('rejected', '已驳回'),
                    ('pending_purchase', '待采购'),
                    ('purchased', '已采购'),
                    ('returned', '已退回'),
                    ('pending_acceptance', '待验收'),
                    ('accepted', '已验收'),
                    ('pending_reimbursement', '待结算'),
                    ('settled', '已结算')
                ],
                db_index=True,
                default='draft',
                max_length=30,
                verbose_name='当前状态'
            ),
        ),
        
        # 更新字段类型
        migrations.AlterField(
            model_name='purchaserequest',
            name='quantity',
            field=models.PositiveIntegerField(
                help_text='必须为正整数',
                verbose_name='需求数量'
            ),
        ),
        
        migrations.AlterField(
            model_name='purchaserequest',
            name='unit_price',
            field=models.DecimalField(
                decimal_places=2,
                help_text='单位：元',
                max_digits=12,
                verbose_name='预算单价'
            ),
        ),
        
        migrations.AlterField(
            model_name='purchaserequest',
            name='amount',
            field=models.DecimalField(
                decimal_places=2,
                editable=False,
                help_text='自动计算：预算单价 × 需求数量',
                max_digits=15,
                verbose_name='预算金额'
            ),
        ),
        
        # 更新订单号字段为唯一
        migrations.AlterField(
            model_name='purchaserequest',
            name='order_number',
            field=models.CharField(
                blank=True,
                help_text='审批通过后自动生成，格式：PO+日期+序号',
                max_length=50,
                unique=True,
                verbose_name='采购订单号'
            ),
        ),
    ]
