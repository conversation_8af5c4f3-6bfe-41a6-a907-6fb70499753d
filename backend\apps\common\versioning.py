"""
API版本控制
"""
from rest_framework.versioning import URLPathVersioning


class CustomURLPathVersioning(URLPathVersioning):
    """自定义URL路径版本控制"""
    
    default_version = 'v1'
    allowed_versions = ['v1', 'v2']
    version_param = 'version'
    
    def determine_version(self, request, *args, **kwargs):
        """确定API版本"""
        version = super().determine_version(request, *args, **kwargs)
        
        # 如果没有指定版本，使用默认版本
        if version is None:
            version = self.default_version
        
        # 验证版本是否支持
        if version not in self.allowed_versions:
            version = self.default_version
        
        return version


class APIVersionMixin:
    """API版本混入类"""
    
    def get_serializer_class(self):
        """根据版本选择序列化器"""
        version = getattr(self.request, 'version', 'v1')
        
        # 尝试获取版本特定的序列化器
        version_serializer_name = f"{self.serializer_class.__name__}_{version.upper()}"
        
        # 检查是否存在版本特定的序列化器
        if hasattr(self, version_serializer_name.lower()):
            return getattr(self, version_serializer_name.lower())
        
        return super().get_serializer_class()
    
    def get_queryset(self):
        """根据版本调整查询集"""
        queryset = super().get_queryset()
        version = getattr(self.request, 'version', 'v1')
        
        # 可以根据版本调整查询逻辑
        # 例如：v2版本可能包含更多字段或不同的过滤逻辑
        
        return queryset
