"""
自定义中间件
"""
import time
import logging
from django.utils.deprecation import MiddlewareMixin
from django.db import connection
from django.conf import settings

logger = logging.getLogger(__name__)


class APIPerformanceMiddleware(MiddlewareMixin):
    """API性能监控中间件"""

    def process_request(self, request):
        """请求开始时记录时间和数据库查询数"""
        request.start_time = time.time()
        request.start_queries = len(connection.queries)
        return None

    def process_response(self, request, response):
        """请求结束时计算耗时和数据库查询数"""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            query_count = len(connection.queries) - getattr(request, 'start_queries', 0)

            # 记录慢查询（超过1秒）
            if duration > 1.0:
                logger.warning(
                    f"Slow API: {request.method} {request.path} "
                    f"took {duration:.2f}s with {query_count} DB queries"
                )

            # 记录数据库查询过多的请求
            if query_count > 20:
                logger.warning(
                    f"High DB queries: {request.method} {request.path} "
                    f"used {query_count} queries in {duration:.2f}s"
                )

            # 在调试模式下添加性能信息到响应头
            if settings.DEBUG:
                response['X-Response-Time'] = f"{duration:.3f}s"
                response['X-DB-Queries'] = str(query_count)

        return response


class RequestLoggingMiddleware(MiddlewareMixin):
    """请求日志中间件"""
    
    def process_request(self, request):
        """记录请求信息"""
        if request.path.startswith('/api/'):
            logger.info(
                f"API Request: {request.method} {request.path} "
                f"from {self.get_client_ip(request)}"
            )
        return None
    
    def process_response(self, request, response):
        """记录响应信息"""
        if request.path.startswith('/api/'):
            logger.info(
                f"API Response: {request.method} {request.path} "
                f"status={response.status_code}"
            )
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class CacheControlMiddleware(MiddlewareMixin):
    """缓存控制中间件"""

    def process_response(self, request, response):
        """为API响应添加缓存控制头"""
        if request.path.startswith('/api/'):
            if request.method == 'GET':
                if 'user_menus' in request.path:
                    # 用户菜单缓存15分钟（关键优化）
                    response['Cache-Control'] = 'public, max-age=900'
                    response['ETag'] = f'"{hash(str(response.content))}"'
                elif 'dashboard' in request.path:
                    # 仪表板数据缓存5分钟
                    response['Cache-Control'] = 'public, max-age=300'
                elif 'departments' in request.path or 'dicts' in request.path:
                    # 基础数据缓存30分钟
                    response['Cache-Control'] = 'public, max-age=1800'
                elif 'menus' in request.path and 'tree' in request.path:
                    # 菜单树缓存20分钟
                    response['Cache-Control'] = 'public, max-age=1200'
                elif 'requests' in request.path:
                    # 采购需求数据缓存1分钟
                    response['Cache-Control'] = 'public, max-age=60'
                else:
                    # 默认缓存5分钟
                    response['Cache-Control'] = 'public, max-age=300'
            else:
                # 非GET请求不缓存
                response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response['Pragma'] = 'no-cache'
                response['Expires'] = '0'

        return response


class CORSMiddleware(MiddlewareMixin):
    """自定义CORS中间件（如果需要更细粒度的控制）"""

    def process_response(self, request, response):
        """添加CORS头"""
        # 这里可以根据需要添加更复杂的CORS逻辑
        # 目前使用django-cors-headers，这个中间件作为备用
        return response
