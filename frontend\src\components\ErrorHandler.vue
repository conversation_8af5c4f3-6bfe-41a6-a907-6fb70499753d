<template>
  <div class="error-boundary" v-if="hasError">
    <a-result
      status="error"
      :title="errorTitle"
      :sub-title="errorMessage"
    >
      <template #extra>
        <a-button type="primary" @click="retry">重试</a-button>
        <a-button @click="goHome">返回首页</a-button>
      </template>
    </a-result>
  </div>
  <slot v-else />
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'ErrorHandler',
  props: {
    error: {
      type: Error,
      default: null
    }
  },
  setup(props, { emit }) {
    const router = useRouter()
    const hasError = ref(false)
    const errorTitle = ref('出现错误')
    const errorMessage = ref('页面加载失败，请稍后重试')

    // 监听错误
    if (props.error) {
      hasError.value = true
      errorTitle.value = '系统错误'
      errorMessage.value = props.error.message || '发生未知错误'
    }

    // 重试
    const retry = () => {
      hasError.value = false
      emit('retry')
      // 刷新页面
      window.location.reload()
    }

    // 返回首页
    const goHome = () => {
      router.push('/dashboard')
    }

    return {
      hasError,
      errorTitle,
      errorMessage,
      retry,
      goHome
    }
  }
}
</script>

<style scoped>
.error-boundary {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>