# Generated by Django 5.2.4 on 2025-07-17 13:20

from django.db import migrations


def copy_type_field_data(apps, schema_editor):
    """复制type和type_display字段的数据到新字段"""
    Dictionary = apps.get_model('purchase', 'Dictionary')

    updated_count = 0
    for record in Dictionary.objects.all():
        record.type_code = record.type
        record.type_name = record.type_display
        record.save()
        updated_count += 1

    print(f"复制了 {updated_count} 条记录的字段数据")


def reverse_copy_type_field_data(apps, schema_editor):
    """回滚操作"""
    Dictionary = apps.get_model('purchase', 'Dictionary')
    Dictionary.objects.all().update(type_code=None, type_name=None)


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0034_add_new_type_fields"),
    ]

    operations = [
        migrations.RunPython(
            copy_type_field_data,
            reverse_copy_type_field_data
        ),
    ]
