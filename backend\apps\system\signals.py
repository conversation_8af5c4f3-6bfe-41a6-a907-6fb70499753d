"""
系统信号处理器
用于自动触发通知和日志记录
"""
from django.db.models.signals import post_save, pre_save, post_delete
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import SystemLog, Notification
from .logging_service import LoggingService, log_user_login, log_user_logout
from .notification_service import NotificationService

User = get_user_model()


@receiver(user_logged_in)
def log_user_login_signal(sender, request, user, **kwargs):
    """用户登录信号处理"""
    ip_address = get_client_ip(request) if request else None
    user_agent = request.META.get('HTTP_USER_AGENT', '') if request else ''
    
    # 记录登录日志
    log_user_login(user, ip_address, user_agent, success=True)
    
    # 发送登录通知（可选，根据需要启用）
    # NotificationService.create_notification(
    #     title='登录通知',
    #     content=f'您于 {timezone.now().strftime("%Y-%m-%d %H:%M:%S")} 登录系统',
    #     notification_type='system',
    #     recipient=user,
    #     priority='low'
    # )


@receiver(user_logged_out)
def log_user_logout_signal(sender, request, user, **kwargs):
    """用户登出信号处理"""
    if user:
        ip_address = get_client_ip(request) if request else None
        log_user_logout(user, ip_address)


@receiver(user_login_failed)
def log_user_login_failed_signal(sender, credentials, request, **kwargs):
    """用户登录失败信号处理"""
    username = credentials.get('username', 'unknown')
    ip_address = get_client_ip(request) if request else None
    user_agent = request.META.get('HTTP_USER_AGENT', '') if request else ''

    try:
        # 尝试查找用户，如果不存在则使用None
        user_obj = None
        if username and username != 'unknown':
            try:
                user_obj = User.objects.get(username=username)
            except User.DoesNotExist:
                # 用户不存在，使用None，这样会记录为匿名用户的安全事件
                pass

        # 记录登录失败日志
        LoggingService.log_security_event(
            user=user_obj,
            event_type='login_failed',
            event_description=f'用户 {username} 登录失败',
            ip_address=ip_address,
            user_agent=user_agent,
            risk_level='MEDIUM'
        )
    except Exception as e:
        # 如果日志记录失败，不要影响正常的登录流程
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"记录登录失败日志时出错: {e}")


@receiver(post_save, sender=User)
def log_user_changes(sender, instance, created, **kwargs):
    """用户变更日志"""
    if created:
        LoggingService.log_user_action(
            user=instance,
            log_type='create',
            action=f'创建用户账户: {instance.username}',
            target_model='User',
            target_id=instance.id
        )
        
        # 发送欢迎通知
        NotificationService.create_notification(
            title='欢迎使用采购管理系统',
            content='您的账户已创建成功，欢迎使用采购管理系统！',
            notification_type='system',
            recipient=instance,
            priority='normal'
        )
    else:
        LoggingService.log_user_action(
            user=instance,
            log_type='update',
            action=f'更新用户信息: {instance.username}',
            target_model='User',
            target_id=instance.id
        )


@receiver(post_delete, sender=User)
def log_user_deletion(sender, instance, **kwargs):
    """用户删除日志"""
    LoggingService.log_user_action(
        user=None,
        log_type='delete',
        action=f'删除用户账户: {instance.username}',
        target_model='User',
        target_id=instance.id
    )


@receiver(post_save, sender=SystemLog)
def process_critical_logs(sender, instance, created, **kwargs):
    """处理关键日志事件"""
    if not created:
        return
    
    # 检查是否为安全事件
    if instance.log_type == 'security':
        request_data = instance.request_data or {}
        risk_level = request_data.get('risk_level', 'MEDIUM')
        
        # 高风险事件需要通知管理员
        if risk_level in ['HIGH', 'CRITICAL']:
            # 获取管理员用户
            admin_users = User.objects.filter(is_superuser=True, is_active=True)
            
            for admin in admin_users:
                NotificationService.create_notification(
                    title=f'安全警告: {instance.action}',
                    content=f'检测到高风险安全事件，请及时处理。\n事件详情: {instance.action}\n时间: {instance.created_at.strftime("%Y-%m-%d %H:%M:%S")}',
                    notification_type='system',
                    recipient=admin,
                    priority='urgent',
                    target_model='SystemLog',
                    target_id=instance.id
                )
    
    # 检查是否为系统错误
    elif instance.log_type == 'system':
        request_data = instance.request_data or {}
        level = request_data.get('level', 'INFO')
        
        # 错误级别事件需要通知技术人员
        if level in ['ERROR', 'CRITICAL']:
            # 这里可以根据实际情况获取技术人员
            tech_users = User.objects.filter(
                groups__name='技术人员',
                is_active=True
            )
            
            for tech_user in tech_users:
                NotificationService.create_notification(
                    title=f'系统错误: {instance.action}',
                    content=f'系统发生错误，请及时处理。\n错误详情: {instance.action}\n时间: {instance.created_at.strftime("%Y-%m-%d %H:%M:%S")}',
                    notification_type='system',
                    recipient=tech_user,
                    priority='high',
                    target_model='SystemLog',
                    target_id=instance.id
                )


def get_client_ip(request):
    """获取客户端IP地址"""
    if not request:
        return None
    
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


# 采购流程相关信号处理
def setup_purchase_signals():
    """设置采购流程相关信号"""
    from apps.purchase.models import PurchaseRequest
    
    @receiver(post_save, sender=PurchaseRequest)
    def handle_purchase_request_changes(sender, instance, created, **kwargs):
        """处理采购申请变更"""
        if created:
            # # 记录创建日志
            # LoggingService.log_business_operation(
            #     user=instance.requester,
            #     operation_type='create',
            #     operation_description=f'创建采购申请: {instance.item_name}',
            #     business_object='PurchaseRequest',
            #     object_id=instance.id,
            #     after_data={
            #         'item_name': instance.item_name,
            #         'quantity': instance.budget_quantity,
            #         'unit_price': str(instance.budget_unit_price),
            #         'amount': str(instance.budget_total_amount),
            #         'status': instance.status
            #     }
            # )
            
            # 发送提交通知给申请人
            NotificationService.create_notification(
                title=f'采购申请已提交 - {instance.item_name}',
                content=f'您的采购申请已成功提交，申请编号：{instance.id}',
                notification_type='purchase',
                recipient=instance.requester,
                target_model='PurchaseRequest',
                target_id=instance.id,
                target_url=f'/purchase/requests/{instance.id}',
                priority='normal'
            )
        
        else:
            # 记录状态变更日志，将状态编码转换为名称
            try:
                from apps.purchase.services.dict_service import DictService
                status_name = DictService.get_text('status', instance.status, instance.status)
            except Exception:
                status_name = instance.status

            # 获取请求上下文信息
            try:
                from .request_context import RequestContext
                ip_address = RequestContext.get_client_ip()
                user_agent = RequestContext.get_user_agent()
                current_user = RequestContext.get_user()
            except Exception:
                ip_address = None
                user_agent = None
                current_user = None

            LoggingService.log_business_operation(
                user=current_user or instance.requester,
                operation_type='update',
                operation_description=f'更新采购申请状态: {instance.item_name} -> {status_name}',
                business_object='PurchaseRequest',
                object_id=instance.id,
                before_data={
                    'ip_address': ip_address,
                    'user_agent': user_agent
                },
                after_data={
                    'status': status_name,  # 保存状态名称而不是编码
                    'status_code': instance.status,  # 同时保存编码用于系统处理
                    'updated_at': instance.updated_at.isoformat() if instance.updated_at else None
                }
            )


# 数据变更审计
def setup_audit_signals():
    """设置数据变更审计信号"""
    from apps.system.models import Department
    from apps.purchase.models import Dictionary

    @receiver(post_save, sender=Department)
    def audit_department_changes(sender, instance, created, **kwargs):
        """审计部门变更"""
        action = '创建部门' if created else '更新部门'
        LoggingService.log_user_action(
            user=None,  # 这里可以从request中获取当前用户
            log_type='update' if not created else 'create',
            action=f'{action}: {instance.dept_name}',
            target_model='Department',
            target_id=instance.id
        )

    @receiver(post_save, sender=Dictionary)
    def audit_dict_changes(sender, instance, created, **kwargs):
        """审计字典数据变更"""
        action = '创建字典项' if created else '更新字典项'
        LoggingService.log_user_action(
            user=None,  # 这里可以从request中获取当前用户
            log_type='update' if not created else 'create',
            action=f'{action}: {instance.type_code} - {instance.name}',
            target_model='Dictionary',
            target_id=instance.id
        )


# 初始化所有信号
def init_signals():
    """初始化所有信号处理器"""
    setup_purchase_signals()
    setup_audit_signals()
    import logging
    logger = logging.getLogger(__name__)
    logger.info("系统信号处理器已初始化")
