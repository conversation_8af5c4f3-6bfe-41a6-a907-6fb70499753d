/**
 * 筛选功能验证工具
 * 用于验证统一筛选布局后的功能完整性
 */

/**
 * 验证筛选样式类是否正确应用
 * @param {Element} container - 筛选容器元素
 * @returns {Object} 验证结果
 */
export function validateFilterStyles(container) {
  const results = {
    success: true,
    errors: [],
    warnings: []
  };

  // 检查容器样式类
  if (!container.classList.contains('unified-filter-container')) {
    results.errors.push('筛选容器缺少 unified-filter-container 样式类');
    results.success = false;
  }

  // 检查筛选行
  const filterRows = container.querySelectorAll('.unified-filter-row');
  if (filterRows.length === 0) {
    results.errors.push('未找到筛选行元素');
    results.success = false;
  }

  // 检查筛选控件样式
  const selects = container.querySelectorAll('.unified-filter-select');
  const inputs = container.querySelectorAll('.unified-filter-input');
  const datePickers = container.querySelectorAll('.unified-filter-date-picker');

  if (selects.length === 0 && inputs.length === 0 && datePickers.length === 0) {
    results.errors.push('未找到任何筛选控件');
    results.success = false;
  }

  // 检查响应式布局
  const cols = container.querySelectorAll('.ant-col');
  cols.forEach((col, index) => {
    const hasResponsiveClasses = ['xs', 'sm', 'md', 'lg', 'xl'].some(size => 
      Array.from(col.classList).some(cls => cls.includes(`ant-col-${size}`))
    );
    
    if (!hasResponsiveClasses) {
      results.warnings.push(`第${index + 1}个列缺少响应式样式类`);
    }
  });

  return results;
}

/**
 * 验证筛选功能是否正常工作
 * @param {Object} filterData - 筛选数据对象
 * @param {Function} filterChangeHandler - 筛选变化处理函数
 * @returns {Object} 验证结果
 */
export function validateFilterFunctionality(filterData, filterChangeHandler) {
  const results = {
    success: true,
    errors: [],
    warnings: []
  };

  // 检查筛选数据对象
  if (!filterData || typeof filterData !== 'object') {
    results.errors.push('筛选数据对象无效');
    results.success = false;
    return results;
  }

  // 检查必要的筛选字段
  const requiredFields = ['status', 'department', 'itemName'];
  requiredFields.forEach(field => {
    if (!(field in filterData)) {
      results.warnings.push(`缺少必要的筛选字段: ${field}`);
    }
  });

  // 检查筛选处理函数
  if (typeof filterChangeHandler !== 'function') {
    results.errors.push('筛选变化处理函数无效');
    results.success = false;
  }

  return results;
}

/**
 * 验证页面筛选配置的完整性
 * @param {string} pageName - 页面名称
 * @returns {Object} 验证结果
 */
export function validatePageFilterConfig(pageName) {
  const results = {
    success: true,
    errors: [],
    warnings: [],
    pageInfo: {
      name: pageName,
      expectedFilters: []
    }
  };

  // 定义各页面预期的筛选项
  const pageFilterConfigs = {
    'purchase/requests': {
      required: ['status', 'purchaseType', 'department', 'requester', 'minAmount', 'maxAmount', 'dateRange'],
      optional: ['id', 'itemName', 'specification', 'procurementMethod', 'requirementSource', 'fundProject', 'unit', 'itemCategory', 'remarks']
    },
    'purchase-approval': {
      required: ['status', 'purchaseType', 'department', 'requester', 'approver', 'minAmount', 'maxAmount', 'approvalTimeRange'],
      optional: ['id', 'itemName', 'spec', 'procurementMethod', 'requirementSource', 'fundProject', 'unit', 'itemCategory', 'remarks']
    },
    'purchase-procurement': {
      required: ['status', 'purchaseType', 'department', 'purchaser', 'minAmount', 'maxAmount', 'dateRange'],
      optional: ['id', 'itemName', 'specification', 'procurementMethod', 'requirementSource', 'fundProject', 'unit', 'itemCategory', 'remarks']
    },
    'purchase/acceptance': {
      required: ['status', 'purchaseType', 'department', 'acceptor', 'minSettlementAmount', 'maxSettlementAmount', 'acceptanceTimeRange'],
      optional: ['id', 'itemName', 'specification', 'procurementMethod', 'requirementSource', 'fundProject', 'unit', 'itemCategory', 'remarks']
    },
    'purchase/reimbursement': {
      required: ['status', 'purchaseType', 'department', 'financeReviewer', 'minSettlementAmount', 'maxSettlementAmount', 'reimbursementTimeRange'],
      optional: ['id', 'itemName', 'specification', 'procurementMethod', 'requirementSource', 'fundProject', 'unit', 'itemCategory', 'remarks']
    }
  };

  const config = pageFilterConfigs[pageName];
  if (!config) {
    results.errors.push(`未找到页面 ${pageName} 的筛选配置`);
    results.success = false;
    return results;
  }

  results.pageInfo.expectedFilters = [...config.required, ...config.optional];
  results.pageInfo.requiredCount = config.required.length;
  results.pageInfo.optionalCount = config.optional.length;

  return results;
}

/**
 * 生成筛选功能测试报告
 * @param {Array} pageResults - 各页面验证结果数组
 * @returns {Object} 测试报告
 */
export function generateFilterTestReport(pageResults) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalPages: pageResults.length,
      passedPages: 0,
      failedPages: 0,
      warningPages: 0
    },
    details: pageResults,
    recommendations: []
  };

  // 统计结果
  pageResults.forEach(result => {
    if (result.success) {
      if (result.warnings && result.warnings.length > 0) {
        report.summary.warningPages++;
      } else {
        report.summary.passedPages++;
      }
    } else {
      report.summary.failedPages++;
    }
  });

  // 生成建议
  if (report.summary.failedPages > 0) {
    report.recommendations.push('存在筛选功能错误，需要立即修复');
  }
  
  if (report.summary.warningPages > 0) {
    report.recommendations.push('存在筛选功能警告，建议优化');
  }

  if (report.summary.passedPages === report.summary.totalPages) {
    report.recommendations.push('所有页面筛选功能正常，可以进行性能优化');
  }

  return report;
}

/**
 * 性能优化建议
 */
export const PERFORMANCE_RECOMMENDATIONS = {
  debounce: {
    title: '防抖优化',
    description: '对输入框筛选使用防抖，减少API调用频率',
    implementation: 'debounce(handleFilterChange, 500)'
  },
  
  virtualization: {
    title: '虚拟化优化',
    description: '对大量筛选选项使用虚拟滚动',
    implementation: 'ant-design-vue virtual-select'
  },
  
  caching: {
    title: '缓存优化',
    description: '缓存筛选选项数据，避免重复请求',
    implementation: 'localStorage + TTL'
  },
  
  lazy: {
    title: '懒加载优化',
    description: '筛选选项数据按需加载',
    implementation: 'onFocus 时加载数据'
  }
};

/**
 * 用户体验优化建议
 */
export const UX_RECOMMENDATIONS = {
  placeholder: {
    title: '占位符优化',
    description: '使用更具描述性的占位符文本',
    example: '搜索物品名称 而不是 物品名称'
  },
  
  shortcuts: {
    title: '快捷键支持',
    description: '添加键盘快捷键支持',
    example: 'Ctrl+F 聚焦搜索框'
  },
  
  presets: {
    title: '预设筛选',
    description: '提供常用筛选条件预设',
    example: '今日新增、本周待审批'
  },
  
  persistence: {
    title: '状态持久化',
    description: '记住用户的筛选偏好',
    example: '页面刷新后保持筛选状态'
  }
};
