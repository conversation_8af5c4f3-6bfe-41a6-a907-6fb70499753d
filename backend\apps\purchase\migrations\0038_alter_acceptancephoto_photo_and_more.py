# Generated by Django 5.2.4 on 2025-07-27 16:33

import apps.purchase.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0037_normalize_dict_codes"),
    ]

    operations = [
        migrations.AlterField(
            model_name="acceptancephoto",
            name="photo",
            field=models.ImageField(
                help_text="验收照片",
                upload_to=apps.purchase.models.get_acceptance_photo_path,
                verbose_name="照片",
            ),
        ),
        migrations.AlterField(
            model_name="acceptancephoto",
            name="photo_type",
            field=models.CharField(
                choices=[
                    ("courier", "快递单照片"),
                    ("item", "物品照片"),
                    ("package", "包装照片"),
                    ("front", "正面照片"),
                    ("side", "侧面照片"),
                    ("overall", "整体照片"),
                ],
                default="item",
                max_length=20,
                verbose_name="照片类型",
            ),
        ),
    ]
