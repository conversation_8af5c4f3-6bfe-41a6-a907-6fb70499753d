from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework_simplejwt.tokens import RefreshToken
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth import login
from django.db import models
from .models import User
from .serializers import (
    LoginSerializer, UserSerializer, UserCreateSerializer,
    UserUpdateSerializer, PasswordChangeSerializer
)
from apps.system.permissions import (
    get_user_permissions,
    get_permission_info,
    check_page_permission,
    require_permission,
    get_user_button_permissions
)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """
    用户登录接口
    POST /api/auth/login
    """
    from django.contrib.auth.signals import user_login_failed

    serializer = LoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']

        # 生成JWT token
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # 更新最后登录时间
        login(request, user)

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'token': str(access_token),
                'refresh': str(refresh),
                'user': UserSerializer(user).data
            }
        })

    # 登录失败时触发信号
    username = request.data.get('username', 'unknown')
    user_login_failed.send(
        sender=__name__,
        credentials={'username': username},
        request=request
    )

    return Response({
        'code': 401,
        'message': '用户名或密码错误',
        'data': {}
    }, status=status.HTTP_401_UNAUTHORIZED)


@api_view(['POST'])
def logout_view(request):
    """
    用户登出接口
    POST /api/auth/logout
    """
    try:
        refresh_token = request.data.get('refresh')
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()
        
        return Response({
            'code': 200,
            'message': '登出成功',
            'data': {}
        })
    except Exception as e:
        return Response({
            'code': 400,
            'message': '登出失败',
            'data': {}
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def user_info_view(request):
    """
    获取当前用户信息
    GET /api/auth/user
    """
    serializer = UserSerializer(request.user)
    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


class UserListCreateView(generics.ListCreateAPIView):
    """
    用户列表和创建接口
    GET /api/auth/users - 获取用户列表
    POST /api/auth/users - 创建用户
    """
    queryset = User.objects.all()
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['role', 'is_active', 'dept_id']
    search_fields = ['username', 'real_name', 'phone']
    ordering_fields = ['date_joined', 'username']
    ordering = ['-date_joined']

    # 明确指定分页类
    class CustomPagination(PageNumberPagination):
        page_size = 10
        page_size_query_param = 'page_size'
        max_page_size = 1000

    pagination_class = CustomPagination

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return UserCreateSerializer
        return UserSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # 支持按部门层级路径筛选
        hierarchy_path = self.request.query_params.get('hierarchy_path')
        if hierarchy_path:
            # 由于dept_hierarchy_path是property而不是数据库字段，需要通过部门表查询
            try:
                from apps.system.models import Department
                matching_depts = Department.objects.filter(
                    hierarchy_path__icontains=hierarchy_path
                ).values_list('id', flat=True)
                queryset = queryset.filter(dept_id__in=matching_depts)
            except ImportError:
                # 如果无法导入部门模型，忽略此筛选
                pass

        return queryset
    
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            # 获取分页信息
            paginated_response = self.get_paginated_response(serializer.data)
            return Response({
                'code': 200,
                'message': 'success',
                'data': {
                    'results': serializer.data,
                    'count': paginated_response.data['count'],
                    'next': paginated_response.data['next'],
                    'previous': paginated_response.data['previous']
                }
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'results': serializer.data,
                'count': queryset.count()
            }
        })
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'code': 200,
                'message': '用户创建成功',
                'data': UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'code': 400,
            'message': '数据验证失败',
            'data': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    用户详情、更新和删除接口
    GET /api/auth/users/{id} - 获取用户详情
    PUT /api/auth/users/{id} - 更新用户
    DELETE /api/auth/users/{id} - 删除用户
    """
    queryset = User.objects.all()
    
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return UserUpdateSerializer
        return UserSerializer
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    
    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'code': 200,
                'message': '用户更新成功',
                'data': UserSerializer(user).data
            })
        
        return Response({
            'code': 400,
            'message': '数据验证失败',
            'data': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    def destroy(self, request, *args, **kwargs):
        """删除用户并清理相关数据"""
        instance = self.get_object()
        username = instance.username
        user_id = instance.id

        try:
            from django.db import transaction

            # 首先检查是否有不能删除的关联数据
            self._check_delete_constraints(instance)

            with transaction.atomic():
                # 手动清理相关数据，避免外键约束问题
                from apps.system.models_permission import UserPermission
                from apps.system.models import SystemLog

                # 清空用户角色字段（简化版）
                instance.role = ''
                instance.save(update_fields=['role'])

                # 删除用户权限关联
                UserPermission.objects.filter(user=instance).delete()

                # 更新系统日志（将user设为NULL而不是删除日志）
                SystemLog.objects.filter(user=instance).update(
                    user=None,
                    username=f"已删除用户({username})"
                )

                # 检查并清理验收照片数据
                try:
                    from apps.purchase.models import AcceptancePhoto
                    AcceptancePhoto.objects.filter(uploader=instance).update(uploader=None)
                except ImportError:
                    # 如果模块不存在，跳过
                    pass

                # 删除用户（Django会自动处理CASCADE关系，如通知等）
                instance.delete()

                # 记录删除日志
                try:
                    from apps.system.logging_service import LoggingService
                    LoggingService.log_user_action(
                        user=request.user,
                        log_type='delete',
                        action=f'删除用户账户: {username}',
                        target_model='User',
                        target_id=user_id,
                        ip_address=self.get_client_ip(request),
                        user_agent=request.META.get('HTTP_USER_AGENT', '')
                    )
                except Exception as e:
                    # 日志记录失败不应该影响删除操作
                    print(f"记录删除日志失败: {e}")

            return Response({
                'code': 200,
                'message': '用户删除成功',
                'data': {}
            })

        except ValueError as e:
            # 业务逻辑错误（如有关联数据不能删除）
            return Response({
                'code': 400,
                'message': str(e),
                'data': {}
            }, status=400)
        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response({
                'code': 500,
                'message': f'删除用户失败: {str(e)}',
                'data': {}
            }, status=500)

    def _check_delete_constraints(self, user):
        """检查删除约束"""
        constraints = []

        # 检查采购请求关联
        try:
            from apps.purchase.models import PurchaseRequest

            # 检查作为申请人的采购请求
            requester_count = PurchaseRequest.objects.filter(requester=user).count()
            if requester_count > 0:
                constraints.append(f"该用户是 {requester_count} 个采购请求的申请人")

            # 检查作为采购人的采购请求
            purchaser_count = PurchaseRequest.objects.filter(
                purchaser=user,
                status__in=['purchased', 'pending_acceptance', 'accepted', 'pending_reimbursement', 'settled']
            ).count()
            if purchaser_count > 0:
                constraints.append(f"该用户是 {purchaser_count} 个采购请求的采购人")

            # 检查作为验收人的采购请求
            acceptor_count = PurchaseRequest.objects.filter(
                acceptor=user,
                status__in=['accepted', 'pending_reimbursement', 'settled']
            ).count()
            if acceptor_count > 0:
                constraints.append(f"该用户是 {acceptor_count} 个采购请求的验收人")

            # 检查作为结算人的采购请求
            reimburser_count = PurchaseRequest.objects.filter(
                reimburser=user,
                status='settled'
            ).count()
            if reimburser_count > 0:
                constraints.append(f"该用户是 {reimburser_count} 个采购请求的结算人")

        except ImportError:
            # 如果采购模块不存在，跳过
            pass

        if constraints:
            raise ValueError(f"无法删除用户，存在以下关联数据：\n" + "\n".join(f"• {constraint}" for constraint in constraints))

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@api_view(['POST'])
def change_password_view(request):
    """
    修改密码接口
    POST /api/auth/change-password
    """
    serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        serializer.save()
        return Response({
            'code': 200,
            'message': '密码修改成功',
            'data': {}
        })

    return Response({
        'code': 400,
        'message': '密码修改失败',
        'data': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def user_permissions_view(request):
    """
    获取当前用户权限信息
    GET /api/auth/permissions
    """
    permission_info = get_permission_info(request.user)

    return Response({
        'code': 200,
        'message': 'success',
        'data': permission_info
    })


@api_view(['GET'])
def roles_list_view(request):
    """
    获取所有角色列表
    GET /api/auth/roles
    """
    try:
        # 尝试从角色管理系统获取角色数据
        try:
            from apps.roles.models import Role
            from apps.roles.serializers import RoleSerializer

            # 获取所有激活的角色
            role_objects = Role.objects.filter(is_active=True).order_by('name')
            roles = []

            for role in role_objects:
                roles.append({
                    'key': role.code,  # 使用角色编码作为key
                    'name': role.name,
                    'id': role.id,
                    'description': role.description,
                    'permissions': [perm.codename for perm in role.permissions.all()]
                })

            # 如果角色管理系统中没有角色，使用默认角色
            if not roles:
                raise ImportError("No roles found in role management system")

        except (ImportError, Exception) as e:
            # 如果角色管理系统不可用，回退到硬编码角色
            print(f"角色管理系统不可用，使用默认角色: {e}")
            roles = []
            for role_key, role_data in ROLE_PERMISSIONS.items():
                roles.append({
                    'key': role_key,
                    'name': role_data['name'],
                    'permissions': role_data['permissions']
                })

        return Response({
            'code': 200,
            'message': 'success',
            'data': roles
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取角色列表失败: {str(e)}',
            'data': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def check_permission_view(request):
    """
    检查用户权限
    POST /api/auth/check-permission
    """
    permission = request.data.get('permission')
    page_path = request.data.get('page_path')

    result = {}

    if permission:
        from apps.system.permissions import has_permission
        result['has_permission'] = has_permission(request.user, permission)

    if page_path:
        result['can_access_page'] = check_page_permission(request.user, page_path)

    return Response({
        'code': 200,
        'message': 'success',
        'data': result
    })


@api_view(['GET', 'POST'])
def user_permissions_management_view(request, pk):
    """
    用户权限管理
    GET /api/auth/users/{id}/permissions/ - 获取用户权限
    POST /api/auth/users/{id}/permissions/ - 更新用户权限
    """
    try:
        user = User.objects.get(pk=pk)
    except User.DoesNotExist:
        return Response({
            'code': 404,
            'message': '用户不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)

    if request.method == 'GET':
        # 获取用户权限
        user_permissions = get_user_permissions(user)

        # 获取用户角色信息（简化版）
        try:
            from apps.roles.models import Role
            if user.role:
                role_obj = Role.objects.filter(code=user.role, is_active=True).first()
                if role_obj:
                    role_info = {
                        'roles': [{'id': role_obj.id, 'name': role_obj.name, 'code': role_obj.code}],
                        'primary_role': role_obj
                    }
                else:
                    role_info = {'roles': [], 'primary_role': None}
            else:
                role_info = {'roles': [], 'primary_role': None}
        except Exception:
            role_info = {'roles': [], 'primary_role': None}

        # 转换为前端需要的格式
        permissions_data = []
        for perm in user_permissions:
            permissions_data.append({
                'codename': perm,
                'name': perm,
                'key': perm
            })

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'permissions': permissions_data,
                'role_info': role_info
            }
        })

    elif request.method == 'POST':
        # 更新用户权限（通过角色）
        permission_key = request.data.get('permission_key')
        enabled = request.data.get('enabled', False)

        if not permission_key:
            return Response({
                'code': 400,
                'message': '权限key不能为空',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 注意：这里简化处理，实际应该通过角色来管理权限
        # 因为当前系统是基于角色的权限系统
        return Response({
            'code': 200,
            'message': '用户权限更新成功（注意：当前系统基于角色权限，请通过角色管理权限）',
            'data': {
                'user_id': user.id,
                'permission_key': permission_key,
                'enabled': enabled
            }
        })


@api_view(['GET', 'PUT'])
def user_profile_view(request):
    """
    用户个人资料接口
    GET /api/auth/users/profile/ - 获取个人资料
    PUT /api/auth/users/profile/ - 更新个人资料
    """
    if request.method == 'GET':
        serializer = UserSerializer(request.user)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })

    elif request.method == 'PUT':
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'code': 200,
                'message': '个人资料更新成功',
                'data': UserSerializer(user).data
            })

        return Response({
            'code': 400,
            'message': '数据验证失败',
            'data': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


# ==================== 用户导入导出功能 ====================

@api_view(['GET'])
@permission_classes([AllowAny])
def download_user_template(request):
    """
    下载用户导入模板
    GET /api/auth/users/template/
    """
    try:
        import io
        from django.http import HttpResponse
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = '用户导入模板'

        # 设置表头
        headers = ['登录账号', '真实姓名', '手机号码', '邮箱地址', '所属部门名称', '角色名称', '是否启用', '初始密码']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 获取实际的部门和角色数据生成示例
        try:
            from apps.system.models import Department
            from apps.roles.models import Role

            # 获取前几个可用的部门和角色
            departments = Department.objects.filter(status=True)[:3]
            roles = Role.objects.filter(is_active=True)[:3]

            dept_names = [dept.dept_name for dept in departments] if departments else ['示例部门']
            role_names = [role.name for role in roles] if roles else ['示例角色']

            # 使用实际数据生成示例
            sample_data = [
                ['user001', '张三', '13800138001', '<EMAIL>', dept_names[0], role_names[0], '是', '123456'],
                ['user002', '李四', '13800138002', '<EMAIL>', dept_names[1] if len(dept_names) > 1 else dept_names[0], role_names[1] if len(role_names) > 1 else role_names[0], '是', '123456'],
                ['user003', '王五', '13800138003', '<EMAIL>', dept_names[0], role_names[0], '否', '123456']
            ]
        except Exception as e:
            # 如果获取数据失败，使用默认示例
            sample_data = [
                ['user001', '张三', '13800138001', '<EMAIL>', '请填写实际部门名称', '请填写实际角色名称', '是', '123456'],
                ['user002', '李四', '13800138002', '<EMAIL>', '请填写实际部门名称', '请填写实际角色名称', '是', '123456'],
                ['user003', '王五', '13800138003', '<EMAIL>', '请填写实际部门名称', '请填写实际角色名称', '否', '123456']
            ]

        for row_idx, row_data in enumerate(sample_data, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 设置列宽
        column_widths = [15, 15, 15, 25, 20, 15, 12, 12]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + i)].width = width

        # 添加部门和角色参考列表工作表
        try:
            from apps.system.models import Department
            from apps.roles.models import Role

            # 创建部门列表工作表
            ws_dept = wb.create_sheet('可用部门列表')
            dept_headers = ['部门ID', '部门名称', '层级路径']
            for col, header in enumerate(dept_headers, 1):
                cell = ws_dept.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')

            departments = Department.objects.filter(status=True).order_by('hierarchy_path')
            for idx, dept in enumerate(departments, 2):
                ws_dept.cell(row=idx, column=1, value=dept.id)
                ws_dept.cell(row=idx, column=2, value=dept.dept_name)
                ws_dept.cell(row=idx, column=3, value=dept.hierarchy_path)

            # 设置部门列表列宽
            ws_dept.column_dimensions['A'].width = 10
            ws_dept.column_dimensions['B'].width = 20
            ws_dept.column_dimensions['C'].width = 30

            # 创建角色列表工作表
            ws_role = wb.create_sheet('可用角色列表')
            role_headers = ['角色ID', '角色名称', '角色编码', '角色描述']
            for col, header in enumerate(role_headers, 1):
                cell = ws_role.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')

            roles = Role.objects.filter(is_active=True).order_by('name')
            for idx, role in enumerate(roles, 2):
                ws_role.cell(row=idx, column=1, value=role.id)
                ws_role.cell(row=idx, column=2, value=role.name)
                ws_role.cell(row=idx, column=3, value=role.code)
                ws_role.cell(row=idx, column=4, value=role.description or '无描述')

            # 设置角色列表列宽
            ws_role.column_dimensions['A'].width = 10
            ws_role.column_dimensions['B'].width = 15
            ws_role.column_dimensions['C'].width = 15
            ws_role.column_dimensions['D'].width = 30

        except Exception as e:
            # 如果创建参考列表失败，记录错误但不影响主模板
            print(f"创建参考列表失败: {e}")

        # 创建填写说明工作表
        ws_instructions = wb.create_sheet('填写说明')

        # 说明表头
        instruction_headers = ['字段名称', '是否必填', '字段说明', '示例值']
        for col, header in enumerate(instruction_headers, 1):
            cell = ws_instructions.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 说明数据
        instruction_data = [
            ['登录账号', '必填', '用户的登录账号，不能重复', 'user001'],
            ['真实姓名', '必填', '用户的真实姓名', '张三'],
            ['手机号码', '选填', '用户的手机号码，格式：13800138001', '13800138001'],
            ['邮箱地址', '选填', '用户的邮箱地址', '<EMAIL>'],
            ['所属部门名称', '必填', '用户所属部门的名称，需要在部门管理中查看', '北京分公司'],
            ['角色名称', '必填', '用户角色名称：管理员/审批员/验收员/财务员/申请人', '申请人'],
            ['是否启用', '选填', '用户状态，填写"是"或"否"', '是'],
            ['初始密码', '必填', '用户的初始密码，建议6位以上', '123456']
        ]

        for row_idx, row_data in enumerate(instruction_data, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws_instructions.cell(row=row_idx, column=col_idx, value=value)

        # 设置说明工作表列宽
        ws_instructions.column_dimensions['A'].width = 15
        ws_instructions.column_dimensions['B'].width = 12
        ws_instructions.column_dimensions['C'].width = 50
        ws_instructions.column_dimensions['D'].width = 20

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="用户导入模板.xlsx"'

        return response

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'生成模板失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def import_users(request):
    """
    导入用户数据
    POST /api/auth/users/import/
    """
    try:
        from openpyxl import load_workbook

        if 'file' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要导入的文件',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # 读取Excel文件
        try:
            wb = load_workbook(file)
            ws = wb['用户导入模板']

            # 获取表头
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(cell.value)

            # 读取数据行
            data_rows = []
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):  # 跳过空行
                    data_rows.append(row)

        except Exception as e:
            return Response({
                'code': 400,
                'message': f'文件格式错误: {str(e)}',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        success_count = 0
        error_count = 0
        errors = []

        # 创建字段映射
        field_map = {
            '登录账号': 0,
            '真实姓名': 1,
            '手机号码': 2,
            '邮箱地址': 3,
            '所属部门名称': 4,
            '角色名称': 5,
            '是否启用': 6,
            '初始密码': 7
        }

        # 逐行处理导入数据
        for index, row in enumerate(data_rows):
            try:
                row_num = index + 2  # Excel行号（从第2行开始）

                # 验证必填字段
                username = row[field_map['登录账号']] if len(row) > field_map['登录账号'] else None
                if not username or not str(username).strip():
                    errors.append(f'第{row_num}行：登录账号不能为空')
                    error_count += 1
                    continue

                real_name = row[field_map['真实姓名']] if len(row) > field_map['真实姓名'] else None
                if not real_name or not str(real_name).strip():
                    errors.append(f'第{row_num}行：真实姓名不能为空')
                    error_count += 1
                    continue

                dept_name = row[field_map['所属部门名称']] if len(row) > field_map['所属部门名称'] else None
                if not dept_name or not str(dept_name).strip():
                    errors.append(f'第{row_num}行：所属部门名称不能为空')
                    error_count += 1
                    continue

                role_name = row[field_map['角色名称']] if len(row) > field_map['角色名称'] else None
                if not role_name or not str(role_name).strip():
                    errors.append(f'第{row_num}行：角色名称不能为空')
                    error_count += 1
                    continue

                password = row[field_map['初始密码']] if len(row) > field_map['初始密码'] else None
                if not password or not str(password).strip():
                    errors.append(f'第{row_num}行：初始密码不能为空')
                    error_count += 1
                    continue

                # 检查用户名是否已存在
                username = str(username).strip()
                if User.objects.filter(username=username).exists():
                    errors.append(f'第{row_num}行：登录账号"{username}"已存在')
                    error_count += 1
                    continue

                # 根据部门名称查找部门ID
                try:
                    from apps.system.models import Department
                    dept_name = str(dept_name).strip()
                    department = Department.objects.filter(dept_name=dept_name).first()
                    if not department:
                        errors.append(f'第{row_num}行：部门"{dept_name}"不存在')
                        error_count += 1
                        continue
                    dept_id = department.id
                except ImportError:
                    errors.append(f'第{row_num}行：无法查找部门信息')
                    error_count += 1
                    continue

                # 根据角色名称查找角色编码
                try:
                    from apps.roles.models import Role
                    role_name = str(role_name).strip()
                    role_obj = Role.objects.filter(name=role_name, is_active=True).first()
                    if not role_obj:
                        errors.append(f'第{row_num}行：角色"{role_name}"不存在')
                        error_count += 1
                        continue
                    role_code = role_obj.code
                except ImportError:
                    errors.append(f'第{row_num}行：无法查找角色信息')
                    error_count += 1
                    continue

                # 获取可选字段
                phone = str(row[field_map['手机号码']]).strip() if len(row) > field_map['手机号码'] and row[field_map['手机号码']] else ''
                email = str(row[field_map['邮箱地址']]).strip() if len(row) > field_map['邮箱地址'] and row[field_map['邮箱地址']] else ''
                is_active_str = str(row[field_map['是否启用']]).strip() if len(row) > field_map['是否启用'] and row[field_map['是否启用']] else '是'

                # 创建用户数据
                user_data = {
                    'username': username,
                    'real_name': str(real_name).strip(),
                    'phone': phone,
                    'email': email,
                    'dept_id': dept_id,
                    'role': role_code,
                    'is_active': is_active_str == '是'
                }

                # 创建用户
                user = User.objects.create_user(**user_data)
                user.set_password(str(password).strip())
                user.save()

                success_count += 1

            except Exception as e:
                errors.append(f'第{row_num}行：{str(e)}')
                error_count += 1

        return Response({
            'code': 200,
            'message': f'导入完成，成功{success_count}条，失败{error_count}条',
            'data': {
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors[:10]  # 只返回前10个错误
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导入失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_users(request):
    """
    导出用户数据
    GET /api/auth/users/export/
    """
    try:
        import io
        from django.http import HttpResponse
        from django.utils import timezone
        from django.db import models
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        # 获取查询参数
        dept_id_filter = request.GET.get('dept_id')
        role_filter = request.GET.get('role')
        status_filter = request.GET.get('status')
        search_filter = request.GET.get('search')

        # 构建查询
        queryset = User.objects.all()

        if dept_id_filter:
            queryset = queryset.filter(dept_id=dept_id_filter)

        if role_filter:
            queryset = queryset.filter(role=role_filter)

        if status_filter is not None:
            if status_filter.lower() == 'true':
                queryset = queryset.filter(is_active=True)
            elif status_filter.lower() == 'false':
                queryset = queryset.filter(is_active=False)

        if search_filter:
            queryset = queryset.filter(
                models.Q(username__icontains=search_filter) |
                models.Q(real_name__icontains=search_filter) |
                models.Q(phone__icontains=search_filter) |
                models.Q(email__icontains=search_filter)
            )

        users = queryset.order_by('id')

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = '用户数据'

        # 设置表头
        headers = ['用户ID', '登录账号', '真实姓名', '手机号码', '邮箱地址', '所属部门ID', '所属部门', '角色编码', '角色名称', '状态', '注册时间', '最后登录']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 准备导出数据
        for row_idx, user in enumerate(users, 2):
            # 获取部门信息
            dept_name = ''
            try:
                from apps.system.models import Department
                if user.dept_id:
                    dept = Department.objects.filter(id=user.dept_id).first()
                    dept_name = dept.dept_name if dept else ''
            except ImportError:
                pass

            # 获取角色名称
            role_name = ''
            try:
                from apps.roles.models import Role
                role = Role.objects.filter(code=user.role).first()
                role_name = role.name if role else user.role
            except ImportError:
                role_name = user.role

            # 写入数据行
            row_data = [
                user.id,
                user.username,
                user.real_name,
                user.phone or '',
                user.email or '',
                user.dept_id or '',
                dept_name,
                user.role or '',
                role_name,
                '启用' if user.is_active else '停用',
                user.date_joined.strftime('%Y-%m-%d %H:%M:%S') if user.date_joined else '',
                user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else ''
            ]

            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 设置列宽
        column_widths = [10, 15, 15, 15, 25, 12, 20, 12, 15, 10, 20, 20]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + i)].width = width

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 设置响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="用户数据_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def user_statistics_view(request):
    """
    用户统计信息接口
    GET /api/auth/users/statistics/ - 获取用户统计信息
    """
    from apps.purchase.models import PurchaseRequest
    from django.db.models import Count, Sum

    user = request.user

    # 统计用户相关的采购需求数据
    stats = PurchaseRequest.objects.filter(requester_id=user.id).aggregate(
        total_requests=Count('id'),
        total_amount=Sum('budget_total_amount'),
        pending_requests=Count('id', filter=models.Q(status='draft')),
        approved_requests=Count('id', filter=models.Q(status='approved'))
    )

    # 最近30天的统计
    from datetime import datetime, timedelta
    thirty_days_ago = datetime.now() - timedelta(days=30)
    recent_stats = PurchaseRequest.objects.filter(
        requester_id=user.id,
        created_at__gte=thirty_days_ago
    ).aggregate(
        recent_requests=Count('id'),
        recent_amount=Sum('budget_total_amount')
    )

    # 获取用户日志统计
    from apps.system.models import SystemLog

    # 登录次数统计
    login_count = SystemLog.objects.filter(
        user=user,
        log_type='login'
    ).count()

    # 审批次数统计（用户作为审批人的次数）
    approval_count = SystemLog.objects.filter(
        user=user,
        log_type__in=['approve', 'reject']
    ).count()

    # 操作次数统计（最近30天）
    recent_operations = SystemLog.objects.filter(
        user=user,
        created_at__gte=thirty_days_ago
    ).count()

    # 总操作次数
    total_operations = SystemLog.objects.filter(user=user).count()

    # 计算加入天数
    from django.utils import timezone
    join_days = (timezone.now().date() - user.date_joined.date()).days

    return Response({
        'code': 200,
        'message': 'success',
        'data': {
            # 采购相关统计
            'total_requests': stats['total_requests'] or 0,
            'request_count': stats['total_requests'] or 0,  # 兼容前端字段名
            'total_amount': float(stats['total_amount'] or 0),
            'pending_requests': stats['pending_requests'] or 0,
            'approved_requests': stats['approved_requests'] or 0,
            'recent_requests': recent_stats['recent_requests'] or 0,
            'recent_amount': float(recent_stats['recent_amount'] or 0),

            # 用户活动统计
            'login_count': login_count,
            'approval_count': approval_count,
            'total_operations': total_operations,
            'recent_operations': recent_operations,

            # 用户基本信息
            'join_date': user.date_joined.strftime('%Y-%m-%d'),
            'join_days': join_days,
            'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录'
        }
    })


@api_view(['GET'])
def admin_user_statistics_view(request):
    """
    管理员用户统计信息接口
    GET /api/auth/users/admin-statistics/ - 获取用户管理统计信息
    """
    try:
        from django.db.models import Count, Q
        from django.utils import timezone
        from datetime import timedelta

        # 总用户数
        total_count = User.objects.count()

        # 活跃用户数
        active_count = User.objects.filter(is_active=True).count()

        # 停用用户数
        inactive_count = User.objects.filter(is_active=False).count()

        # 管理员数量（超级用户或有管理员角色的用户）
        admin_count = User.objects.filter(
            Q(is_superuser=True) | Q(role='admin')
        ).count()

        # 在线用户数（最近15分钟有活动的用户）
        online_threshold = timezone.now() - timedelta(minutes=15)
        online_count = get_online_users_count(online_threshold)

        # 最近7天新注册用户数
        week_ago = timezone.now() - timedelta(days=7)
        recent_users = User.objects.filter(date_joined__gte=week_ago).count()

        # 按部门统计用户数
        # 由于dept_id是整数字段而不是外键，需要手动关联部门信息
        from apps.system.models import Department

        dept_stats_raw = User.objects.values('dept_id').annotate(
            count=Count('id')
        ).order_by('-count')[:10]  # 取前10个部门

        # 手动获取部门名称
        dept_stats = []
        for stat in dept_stats_raw:
            dept_id = stat['dept_id']
            if dept_id:
                try:
                    dept = Department.objects.get(id=dept_id)
                    dept_stats.append({
                        'dept__dept_name': dept.dept_name,
                        'count': stat['count']
                    })
                except Department.DoesNotExist:
                    dept_stats.append({
                        'dept__dept_name': f'未知部门(ID:{dept_id})',
                        'count': stat['count']
                    })
            else:
                dept_stats.append({
                    'dept__dept_name': '未分配部门',
                    'count': stat['count']
                })

        # 按角色统计用户数
        role_stats = User.objects.values('role').annotate(
            count=Count('id')
        ).order_by('-count')

        return Response({
            'code': 200,
            'message': '获取统计信息成功',
            'data': {
                'total_count': total_count,
                'active_count': active_count,
                'inactive_count': inactive_count,
                'admin_count': admin_count,
                'online_count': online_count,
                'recent_users': recent_users,
                'dept_stats': list(dept_stats),
                'role_stats': list(role_stats)
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取统计信息失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def online_users_count_view(request):
    """
    获取在线用户数量接口
    GET /api/auth/users/online-count/ - 获取当前在线用户数量
    """
    try:
        from django.utils import timezone
        from datetime import timedelta

        # 在线用户判断标准：最近15分钟有活动的用户
        online_threshold = timezone.now() - timedelta(minutes=15)
        online_count = get_online_users_count(online_threshold)

        return Response({
            'code': 200,
            'message': '获取在线用户数量成功',
            'data': {
                'count': online_count,
                'threshold_minutes': 15,
                'timestamp': timezone.now().isoformat()
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取在线用户数量失败: {str(e)}',
            'data': {'count': 0}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def get_online_users_count(threshold_time):
    """
    获取在线用户数量
    基于用户最后活动时间判断 - 使用SystemLog统计
    """
    try:
        from apps.system.models import SystemLog
        # 使用SystemLog统计最近活跃的用户
        return SystemLog.objects.filter(
            created_at__gte=threshold_time,
            user__isnull=False
        ).values('user').distinct().count()
    except:
        # 备用方案：使用last_login字段估算
        return User.objects.filter(
            last_login__gte=threshold_time,
            is_active=True
        ).count()


@api_view(['GET'])
def user_logs_view(request):
    """
    获取当前用户的日志记录
    GET /api/auth/users/logs/ - 获取个人日志记录
    """
    try:
        from apps.system.models import SystemLog
        from django.core.paginator import Paginator
        from django.db.models import Q

        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        log_type = request.GET.get('log_type', '')
        start_date = request.GET.get('start_date', '')
        end_date = request.GET.get('end_date', '')
        search = request.GET.get('search', '')

        # 构建查询条件 - 只查询当前用户的日志
        queryset = SystemLog.objects.filter(user=request.user)

        # 按日志类型筛选
        if log_type:
            queryset = queryset.filter(log_type=log_type)

        # 按时间范围筛选
        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)

        # 搜索功能
        if search:
            queryset = queryset.filter(
                Q(action__icontains=search) |
                Q(target_model__icontains=search) |
                Q(ip_address__icontains=search)
            )

        # 按时间倒序排列
        queryset = queryset.order_by('-created_at')

        # 分页处理
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # 转换数据格式
        logs = []
        for log in page_obj:
            logs.append({
                'id': log.id,
                'log_type': log.log_type,
                'log_type_display': log.get_log_type_display(),
                'action': log.action,
                'target_model': log.target_model or '-',
                'target_id': log.target_id,
                'ip_address': log.ip_address or '-',
                'user_agent': log.user_agent or '',
                'created_at': log.created_at.isoformat() if log.created_at else '',
                'request_data': log.request_data or {},
                'response_data': log.response_data or {},
            })

        return Response({
            'code': 200,
            'message': '获取个人日志成功',
            'data': {
                'results': logs,
                'count': paginator.count,
                'page': page,
                'page_size': page_size,
                'total_pages': paginator.num_pages
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取个人日志失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
