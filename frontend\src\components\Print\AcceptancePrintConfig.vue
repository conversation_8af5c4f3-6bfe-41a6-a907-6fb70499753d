<template>
  <a-modal :open="open" title="打印验收单配置" width="80%" :footer="null" @cancel="$emit('cancel')">
    <div class="print-config-container">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <h4>筛选条件</h4>
        <div class="filter-form-container">
          <a-form :model="filters" :label-col="{ style: { width: '80px', textAlign: 'right' } }">
            <a-row :gutter="[12, 8]">
              <a-col :span="4">
                <a-form-item label="需求单位">
                  <a-select v-model:value="filters.department" placeholder="选择需求单位" allowClear show-search
                    :filter-option="filterOption" @change="searchRecords">
                    <a-select-option v-for="dept in departmentOptions" :key="dept.value" :value="dept.value"
                      :label="dept.label">
                      {{ dept.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="验收人">
                  <a-select v-model:value="filters.acceptor" placeholder="选择验收人" allowClear show-search
                    :filter-option="filterOption" @change="searchRecords">
                    <a-select-option v-for="acceptor in acceptorOptions" :key="acceptor.value" :value="acceptor.value"
                      :label="acceptor.label">
                      {{ acceptor.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="采购类型">
                  <a-select v-model:value="filters.purchase_type" placeholder="选择采购类型" allowClear
                    @change="searchRecords">
                    <a-select-option v-for="type in purchaseTypeOptions" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="验收日期">
                  <a-range-picker v-model:value="filters.dateRange" format="YYYY-MM-DD" placeholder="['开始日期', '结束日期']"
                    @change="searchRecords" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item>
                  <a-space>
                    <a-button @click="resetFilters" size="large">
                      重置
                    </a-button>
                    <a-button type="primary" @click="searchRecords" size="large">
                      查询记录
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>

      <!-- 打印字段配置 -->
      <div class="fields-section" style=" border-radius: 6px; margin: 16px 0;">
        <a-collapse v-model:activeKey="fieldsCollapseKey" size="small">
          <a-collapse-panel key="1" header="打印字段配置">
            <template #extra>
              <a-space>
                <a-button @click.stop="selectAllFields" size="small">全选</a-button>
                <a-button @click.stop="resetFields" size="small">重置</a-button>
                <a-button @click.stop="selectRequiredFields" size="small">仅必选</a-button>
              </a-space>
            </template>
            <a-checkbox-group v-model:value="selectedFields" @change="onFieldsChange">
              <!-- 使用flex横向布局 -->
              <div class="field-categories-container">
                <div v-for="category in fieldCategories" :key="category.key" class="print-field-category">
                  <div class="print-category-header">
                    <h5 class="print-category-title">{{ category.title }}</h5>
                  </div>
                  <div class="print-category-fields">
                    <div v-for="field in category.fields" :key="field.key" class="print-field-option">
                      <a-checkbox :value="field.key" :disabled="field.required">
                        <span class="field-title">{{ field.title }}</span>
                        <a-tag v-if="field.required" size="small" color="blue">必选</a-tag>
                      </a-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </a-checkbox-group>
          </a-collapse-panel>
        </a-collapse>
      </div>

      <!-- 记录选择 -->
      <div class="records-section">
        <h4>选择打印记录 ({{ selectedRecords.length }}/{{ records.length }})
          <a-space style="margin-left: 16px;">
            <span style="font-size: 12px; font-weight: normal;">每页显示：</span>
            <a-select v-model:value="recordPageSize" size="small" style="width: 80px;" @change="onRecordPageSizeChange">
              <a-select-option :value="10">10条</a-select-option>
              <a-select-option :value="20">20条</a-select-option>
              <a-select-option :value="50">50条</a-select-option>
              <a-select-option :value="100">100条</a-select-option>
            </a-select>
          </a-space>
        </h4>
        <a-table :columns="recordColumns" :data-source="records" :row-selection="recordRowSelection"
          :pagination="{ pageSize: recordPageSize, showSizeChanger: false, showQuickJumper: true }" size="small"
          :scroll="{ y: 400 }" row-key="id">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'acceptance_photos'">
              <div class="photo-preview" v-if="record.acceptance_photos && record.acceptance_photos.length > 0">
                <a-image v-for="(photo, index) in getLatestPhotos(record.acceptance_photos)" :key="index"
                  :src="getPhotoUrl(photo.url)" :width="30" :height="30" :preview="false"
                  style="margin-right: 4px; border-radius: 4px; object-fit: cover;"
                  :title="photo.description || getPhotoTypeLabel(photo.type)" />
              </div>
              <span v-else class="no-photos">无照片</span>
            </template>
          </template>
        </a-table>
      </div>




      <!-- 打印预览和操作 -->
      <div class="actions-section">
        <a-space>
          <a-button @click="previewPrint" :disabled="selectedRecords.length === 0">
            <EyeOutlined />
            预览
          </a-button>
          <a-button type="primary" @click="executePrint" :disabled="selectedRecords.length === 0" :loading="printing">
            <PrinterOutlined />
            打印 ({{ selectedRecords.length }}条)
          </a-button>
          <a-button @click="$emit('cancel')">取消</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>

  <!-- 打印预览模态框 -->
  <a-modal :open="previewVisible" title="物资验收单打印预览" :width="1200"
    class="print-preview-modal" @cancel="previewVisible = false">
    <AcceptancePrintPreview :records="selectedRecordData" ref="printContent" />

    <template #footer>
      <a-space>
        <a-button @click="previewVisible = false">关闭预览</a-button>
        <a-button type="primary" @click="printDocument" :loading="printing">
          <PrinterOutlined />
          确认打印
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PrinterOutlined, EyeOutlined } from '@ant-design/icons-vue'
import api from '@/api'
import { getPhotoUrl } from '@/utils/photo'
import { getPageFieldCategories, getPageColumnOptions } from '@/utils/validation'
import { useDictMixin } from '@/mixins/dictMixin'
import AcceptancePrintPreview from './AcceptancePrintPreview.vue'

export default {
  name: 'AcceptancePrintConfig',
  components: {
    PrinterOutlined,
    EyeOutlined,
    AcceptancePrintPreview
  },
  props: {
    open: {
      type: Boolean,
      default: false
    },
    userDepartment: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['cancel', 'print-complete'],
  setup(props, { emit }) {
    const records = ref([])
    const selectedRecords = ref([])
    const selectedRecordData = ref([])
    const departmentOptions = ref([])
    const acceptorOptions = ref([])
    const purchaseTypeOptions = ref([])
    const printing = ref(false)
    const previewVisible = ref(false)
    const printContent = ref(null)

    // 使用字典混入
    const {
      formatDateToYMD: dictFormatDateToYMD
    } = useDictMixin()

    // 字段配置折叠状态
    const fieldsCollapseKey = ref([])

    // 记录分页大小
    const recordPageSize = ref(20)

    const filters = ref({
      department: undefined,
      acceptor: undefined,
      purchase_type: undefined,
      dateRange: undefined
    })

    const printConfig = ref({
      mode: 'card', // 保留卡片模式用于照片打印
      recordsPerPage: 4,
      orientation: 'portrait', // 保留A4纵向布局
      cardsPerRow: 1,
      showPhotos: true,
      maxPhotos: 3
    })



    // 打印字段配置
    const selectedFields = ref([
      'item_name', 'specification', 'hierarchy_path', 'purchase_quantity',
      'supplier_name', 'acceptor_name', 'acceptance_quantity', 'acceptance_date'
    ])

    // 分页记录计算属性
    const paginatedRecords = computed(() => {
      const pages = []
      const pageSize = printConfig.value.recordsPerPage
      for (let i = 0; i < selectedRecordData.value.length; i += pageSize) {
        pages.push(selectedRecordData.value.slice(i, i + pageSize))
      }
      return pages
    })

    // 基于业务流程的字段配置（验收阶段）
    const columnOptions = getPageColumnOptions('acceptance')
    const rawFieldCategories = getPageFieldCategories('acceptance')

    // 构建字段分类数据结构
    const fieldCategories = computed(() => {
      return rawFieldCategories.map(category => ({
        key: category.key,
        title: category.title,
        fields: columnOptions
          .filter(opt => opt.category === category.key && opt.key !== 'action')
          .map(opt => ({
            key: opt.key,
            title: opt.title,
            required: opt.required || false
          }))
      })).filter(category => category.fields.length > 0)
    })

    // 记录表格列配置
    const recordColumns = [
      { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
      { title: '物品名称', dataIndex: 'item_name', key: 'item_name', width: 120 },
      { title: '规格型号', dataIndex: 'specification', key: 'specification', width: 120 },
      { title: '需求单位', dataIndex: 'hierarchy_path', key: 'hierarchy_path', width: 120 },
      { title: '验收数量', dataIndex: 'acceptance_quantity', key: 'acceptance_quantity', width: 80 },
      { title: '验收人', dataIndex: 'acceptor_name', key: 'acceptor_name', width: 80 },
      {
        title: '验收时间',
        dataIndex: 'acceptance_date',
        key: 'acceptance_date',
        width: 100,
        customRender: ({ text }) => {
          return text ? dictFormatDateToYMD(text) : '-'
        }
      },
      { title: '验收照片', key: 'acceptance_photos', width: 120 }
    ]

    // 记录选择配置
    const recordRowSelection = computed(() => ({
      selectedRowKeys: selectedRecords.value,
      onChange: (selectedRowKeys, selectedRows) => {
        console.log('Record selection changed:', {
          selectedRowKeys,
          selectedRowsCount: selectedRows.length,
          selectedRows: selectedRows.map(r => ({ id: r.id, item_name: r.item_name }))
        })
        selectedRecords.value = selectedRowKeys
        selectedRecordData.value = selectedRows
      },
      getCheckboxProps: () => ({
        disabled: false
      })
    }))

    // 初始化部门选项
    const initDepartmentOptions = async () => {
      try {
        const response = await api.departments.getList()
        const allDepartments = response.data.results || response.data

        if (!props.userDepartment || !props.userDepartment.id) {
          // 用户部门为null，可以选择所有单位
          departmentOptions.value = allDepartments.map(dept => ({
            label: dept.hierarchy_path,
            value: dept.id
          }))
        } else {
          // 根据用户部门级别筛选可选部门
          const userLevel = props.userDepartment.level
          const userPath = props.userDepartment.hierarchy_path

          if (userLevel === 1) {
            // 一级单位：可选择本单位及所有下属二级单位
            departmentOptions.value = allDepartments
              .filter(dept => dept.hierarchy_path.startsWith(userPath))
              .map(dept => ({
                label: dept.hierarchy_path,
                value: dept.id
              }))
          } else {
            // 二级单位：只能选择本单位
            departmentOptions.value = [{
              label: userPath,
              value: props.userDepartment.id
            }]
          }
        }

        // 同时初始化验收人选项
        const usersResponse = await api.users.getList()
        const allUsers = usersResponse.data.results || usersResponse.data
        acceptorOptions.value = allUsers.map(user => ({
          value: user.id,
          label: user.username
        }))

        // 获取采购类型选项
        const purchaseTypeResponse = await api.dicts.getDict('purchase_type')
        if (purchaseTypeResponse.code === 200) {
          purchaseTypeOptions.value = (purchaseTypeResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        }

        // 初始化完成后查询记录
        await searchRecords()
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取部门列表失败:', error)
        }
        message.error('获取部门列表失败')
      }
    }

    // 查询记录
    const searchRecords = async () => {
      try {
        const params = {
          page_size: 10000, // 获取所有记录
          status_in: 'accepted' // 只获取已验收的记录
        }

        if (filters.value.department) {
          params.dept_id = filters.value.department
        }

        if (filters.value.acceptor) {
          params.acceptor_id = filters.value.acceptor
        }

        if (filters.value.purchase_type) {
          params.purchase_type = filters.value.purchase_type
        }

        if (filters.value.dateRange && filters.value.dateRange.length === 2) {
          params.acceptance_date_start = filters.value.dateRange[0].format('YYYY-MM-DD')
          params.acceptance_date_end = filters.value.dateRange[1].format('YYYY-MM-DD')
        }

        const response = await api.acceptances.getList(params)
        records.value = response.data.results || response.data || []

        // 清空之前的选择
        selectedRecords.value = []
        selectedRecordData.value = []

        message.success(`查询到 ${records.value.length} 条已验收记录`)
      } catch (error) {
        message.error('查询记录失败')
      }
    }

    // 重置筛选条件
    const resetFilters = () => {
      filters.value.department = undefined
      filters.value.acceptor = undefined
      filters.value.purchase_type = undefined
      filters.value.dateRange = undefined
      records.value = []
      selectedRecords.value = []
      selectedRecordData.value = []
    }

    // 筛选选项函数
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }



    // 记录分页大小变化处理
    const onRecordPageSizeChange = (value) => {
      recordPageSize.value = value
    }





    // 获取列样式
    const getColumnStyle = (fieldKey) => {
      const widthMap = {
        'item_name': '120px',
        'specification': '100px',
        'hierarchy_path': '100px',
        'purchase_quantity': '80px',
        'acceptance_quantity': '80px',
        'supplier_name': '100px',
        'acceptor_name': '80px',
        'acceptance_date': '100px',
        'purchase_unit_price': '90px',
        'settlement_amount': '90px'
      }
      return { width: widthMap[fieldKey] || '80px' }
    }

    // 生成打印样式
    const getPrintStyles = () => {
      return `
        @page {
          size: A4;
          margin: 15mm;
        }

        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        body {
          font-family: Arial, sans-serif;
          font-size: 12px;
          line-height: 1.4;
          margin: 0;
          padding: 0;
        }

        .print-page {
          min-height: 100vh;
          position: relative;
          page-break-after: always;
          display: block;
        }
        .print-page:last-child { page-break-after: avoid; }

        /* 页面头部 */
        .page-header {
          text-align: center;
          margin-bottom: 15px;
          background: white !important;
        }
        .page-header h2 {
          margin: 0;
          font-size: 18px;
          font-weight: bold;
          color: #000 !important;
        }
        .page-info {
          margin-top: 5px;
          font-size: 11px;
          color: #666 !important;
        }

        /* 表格样式 */
        table {
          width: 100%;
          border-collapse: collapse;
          font-size: 11px;
        }
        th, td {
          border: 1px solid #000;
          padding: 4px;
          text-align: center;
          vertical-align: middle;
        }
        th {
          background-color: #f0f0f0 !important;
          font-weight: bold;
        }

        /* 卡片样式 - 关键布局 */
        .card-container {
          border: 1px solid #000 !important;
          margin-bottom: 15px;
          padding: 8px;
          display: flex !important;
          flex-direction: row !important;
          min-height: 140px;
          page-break-inside: avoid;
          background: white !important;
        }

        .card-main-content {
          flex: 1 !important;
          min-width: 0;
          padding-right: 10px;
        }

        .card-photos {
          width: 180px !important;
          min-width: 180px !important;
          max-width: 180px !important;
          border-left: 1px solid #000;
          padding-left: 8px;
          display: flex !important;
          flex-direction: column !important;
          gap: 4px;
        }

        .card-photos > div:first-child {
          font-weight: bold;
          font-size: 10px;
          margin-bottom: 4px;
          color: #000 !important;
        }

        .photo-item {
          width: 100% !important;
          height: 50px !important;
          object-fit: cover !important;
          border: 1px solid #ccc;
          display: block !important;
          margin-bottom: 2px;
        }

        .field-grid {
          display: grid !important;
          grid-template-columns: 1fr 1fr !important;
          gap: 6px;
          font-size: 10px;
        }

        .field-item {
          display: flex !important;
          margin-bottom: 2px;
        }

        .field-label {
          font-weight: bold;
          min-width: 70px;
          color: #000 !important;
        }

        .field-value {
          flex: 1;
          color: #000 !important;
        }

        /* 页脚样式 - 只在最后一页显示 */
        .page-footer {
          position: absolute;
          bottom: 10mm;
          left: 0;
          right: 0;
          border-top: none;
          padding-top: 10px;
        }

        .signature-area {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
        }

        .signature-item {
          text-align: center;
          color: #000 !important;
        }
      `
    }

    // 字段选择相关方法
    const selectAllFields = () => {
      const allFields = fieldCategories.value.flatMap(category =>
        category.fields.map(field => field.key)
      )
      selectedFields.value = allFields
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    }

    const selectRequiredFields = () => {
      const requiredFields = fieldCategories.value.flatMap(category =>
        category.fields.filter(field => field.required).map(field => field.key)
      )
      selectedFields.value = requiredFields
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    }

    const resetFields = () => {
      selectedFields.value = [
        'item_name', 'specification', 'hierarchy_path', 'purchase_quantity',
        'supplier_name', 'acceptor_name', 'acceptance_quantity', 'acceptance_date'
      ]
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    }

    const onFieldsChange = (checkedValues) => {
      // 确保必选字段始终被选中
      const requiredFields = fieldCategories.value.flatMap(category =>
        category.fields.filter(field => field.required).map(field => field.key)
      )
      selectedFields.value = [...new Set([...checkedValues, ...requiredFields])]

      // 字段变化处理 - 重新搜索记录以更新表格显示
      searchRecords()
    }

    // 获取显示字段配置
    const getDisplayFields = () => {
      return fieldCategories.value.flatMap(category =>
        category.fields.filter(field => selectedFields.value.includes(field.key))
      )
    }

    // 获取字段值
    const getFieldValue = (record, fieldKey) => {
      const value = record[fieldKey]
      const displayValue = record[`${fieldKey}_display`]

      switch (fieldKey) {
        case 'acceptance_photos':
          if (!value || !Array.isArray(value) || value.length === 0) {
            return '无照片'
          }
          return `${value.length}张照片`
        case 'purchase_type':
          return displayValue || value || '-'
        case 'item_category':
        case 'unit':
        case 'procurement_method':
        case 'requirement_source':
        case 'fund_project':
          return displayValue || value || '-'
        case 'status':
          return displayValue || getStatusText(value) || value || '-'
        case 'acceptance_date':
        case 'purchase_date':
        case 'created_at':
        case 'submission_date':
        case 'approved_at':
        case 'reimbursement_date':
          return value ? dictFormatDateToYMD(value) : '-'
        case 'budget_unit_price':
        case 'actual_unit_price':
        case 'purchase_unit_price':
        case 'settlement_amount':
        case 'actual_total_price':
        case 'budget_total_amount':
          return value ? `¥${parseFloat(value).toFixed(2)}` : '-'
        default:
          return displayValue || value || '-'
      }
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        'draft': '草稿',
        'pending_approval': '待审批',
        'approved': '已审批',
        'rejected': '已驳回',
        'pending_purchase': '待采购',
        'purchased': '已采购',
        'returned': '已退回',
        'pending_acceptance': '待验收',
        'accepted': '已验收',
        'pending_reimbursement': '待结算',
        'settled': '已结算'
      }
      return statusMap[status] || status
    }



    // 打印预览
    const previewPrint = () => {
      if (selectedRecords.value.length === 0) {
        message.warning('请先选择要打印的记录')
        return
      }

      // 确保selectedRecordData与selectedRecords同步
      if (selectedRecordData.value.length !== selectedRecords.value.length) {
        // 从records中重新获取选中的数据
        const selectedData = records.value.filter(record =>
          selectedRecords.value.includes(record.id)
        )
        selectedRecordData.value = selectedData
      }

      // 验证数据完整性
      if (selectedRecordData.value.length === 0) {
        message.error('无法获取选中记录的详细数据，请重新选择')
        return
      }
      previewVisible.value = true
    }

    // 执行打印
    const executePrint = async () => {
      if (selectedRecords.value.length === 0) {
        message.warning('请先选择要打印的记录')
        return
      }

      printing.value = true
      try {
        await printDocument()
        message.success('打印任务已发送到系统打印队列')
        emit('print-complete', selectedRecordData.value)
        emit('cancel')
      } catch (error) {
        message.error(`打印失败: ${error.message}`)
      } finally {
        printing.value = false
      }
    }

    // 打印文档
    const printDocument = async () => {
      // 获取打印内容
      let printHTML = ''

      if (printContent.value && printContent.value.$el) {
        // 使用新的打印预览组件
        const previewElement = printContent.value.$el
        printHTML = `<!DOCTYPE html>
<html>
<head>
  <title>物资验收单打印</title>
  <style>
    @page {
      size: A4;
      margin: 0;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      background: white;
    }

    .print-page {
      width: 100%;
      min-height: 100vh;
      margin: 0;
      padding: 15mm;
      box-shadow: none;
      page-break-after: always;
      box-sizing: border-box;
    }

    .print-page:last-child {
      page-break-after: avoid;
    }

    .page-header h2 {
      color: #000 !important;
    }

    .section-title,
    .info-item .label,
    .footer-item .label {
      color: #000 !important;
    }

    .photo-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  </style>
</head>
<body>
  ${previewElement.innerHTML}
</body>
</html>`
      } else {
        message.error('打印内容未准备就绪，请先预览')
        return
      }

      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        message.error('无法打开打印窗口，请检查浏览器弹窗设置')
        return
      }

      printWindow.document.write(printHTML)
      printWindow.document.close()

      // 等待内容加载完成后打印
      printWindow.onload = () => {
        printWindow.print()
        printWindow.close()
      }
    }
    // 动态生成打印内容
    const generatePrintContent = () => {
      const displayFields = getDisplayFields()
      let html = ''

      if (printConfig.value.mode === 'table') {
        // 表格模式
        paginatedRecords.value.forEach((pageRecords, pageIndex) => {
          const isLastPage = pageIndex === paginatedRecords.value.length - 1
          html += `
            <div class="print-page">
              <div class="page-header">
                <h2>物资验收单汇总表</h2>
                <div class="page-info">
                  第 ${pageIndex + 1} 页，共 ${paginatedRecords.value.length} 页 |
                  本页 ${pageRecords.length} 条，总计 ${selectedRecordData.value.length} 条记录
                </div>
              </div>
              <div class="table-container">
                <table class="print-table">
                  <thead>
                    <tr>
                      <th style="width: 40px;">序号</th>
                      ${displayFields.map(field => `<th style="${getColumnStyle(field.key)}">${field.title}</th>`).join('')}
                    </tr>
                  </thead>
                  <tbody>
                    ${pageRecords.map((record, index) => `
                      <tr>
                        <td>${pageIndex * printConfig.value.recordsPerPage + index + 1}</td>
                        ${displayFields.map(field => `<td>${getFieldValue(record, field.key)}</td>`).join('')}
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
              ${isLastPage ? `
                <div class="page-footer">
                  <div class="signature-area">
                    <div class="signature-item">
                      <span>制表人：系统管理员</span>
                      <span>制表时间：${dictFormatDateToYMD(new Date())}</span>
                    </div>
                  </div>
                </div>
              ` : ''}
            </div>
          `
        })
      } else {
        // 卡片模式
        paginatedRecords.value.forEach((pageRecords, pageIndex) => {
          const isLastPage = pageIndex === paginatedRecords.value.length - 1
          html += `
            <div class="print-page">
              <div class="page-header">
                <h2>物资验收单</h2>
                <div class="page-info">
                  第 ${pageIndex + 1} 页，共 ${paginatedRecords.value.length} 页 |
                  本页 ${pageRecords.length} 条，总计 ${selectedRecordData.value.length} 条记录
                </div>
              </div>
              ${pageRecords.map(record => {
            // 获取验收照片
            const photos = record.acceptance_photos || []
            const photoHtml = photos.length > 0 ?
              photos.slice(0, printConfig.value.maxPhotos).map(photo =>
                `<img src="${getPhotoUrl(photo.url || photo)}" alt="验收照片" class="photo-item" />`
              ).join('') :
              '<div style="color: #999; font-size: 10px;">无验收照片</div>'

            return `
                  <div class="card-container">
                    <div class="card-main-content">
                      <div class="field-grid">
                        ${displayFields.filter(field => field.key !== 'acceptance_photos').map(field => `
                          <div class="field-item">
                            <span class="field-label">${field.title}：</span>
                            <span class="field-value">${getFieldValue(record, field.key)}</span>
                          </div>
                        `).join('')}
                      </div>
                    </div>
                    <div class="card-photos">
                      <div style="font-weight: bold; font-size: 10px; margin-bottom: 5px;">验收照片</div>
                      ${photoHtml}
                    </div>
                  </div>
                `
          }).join('')}
              ${isLastPage ? `
                <div class="page-footer">
                  <div class="signature-area">
                    <div class="signature-item">
                      <span>制表人：系统管理员</span>
                      <span>制表时间：${dictFormatDateToYMD(new Date())}</span>
                    </div>
                  </div>
                </div>
              ` : ''}
            </div>
          `
        })
      }

      return html
    }





    // 监听open变化，初始化数据
    watch(() => props.open, (newVal) => {
      if (newVal) {
        initDepartmentOptions()
      }
    })

    // 组件挂载时初始化
    // 获取最新的3张照片（正面、侧面、整体）
    const getLatestPhotos = (photos) => {
      if (!photos || !Array.isArray(photos)) return []

      // 按类型分组，每种类型只保留最新的一张
      const photosByType = {
        front: null,
        side: null,
        overall: null
      }

      // 按上传时间排序（最新的在前）
      const sortedPhotos = [...photos].sort((a, b) => {
        const timeA = new Date(a.upload_time || 0).getTime()
        const timeB = new Date(b.upload_time || 0).getTime()
        return timeB - timeA
      })

      // 为每种类型分配最新的照片
      sortedPhotos.forEach(photo => {
        const type = photo.type || 'front'
        if (photosByType[type] === null) {
          photosByType[type] = photo
        }
      })

      // 返回非空的照片，最多3张
      return Object.values(photosByType).filter(photo => photo !== null)
    }

    // 获取照片类型标签
    const getPhotoTypeLabel = (type) => {
      const typeLabels = {
        front: '正面照片',
        side: '侧面照片',
        overall: '整体照片'
      }
      return typeLabels[type] || '照片'
    }

    onMounted(() => {
      if (props.open) {
        initDepartmentOptions()
      }
    })

    return {
      records,
      selectedRecords,
      selectedRecordData,
      departmentOptions,
      acceptorOptions,
      purchaseTypeOptions,
      printing,
      previewVisible,
      printContent,
      filters,
      printConfig,
      fieldsCollapseKey,
      recordPageSize,

      paginatedRecords,
      selectedFields,
      fieldCategories,
      recordColumns,
      recordRowSelection,
      initDepartmentOptions,
      searchRecords,
      resetFilters,
      filterOption,
      onRecordPageSizeChange,

      selectAllFields,
      selectRequiredFields,
      resetFields,
      onFieldsChange,
      getDisplayFields,
      getFieldValue,
      getPhotoUrl,
      getLatestPhotos,
      getPhotoTypeLabel,
      getColumnStyle,
      getPrintStyles,
      generatePrintContent,
      previewPrint,
      executePrint,
      printDocument,
      dictFormatDateToYMD,
      getStatusText
    }
  }
}
</script>

<style scoped>
.print-config-container {
  max-height: 70vh;
  overflow-y: auto;
}

.filter-section,
.records-section,
.fields-section,
.actions-section {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.filter-section h4,
.records-section h4,
.fields-section h4 {
  margin: 0 0 12px 0;
  color: #1890ff;
  font-weight: 600;
  font-size: 14px;
}

.fields-section .ant-collapse {
  margin-bottom: 0;
  background: transparent;
  border: none;
}

.fields-section .ant-collapse-content-box {
  padding: 8px 0;
}

.fields-section .ant-collapse-header {
  padding: 8px 0 !important;
  background: transparent !important;
}


.photo-preview {
  display: flex;
  align-items: center;
}

.photo-count {
  font-size: 12px;
  color: #666;
}

.no-photos {
  color: #999;
  font-size: 12px;
}

.field-actions {
  margin-bottom: 16px;
  text-align: right;
}

.fields-section {
  margin-bottom: 16px;
}


/* 保留原有样式以兼容其他部分 */
.field-category {
  margin-bottom: 16px;
}

.category-title {
  margin: 0 0 8px 0;
  color: #666;
  font-weight: 600;
  font-size: 13px;
}

.category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
}

.field-option {
  display: flex;
  align-items: center;
}

.actions-section {
  text-align: center;
  border: none;
  background: #fafafa;
}

/* 打印预览样式 */
.print-preview-modal .ant-modal-body {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

/* 强制移除所有可能的蓝色背景 */
.print-preview-content .page-header,
.print-preview-content .page-header *,
.print-page .page-header,
.print-page .page-header *,
div.page-header,
div.page-header h2,
.ant-modal .page-header,
.ant-modal .page-header h2 {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  background-attachment: initial !important;
  background-blend-mode: initial !important;
  background-clip: initial !important;
  background-origin: initial !important;
  background-position: initial !important;
  background-repeat: initial !important;
  background-size: initial !important;
}

.print-preview-content {
  background: #f5f5f5;
  padding: 20px;
}

.print-page {
  background: white;
  margin-bottom: 20px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  min-height: 800px;
}

.print-page:last-child {
  margin-bottom: 0;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
}

.page-header h2 {
  font-size: 20px;
  margin-bottom: 6px;
  color: #333;
  font-weight: 600;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
}

.page-info {
  font-size: 12px;
  color: #666;
}

.record-info {
  margin-bottom: 30px;
}

.photos-section {
  margin: 30px 0;
}

.photos-section h4 {
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.photo-item {
  text-align: center;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
}

.photo-item img {
  width: 100%;
  max-width: 200px;
  height: 160px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  transition: transform 0.2s;
}

.photo-item img:hover {
  transform: scale(1.05);
}

.photo-caption {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.page-footer {
  margin-top: 40px;
  padding-top: 20px;
}

.signature-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.signature-item {
  display: flex;
  gap: 60px;
  font-size: 14px;
  font-weight: 500;
}

.signature-item span {
  padding: 8px 0;
  border-bottom: 1px solid #333;
  min-width: 120px;
  text-align: center;
}

/* 表格模式样式 */
.table-container {
  margin: 20px 0;
}

.print-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.print-table th,
.print-table td {
  border: 1px solid #d9d9d9;
  padding: 8px 4px;
  text-align: left;
  vertical-align: middle;
}

.print-table th {
  background-color: #fafafa;
  font-weight: 600;
  text-align: center;
}

.print-table td {
  word-break: break-all;
  max-width: 120px;
}

/* 卡片模式样式 */
.cards-container {
  display: grid;
  gap: 12px;
  margin: 15px 0;
}

.cards-per-row-1 {
  grid-template-columns: 1fr;
}

.cards-per-row-2 {
  grid-template-columns: 1fr 1fr;
}

.cards-per-row-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.record-card {
  border: 1px solid #333;
  border-radius: 4px;
  padding: 8px;
  background: #ffffff;
  min-height: 280px;
  page-break-inside: avoid;
}

.card-content {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  height: 100%;
}

.card-main-content {
  flex: 1;
  min-width: 25%;
}

.photos-section {
  flex: 3;
  display: flex;
  flex-direction: column;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  flex: 1;
}

.card-main-content {
  flex: 1;
  min-width: 0;
}

.card-header {
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 1px;
  margin-bottom: 1px;
}

.card-header h4 {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.field-grid {
  margin-bottom: 8px;
}

.field-item {
  display: flex;
  font-size: 11px;
  line-height: 1.3;
  padding: 2px 0;
  margin: 10px 0;
}

.field-label {
  font-weight: 600;
  color: #666;
  min-width: 50px;
  flex-shrink: 0;
}

.field-value {
  color: #333;
  word-break: break-all;
}

.record-card .photos-section {
  flex: 3;
  margin-top: 0;
  border-left: 1px solid #e0e0e0;
  padding: 2px 0 2px 4px;
  display: flex;
  flex-direction: column;
}

.record-card .photos-section h5 {
  margin-bottom: 6px;
  font-size: 10px;
  color: #333;
  font-weight: 600;
  text-align: center;
}

.record-card .photos-grid {
  display: flex;
  flex-direction: row;
  gap: 1px;
  flex: 1;
  margin: 0;
  padding: 0;
}

.record-card .photo-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
}

.record-card .photo-item img {
  width: 100%;
  height: 160px;
  object-fit: cover;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  margin: 0;
}

/* 打印专用样式 */
@media print {
  @page {
    size: A4;
    margin: 15mm;
  }

  body {
    margin: 0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    background: white !important;
  }

  .print-page {
    margin: 0;
    padding: 0;
    box-shadow: none;
    border-radius: 0;
    background: white !important;
    min-height: auto;
    page-break-inside: avoid;
  }

  .page-header {
    background: white !important;
    margin-bottom: 10mm;
    padding-bottom: 5mm;
  }

  .page-header h2 {
    background: white !important;
    color: #000 !important;
    font-size: 16pt;
    font-weight: bold;
  }

  .page-info {
    color: #000 !important;
    font-size: 11px;
  }

  .record-card {
    page-break-inside: avoid;
    border: 1px solid #000;
    background: white !important;
    min-height: 240px;
    padding: 6px;
  }

  .card-content {
    display: flex;
    gap: 6px;
    height: 100%;
  }

  .card-main-content {
    flex: 1;
    min-width: 25%;
  }

  .photos-section {
    flex: 3;
    display: flex;
    flex-direction: column;
  }

  .photos-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 4px;
    flex: 1;
  }

  .card-main-content {
    flex: 1;
    min-width: 0;
  }

  .card-header h4 {
    color: #000 !important;
    font-size: 11px;
  }

  .field-label {
    color: #000 !important;
    font-size: 10px;
  }

  .field-value {
    color: #000 !important;
    font-size: 10px;
  }

  .record-card .photos-section {
    flex: 3;
    border-left: 1px solid #000;
    padding: 1px 0 1px 3px;
    display: flex;
    flex-direction: column;
  }

  .record-card .photos-section h5 {
    color: #000 !important;
    font-size: 9px;
  }

  .record-card .photos-grid {
    display: flex;
    flex-direction: row;
    gap: 1px;
    flex: 1;
    margin: 0;
    padding: 0;
  }

  .record-card .photo-item {
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0;
  }

  .record-card .photo-item img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-radius: 1px;
    margin: 0;
  }

  .cards-container {
    gap: 8px;
    margin: 10px 0;
  }

  /* 新的卡片容器样式 - 与打印样式保持一致 */
  .card-container {
    border: 1px solid #333;
    margin-bottom: 15px;
    padding: 8px;
    background: white;
    page-break-inside: avoid;
    display: flex;
    flex-direction: row;
    min-height: 140px;
  }

  .card-container .card-main-content {
    flex: 1;
    min-width: 0;
    padding-right: 10px;
  }

  .card-container .card-header h4 {
    color: #333;
    font-size: 12px;
    margin: 0 0 8px 0;
    font-weight: bold;
  }

  .card-container .field-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
    font-size: 10px;
  }

  .card-container .field-item {
    display: flex;
    margin-bottom: 2px;
  }

  .card-container .field-label {
    color: #333;
    font-size: 10px;
    font-weight: bold;
    min-width: 70px;
  }

  .card-container .field-value {
    color: #333;
    font-size: 10px;
    flex: 1;
  }

  /* 照片区域样式 */
  .card-container .card-photos {
    width: 180px;
    min-width: 180px;
    max-width: 180px;
    border-left: 1px solid #333;
    padding-left: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .card-container .photo-item {
    width: 100%;
    height: 50px;
    object-fit: cover;
    border: 1px solid #ccc;
    display: block;
    margin-bottom: 2px;
  }

  .page-footer {
    margin-top: 20px;
    padding-top: 10px;
  }

  /* 打印字段配置样式 */
  .field-categories-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 0;
  }

  .print-field-category {
    flex: 1;
    min-width: 200px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
    overflow: hidden;
  }

  .print-category-header {
    background-color: #e6f7ff;
    border-bottom: 1px solid #d9d9d9;
    padding: 8px 12px;
  }

  .print-category-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1890ff;
  }

  .print-category-fields {
    padding: 12px;
  }

  .print-field-option {
    margin-bottom: 8px;
  }

  .print-field-option:last-child {
    margin-bottom: 0;
  }

  .field-title {
    margin-right: 8px;
  }

  .filter-form-container {
    padding: 0;
  }

  /* 筛选区域样式优化 */
  .filter-section {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
  }

  .filter-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  /* 记录选择区域样式 */
  .records-section {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
  }

  .records-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  /* 操作区域样式 */
  .actions-section {
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    text-align: center;
  }

  .signature-item {
    color: #000 !important;
    font-size: 12px;
  }

  .signature-item span {
    border-bottom: 1px solid #000;
  }
}
</style>
