"""
统一权限验证系统
"""
from functools import wraps
from django.http import JsonResponse
from django.db import connection
from rest_framework.permissions import BasePermission


def get_user_permissions(user):
    """获取用户的所有权限 - 简化版，直接使用user.role字段"""
    if not user or not user.is_authenticated:
        return []

    cursor = connection.cursor()

    # 超级用户拥有所有权限
    if user.is_superuser:
        cursor.execute("SELECT code FROM sys_permission WHERE is_active = 1")
        all_permissions = [row[0] for row in cursor.fetchall()]
        return all_permissions

    # 通过用户的role字段获取权限
    if not user.role:
        return []

    # 修复：通过角色代码获取角色ID，然后获取权限
    cursor.execute("""
        SELECT DISTINCT p.code
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_role r ON rp.role_id = r.id
        WHERE r.code = %s AND r.is_active = 1 AND p.is_active = 1
    """, [user.role])

    permissions = [row[0] for row in cursor.fetchall()]

    # 获取用户直接分配的权限
    cursor.execute("""
        SELECT DISTINCT p.code
        FROM sys_permission p
        INNER JOIN sys_user_permission up ON p.id = up.permission_id
        WHERE up.user_id = %s AND up.is_granted = 1 AND p.is_active = 1
    """, [user.id])

    user_permissions = [row[0] for row in cursor.fetchall()]
    permissions.extend(user_permissions)

    return list(set(permissions))


def has_permission(user, permission_code):
    """检查用户是否有指定权限"""
    if not user or not user.is_authenticated:
        return False
    
    # 超级用户拥有所有权限
    if user.is_superuser:
        return True
    
    # 获取用户权限
    user_permissions = get_user_permissions(user)
    
    # 检查是否有通配符权限
    if '*' in user_permissions:
        return True
    
    # 检查具体权限
    return permission_code in user_permissions


def require_permission(permission_code):
    """权限装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not has_permission(request.user, permission_code):
                return JsonResponse({
                    'code': 403,
                    'message': '权限不足',
                    'data': {
                        'required_permission': permission_code,
                        'user_permissions': get_user_permissions(request.user)
                    }
                }, status=403)
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


class PermissionRequired(BasePermission):
    """DRF权限类"""
    permission_required = None
    
    def has_permission(self, request, view):
        # 从视图获取所需权限
        permission = getattr(view, 'permission_required', self.permission_required)
        
        if not permission:
            return True
        
        return has_permission(request.user, permission)


def check_button_permission(user, button_code):
    """检查按钮权限"""
    return has_permission(user, button_code)


def get_user_button_permissions(user, module=None):
    """获取用户的按钮权限"""
    if not user or not user.is_authenticated:
        return []
    
    # 超级用户拥有所有权限
    if user.is_superuser:
        cursor = connection.cursor()
        if module:
            cursor.execute("""
                SELECT code FROM sys_permission 
                WHERE category = 'button' AND module = %s AND is_active = 1
            """, [module])
        else:
            cursor.execute("""
                SELECT code FROM sys_permission 
                WHERE category = 'button' AND is_active = 1
            """)
        return [row[0] for row in cursor.fetchall()]
    
    # 通过角色获取按钮权限
    cursor = connection.cursor()
    if module:
        cursor.execute("""
            SELECT DISTINCT p.code
            FROM sys_permission p
            INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
            INNER JOIN sys_role r ON rp.role_id = r.id
            WHERE r.code = %s AND rp.granted = 1 AND p.is_active = 1
            AND p.category = 'button' AND p.module = %s AND r.is_active = 1
        """, [user.role, module])
    else:
        cursor.execute("""
            SELECT DISTINCT p.code
            FROM sys_permission p
            INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
            INNER JOIN sys_role r ON rp.role_id = r.id
            WHERE r.code = %s AND rp.granted = 1 AND p.is_active = 1
            AND p.category = 'button' AND r.is_active = 1
        """, [user.role])
    
    return [row[0] for row in cursor.fetchall()]


def get_user_page_permissions(user):
    """获取用户的页面权限"""
    if not user or not user.is_authenticated:
        return []
    
    # 超级用户拥有所有权限
    if user.is_superuser:
        cursor = connection.cursor()
        cursor.execute("""
            SELECT code FROM sys_permission 
            WHERE category = 'page' AND is_active = 1
        """)
        return [row[0] for row in cursor.fetchall()]
    
    # 通过角色获取页面权限
    cursor = connection.cursor()
    cursor.execute("""
        SELECT DISTINCT p.code
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_role r ON rp.role_id = r.id
        WHERE r.code = %s AND rp.granted = 1 AND p.is_active = 1
        AND p.category = 'page' AND r.is_active = 1
    """, [user.role])
    
    return [row[0] for row in cursor.fetchall()]


# 页面权限映射 - 基于自定义权限系统
PAGE_PERMISSION_MAP = {
    '/dashboard': 'dashboard:view',
    '/purchase/overview': 'purchase:overview:view',
    '/purchase/requests': 'purchase:request:view',
    '/purchase/approval': 'purchase:approval:view',
    '/purchase/procurement': 'purchase:procurement:view',
    '/purchase/acceptance': 'purchase:acceptance:view',
    '/purchase/reimbursement': 'purchase:reimbursement:view',
    '/settings/users': 'system:user:view',
    '/settings/roles': 'system:role:view',
    '/settings/permissions': 'system:permission:view',
    '/settings/menus': 'system:menu:view',
    '/settings/departments': 'system:dept:view',
    '/settings/dicts': 'system:dict:view',
    '/settings/logs': 'system:log:view',
    '/userInfo/profile': 'user:profile:view',
    '/userInfo/notifications': 'user:notification:view',
}


def check_page_permission(user, page_path):
    """检查页面访问权限"""
    required_permission = PAGE_PERMISSION_MAP.get(page_path)
    if not required_permission:
        return True  # 没有配置权限要求的页面默认允许访问
    
    return has_permission(user, required_permission)


def get_permission_info(user):
    """获取用户权限信息"""
    return {
        'all_permissions': get_user_permissions(user),
        'page_permissions': get_user_page_permissions(user),
        'button_permissions': get_user_button_permissions(user),
        'is_superuser': user.is_superuser if user else False
    }
