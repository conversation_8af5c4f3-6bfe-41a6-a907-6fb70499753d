<template>
  <div class="acceptance-detail">
    <a-card title="物品验收详情" :bordered="false">
      <!-- 采购需求卡片 -->
      <a-card title="采购需求信息" style="margin-bottom: 24px;">
        <a-descriptions bordered size="middle">
          <a-descriptions-item label="物品种类">{{ getDictText('物品种类', acceptanceData.purchase_request.item_category)
          }}</a-descriptions-item>
          <a-descriptions-item label="物品名称">{{ acceptanceData.purchase_request.item_name }}</a-descriptions-item>
          <a-descriptions-item label="规格型号">{{ acceptanceData.purchase_request.spec }}</a-descriptions-item>
          <a-descriptions-item label="数量">{{ acceptanceData.purchase_request.quantity }}</a-descriptions-item>
          <a-descriptions-item label="需求单位">{{ acceptanceData.purchase_request.hierarchy_path }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 验收信息表单 -->
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="快递公司" name="courier_company">
              <a-input v-model:value="formData.courier_company" placeholder="请输入快递公司" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="快递单号" name="tracking_number">
              <a-input v-model:value="formData.tracking_number" placeholder="请输入快递单号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="快递单照片" name="courier_receipt_photo">
              <a-upload v-model:file-list="formData.courier_receipt_photo" :customRequest="customUpload"
                list-type="picture-card" :show-upload-list="true">
                <div>上传照片</div>
              </a-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="物品照片" name="item_photo">
              <a-upload v-model:file-list="formData.item_photo" :customRequest="customUpload" list-type="picture-card"
                :show-upload-list="true">
                <div>上传照片</div>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 数量验收 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="预期数量" name="expected_quantity">
              <a-input-number v-model:value="expectedQuantity" :disabled="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="实际验收数量" name="actual_quantity">
              <a-input-number v-model:value="formData.actual_quantity" :min="0" style="width: 100%"
                @change="calculateQuantityDifference" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="数量差异">
              <a-input-group compact>
                <a-input :value="quantityDifference" :disabled="true" style="width: 60%" />
                <a-input :value="`${differenceRate.toFixed(2)}%`" :disabled="true" style="width: 40%" />
              </a-input-group>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 差异预警 -->
        <a-row :gutter="16" v-if="hasException">
          <a-col :span="24">
            <a-alert type="warning" show-icon :message="`数量差异过大：差异率 ${differenceRate.toFixed(2)}%`"
              :description="exceptionReason" style="margin-bottom: 16px" />
            <a-form-item label="异常说明" name="exception_reason">
              <a-textarea v-model:value="formData.exception_reason" placeholder="请说明数量差异的原因" :rows="3" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="验收人" name="acceptor">
              <dict-selector dict-type="用户-验收人" v-model:model-value="formData.acceptor" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="验收日期" name="acceptance_date">
              <a-date-picker v-model:value="formData.acceptance_date" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 照片预览区 -->
        <a-card title="照片预览" style="margin-top: 24px;">
          <a-row :gutter="16">
            <a-col :span="12">
              <div class="photo-preview">
                <h4>快递单照片</h4>
                <div v-if="formData.courier_receipt_photo.length > 0" class="photo-container">
                  <img v-for="(file, index) in formData.courier_receipt_photo" :key="index" :src="getFileUrl(file)"
                    @click="previewImage(file)" />
                </div>
                <div v-else class="empty-photo">暂无照片</div>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="photo-preview">
                <h4>物品照片</h4>
                <div v-if="formData.item_photo.length > 0" class="photo-container">
                  <img v-for="(file, index) in formData.item_photo" :key="index" :src="getFileUrl(file)"
                    @click="previewImage(file)" />
                </div>
                <div v-else class="empty-photo">暂无照片</div>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 操作栏 -->
        <a-row style="margin-top: 24px;">
          <a-col :span="24" style="text-align: right">
            <a-button :loading="loading" type="primary" @click="submitForm">
              {{ isEditMode ? '更新验收' : '提交验收' }}
            </a-button>
            <a-button @click="cancel" style="margin-left: 8px;">取消</a-button>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 图片预览模态框 -->
    <a-modal v-model:open="previewVisible" :footer="null" :width="800">
      <img :src="previewImageSrc" style="width: 100%;" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import DictSelector from '@/components/Dict/Selector.vue';
import { getPhotoUrl } from '@/utils/photo';
import { useDictMixin } from '@/mixins/dictMixin';

const router = useRouter();
const route = useRoute();

// 使用字典混入
const dictTypes = ['物品种类']
const { getDictText, loadDicts } = useDictMixin(dictTypes)

// 是否为编辑模式
const isEditMode = ref(route.path.includes('/edit'));

// 表单引用
const formRef = ref(null);

// 表单数据
const formData = ref({
  courier_company: '',
  tracking_number: '',
  courier_receipt_photo: [],
  item_photo: [],
  actual_quantity: 0,
  exception_reason: '',
  acceptor: null,
  acceptance_date: null
});

// 数量相关状态
const expectedQuantity = ref(0);
const quantityDifference = ref(0);
const differenceRate = ref(0);
const hasException = ref(false);
const exceptionReason = ref('');

// 采购需求数据 (removed unused variable)

// 验收数据
const acceptanceData = ref({
  purchase_request: {}
});

// 表单验证规则
const rules = {
  courier_company: [{ required: true, message: '请输入快递公司' }],
  tracking_number: [{ required: true, message: '请输入快递单号' }],
  actual_quantity: [{ required: true, message: '请输入实际验收数量' }],
  acceptor: [{ required: true, message: '请选择验收人' }],
  acceptance_date: [{ required: true, message: '请选择验收日期' }]
};

// 计算数量差异
const calculateQuantityDifference = () => {
  const expected = expectedQuantity.value;
  const actual = formData.value.actual_quantity || 0;

  quantityDifference.value = actual - expected;

  if (expected > 0) {
    differenceRate.value = Math.abs(quantityDifference.value) / expected * 100;
  } else {
    differenceRate.value = 0;
  }

  // 判断是否需要异常处理（差异率≥5%）
  if (differenceRate.value >= 5) {
    hasException.value = true;
    exceptionReason.value = `验收数量差异过大：预期${expected}，实际${actual}，差异率${differenceRate.value.toFixed(2)}%`;
    formData.value.exception_reason = exceptionReason.value;
  } else {
    hasException.value = false;
    exceptionReason.value = '';
    formData.value.exception_reason = '';
  }
};

// 图片预览相关
const previewVisible = ref(false);
const previewImageSrc = ref('');

// 获取文件URL
const getFileUrl = (file) => {
  if (file.url) {
    return file.url;
  }
  return URL.createObjectURL(file.originFileObj);
};



// 图片预览
const previewImage = (file) => {
  previewImageSrc.value = getFileUrl(file);
  previewVisible.value = true;
};

// 自定义上传方法
const customUpload = ({ file, onSuccess }) => {
  // 实现自定义上传逻辑
  setTimeout(() => {
    onSuccess("ok");
    // 添加水印
    addWatermark(file);
  }, 1000);
};

// 添加水印
const addWatermark = (file) => {
  console.log('添加水印:', file);
  // 这里应该实现真正的水印添加逻辑
  // 可能需要调用后端API处理图片并返回带水印的图片地址
};

// 加载状态
const loading = ref(false);

// 提交表单
const submitForm = async () => {
  try {
    const values = await formRef.value.validateFields();
    loading.value = true;

    if (isEditMode.value) {
      // 更新验收记录
      console.log('更新验收记录:', values);
      // 实际开发中替换为真实API调用
      // await api.updateAcceptance(route.params.id, values);
    } else {
      // 创建验收记录
      console.log('创建验收记录:', values);
      // 实际开发中替换为真实API调用
      // await api.createAcceptance({ ...values, purchase_request_id: route.params.requestId });
    }

    router.push('/purchase/acceptance');
  } catch (error) {
    console.error('表单提交失败：', error);
  } finally {
    loading.value = false;
  }
};

// 取消操作
const cancel = () => {
  router.back();
};

// 获取采购需求详情
const getPurchaseRequestDetail = async () => {
  try {
    // 这里应该调用API获取真实数据
    // 下面是模拟数据，实际开发中应替换为真实API调用
    const mockData = {
      id: 1,
      item_category: '办公用品',
      item_name: '打印机',
      spec: 'HP LaserJet Pro MFP M28w',
      quantity: 5,
      unit_price: 1200,
      amount: 6000,
      hierarchy_path: '总部-技术部'
    };

    acceptanceData.value.purchase_request = mockData;
    expectedQuantity.value = mockData.quantity;
    formData.value.actual_quantity = mockData.quantity; // 默认设置为预期数量

    // 实际开发中应使用如下方式获取数据：
    /*
    const response = await fetch(`/api/requests/${route.params.requestId}`);
    const data = await response.json();
    acceptanceData.value.purchase_request = data;
    expectedQuantity.value = data.quantity;
    formData.value.actual_quantity = data.quantity;
    */
  } catch (error) {
    console.error('获取采购需求详情失败：', error);
  }
};

// 获取验收详情（编辑模式）
const getAcceptanceDetail = async () => {
  if (!isEditMode.value) return;

  try {
    const response = await fetch(`/api/purchase/acceptance/${route.params.id}`);
    const data = await response.json();

    formData.value = {
      ...data,
      courier_receipt_photo: data.courier_receipt_photo ? [{ url: getPhotoUrl(data.courier_receipt_photo) }] : [],
      item_photo: data.item_photo ? [{ url: getPhotoUrl(data.item_photo) }] : []
    };

    acceptanceData.value = data;
  } catch (error) {
    console.error('获取验收详情失败：', error);
  }
};

// 页面加载时初始化
onMounted(async () => {
  // 加载字典数据
  await loadDicts();

  await getPurchaseRequestDetail();

  if (isEditMode.value) {
    await getAcceptanceDetail();
  }
});
</script>

<style scoped>
.acceptance-detail {
  background-color: #f5f5f5;
  padding: 24px;
}

.photo-preview img {
  width: 100%;
  height: auto;
  cursor: pointer;
  border-radius: 4px;
  transition: transform 0.3s;
}

.photo-preview img:hover {
  transform: scale(1.05);
}

.photo-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.empty-photo {
  text-align: center;
  color: #999;
  padding: 24px;
}
</style>