<template>
  <div v-if="hasPermission">
    <slot />
  </div>
  <div v-else-if="fallback">
    <slot name="fallback" />
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'PermissionWrapper',
  props: {
    permission: {
      type: String,
      required: true
    },
    role: {
      type: String,
      default: null
    },
    fallback: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const store = useStore()
    
    // 权限检查
    const hasPermission = computed(() => {
      // 如果指定了角色，检查用户角色
      if (props.role) {
        const userRole = store.getters.userRole?.code
        if (userRole !== props.role) {
          return false
        }
      }
      
      // 检查权限
      return store.getters.hasPermission(props.permission)
    })
    
    return {
      hasPermission
    }
  }
}
</script>
