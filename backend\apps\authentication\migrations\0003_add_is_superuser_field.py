# Generated by Django 5.2.4 on 2025-07-30 01:47

from django.db import migrations, models, connection
import apps.authentication.models


def safe_remove_fields(apps, schema_editor):
    """安全地移除字段，忽略不存在的表"""
    with connection.cursor() as cursor:
        try:
            # 检查字段是否存在，如果存在则删除
            cursor.execute("SHOW COLUMNS FROM sys_user LIKE 'groups'")
            if cursor.fetchone():
                print("⚠️ groups字段仍然存在，但已在前一个迁移中处理")

            cursor.execute("SHOW COLUMNS FROM sys_user LIKE 'user_permissions'")
            if cursor.fetchone():
                print("⚠️ user_permissions字段仍然存在，但已在前一个迁移中处理")

        except Exception as e:
            print(f"⚠️ 检查字段时出错: {e}")


def reverse_safe_remove_fields(apps, schema_editor):
    """回滚操作 - 无法恢复已删除的字段"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0002_remove_django_groups_permissions"),
    ]

    operations = [
        # 使用Python函数安全地处理字段移除
        migrations.RunPython(
            safe_remove_fields,
            reverse_safe_remove_fields
        ),
        migrations.AlterModelManagers(
            name="user",
            managers=[
                ("objects", apps.authentication.models.UserManager()),
            ],
        ),
        migrations.AlterField(
            model_name="user",
            name="is_superuser",
            field=models.BooleanField(
                default=False,
                help_text="指定该用户拥有所有权限而无需显式分配。",
                verbose_name="超级用户状态",
            ),
        ),
    ]
