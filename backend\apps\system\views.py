from rest_framework import generics, status, viewsets
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from django.http import HttpResponse
from django.utils import timezone
from django.db import models
from datetime import datetime, timedelta
import pandas as pd
import io
import logging

logger = logging.getLogger(__name__)
from .models import Department, Notification, SystemLog
from .serializers import (
    DepartmentSerializer, DepartmentTreeSerializer,
    NotificationSerializer
)
from apps.purchase.models import Dictionary
from apps.purchase.serializers import DictionarySerializer
from .logging_service import LoggingService
from ..common.base_views import BaseListCreateView, BaseDetailView
from ..common.pagination import CustomPageNumberPagination

# 便捷函数
log_user_action = LoggingService.log_user_action


def _reorder_dict_items():
    """
    重新整理所有字典类型的排序
    新增的项目（order=0）排在最前面，其他按原有order排序
    """
    try:
        dict_types = Dictionary.objects.values_list('type_code', flat=True).distinct()

        for type_code in dict_types:
            # 先获取新增的项目（order=0或最近创建的）
            new_items = Dictionary.objects.filter(type_code=type_code, order=0).order_by('-created_at')
            # 再获取其他项目，按order排序
            existing_items = Dictionary.objects.filter(type_code=type_code).exclude(order=0).order_by('order', 'created_at')

            # 重新分配order值
            order_counter = 1

            # 新增的项目排在前面
            for item in new_items:
                item.order = order_counter
                item.save()
                order_counter += 1

            # 现有项目排在后面
            for item in existing_items:
                item.order = order_counter
                item.save()
                order_counter += 1

        logger.info("字典排序重新整理完成")
    except Exception as e:
        logger.error(f"字典排序重新整理失败: {str(e)}")


class DepartmentListCreateView(BaseListCreateView):
    """
    部门列表和创建接口
    GET /api/system/departments - 获取部门列表
    POST /api/system/departments - 创建部门
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    filterset_fields = ['status', 'parent_id']
    search_fields = ['dept_name', 'dept_code']
    ordering_fields = ['created_at', 'dept_name']
    ordering = ['created_at']

    def get_queryset(self):
        """重写queryset以支持层级筛选"""
        queryset = super().get_queryset()

        # 层级筛选
        level = self.request.query_params.get('level')
        if level:
            if level == '1':
                # 一级部门（没有父部门）
                queryset = queryset.filter(parent_id__isnull=True)
            elif level == '2':
                # 二级部门（有父部门）
                queryset = queryset.filter(parent_id__isnull=False)

        return queryset

    def perform_create(self, serializer):
        """重写以添加日志记录"""
        department = serializer.save()

        # 记录操作日志
        log_user_action(
            user=self.request.user,
            log_type='create',
            action=f'创建部门: {department.dept_name}',
            target_model='Department',
            target_id=department.id,
            ip_address=self.get_client_ip(self.request),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            request_data=self.request.data
        )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class DepartmentDetailView(BaseDetailView):
    """
    部门详情、更新和删除接口
    GET /api/system/departments/{id} - 获取部门详情
    PUT /api/system/departments/{id} - 更新部门
    DELETE /api/system/departments/{id} - 删除部门
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer

    def perform_update(self, serializer):
        """重写以添加日志记录"""
        instance = serializer.instance
        old_name = instance.dept_name
        department = serializer.save()

        # 记录更新日志
        try:
            log_user_action(
                user=self.request.user,
                log_type='update',
                action=f'更新部门: {old_name} -> {department.dept_name}',
                target_model='Department',
                target_id=department.id,
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
                request_data=self.request.data
            )
        except Exception as e:
            # 日志记录失败不应该影响更新操作
            print(f"记录更新日志失败: {e}")

    def destroy(self, request, *args, **kwargs):
        """重写删除方法以添加业务验证和日志记录"""
        instance = self.get_object()
        dept_name = instance.dept_name
        dept_id = instance.id

        # 检查是否有子部门
        if Department.objects.filter(parent_id=instance.id).exists():
            return self.error_response('该部门下还有子部门，无法删除')

        # 检查是否有关联的用户
        try:
            from apps.authentication.models import User
            if User.objects.filter(dept_id=instance.id).exists():
                return self.error_response('该部门下还有用户，无法删除')
        except ImportError:
            # 如果User模型不存在，跳过检查
            pass

        # 执行删除
        response = super().destroy(request, *args, **kwargs)

        if response.status_code == 200:
            # 记录删除日志
            try:
                log_user_action(
                    user=request.user,
                    log_type='delete',
                    action=f'删除部门: {dept_name}',
                    target_model='Department',
                    target_id=dept_id,
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
            except Exception as e:
                # 日志记录失败不应该影响删除操作
                print(f"记录删除日志失败: {e}")

        return response

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@api_view(['GET'])
def department_all_view(request):
    """
    获取所有部门数据（不分页）
    GET /api/system/departments/all/
    用于下拉选择、筛选选项等场景
    """
    try:
        # 获取所有启用的部门
        departments = Department.objects.filter(status=True).order_by('dept_name')

        serializer = DepartmentSerializer(departments, many=True)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    except Exception as e:
        logger.error(f'获取所有部门数据失败: {str(e)}', exc_info=True)
        return Response({
            'code': 500,
            'message': f'获取部门数据失败: {str(e)}',
            'data': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def department_tree_view(request):
    """
    获取部门树形结构
    GET /api/system/departments/tree
    """
    # 获取顶级部门（parent_id为空或0）
    root_departments = Department.objects.filter(
        parent_id__isnull=True,
        status=True
    ).order_by('dept_name')

    serializer = DepartmentTreeSerializer(root_departments, many=True)
    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def department_statistics_view(request):
    """
    获取部门统计信息
    GET /api/system/departments/statistics/
    """
    try:
        # 获取总部门数
        total_count = Department.objects.count()

        # 获取启用部门数
        active_count = Department.objects.filter(status=True).count()

        # 获取停用部门数
        inactive_count = Department.objects.filter(status=False).count()

        # 获取一级部门数（没有父部门）
        top_level_count = Department.objects.filter(parent_id__isnull=True).count()

        # 获取二级部门数（有父部门）
        sub_level_count = Department.objects.filter(parent_id__isnull=False).count()

        # 获取关联用户数（需要从用户表统计）
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user_count = User.objects.filter(dept_id__isnull=False).count()
        except Exception:
            user_count = 0

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'total_count': total_count,
                'active_count': active_count,
                'inactive_count': inactive_count,
                'top_level_count': top_level_count,
                'sub_level_count': sub_level_count,
                'user_count': user_count
            }
        })
    except Exception as e:
        logger.error(f'获取部门统计信息失败: {str(e)}', exc_info=True)
        return Response({
            'code': 500,
            'message': f'获取统计信息失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DictDataListCreateView(BaseListCreateView):
    """
    数据字典列表和创建接口
    GET /api/system/dicts - 获取数据字典列表
    POST /api/system/dicts - 创建数据字典
    """
    queryset = Dictionary.objects.all()
    serializer_class = DictionarySerializer
    filterset_fields = ['type_code', 'status', 'editable']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['created_at', 'order', 'type_code']
    ordering = ['type_code', 'order', 'code']
    permission_classes = [AllowAny]  # 临时允许匿名访问用于测试

    def create(self, request, *args, **kwargs):
        """重写create方法，添加排序功能和编码标准化"""
        try:
            # 标准化编码为小写
            if 'code' in request.data:
                request.data['code'] = request.data['code'].lower()

            # 调用父类的create方法
            response = super().create(request, *args, **kwargs)

            # 如果创建成功，重新整理排序并清除缓存
            if response.status_code == 201:
                _reorder_dict_items()
                # 清除字典缓存
                from apps.purchase.services.dict_service import DictService
                DictService.clear_cache()

            return response
        except Exception as e:
            logger.error(f"创建字典项失败: {str(e)}")
            return Response({
                'code': 500,
                'message': f'创建失败: {str(e)}',
                'data': {}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def list(self, request, *args, **kwargs):
        """重写list方法，返回统一格式的响应"""
        try:
            # 获取筛选后的查询集
            queryset = self.filter_queryset(self.get_queryset())

            # 获取总记录数（用于统计）
            total_count = queryset.count()

            # 执行分页
            page = self.paginate_queryset(queryset)

            if page is not None:
                # 序列化当前页数据
                serializer = self.get_serializer(page, many=True)

                # 获取分页信息
                paginator = self.paginator
                current_page = paginator.page.number
                page_size = paginator.page.paginator.per_page
                total_pages = paginator.page.paginator.num_pages
                has_next = paginator.page.has_next()
                has_previous = paginator.page.has_previous()

                return Response({
                    'code': 200,
                    'message': '获取成功',
                    'data': {
                        'results': serializer.data,
                        'count': total_count,
                        'total': total_count,
                        'current_page': current_page,
                        'page_size': page_size,
                        'total_pages': total_pages,
                        'has_next': has_next,
                        'has_previous': has_previous
                    }
                })

            # 无分页情况（返回所有数据）
            serializer = self.get_serializer(queryset, many=True)
            return Response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'results': serializer.data,
                    'count': total_count,
                    'total': total_count,
                    'current_page': 1,
                    'page_size': total_count,
                    'total_pages': 1,
                    'has_next': False,
                    'has_previous': False
                }
            })

        except Exception as e:
            logger.error(f'获取数据字典列表失败: {str(e)}', exc_info=True)
            return Response({
                'code': 500,
                'message': f'获取数据字典列表失败: {str(e)}',
                'data': {}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)




# 通知管理相关视图
class NotificationViewSet(viewsets.ModelViewSet):
    """
    通知视图集
    """
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['notification_type', 'priority', 'is_read']
    search_fields = ['title', 'content']
    ordering_fields = ['created_at', 'priority']
    ordering = ['-created_at']
    permission_classes = [AllowAny]  # 临时允许匿名访问用于测试
    pagination_class = CustomPageNumberPagination

    def get_queryset(self):
        """返回通知列表 - 与统计API保持一致的逻辑"""
        if not self.request.user.is_authenticated:
            return Notification.objects.none()

        # 如果是管理员，返回所有通知；否则只返回当前用户的通知
        if self.request.user.is_superuser or getattr(self.request.user, 'role', '') == 'admin':
            # 管理员查看所有通知
            return Notification.objects.filter(is_deleted=False)
        else:
            # 普通用户只查看自己的通知
            return Notification.objects.filter(
                recipient=self.request.user,
                is_deleted=False
            )

    def list(self, request, *args, **kwargs):
        """重写list方法，使用自定义分页器"""
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                # 直接返回自定义分页器的响应
                return self.get_paginated_response(serializer.data)

            # 如果没有分页，返回所有数据
            serializer = self.get_serializer(queryset, many=True)
            return Response({
                'code': 200,
                'message': '获取成功',
                'data': {
                    'results': serializer.data,
                    'count': len(serializer.data)
                }
            })
        except Exception as e:
            logger.error(f'获取通知列表失败: {str(e)}', exc_info=True)
            return Response({
                'code': 500,
                'message': f'获取失败: {str(e)}',
                'data': {}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def mark_notification_read(request, pk):
    """
    标记通知为已读
    """
    try:
        # 添加调试信息
        logger.info(f"标记通知已读 - 通知ID: {pk}, 用户: {request.user}, 用户ID: {request.user.id if request.user.is_authenticated else 'Anonymous'}")

        # 首先检查通知是否存在
        try:
            notification = Notification.objects.get(pk=pk)
            logger.info(f"找到通知 - ID: {notification.id}, 标题: {notification.title}, 接收者: {notification.recipient}, 接收者ID: {notification.recipient.id}, 是否删除: {notification.is_deleted}")
        except Notification.DoesNotExist:
            logger.warning(f"通知不存在 - ID: {pk}")
            return Response({
                'code': 404,
                'message': '通知不存在',
                'data': {}
            }, status=status.HTTP_404_NOT_FOUND)

        # 检查用户认证状态
        if not request.user.is_authenticated:
            logger.warning(f"用户未认证")
            return Response({
                'code': 401,
                'message': '用户未认证',
                'data': {}
            }, status=status.HTTP_401_UNAUTHORIZED)

        # 临时允许标记任何通知为已读（用于测试）
        # 在生产环境中应该启用下面的权限检查
        # if notification.recipient.id != request.user.id:
        #     logger.warning(f"通知不属于当前用户 - 通知接收者: {notification.recipient.id}, 当前用户: {request.user.id}")
        #     return Response({
        #         'code': 403,
        #         'message': '无权限操作此通知',
        #         'data': {}
        #     }, status=status.HTTP_403_FORBIDDEN)

        # 检查通知是否已删除
        if notification.is_deleted:
            logger.warning(f"通知已删除 - ID: {pk}")
            return Response({
                'code': 404,
                'message': '通知已删除',
                'data': {}
            }, status=status.HTTP_404_NOT_FOUND)

        # 标记为已读
        notification.mark_as_read()
        logger.info(f"通知标记已读成功 - ID: {pk}")

        return Response({
            'code': 200,
            'message': '标记成功',
            'data': {}
        })

    except Exception as e:
        logger.error(f'标记通知已读失败 - ID: {pk}, 错误: {str(e)}', exc_info=True)
        return Response({
            'code': 500,
            'message': f'标记失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def mark_all_notifications_read(request):
    """
    标记所有通知为已读
    """
    try:
        count = Notification.objects.filter(
            recipient=request.user,
            is_read=False,
            is_deleted=False
        ).update(
            is_read=True,
            read_at=timezone.now()
        )

        return Response({
            'code': 200,
            'message': f'已标记 {count} 条通知为已读',
            'data': {'count': count}
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'标记失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def notification_count(request):
    """
    获取当前用户的未读通知数量
    """
    try:
        # 只统计当前用户的未读通知数量
        unread_count = Notification.objects.filter(
            recipient=request.user,
            is_read=False,
            is_deleted=False
        ).count()

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {'unread_count': unread_count}
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def notification_statistics(request):
    """
    获取通知统计信息 - 只统计当前用户的通知（优化版本）
    """
    try:
        from django.core.cache import cache
        from django.db.models import Count

        # 生成缓存键
        cache_key = f"notification_stats_{request.user.id}"
        if request.user.is_superuser or getattr(request.user, 'role', '') == 'admin':
            cache_key = "notification_stats_admin"

        # 尝试从缓存获取
        cached_result = cache.get(cache_key)
        if cached_result:
            return Response(cached_result)

        # 如果是管理员，统计所有通知；否则只统计当前用户的通知
        if request.user.is_superuser or getattr(request.user, 'role', '') == 'admin':
            # 管理员查看所有通知统计
            base_queryset = Notification.objects.filter(is_deleted=False)
        else:
            # 普通用户只查看自己的通知统计
            base_queryset = Notification.objects.filter(
                recipient=request.user,
                is_deleted=False
            )

        # 使用单个查询获取所有统计数据
        stats = base_queryset.aggregate(
            total_count=Count('id'),
            unread_count=Count('id', filter=models.Q(is_read=False)),
            read_count=Count('id', filter=models.Q(is_read=True))
        )

        # 按类型统计（限制查询数量）
        type_stats = base_queryset.values('notification_type').annotate(
            count=Count('id')
        ).order_by('-count')[:10]  # 限制返回数量

        # 按优先级统计（限制查询数量）
        priority_stats = base_queryset.values('priority').annotate(
            count=Count('id')
        ).order_by('-count')[:10]  # 限制返回数量

        result = {
            'code': 200,
            'message': '获取成功',
            'data': {
                'total_count': stats['total_count'],
                'unread_count': stats['unread_count'],
                'read_count': stats['read_count'],
                'type_stats': list(type_stats),
                'priority_stats': list(priority_stats)
            }
        }

        # 缓存结果5分钟
        cache.set(cache_key, result, 300)

        return Response(result)

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取统计信息失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def create_notification(request):
    """
    创建通知
    """
    try:
        data = request.data

        # 获取接收者
        recipient_id = data.get('recipient_id')
        if not recipient_id:
            return Response({
                'code': 400,
                'message': '接收者ID不能为空',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            recipient = User.objects.get(id=recipient_id)
        except User.DoesNotExist:
            return Response({
                'code': 400,
                'message': '接收者不存在',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建通知
        notification = Notification.create_notification(
            title=data.get('title', ''),
            content=data.get('content', ''),
            notification_type=data.get('notification_type', 'system'),
            recipient=recipient,
            sender=request.user if request.user.is_authenticated else None,
            target_model=data.get('target_model'),
            target_id=data.get('target_id'),
            target_url=data.get('target_url'),
            priority=data.get('priority', 'normal')
        )

        serializer = NotificationSerializer(notification)
        return Response({
            'code': 200,
            'message': '通知创建成功',
            'data': serializer.data
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'创建通知失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

def send_notification_to_role(title, content, notification_type, role_name,
                             sender=None, target_model=None, target_id=None,
                             target_url=None, priority='normal'):
    """
    向指定角色的所有用户发送通知
    """
    try:
        from django.contrib.auth import get_user_model
        from apps.roles.models import Role

        User = get_user_model()

        # 获取角色
        try:
            role = Role.objects.get(code=role_name)
            users = User.objects.filter(roles=role, is_active=True)
        except Role.DoesNotExist:
            print(f"角色 {role_name} 不存在")
            return

        # 批量创建通知
        notifications = []
        for user in users:
            notifications.append(Notification(
                title=title,
                content=content,
                notification_type=notification_type,
                priority=priority,
                sender=sender,
                recipient=user,
                target_model=target_model,
                target_id=target_id,
                target_url=target_url
            ))

        Notification.objects.bulk_create(notifications)
        print(f"已向 {len(notifications)} 个用户发送通知")

    except Exception as e:
        print(f"发送通知失败: {str(e)}")

def send_notification_to_user(title, content, notification_type, user_id,
                             sender=None, target_model=None, target_id=None,
                             target_url=None, priority='normal'):
    """
    向指定用户发送通知
    """
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            user = User.objects.get(id=user_id, is_active=True)
        except User.DoesNotExist:
            logger.warning(f"用户 {user_id} 不存在")
            return

        Notification.create_notification(
            title=title,
            content=content,
            notification_type=notification_type,
            recipient=user,
            sender=sender,
            target_model=target_model,
            target_id=target_id,
            target_url=target_url,
            priority=priority
        )

        logger.info(f"已向用户 {user.username} 发送通知")

    except Exception as e:
        logger.error(f"发送通知失败: {str(e)}")


class DictDataDetailView(BaseDetailView):
    """
    数据字典详情、更新和删除接口
    GET /api/system/dicts/{id} - 获取数据字典详情
    PUT /api/system/dicts/{id} - 更新数据字典
    DELETE /api/system/dicts/{id} - 删除数据字典
    """
    queryset = Dictionary.objects.all()
    serializer_class = DictionarySerializer
    permission_classes = [AllowAny]  # 临时允许匿名访问用于测试

    def update(self, request, *args, **kwargs):
        """重写update方法，添加排序功能和编码标准化"""
        try:
            # 标准化编码为小写
            if 'code' in request.data:
                request.data['code'] = request.data['code'].lower()

            # 调用父类的update方法
            response = super().update(request, *args, **kwargs)

            # 如果更新成功，重新整理排序并清除缓存
            if response.status_code == 200:
                _reorder_dict_items()
                # 清除字典缓存
                from apps.purchase.services.dict_service import DictService
                DictService.clear_cache()

            return response
        except Exception as e:
            logger.error(f"更新字典项失败: {str(e)}")
            return Response({
                'code': 500,
                'message': f'更新失败: {str(e)}',
                'data': {}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def destroy(self, request, *args, **kwargs):
        """重写destroy方法，添加缓存清理"""
        try:
            # 调用父类的destroy方法
            response = super().destroy(request, *args, **kwargs)

            # 如果删除成功，重新整理排序并清除缓存
            if response.status_code == 204:
                _reorder_dict_items()
                # 清除字典缓存
                from apps.purchase.services.dict_service import DictService
                DictService.clear_cache()

            return response
        except Exception as e:
            logger.error(f"删除字典项失败: {str(e)}")
            return Response({
                'code': 500,
                'message': f'删除失败: {str(e)}',
                'data': {}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def dict_statistics_view(request):
    """
    获取数据字典统计信息
    GET /api/system/dicts/statistics/
    """
    try:
        # 获取总数
        total_count = Dictionary.objects.count()

        # 获取启用/停用数量
        active_count = Dictionary.objects.filter(status=True).count()
        inactive_count = Dictionary.objects.filter(status=False).count()

        # 获取字典类型数量
        type_count = Dictionary.objects.values('type_code').distinct().count()

        # 获取所有字典类型列表（包含显示名称）
        types_data = Dictionary.objects.values('type_code', 'type_name').distinct().order_by('type_code')
        types = [{'type_code': item['type_code'], 'type_name': item['type_name']} for item in types_data]

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'total_count': total_count,
                'active_count': active_count,
                'inactive_count': inactive_count,
                'type_count': type_count,
                'types': types
            }
        })
    except Exception as e:
        logger.error(f'获取数据字典统计信息失败: {str(e)}', exc_info=True)
        return Response({
            'code': 500,
            'message': f'获取统计信息失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)




@api_view(['GET'])
@permission_classes([AllowAny])
def dict_by_type_view(request, dict_type):
    """
    根据类型获取数据字典
    GET /api/system/dict/{type}
    """
    enabled = request.GET.get('enabled', 'true').lower() == 'true'

    queryset = Dictionary.objects.filter(type_code=dict_type)
    if enabled:
        queryset = queryset.filter(status=True)

    queryset = queryset.order_by('order', 'code')

    serializer = DictionarySerializer(queryset, many=True)
    return Response({
        'code': 200,
        'message': 'success',
        'data': {
            'type_code': dict_type,
            'items': serializer.data
        }
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def export_departments(request):
    """
    导出部门数据
    GET /api/system/departments/export/
    """
    try:
        # 获取查询参数
        level_filter = request.GET.get('level')
        status_filter = request.GET.get('status')
        search_filter = request.GET.get('search')

        # 构建查询
        queryset = Department.objects.all()

        if level_filter:
            if level_filter == '1':
                # 一级部门（没有父部门）
                queryset = queryset.filter(parent_id__isnull=True)
            elif level_filter == '2':
                # 二级部门（有父部门）
                queryset = queryset.filter(parent_id__isnull=False)

        if status_filter is not None:
            if status_filter.lower() == 'true':
                queryset = queryset.filter(status=True)
            elif status_filter.lower() == 'false':
                queryset = queryset.filter(status=False)

        if search_filter:
            queryset = queryset.filter(
                models.Q(dept_name__icontains=search_filter) |
                models.Q(dept_code__icontains=search_filter) |
                models.Q(manager_name__icontains=search_filter) |
                models.Q(description__icontains=search_filter)
            )

        departments = queryset.order_by('id')

        # 记录查询结果数量
        logger.info(f"导出部门数据，查询到 {departments.count()} 条记录")

        # 准备导出数据
        data = []
        for dept in departments:
            # 获取上级部门名称
            parent_name = ''
            if dept.parent_id:
                try:
                    parent_dept = Department.objects.get(id=dept.parent_id)
                    parent_name = parent_dept.dept_name
                except Department.DoesNotExist:
                    parent_name = '未找到上级部门'

            data.append({
                '部门ID': dept.id,
                '部门名称': dept.dept_name,
                '部门编码': dept.dept_code,
                '上级部门': parent_name,
                '部门负责人': dept.manager_name or '',
                '联系电话': dept.contact_phone or '',
                '审批额度': dept.approval_limit or 0,
                '可审批': '是' if dept.can_approve else '否',
                '可验收': '是' if dept.can_accept else '否',
                '可财务': '是' if dept.can_finance else '否',
                '部门描述': dept.description or '',
                '状态': '启用' if dept.status else '停用',
                '创建时间': dept.created_at.strftime('%Y-%m-%d %H:%M:%S') if dept.created_at else ''
            })

        # 如果没有数据，创建一个空的Excel文件
        if not data:
            data = [{
                '部门ID': '',
                '部门名称': '',
                '部门编码': '',
                '上级部门': '',
                '部门负责人': '',
                '联系电话': '',
                '审批额度': '',
                '可审批': '',
                '可验收': '',
                '可财务': '',
                '部门描述': '',
                '状态': '',
                '创建时间': ''
            }]

        # 创建Excel文件
        df = pd.DataFrame(data)
        output = io.BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='部门数据', index=False)

        output.seek(0)

        # 设置响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="部门数据_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response

    except Exception as e:
        logger.error(f'导出部门数据失败: {str(e)}', exc_info=True)
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def import_departments(request):
    """
    导入部门数据
    POST /api/system/departments/import/
    """
    try:
        if 'file' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要导入的文件',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # 读取Excel文件
        df = pd.read_excel(file)

        success_count = 0
        error_count = 0
        errors = []

        # 步骤4：逐行处理导入数据
        for index, row in df.iterrows():
            try:
                # 验证必填字段
                if pd.isna(row.get('部门名称')) or not str(row['部门名称']).strip():
                    errors.append(f'第{index + 2}行：部门名称不能为空')
                    continue

                if pd.isna(row.get('部门编码')) or not str(row['部门编码']).strip():
                    errors.append(f'第{index + 2}行：部门编码不能为空')
                    continue

                # 查找上级部门
                parent = None
                parent_id = None
                if pd.notna(row.get('上级部门')) and str(row['上级部门']).strip():
                    parent = Department.objects.filter(dept_name=str(row['上级部门']).strip()).first()
                    if not parent:
                        errors.append(f'第{index + 2}行：找不到上级部门"{row["上级部门"]}"')
                        continue
                    parent_id = parent.id

                # 创建或更新部门数据
                dept_data = {
                    'dept_name': str(row['部门名称']).strip(),
                    'dept_code': str(row['部门编码']).strip().upper(),
                    'parent_id': parent_id,
                    'manager_name': str(row.get('负责人', '')).strip() if pd.notna(row.get('负责人')) else '',
                    'contact_phone': str(row.get('联系电话', '')).strip() if pd.notna(row.get('联系电话')) else '',
                    'description': str(row.get('部门描述', '')).strip() if pd.notna(row.get('部门描述')) else '',
                    'status': str(row.get('状态', '启用')).strip() == '启用'
                }

                # 检查是否已存在
                existing_dept = Department.objects.filter(dept_code=row['部门编码']).first()
                if existing_dept:
                    # 更新现有部门
                    for key, value in dept_data.items():
                        setattr(existing_dept, key, value)
                    existing_dept.save()
                else:
                    # 创建新部门
                    Department.objects.create(**dept_data)

                success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f'第{index + 2}行: {str(e)}')

        return Response({
            'code': 200,
            'message': f'导入完成，成功{success_count}条，失败{error_count}条',
            'data': {
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导入失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def download_department_template(request):
    """
    下载部门导入模板
    GET /api/system/departments/template/
    """
    try:
        import io
        from django.http import HttpResponse
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = '部门导入模板'

        # 设置表头
        headers = ['部门名称', '部门编码', '上级部门', '部门描述', '状态', '负责人', '联系电话']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 添加示例数据
        sample_data = [
            ['总经理办公室', 'CEO_OFFICE', '', '负责公司整体管理和决策', '启用', '张总', '13800138000'],
            ['技术研发部', 'TECH_DEV', '总经理办公室', '负责产品技术研发和创新', '启用', '李技术总监', '13800138001'],
            ['市场营销部', 'MARKETING', '总经理办公室', '负责市场推广和客户关系', '启用', '王市场总监', '13800138002'],
            ['财务管理部', 'FINANCE', '总经理办公室', '负责财务管理和成本控制', '启用', '赵财务总监', '13800138003'],
            ['人力资源部', 'HR', '总经理办公室', '负责人员招聘和培训', '启用', '刘人事总监', '13800138004']
        ]

        for row_idx, row_data in enumerate(sample_data, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)


        # 设置列宽
        column_widths = [20, 15, 20, 30, 10, 15, 15]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + i)].width = width

        # 创建填写说明工作表
        ws_instructions = wb.create_sheet('填写说明')

        # 说明表头
        instruction_headers = ['字段名称', '是否必填', '字段说明', '示例值']
        for col, header in enumerate(instruction_headers, 1):
            cell = ws_instructions.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 说明数据
        instruction_data = [
            ['部门名称', '必填', '部门的显示名称，不能重复', '技术研发部'],
            ['部门编码', '必填', '部门的唯一标识，建议使用大写字母和下划线，不能重复', 'TECH_DEV'],
            ['上级部门', '选填', '填写上级部门的名称，如果是顶级部门则留空', '总经理办公室'],
            ['部门负责人', '选填', '部门负责人的姓名', '张三'],
            ['联系电话', '选填', '负责人的联系电话', '13800138001'],
            ['审批额度', '选填', '部门可审批的金额上限，单位为元', '500000'],
            ['可审批', '选填', '是否具有审批权限，填写"是"或"否"', '是'],
            ['可验收', '选填', '是否具有验收权限，填写"是"或"否"', '是'],
            ['可财务', '选填', '是否具有财务权限，填写"是"或"否"', '否'],
            ['部门描述', '选填', '部门的职能描述', '负责产品技术研发'],
            ['状态', '选填', '部门状态，填写"启用"或"停用"', '启用']
        ]

        for row_idx, row_data in enumerate(instruction_data, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws_instructions.cell(row=row_idx, column=col_idx, value=value)

        # 设置说明工作表列宽
        ws_instructions.column_dimensions['A'].width = 15
        ws_instructions.column_dimensions['B'].width = 12
        ws_instructions.column_dimensions['C'].width = 50
        ws_instructions.column_dimensions['D'].width = 20

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 设置响应头并返回文件
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="部门导入模板.xlsx"'

        return response

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'模板下载失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_dicts(request):
    """
    导出数据字典
    GET /api/system/dicts/export/
    """
    try:
        # 获取查询参数
        type_filter = request.GET.get('type')
        status_filter = request.GET.get('status')
        search_filter = request.GET.get('search')

        # 构建查询
        queryset = Dictionary.objects.all()

        if type_filter:
            queryset = queryset.filter(type_code=type_filter)

        if status_filter is not None:
            if status_filter.lower() == 'true':
                queryset = queryset.filter(status=True)
            elif status_filter.lower() == 'false':
                queryset = queryset.filter(status=False)

        if search_filter:
            queryset = queryset.filter(
                models.Q(name__icontains=search_filter) |
                models.Q(code__icontains=search_filter) |
                models.Q(description__icontains=search_filter)
            )

        dicts = queryset.order_by('type_code', 'order', 'code')

        # 记录查询结果数量
        logger.info(f"导出数据字典，查询到 {dicts.count()} 条记录")

        # 准备导出数据
        data = []
        for dict_item in dicts:
            data.append({
                '字典ID': dict_item.id,
                '字典类型编码': dict_item.type_code,
                '字典类型名称': dict_item.type_name,
                '字典名称': dict_item.name,
                '字典编码': dict_item.code,
                '排序': dict_item.order,
                '描述': dict_item.description or '',
                '状态': '启用' if dict_item.status else '停用',
                '可编辑': '可编辑' if dict_item.editable == 1 else '不可编辑',
                '创建时间': dict_item.created_at.strftime('%Y-%m-%d %H:%M:%S') if dict_item.created_at else ''
            })

        # 如果没有数据，创建一个空的Excel文件
        if not data:
            data = [{
                '字典ID': '',
                '字典类型编码': '',
                '字典类型名称': '',
                '字典名称': '',
                '字典编码': '',
                '排序': '',
                '描述': '',
                '状态': '',
                '可编辑': '',
                '创建时间': ''
            }]

        # 创建Excel文件
        df = pd.DataFrame(data)
        output = io.BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='数据字典', index=False)

        output.seek(0)

        # 设置响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="数据字典_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response

    except Exception as e:
        logger.error(f'导出数据字典失败: {str(e)}', exc_info=True)
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def import_dicts(request):
    """
    导入数据字典
    POST /api/system/dicts/import/
    """
    try:
        if 'file' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要导入的文件',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # 读取Excel文件
        df = pd.read_excel(file)

        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                # 验证必填字段
                required_fields = ['字典类型名称', '字典名称', '字典编码', '状态', '可编辑']
                for field in required_fields:
                    if pd.isna(row.get(field)) or str(row.get(field)).strip() == '':
                        raise ValueError(f'第{index+2}行：{field}为必填字段')

                # 根据字典类型名称查找对应的type_code
                type_name = str(row['字典类型名称']).strip()
                existing_type = Dictionary.objects.filter(type_name=type_name).first()

                if existing_type:
                    type_code = existing_type.type_code
                else:
                    # 如果是新的字典类型，需要生成type_code
                    # 这里可以根据业务规则生成，暂时使用拼音或英文
                    type_code = type_name.lower().replace(' ', '_')

                # 验证状态字段
                status_value = str(row['状态']).strip()
                if status_value not in ['启用', '停用']:
                    raise ValueError(f'第{index+2}行：状态必须是"启用"或"停用"')

                # 验证可编辑字段
                editable_value = str(row['可编辑']).strip()
                if editable_value not in ['可编辑', '不可编辑']:
                    raise ValueError(f'第{index+2}行：可编辑必须是"可编辑"或"不可编辑"')

                # 自动计算排序值（同类型最大值+1）
                max_order = Dictionary.objects.filter(type_code=type_code).aggregate(
                    max_order=models.Max('order')
                )['max_order'] or 0

                # 创建或更新数据字典
                dict_data = {
                    'type_code': type_code,
                    'type_name': type_name,
                    'name': str(row['字典名称']).strip(),
                    'code': str(row['字典编码']).strip().lower(),  # 强制转换为小写
                    'order': max_order + 1,
                    'description': str(row.get('描述', '')).strip(),
                    'status': status_value == '启用',
                    'editable': 1 if editable_value == '可编辑' else 2
                }

                # 检查编码唯一性
                existing_dict = Dictionary.objects.filter(
                    type_code=type_code,
                    code=dict_data['code']
                ).first()

                if existing_dict:
                    # 更新现有字典
                    for key, value in dict_data.items():
                        setattr(existing_dict, key, value)
                    existing_dict.save()
                else:
                    # 创建新字典
                    Dictionary.objects.create(**dict_data)

                success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f'第{index + 2}行: {str(e)}')

        # 导入完成后，重新整理所有字典类型的排序并清除缓存
        if success_count > 0:
            _reorder_dict_items()
            # 清除字典缓存
            from apps.purchase.services.dict_service import DictService
            DictService.clear_cache()

        return Response({
            'code': 200,
            'message': f'导入完成，成功{success_count}条，失败{error_count}条',
            'data': {
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导入失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def download_dict_template(request):
    """
    下载数据字典导入模板（增强版，包含下拉验证）
    GET /api/system/dicts/template/
    """
    try:
        from openpyxl import Workbook
        from openpyxl.worksheet.datavalidation import DataValidation
        from openpyxl.styles import Font, PatternFill, Alignment

        # 获取现有字典类型名称
        existing_type_names = list(Dictionary.objects.values_list('type_name', flat=True).distinct().order_by('type_name'))

        # 如果没有数据，提供默认的字典类型
        if not existing_type_names:
            existing_type_names = ['状态', '物品种类', '计量单位', '采购方式', '经费项目', '采购类型', '需求来源']

        logger.info(f"模板生成 - 字典类型名称: {existing_type_names}")

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "数据字典模板"

        # 定义表头
        headers = ['字典类型名称', '字典名称', '字典编码', '状态', '可编辑', '描述']

        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # 添加示例数据
        sample_data = [
            ['物品种类', '办公用品', 'OFFICE_SUPPLIES', '启用', '可编辑', '办公日常用品'],
            ['物品种类', '电子设备', 'ELECTRONIC_DEVICE', '启用', '可编辑', '电子设备类'],
            ['紧急程度', '紧急', 'URGENT', '启用', '不可编辑', '紧急采购']
        ]

        for row, data in enumerate(sample_data, 2):
            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)

        # 创建下拉验证
        # 1. 字典类型名称下拉验证
        if existing_type_names:
            type_validation = DataValidation(
                type="list",
                formula1=f'"{",".join(existing_type_names)}"',
                allow_blank=False
            )
            type_validation.error = '请从下拉列表中选择字典类型名称'
            type_validation.errorTitle = '输入错误'
            ws.add_data_validation(type_validation)
            type_validation.add(f'A2:A1000')  # 应用到A列的数据行

        # 2. 状态下拉验证
        status_validation = DataValidation(
            type="list",
            formula1='"启用,停用"',
            allow_blank=False
        )
        status_validation.error = '请选择"启用"或"停用"'
        status_validation.errorTitle = '输入错误'
        ws.add_data_validation(status_validation)
        status_validation.add('D2:D1000')  # 应用到D列的数据行

        # 3. 可编辑下拉验证
        editable_validation = DataValidation(
            type="list",
            formula1='"可编辑,不可编辑"',
            allow_blank=False
        )
        editable_validation.error = '请选择"可编辑"或"不可编辑"'
        editable_validation.errorTitle = '输入错误'
        ws.add_data_validation(editable_validation)
        editable_validation.add('E2:E1000')  # 应用到E列的数据行

        # 设置列宽
        column_widths = [20, 20, 20, 10, 12, 30]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width

        # 添加说明工作表
        ws_instructions = wb.create_sheet("填写说明")
        instructions_data = [
            ['字段名称', '是否必填', '说明'],
            ['字典类型名称', '必填', '从下拉列表中选择现有的字典类型，如：物品种类、状态等'],
            ['字典名称', '必填', '字典项的中文显示名称，如：办公用品、草稿等'],
            ['字典编码', '必填', '字典项的唯一英文编码，如：OFFICE_SUPPLIES、DRAFT等'],
            ['状态', '必填', '从下拉列表选择：启用 或 停用'],
            ['可编辑', '必填', '从下拉列表选择：可编辑 或 不可编辑'],
            ['描述', '可选', '字典项的详细说明，可以为空']
        ]

        # 设置说明表头样式
        for row, data in enumerate(instructions_data, 1):
            for col, value in enumerate(data, 1):
                cell = ws_instructions.cell(row=row, column=col, value=value)
                if row == 1:  # 表头
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment

        # 设置说明表列宽
        ws_instructions.column_dimensions['A'].width = 15
        ws_instructions.column_dimensions['B'].width = 10
        ws_instructions.column_dimensions['C'].width = 50

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="数据字典导入模板.xlsx"'

        return response

    except Exception as e:
        logger.error(f"生成字典导入模板失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'模板下载失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 日志管理相关视图
@api_view(['GET'])
def get_system_logs(request):
    """
    获取系统日志列表（增强版）
    """
    try:
        from django.core.paginator import Paginator
        from django.db.models import Q, Count
        from django.utils import timezone
        from datetime import timedelta

        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        log_level = request.GET.get('logLevel', '')
        module = request.GET.get('module', '')
        username = request.GET.get('username', '')
        start_date = request.GET.get('start_date', '')
        end_date = request.GET.get('end_date', '')
        ip_address = request.GET.get('ip_address', '')
        search = request.GET.get('search', '')

        # 构建查询条件
        queryset = SystemLog.objects.all()

        if log_level:
            queryset = queryset.filter(log_type=log_level.lower())

        if module:
            queryset = queryset.filter(target_model__icontains=module)

        if username:
            queryset = queryset.filter(username__icontains=username)

        if ip_address:
            queryset = queryset.filter(ip_address__icontains=ip_address)

        if search:
            queryset = queryset.filter(
                Q(action__icontains=search) |
                Q(username__icontains=search) |
                Q(target_model__icontains=search)
            )

        # 日期范围过滤
        if start_date:
            try:
                start_dt = timezone.datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                queryset = queryset.filter(created_at__gte=start_dt)
            except ValueError:
                pass

        if end_date:
            try:
                end_dt = timezone.datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                queryset = queryset.filter(created_at__lte=end_dt)
            except ValueError:
                pass

        # 按创建时间倒序排列
        queryset = queryset.order_by('-created_at')

        # 分页处理
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # 转换数据格式
        logs = []
        for log in page_obj:
            # 构建详细信息
            details = {
                'log_id': log.id,
                'log_type': log.log_type,
                'log_type_display': log.get_log_type_display(),
                'target_model': log.target_model,
                'target_id': log.target_id,
                'ip_address': log.ip_address,
                'user_agent': log.user_agent,
                'created_at': log.created_at.isoformat() if log.created_at else '',
                'request_data': log.request_data or {},
                'response_data': log.response_data or {},
                'user_info': {
                    'user_id': log.user.id if log.user else None,
                    'username': log.username,
                    'real_name': log.user.real_name if log.user else None
                }
            }

            logs.append({
                'id': log.id,
                'level': log.log_type.upper() if log.log_type else 'INFO',
                'module': log.target_model or 'system',
                'username': log.username or 'system',
                'message': log.action or '系统操作',
                'ip_address': log.ip_address or '-',
                'timestamp': log.created_at.isoformat() if log.created_at else '',
                'user_agent': log.user_agent or '',
                'target_id': log.target_id,
                'request_data': log.request_data or {},
                'response_data': log.response_data or {},
                'details': details
            })

        return Response({
            'code': 200,
            'message': '获取日志成功',
            'data': {
                'results': logs,
                'count': paginator.count,
                'page': page,
                'page_size': page_size
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取日志失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_log_statistics(request):
    """
    获取日志统计信息（真实数据）
    """
    try:
        from django.db.models import Count
        from datetime import datetime, timedelta

        # 确保有一些测试日志数据
        if SystemLog.objects.count() == 0:
            # 创建一些测试日志数据
            test_user = request.user if request.user.is_authenticated else None
            LoggingService.log_user_action(
                user=test_user,
                log_type='info',
                action='系统启动日志统计功能',
                target_model='System',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT')
            )

        # 获取当前时间和日期
        now = timezone.now()
        today = now.date()

        # 今日日志数量 - 使用时间范围查询避免时区问题
        today_start = timezone.make_aware(datetime.combine(today, datetime.min.time()))
        today_end = timezone.make_aware(datetime.combine(today, datetime.max.time()))
        today_count = SystemLog.objects.filter(
            created_at__gte=today_start,
            created_at__lte=today_end
        ).count()

        # 本周日志数量
        week_start = today - timedelta(days=today.weekday())
        week_start_datetime = timezone.make_aware(datetime.combine(week_start, datetime.min.time()))
        week_count = SystemLog.objects.filter(created_at__gte=week_start_datetime).count()

        # 总日志数量
        total_count = SystemLog.objects.count()

        # 活跃用户数量（最近7天有日志记录的用户）
        week_ago = now - timedelta(days=7)
        user_count = SystemLog.objects.filter(
            created_at__gte=week_ago,
            user__isnull=False
        ).values('user').distinct().count()

        # 错误日志数量（最近7天）- 包含多种错误类型
        error_count = SystemLog.objects.filter(
            created_at__gte=week_ago,
            log_type__in=['error', 'security', 'permission_denied', 'authentication_failed']
        ).count()

        statistics = {
            'today_count': today_count,
            'week_count': week_count,
            'total_count': total_count,
            'user_count': user_count,
            'error_count': error_count
        }

        # 记录统计查询日志
        LoggingService.log_user_action(
            user=request.user if request.user.is_authenticated else None,
            log_type='info',
            action='查询日志统计信息',
            target_model='SystemLog',
            ip_address=request.META.get('REMOTE_ADDR'),
            response_data=statistics
        )

        return Response({
            'code': 200,
            'message': '获取统计信息成功',
            'data': statistics
        })

    except Exception as e:
        # 记录错误日志
        try:
            LoggingService.log_user_action(
                user=request.user if request.user.is_authenticated else None,
                log_type='error',
                action=f'获取日志统计信息失败: {str(e)}',
                target_model='SystemLog',
                ip_address=request.META.get('REMOTE_ADDR')
            )
        except:
            pass

        return Response({
            'code': 500,
            'message': f'获取统计信息失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_system_logs(request):
    """
    导出系统日志
    GET /api/system/logs/export/
    """
    try:
        import io
        from django.http import HttpResponse
        from django.utils import timezone
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        from django.db.models import Q

        # 获取查询参数
        log_level = request.GET.get('logLevel', '')
        module = request.GET.get('module', '')
        username = request.GET.get('username', '')
        start_date = request.GET.get('start_date', '')
        end_date = request.GET.get('end_date', '')

        # 构建查询条件
        queryset = SystemLog.objects.all()

        if log_level:
            queryset = queryset.filter(log_type=log_level.lower())

        if module:
            queryset = queryset.filter(target_model__icontains=module)

        if username:
            queryset = queryset.filter(username__icontains=username)

        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)

        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)

        # 按时间倒序排列
        logs = queryset.order_by('-created_at')

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = '系统日志'

        # 设置表头
        headers = ['日志ID', '日志级别', '模块', '用户', '操作描述', 'IP地址', '用户代理', '目标ID', '记录时间']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 添加数据
        for row_idx, log in enumerate(logs, 2):
            row_data = [
                log.id,
                log.log_type.upper() if log.log_type else 'INFO',
                log.target_model or 'system',
                log.username or 'system',
                log.action or '系统操作',
                log.ip_address or '-',
                log.user_agent or '-',
                log.target_id or '-',
                log.created_at.strftime('%Y-%m-%d %H:%M:%S') if log.created_at else ''
            ]

            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 设置列宽
        column_widths = [10, 12, 15, 15, 30, 15, 20, 15, 20]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + i)].width = width

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 设置响应头并返回文件
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="系统日志_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def batch_export_system_logs(request):
    """
    批量导出系统日志
    POST /api/system/logs/batch-export/
    """
    try:
        import io
        from django.http import HttpResponse
        from django.utils import timezone
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        # 获取要导出的日志ID列表
        log_ids = request.data.get('log_ids', [])

        if not log_ids:
            return Response({
                'code': 400,
                'message': '请选择要导出的日志',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取指定的日志
        logs = SystemLog.objects.filter(id__in=log_ids).order_by('-created_at')

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = '系统日志'

        # 设置表头
        headers = ['日志ID', '日志级别', '模块', '用户', '操作描述', 'IP地址', '用户代理', '目标ID', '记录时间', '请求数据', '响应数据']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 添加数据
        for row_idx, log in enumerate(logs, 2):
            row_data = [
                log.id,
                log.log_type.upper() if log.log_type else 'INFO',
                log.target_model or 'system',
                log.username or 'system',
                log.action or '系统操作',
                log.ip_address or '-',
                log.user_agent or '-',
                log.target_id or '-',
                log.created_at.strftime('%Y-%m-%d %H:%M:%S') if log.created_at else '',
                str(log.request_data) if log.request_data else '-',
                str(log.response_data) if log.response_data else '-'
            ]

            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 设置列宽
        column_widths = [10, 12, 15, 15, 30, 15, 20, 15, 20, 25, 25]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + i)].width = width

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 设置响应头并返回文件
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="系统日志_批量_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'批量导出失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 菜单管理相关视图 ====================

from rest_framework.decorators import action
from .models import Menu
from .serializers import MenuSerializer


class MenuViewSet(viewsets.ModelViewSet):
    """
    菜单管理视图集
    提供菜单的CRUD操作和权限相关功能
    """
    queryset = Menu.objects.all()
    serializer_class = MenuSerializer
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """创建菜单并自动创建对应权限"""
        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                menu = serializer.save()

                # 自动创建对应的权限
                self._auto_create_permission(menu)

                return Response({
                    'code': 200,
                    'message': '菜单创建成功',
                    'data': MenuSerializer(menu).data
                })
            else:
                return Response({
                    'code': 400,
                    'message': '数据验证失败',
                    'data': serializer.errors
                })
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'创建菜单失败: {str(e)}',
                'data': None
            })

    def update(self, request, *args, **kwargs):
        """更新菜单并同步权限"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)

            if serializer.is_valid():
                menu = serializer.save()

                # 同步权限信息
                self._sync_permission(menu)

                return Response({
                    'code': 200,
                    'message': '菜单更新成功',
                    'data': MenuSerializer(menu).data
                })
            else:
                return Response({
                    'code': 400,
                    'message': '数据验证失败',
                    'data': serializer.errors
                })
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'更新菜单失败: {str(e)}',
                'data': None
            })

    def destroy(self, request, *args, **kwargs):
        """删除菜单并清理权限"""
        try:
            instance = self.get_object()
            menu_name = instance.menu_name
            permission_code = instance.permission_code

            # 删除菜单
            instance.delete()

            # 清理对应的权限
            self._cleanup_permission(permission_code)

            return Response({
                'code': 200,
                'message': f'菜单 "{menu_name}" 删除成功',
                'data': None
            })
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'删除菜单失败: {str(e)}',
                'data': None
            })

    def _cleanup_permission(self, permission_code):
        """清理权限"""
        if not permission_code:
            return

        try:
            from apps.system.models_permission import Permission, RolePermission

            # 查找对应的权限
            permission = Permission.objects.filter(code=permission_code).first()
            if permission:
                # 检查是否还有其他菜单使用这个权限
                other_menus = Menu.objects.filter(permission_code=permission_code).exists()

                if not other_menus:
                    # 没有其他菜单使用，可以删除权限
                    # 先删除角色权限关联
                    RolePermission.objects.filter(permission=permission).delete()

                    # 删除权限
                    permission.delete()
                    print(f"✅ 自动删除权限: {permission_code}")
                else:
                    print(f"⚠️ 权限 {permission_code} 仍被其他菜单使用，不删除")

        except Exception as e:
            print(f"❌ 清理权限失败: {str(e)}")

    def _auto_create_permission(self, menu):
        """自动创建菜单对应的权限，支持层级联动"""
        from apps.system.models_permission import Permission

        if not menu.permission_code:
            # 如果菜单没有权限代码，自动生成一个
            menu.permission_code = self._generate_permission_code(menu)
            menu.save()

        try:
            # 检查权限是否已存在
            if Permission.objects.filter(code=menu.permission_code).exists():
                print(f"✅ 权限 {menu.permission_code} 已存在")
                return

            # 确定权限类型
            category = self._determine_permission_category(menu)

            # 确定模块名称
            module = self._extract_module_from_permission_code(menu.permission_code)

            # 确定父权限
            parent_permission = self._find_parent_permission(menu.permission_code)

            # 创建权限
            permission = Permission.objects.create(
                name=menu.menu_name,
                code=menu.permission_code,
                category=category,
                module=module,
                module_name=self._get_module_display_name(module),
                description=f"自动创建的{menu.menu_name}权限",
                parent=parent_permission,
                sort_order=menu.sort_order,
                is_active=True
            )

            print(f"✅ 自动创建权限: {permission.name} ({permission.code})")

            # 自动为超级管理员角色分配新权限
            self._assign_permission_to_super_admin(permission)

            # 如果是页面权限，自动创建相关的按钮权限
            if menu.menu_type == 'page':
                self._auto_create_related_button_permissions(menu, permission)

        except Exception as e:
            print(f"❌ 自动创建权限失败: {str(e)}")

    def _auto_create_related_button_permissions(self, page_menu, page_permission):
        """为页面权限自动创建相关的按钮权限"""
        from apps.system.models_permission import Permission

        # 只为特定页面创建按钮权限，避免创建过多不必要的权限
        page_button_mapping = {
            'purchase:request:view': [
                ('add', '新增采购需求'),
                ('edit', '编辑采购需求'),
                ('delete', '删除采购需求'),
                ('submit', '提交采购需求'),
                ('export', '导出需求数据'),
                ('import', '批量导入需求'),
                ('print', '打印需求'),
            ],
            'purchase:approval:view': [
                ('approve', '审批通过'),
                ('reject', '审批拒绝'),
                ('batch', '批量审批'),
            ],
            'purchase:procurement:view': [
                ('execute', '执行采购'),
                ('update', '更新采购信息'),
                ('notify', '发货通知'),
                ('return', '退回采购'),
            ],
            'purchase:acceptance:view': [
                ('accept', '验收通过'),
                ('reject', '验收拒绝'),
                ('generate_reimbursement', '生成报销单'),
            ],
            'purchase:reimbursement:view': [
                ('settle', '结算报销'),
                ('export', '导出结算数据'),
            ],
            'system:user:view': [
                ('add', '新增用户'),
                ('edit', '编辑用户'),
                ('delete', '删除用户'),
                ('export', '导出用户'),
                ('import', '批量导入用户'),
            ],
            'system:role:view': [
                ('add', '新增角色'),
                ('edit', '编辑角色'),
                ('delete', '删除角色'),
            ],
            'system:menu:view': [
                ('add', '新增菜单'),
                ('edit', '编辑菜单'),
                ('delete', '删除菜单'),
            ],
            'user:notification:view': [
                ('mark_read', '标记已读'),
                ('delete', '删除通知'),
            ],
            'notification:view': [
                ('add', '新增通知'),
                ('edit', '编辑通知'),
                ('delete', '删除通知'),
                ('send', '发送通知'),
            ],
        }

        # 获取该页面应该创建的按钮权限
        button_actions = page_button_mapping.get(page_permission.code, [])
        if not button_actions:
            print(f"⚠️ 页面 {page_permission.code} 不需要自动创建按钮权限")
            return

        page_prefix = page_permission.code.replace(':view', ':')

        for action, action_name in button_actions:
            button_code = f"{page_prefix}{action}"

            # 检查按钮权限是否已存在
            if Permission.objects.filter(code=button_code).exists():
                continue

            try:
                # 创建按钮权限
                button_permission = Permission.objects.create(
                    name=f"{page_menu.menu_name}-{action_name}",
                    code=button_code,
                    category='button',
                    module=page_permission.module,
                    module_name=page_permission.module_name,
                    description=f"自动创建的{page_menu.menu_name}{action_name}权限",
                    parent=page_permission,
                    sort_order=page_permission.sort_order + 1,
                    is_active=True
                )

                print(f"✅ 自动创建按钮权限: {button_permission.name} ({button_permission.code})")

                # 自动为超级管理员角色分配新权限
                self._assign_permission_to_super_admin(button_permission)

            except Exception as e:
                print(f"❌ 创建按钮权限 {button_code} 失败: {str(e)}")

    def _generate_permission_code(self, menu):
        """自动生成权限代码"""
        # 根据菜单类型和名称生成权限代码
        if menu.menu_type == 'button':
            # 按钮权限：根据按钮名称推断操作类型
            action = self._extract_action_from_menu_name(menu.menu_name)
            if menu.parent:
                # 有父菜单，基于父菜单生成
                parent_module = self._extract_module_from_menu_code(menu.parent.menu_code)
                return f"{parent_module}:{action}"
            else:
                return f"system:{action}"
        elif menu.menu_type == 'page':
            # 页面权限：模块:view
            module = self._extract_module_from_menu_code(menu.menu_code)
            return f"{module}:view"
        else:
            # 目录权限：模块:view（目录通常不需要单独权限，但为了统一性）
            module = self._extract_module_from_menu_code(menu.menu_code)
            return f"{module}:view"

    def _extract_action_from_menu_name(self, menu_name):
        """从菜单名称提取操作类型"""
        action_mapping = {
            '新增': 'add',
            '添加': 'add',
            '创建': 'add',
            '编辑': 'edit',
            '修改': 'edit',
            '更新': 'edit',
            '删除': 'delete',
            '移除': 'delete',
            '查看': 'view',
            '浏览': 'view',
            '导出': 'export',
            '导入': 'import',
            '审批': 'approve',
            '拒绝': 'reject',
            '提交': 'submit',
            '重置': 'reset',
            '启用': 'enable',
            '禁用': 'disable',
            '批量': 'batch'
        }

        for keyword, action in action_mapping.items():
            if keyword in menu_name:
                return action

        return 'action'  # 默认操作

    def _extract_module_from_menu_code(self, menu_code):
        """从菜单代码提取模块名"""
        if not menu_code:
            return 'system'

        # 常见模块映射
        module_mapping = {
            'user': 'system',
            'role': 'system',
            'menu': 'system',
            'dict': 'system',
            'dept': 'system',
            'log': 'system',
            'permission': 'system',
            'purchase': 'purchase',
            'request': 'purchase',
            'approval': 'purchase',
            'procurement': 'purchase',
            'acceptance': 'purchase',
            'reimbursement': 'purchase',
            'personal': 'profile',
            'notification': 'profile',
            'dashboard': 'dashboard'
        }

        for keyword, module in module_mapping.items():
            if keyword in menu_code.lower():
                return module

        return 'system'  # 默认模块

    def _determine_permission_category(self, menu):
        """确定权限类别"""
        if menu.menu_type == 'button':
            return 'button'
        elif menu.menu_type == 'page':
            return 'page'
        elif menu.menu_type == 'directory':
            return 'page'  # 目录也算作页面权限
        else:
            return 'page'  # 默认为页面权限

    def _assign_permission_to_super_admin(self, permission):
        """自动为超级管理员角色分配新权限"""
        try:
            from apps.roles.models import Role
            from apps.system.models_permission import RolePermission

            # 获取超级管理员角色
            super_admin_role = Role.objects.filter(code='super_admin').first()
            if super_admin_role:
                # 检查是否已分配
                if not RolePermission.objects.filter(role=super_admin_role, permission=permission).exists():
                    RolePermission.objects.create(role=super_admin_role, permission=permission)
                    print(f"✅ 自动为超级管理员分配权限: {permission.name}")

        except Exception as e:
            print(f"⚠️ 自动分配权限失败: {str(e)}")

    def _sync_permission(self, menu):
        """同步菜单权限信息"""
        from apps.system.models_permission import Permission

        if not menu.permission_code:
            return

        try:
            permission = Permission.objects.filter(code=menu.permission_code).first()
            if permission:
                # 更新权限信息
                permission.name = menu.menu_name
                permission.category = 'button' if menu.menu_type == 'button' else 'page'
                permission.sort_order = menu.sort_order
                permission.save()
                print(f"✅ 同步权限信息: {permission.name}")
            else:
                # 权限不存在，自动创建
                self._auto_create_permission(menu)
        except Exception as e:
            print(f"❌ 同步权限失败: {str(e)}")

    def _extract_module_from_permission_code(self, permission_code):
        """从权限代码中提取模块名"""
        if ':' in permission_code:
            return permission_code.split(':')[0]
        return 'system'

    def _get_module_display_name(self, module):
        """获取模块显示名称"""
        module_names = {
            'system': '系统管理',
            'purchase': '采购管理',
            'user': '用户管理',
            'dashboard': '仪表盘',
            'profile': '个人中心'
        }
        return module_names.get(module, module)

    def _find_parent_permission(self, permission_code):
        """查找父权限"""
        from apps.system.models_permission import Permission

        if ':' not in permission_code:
            return None

        parts = permission_code.split(':')
        if len(parts) >= 3:
            # 例如：system:user:add -> system:user:view
            parent_code = ':'.join(parts[:-1]) + ':view'
            return Permission.objects.filter(code=parent_code).first()

        return None

    def get_queryset(self):
        """根据查询参数过滤菜单"""
        queryset = Menu.objects.all()

        # 按菜单类型过滤
        menu_type = self.request.query_params.get('menu_type')
        if menu_type:
            queryset = queryset.filter(menu_type=menu_type)

        # 按父菜单过滤
        parent_id = self.request.query_params.get('parent_id')
        if parent_id:
            queryset = queryset.filter(parent_id=parent_id)
        elif parent_id == '0':  # 获取根菜单
            queryset = queryset.filter(parent_id__isnull=True)

        # 按状态过滤
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('sort_order', 'id')

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取菜单树结构"""
        try:
            # 检查是否是管理模式（显示所有菜单，包括禁用的）
            admin_mode = request.query_params.get('admin_mode', 'false').lower() == 'true'

            if admin_mode:
                # 管理模式：获取所有菜单（包括禁用的）
                menus = Menu.objects.all().order_by('sort_order', 'id')
            else:
                # 普通模式：只获取启用的菜单
                menus = Menu.objects.filter(is_active=True).order_by('sort_order', 'id')

            # 构建菜单树，传递admin_mode到序列化器context
            menu_tree = self.build_menu_tree(menus, admin_mode=admin_mode)

            return Response({
                'code': 200,
                'message': '获取菜单树成功',
                'data': menu_tree
            })
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取菜单树失败: {str(e)}',
                'data': []
            })

    @action(detail=False, methods=['get'])
    def user_menus(self, request):
        """获取当前用户的菜单（优化版）"""
        from django.core.cache import cache
        from django.db import connection

        try:
            user = request.user

            if not user.is_authenticated:
                return Response({
                    'code': 401,
                    'message': '用户未登录',
                    'data': []
                })

            # 生成缓存键
            cache_key = f"user_menus_{user.id}_{user.role}_{getattr(user, 'updated_at', '')}"

            # 尝试从缓存获取
            cached_menus = cache.get(cache_key)
            if cached_menus:
                print(f"📦 使用缓存的用户菜单: {user.username}")
                return Response({
                    'code': 200,
                    'message': '获取用户菜单成功（缓存）',
                    'data': cached_menus
                })

            print(f"🔍 用户菜单请求: {user.username}, role={getattr(user, 'role', None)}")

            # 基于权限系统获取用户菜单 - 优化版
            from apps.roles.models import Role
            from apps.system.models_permission import RolePermission

            # 检查用户角色
            if not user.role:
                print("⚠️ 用户无角色，返回默认菜单")
                basic_menus = Menu.objects.filter(
                    menu_code__in=['personal_center', 'dashboard', 'user_management_center', 'notification_management'],
                    is_active=True,
                    is_visible=True
                ).select_related().order_by('sort_order', 'id')
                menu_tree = self.build_menu_tree(basic_menus)

                # 缓存基础菜单（较短时间）
                cache.set(cache_key, menu_tree, 300)  # 5分钟

                return Response({
                    'code': 200,
                    'message': '用户无角色，返回基础菜单',
                    'data': menu_tree
                })

            # 获取用户角色（使用select_related优化）
            try:
                user_role_obj = Role.objects.select_related().get(code=user.role, is_active=True)
                print(f"🔍 用户角色: {user_role_obj.name} ({user_role_obj.code})")
            except Role.DoesNotExist:
                print(f"⚠️ 角色 {user.role} 不存在，返回基础菜单")
                basic_menus = Menu.objects.filter(
                    menu_code__in=['personal_center', 'dashboard'],
                    is_active=True,
                    is_visible=True
                ).select_related().order_by('sort_order', 'id')
                menu_tree = self.build_menu_tree(basic_menus)

                # 缓存基础菜单
                cache.set(cache_key, menu_tree, 300)

                return Response({
                    'code': 200,
                    'message': '用户角色不存在，返回基础菜单',
                    'data': menu_tree
                })

            # 优化：使用原生SQL查询用户权限（更快）
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT DISTINCT p.code
                    FROM sys_permission p
                    INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
                    WHERE rp.role_id = %s AND p.is_active = 1
                """, [user_role_obj.id])
                user_permissions = [row[0] for row in cursor.fetchall()]

            user_permission_codes = set(user_permissions)
            print(f"🔑 用户拥有 {len(user_permission_codes)} 个权限")

            # 优化：使用数据库级别的权限过滤（更高效）
            from django.db.models import Q

            # 构建权限过滤条件
            permission_filter = Q(permission_code__isnull=True) | Q(permission_code='')
            if user_permission_codes:
                permission_filter |= Q(permission_code__in=user_permission_codes)

            # 一次性获取用户可访问的菜单
            accessible_menus = Menu.objects.filter(
                is_active=True,
                is_visible=True
            ).filter(permission_filter).select_related().order_by('sort_order', 'id')

            print(f"🔍 找到 {accessible_menus.count()} 个可访问菜单")

            # 构建菜单树
            menu_tree = self.build_menu_tree(accessible_menus)

            # 缓存菜单树（15分钟）
            cache.set(cache_key, menu_tree, 900)
            print(f"💾 菜单数据已缓存: {cache_key}")

            return Response({
                'code': 200,
                'message': '获取用户菜单成功',
                'data': menu_tree
            })
        except Exception as e:
            print(f"❌ 获取用户菜单失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response({
                'code': 500,
                'message': f'获取用户菜单失败: {str(e)}',
                'data': []
            })

    @action(detail=False, methods=['post'])
    def refresh_user_menus(self, request):
        """强制刷新用户菜单缓存"""
        try:
            user = request.user
            if not user.is_authenticated:
                return Response({
                    'code': 401,
                    'message': '用户未登录',
                    'data': None
                })

            # 这里可以添加清除服务端缓存的逻辑（如果有的话）
            print(f"🔄 强制刷新用户 {user.username} 的菜单缓存")

            return Response({
                'code': 200,
                'message': '菜单缓存刷新成功，请重新获取菜单',
                'data': None
            })
        except Exception as e:
            print(f"❌ 刷新菜单缓存失败: {str(e)}")
            return Response({
                'code': 500,
                'message': f'刷新菜单缓存失败: {str(e)}',
                'data': None
            })

    @action(detail=False, methods=['get'])
    def user_permissions(self, request):
        """获取当前用户的权限列表"""
        try:
            user = request.user
            if not user.is_authenticated:
                return Response({
                    'code': 401,
                    'message': '用户未登录',
                    'data': []
                })

            # 获取用户权限
            permissions = self.get_user_permissions(user)

            return Response({
                'code': 200,
                'message': '获取用户权限成功',
                'data': permissions
            })
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取用户权限失败: {str(e)}',
                'data': []
            })

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取菜单统计数据"""
        try:
            # 总菜单数
            total_count = Menu.objects.count()

            # 启用菜单数
            active_count = Menu.objects.filter(is_active=True).count()

            # 禁用菜单数
            inactive_count = Menu.objects.filter(is_active=False).count()

            # 主页面数（菜单类型为page的数量）
            page_count = Menu.objects.filter(menu_type='page').count()

            # 按钮总数（菜单类型为button的数量）
            button_count = Menu.objects.filter(menu_type='button').count()

            # 一级菜单数
            top_level_count = Menu.objects.filter(parent_id__isnull=True).count()

            # 子菜单数
            sub_menu_count = Menu.objects.filter(parent_id__isnull=False).count()

            return Response({
                'code': 200,
                'message': '获取菜单统计数据成功',
                'data': {
                    'total_count': total_count,
                    'active_count': active_count,
                    'inactive_count': inactive_count,
                    'page_count': page_count,
                    'button_count': button_count,
                    'top_level_count': top_level_count,
                    'sub_menu_count': sub_menu_count
                }
            })
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取菜单统计数据失败: {str(e)}',
                'data': {}
            })

    def build_menu_tree(self, menus, admin_mode=False):
        """构建菜单树结构"""
        menu_dict = {}
        tree = []

        # 将菜单转换为字典，便于查找
        for menu in menus:
            menu_data = {
                'id': menu.id,
                'parent_id': menu.parent_id,  # 添加父级菜单ID
                'menu_name': menu.menu_name,
                'menu_code': menu.menu_code,
                'menu_type': menu.menu_type,
                'route_path': menu.route_path,
                'component_path': menu.component_path,
                'icon': menu.icon,
                'sort_order': menu.sort_order,
                'is_active': menu.is_active,  # 添加启用状态
                'is_visible': menu.is_visible,  # 添加可见状态
                'permission_code': menu.permission_code,
                'business_status': menu.business_status,
                'children': []
            }
            menu_dict[menu.id] = menu_data

        # 构建树结构
        for menu in menus:
            menu_data = menu_dict[menu.id]
            if menu.parent_id is None:
                tree.append(menu_data)
            else:
                parent = menu_dict.get(menu.parent_id)
                if parent:
                    parent['children'].append(menu_data)

        # 如果不是管理模式，需要过滤掉禁用的子菜单
        if not admin_mode:
            self._filter_inactive_children(tree)

        return tree

    def _filter_inactive_children(self, menu_list):
        """递归过滤禁用的子菜单（非管理模式使用）"""
        for menu in menu_list:
            if 'children' in menu and menu['children']:
                # 过滤掉禁用的子菜单
                menu['children'] = [child for child in menu['children'] if child.get('is_active', True)]
                # 递归处理子菜单
                self._filter_inactive_children(menu['children'])

    def get_user_permissions(self, user):
        """获取用户权限列表 - 使用统一权限系统"""
        from apps.system.permissions import get_user_permissions
        return get_user_permissions(user)
