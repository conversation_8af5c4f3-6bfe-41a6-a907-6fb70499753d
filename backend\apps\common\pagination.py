from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class CustomPageNumberPagination(PageNumberPagination):
    """
    自定义分页器
    支持前端传递 page_size 参数来动态设置每页显示数量
    """
    page_size = 10  # 默认每页显示数量
    page_size_query_param = 'page_size'  # 前端传递的参数名
    max_page_size = 1000  # 最大每页显示数量
    page_query_param = 'page'  # 页码参数名

    def get_paginated_response(self, data):
        """
        返回分页响应
        """
        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'results': data,
                'count': self.page.paginator.count,
                'next': self.get_next_link(),
                'previous': self.get_previous_link(),
                'page_size': self.page_size,
                'current_page': self.page.number,
                'total_pages': self.page.paginator.num_pages
            }
        })

    def get_page_size(self, request):
        """
        获取每页显示数量
        """
        if self.page_size_query_param:
            try:
                page_size = int(request.query_params[self.page_size_query_param])
                if page_size > 0:
                    return min(page_size, self.max_page_size)
            except (KeyError, ValueError):
                pass
        return self.page_size
