<template>
  <a-modal :open="visible" title="导出Excel配置" width="80%" :footer="null" @cancel="handleCancel" :style="{ top: '10vh' }"
    :body-style="{ height: '80vh', overflow: 'auto' }">
    <div class="export-config-container">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <h4>筛选条件</h4>
        <div class="filter-form-container">
          <a-form :model="filters" :label-col="{ style: { width: '70px', textAlign: 'right' } }">
            <a-row :gutter="[12, 6]">
              <a-col :span="4">
                <a-form-item label="需求单位">
                  <a-select v-model:value="filters.department" placeholder="选择需求单位" allowClear show-search
                    :filter-option="filterOption" @change="debouncedSearch" size="small">
                    <a-select-option v-for="dept in departmentOptions" :key="dept.value" :value="dept.value"
                      :label="dept.label">
                      {{ dept.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="验收人">
                  <a-select v-model:value="filters.acceptor" placeholder="选择验收人" allowClear show-search
                    :filter-option="filterOption" @change="debouncedSearch" size="small">
                    <a-select-option v-for="acceptor in acceptorOptions" :key="acceptor.value" :value="acceptor.value"
                      :label="acceptor.label">
                      {{ acceptor.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="采购类型">
                  <a-select v-model:value="filters.purchase_type" placeholder="选择采购类型" allowClear
                    @change="debouncedSearch" size="small">
                    <a-select-option v-for="type in purchaseTypeOptions" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="验收日期">
                  <a-range-picker v-model:value="filters.dateRange" style="width: 100%" @change="debouncedSearch"
                    size="small" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="采购方式">
                  <a-input v-model:value="filters.procurementMethod" placeholder="采购方式" allowClear
                    @input="debouncedSearch" size="small" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="需求来源">
                  <a-input v-model:value="filters.requirementSource" placeholder="需求来源" allowClear
                    @input="debouncedSearch" size="small" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="[12, 6]">
              <a-col :span="4">
                <a-form-item label="经费项目">
                  <a-input v-model:value="filters.fundProject" placeholder="经费项目" allowClear @input="debouncedSearch"
                    size="small" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="计量单位">
                  <a-input v-model:value="filters.unit" placeholder="计量单位" allowClear @input="debouncedSearch"
                    size="small" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="物品种类">
                  <a-input v-model:value="filters.itemCategory" placeholder="物品种类" allowClear @input="debouncedSearch"
                    size="small" />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="需求备注">
                  <a-input v-model:value="filters.remarks" placeholder="需求备注" allowClear @input="debouncedSearch"
                    size="small" />
                </a-form-item>
              </a-col>
              <a-col :span="8" style="text-align: right;">
                <a-space>
                  <a-button @click="resetFilters" size="small">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button @click="searchRecords" type="primary" size="small">
                    <SearchOutlined />
                    查询
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>

      <!-- 导出字段配置 -->
      <div class="fields-section" style=" border-radius: 6px; margin: 16px 0;">
        <a-collapse v-model:activeKey="fieldsCollapseKey" size="small">
          <a-collapse-panel key="1" header="导出字段配置">
            <template #extra>
              <a-space>
                <a-button @click.stop="selectAllFields" size="small">全选</a-button>
                <a-button @click.stop="resetFields" size="small">重置</a-button>
                <a-button @click.stop="selectRequiredFields" size="small">仅必选</a-button>
              </a-space>
            </template>
            <a-checkbox-group v-model:value="selectedFields" @change="onFieldsChange">
              <div class="export-fields-container">
                <div v-for="category in fieldCategories" :key="category.key" class="export-field-category">
                  <div class="export-category-header">
                    <h5 class="export-category-title">{{ category.title }}</h5>
                  </div>
                  <div class="export-category-fields">
                    <div v-for="field in category.fields" :key="field.key" class="export-field-option">
                      <a-checkbox :value="field.key" :disabled="field.required">
                        <span class="field-title">{{ field.title }}</span>
                        <a-tag v-if="field.required" size="small" color="blue">必选</a-tag>
                      </a-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </a-checkbox-group>
          </a-collapse-panel>
        </a-collapse>
      </div>

      <!-- 选择导出记录 -->
      <div class="records-section">
        <div class="section-header">
          <h4>选择导出记录</h4>
          <div class="section-actions">
            <a-space>
              <span class="record-count">共 {{ records.length }} 条记录，已选择 {{ selectedRecords.length }} 条</span>
              <a-select v-model:value="recordPageSize" size="small" style="width: 100px"
                @change="onRecordPageSizeChange">
                <a-select-option :value="10">10条/页</a-select-option>
                <a-select-option :value="20">20条/页</a-select-option>
                <a-select-option :value="50">50条/页</a-select-option>
                <a-select-option :value="100">100条/页</a-select-option>
              </a-select>
            </a-space>
          </div>
        </div>

        <a-table :columns="recordColumns" :data-source="records" :row-selection="recordRowSelection"
          :loading="searchLoading" row-key="id" :pagination="{
            pageSize: recordPageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }" size="small" :scroll="{ x: 'max-content' }">
          <template #emptyText>
            <a-empty description="请设置筛选条件并点击查询按钮来加载记录" />
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status_display || record.status || '-' }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'purchase_type'">
              {{ record.purchase_type_display || record.purchase_type || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'procurement_method'">
              {{ record.procurement_method_display || record.procurement_method || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'unit'">
              {{ record.unit_display || record.unit || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'item_category'">
              {{ record.item_category_display || record.item_category || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'requirement_source'">
              {{ record.requirement_source || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'fund_project'">
              {{ record.fund_project_display || record.fund_project || '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'amount'">
              ¥{{ record.amount ? record.amount.toFixed(2) : '0.00' }}
            </template>
            <template
              v-else-if="['created_at', 'acceptance_date', 'purchase_date', 'submission_date', 'approved_at', 'reimbursement_date'].includes(column.dataIndex)">
              {{ record[column.dataIndex] ? formatDateToYMD(record[column.dataIndex]) : '-' }}
            </template>
          </template>
        </a-table>
      </div>

      <!-- 导出预览和操作 -->
      <div class="actions-section">
        <a-space>
          <a-button @click="previewExport" :disabled="selectedRecords.length === 0">
            <EyeOutlined />
            预览 ({{ selectedRecords.length }}条)
          </a-button>
          <a-button type="primary" @click="executeExport" :disabled="selectedRecords.length === 0" :loading="exporting">
            <DownloadOutlined />
            导出Excel ({{ selectedRecords.length }}条)
          </a-button>
          <a-button @click="handleCancel">取消</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>

  <!-- 导出预览模态框 -->
  <a-modal :open="previewVisible" title="导出预览" width="80%" :footer="null" class="export-preview-modal"
    :body-style="{ height: '80vh', overflow: 'auto' }" @cancel="previewVisible = false">
    <div class="export-preview-content">
      <div class="preview-header">
        <h4>预览导出数据（前10条）</h4>
        <a-alert :message="`预计导出 ${selectedRecords.length} 条记录，包含 ${selectedFields.length} 个字段`" type="info"
          show-icon />
      </div>
      <div>
        <a-table :columns="previewColumns" :data-source="previewData" :pagination="true" size="middle"
          :scroll="{ x: 'max-content' }" />
      </div>
    </div>

    <template #footer>
      <a-space>
        <a-button @click="previewVisible = false">关闭预览</a-button>
        <a-button type="primary" @click="executeExport" :loading="exporting">
          <DownloadOutlined />
          确认导出
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined, EyeOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import api from '@/api'
import { getPageFieldCategories, getPageColumnOptions } from '@/utils/validation'
import { useDictMixin } from '@/mixins/dictMixin'

export default {
  name: 'AcceptanceExportConfig',
  components: {
    SearchOutlined,
    ReloadOutlined,
    EyeOutlined,
    DownloadOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['cancel', 'export-complete'],
  setup(props, { emit }) {
    const records = ref([])
    const selectedRecords = ref([])
    const selectedRecordData = ref([])
    const departmentOptions = ref([])
    const acceptorOptions = ref([])
    const purchaseTypeOptions = ref([])

    // 使用字典混入 - 只保留必要的方法
    const {
      getStatusColor,
      formatDateToYMD
    } = useDictMixin()
    const exporting = ref(false)
    const previewVisible = ref(false)
    const previewData = ref([])
    const searchLoading = ref(false)

    // 字段配置折叠状态
    const fieldsCollapseKey = ref([])

    // 防抖搜索函数
    let searchTimeout = null
    const debouncedSearch = () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      searchTimeout = setTimeout(() => {
        searchRecords()
      }, 500) // 500ms防抖延迟
    }

    // 记录分页大小
    const recordPageSize = ref(20)

    const filters = ref({
      department: undefined,
      acceptor: undefined,
      purchase_type: undefined,
      dateRange: undefined,
      // 新增筛选字段
      procurementMethod: undefined,
      requirementSource: undefined,
      fundProject: undefined,
      unit: undefined,
      itemCategory: undefined,
      remarks: undefined
    })

    // 基于业务流程的字段配置（验收阶段）
    const columnOptions = getPageColumnOptions('acceptance')
    const rawFieldCategories = getPageFieldCategories('acceptance')

    // 构建字段分类数据结构
    const fieldCategories = computed(() => {
      return rawFieldCategories.map(category => ({
        key: category.key,
        title: category.title,
        fields: columnOptions
          .filter(opt => opt.category === category.key && opt.key !== 'action')
          .map(opt => ({
            key: opt.key,
            title: opt.title,
            required: opt.required || false
          }))
      })).filter(category => category.fields.length > 0)
    })

    // 初始字段配置（用于重置）
    const initialFields = [
      'id', 'item_name', 'specification', 'unit', 'acceptance_quantity', 'acceptor_name',
      'purchase_type', 'status', 'hierarchy_path', 'courier_company', 'tracking_number',
      'created_at', 'acceptance_date'
    ]

    const selectedFields = ref([...initialFields])

    // 计算属性 - 根据选中字段动态生成表格列
    const recordColumns = computed(() => {
      // 基础列（始终显示）
      const baseColumns = [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          fixed: 'left',
          ellipsis: true,
          align: 'center'
        }
      ]

      // 根据选中字段生成动态列
      const dynamicColumns = selectedFields.value.map(fieldKey => {
        const field = fieldCategories.value
          .flatMap(cat => cat.fields)
          .find(f => f.key === fieldKey)

        if (fieldKey === 'id') return null // ID列已在基础列中

        return {
          title: field ? field.title : fieldKey,
          dataIndex: fieldKey,
          width: getColumnWidth(fieldKey),
          ellipsis: true,
          align: 'center'
        }
      }).filter(Boolean)

      return [...baseColumns, ...dynamicColumns]
    })

    // 获取列宽度
    const getColumnWidth = (fieldKey) => {
      const widthMap = {
        'item_name': 150,
        'specification': 120,
        'quantity': 80,
        'amount': 100,
        'status': 100,
        'department_name': 120,
        'acceptor': 100,
        'acceptance_date': 120,
        'purchase_type': 100,
        'requester': 100,
        'created_at': 120
      }
      return widthMap[fieldKey] || 100
    }

    const recordRowSelection = computed(() => ({
      selectedRowKeys: selectedRecords.value,
      onChange: (selectedRowKeys, selectedRows) => {
        console.log('Record selection changed:')
        console.log('- Selected keys:', selectedRowKeys)
        console.log('- Selected rows count:', selectedRows.length)
        console.log('- First few selected rows:', selectedRows.slice(0, 3))

        selectedRecords.value = [...selectedRowKeys] // 创建新数组避免引用问题
        selectedRecordData.value = [...selectedRows] // 创建新数组避免引用问题

        console.log('Updated selectedRecords:', selectedRecords.value)
        console.log('Updated selectedRecordData count:', selectedRecordData.value.length)
      },
      getCheckboxProps: (record) => ({
        disabled: false,
        name: `record-${record.id}`
      }),
      preserveSelectedRowKeys: false, // 改为false，避免跨页选择问题
      type: 'checkbox'
    }))

    const previewColumns = computed(() => {
      return selectedFields.value.map(fieldKey => {
        const field = fieldCategories.value
          .flatMap(cat => cat.fields)
          .find(f => f.key === fieldKey)
        return {
          title: field ? field.title : fieldKey,
          dataIndex: fieldKey,
          width: 120,
          ellipsis: true,
          align: 'center'
        }
      })
    })

    // 初始化部门选项
    const initDepartmentOptions = async () => {
      try {
        const response = await api.departments.getList()
        // 检查响应数据格式
        const deptData = response.data?.results || response.data || []
        if (Array.isArray(deptData)) {
          departmentOptions.value = deptData.map(dept => ({
            value: dept.id,
            label: dept.dept_name
          }))
        } else {
          console.warn('部门数据格式不正确:', response.data)
          departmentOptions.value = []
        }

        // 初始化验收人选项
        const userResponse = await api.users.getList()
        const userData = userResponse.data?.results || userResponse.data || []
        if (Array.isArray(userData)) {
          acceptorOptions.value = userData.map(user => ({
            value: user.id,
            label: user.username
          }))
        } else {
          console.warn('用户数据格式不正确:', userResponse.data)
          acceptorOptions.value = []
        }

        // 初始化采购类型选项
        const purchaseTypeResponse = await api.dicts.getDict('purchase_type')
        if (purchaseTypeResponse.code === 200) {
          purchaseTypeOptions.value = (purchaseTypeResponse.data.items || []).map(item => ({
            value: item.code,
            label: item.name
          }))
        } else {
          // 使用默认选项
          purchaseTypeOptions.value = [
            { value: 'unified', label: '统一采购' },
            { value: 'self', label: '自行采购' }
          ]
        }
      } catch (error) {
        console.error('初始化选项失败:', error)
        departmentOptions.value = []
        acceptorOptions.value = []
      }
    }

    // 搜索记录
    const searchRecords = async () => {
      if (searchLoading.value) {
        return // 防止重复请求
      }

      try {
        searchLoading.value = true
        const params = {
          page_size: 50, // 大幅减少到50条记录，提高性能
          // 不设置status参数，让acceptances.getList自己处理status_in
        }

        if (filters.value.department) {
          params.dept_id = filters.value.department
        }
        if (filters.value.acceptor) {
          params.acceptor_id = filters.value.acceptor
        }
        if (filters.value.purchase_type) {
          params.purchase_type = filters.value.purchase_type
        }
        if (filters.value.dateRange && filters.value.dateRange.length === 2) {
          params.acceptance_date__gte = filters.value.dateRange[0].format('YYYY-MM-DD')
          params.acceptance_date__lte = filters.value.dateRange[1].format('YYYY-MM-DD')
        }
        // 新增筛选参数
        if (filters.value.procurementMethod) {
          params.procurement_method__icontains = filters.value.procurementMethod
        }
        if (filters.value.requirementSource) {
          params.requirement_source__icontains = filters.value.requirementSource
        }
        if (filters.value.fundProject) {
          params.fund_project__icontains = filters.value.fundProject
        }
        if (filters.value.unit) {
          params.unit__icontains = filters.value.unit
        }
        if (filters.value.itemCategory) {
          params.item_category__icontains = filters.value.itemCategory
        }
        if (filters.value.remarks) {
          params.remarks__icontains = filters.value.remarks
        }

        const response = await api.acceptances.getList(params)

        if (response.code === 200) {
          const rawRecords = response.data.results || response.data || []
          // 使用后端提供的display字段进行显示
          records.value = rawRecords.map(record => ({
            ...record,
            // 使用后端提供的display字段进行显示
            status: record.status_display || record.status,
            item_category: record.item_category_display || record.item_category,
            unit: record.unit_display || record.unit,
            procurement_method: record.procurement_method_display || record.procurement_method,
            fund_project: record.fund_project_display || record.fund_project,
            purchase_type: record.purchase_type_display || record.purchase_type,
            // requirement_source是用户输入字段，不需要字典转换
            requirement_source: record.requirement_source,
            // 保存原始编码用于逻辑判断
            _originalStatus: record.status,
            _originalItemCategory: record.item_category,
            _originalUnit: record.unit,
            _originalProcurementMethod: record.procurement_method,
            _originalFundProject: record.fund_project,
            _originalPurchaseType: record.purchase_type
          }))
        } else {
          throw new Error(`API返回错误: ${response.message || '未知错误'}`)
        }

        // 清空之前的选择
        selectedRecords.value = []
        selectedRecordData.value = []

        message.success(`查询到 ${records.value.length} 条验收记录`)
      } catch (error) {
        console.error('查询记录失败:', error)
        message.error('查询记录失败')
        records.value = []
        selectedRecords.value = []
        selectedRecordData.value = []
      } finally {
        searchLoading.value = false
      }
    }

    // 重置筛选条件
    const resetFilters = () => {
      filters.value.department = undefined
      filters.value.acceptor = undefined
      filters.value.purchase_type = undefined
      filters.value.dateRange = undefined
      // 重置新增的筛选字段
      filters.value.procurementMethod = undefined
      filters.value.requirementSource = undefined
      filters.value.fundProject = undefined
      filters.value.unit = undefined
      filters.value.itemCategory = undefined
      filters.value.remarks = undefined
      records.value = []
      selectedRecords.value = []
      selectedRecordData.value = []
    }

    // 筛选选项函数
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 字段选择相关函数
    const selectAllFields = () => {
      selectedFields.value = fieldCategories.value.flatMap(cat => cat.fields.map(f => f.key))
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    }

    const resetFields = () => {
      // 恢复到初始字段配置
      selectedFields.value = [...initialFields]
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    }

    const selectRequiredFields = () => {
      selectedFields.value = fieldCategories.value
        .flatMap(cat => cat.fields)
        .filter(f => f.required)
        .map(f => f.key)
      // 展开字段配置面板
      if (!fieldsCollapseKey.value.includes("1")) {
        fieldsCollapseKey.value = ["1"];
      }
    }

    const onFieldsChange = (checkedList) => {
      console.log('Fields changed:', checkedList)
      selectedFields.value = checkedList

      // 如果有预览数据，重新生成预览数据以反映字段变化
      if (previewVisible.value && selectedRecordData.value.length > 0) {
        const recordsToPreview = selectedRecordData.value.slice(0, Math.min(10, selectedRecordData.value.length))
        previewData.value = recordsToPreview.map(record => {
          const previewRecord = {}
          selectedFields.value.forEach(fieldKey => {
            // 处理特殊字段的显示
            if (fieldKey === 'purchase_type') {
              previewRecord[fieldKey] = record.purchase_type_display || record[fieldKey] || '-'
            } else if (fieldKey === 'status') {
              previewRecord[fieldKey] = record.status_display || record[fieldKey] || '-'
            } else if (fieldKey === 'procurement_method') {
              previewRecord[fieldKey] = record.procurement_method_display || record[fieldKey] || '-'
            } else if (fieldKey === 'unit') {
              previewRecord[fieldKey] = record.unit_display || record[fieldKey] || '-'
            } else if (fieldKey === 'requirement_source') {
              previewRecord[fieldKey] = record.requirement_source || '-'
            } else if (fieldKey === 'item_category') {
              previewRecord[fieldKey] = record.item_category_display || record[fieldKey] || '-'
            } else if (fieldKey === 'fund_project') {
              previewRecord[fieldKey] = record.fund_project_display || record[fieldKey] || '-'
            } else if (fieldKey === 'urgency_level') {
              previewRecord[fieldKey] = record.urgency_level_display || record[fieldKey] || '-'
            } else if (['created_at', 'acceptance_date', 'purchase_date', 'submission_date', 'approved_at', 'reimbursement_date'].includes(fieldKey)) {
              previewRecord[fieldKey] = record[fieldKey] ? formatDateToYMD(record[fieldKey]) : '-'
            } else {
              previewRecord[fieldKey] = record[fieldKey] || '-'
            }
          })
          return previewRecord
        })
        console.log('Updated preview data after field change:', previewData.value.length, 'records')
      }
    }

    // 记录分页大小变化
    const onRecordPageSizeChange = (value) => {
      recordPageSize.value = value
      // 保持选中状态不变
      console.log('Page size changed to:', value, 'Selected records:', selectedRecords.value.length)
    }







    // 使用dictMixin中的formatDateToYMD方法

    // 导出预览
    const previewExport = () => {
      if (selectedRecords.value.length === 0) {
        message.warning('请先选择要导出的记录')
        return
      }

      console.log('Preview export - selectedRecordData:', selectedRecordData.value.length, 'records')
      console.log('Preview export - selectedRecords:', selectedRecords.value.length, 'keys')

      // 确保selectedRecordData与selectedRecords同步
      if (selectedRecordData.value.length !== selectedRecords.value.length) {
        console.warn('Record data and keys count mismatch, refreshing data...')
        // 从records中重新获取选中的数据
        const selectedData = records.value.filter(record =>
          selectedRecords.value.includes(record.id)
        )
        selectedRecordData.value = selectedData
        console.log('Refreshed selectedRecordData:', selectedRecordData.value.length, 'records')
      }

      // 生成预览数据（显示所有选中的记录，最多10条用于预览）
      const recordsToPreview = selectedRecordData.value.slice(0, Math.min(10, selectedRecordData.value.length))
      previewData.value = recordsToPreview.map(record => {
        const previewRecord = {}
        selectedFields.value.forEach(fieldKey => {
          // 处理特殊字段的显示
          if (fieldKey === 'purchase_type') {
            previewRecord[fieldKey] = record.purchase_type_display || record[fieldKey] || '-'
          } else if (fieldKey === 'status') {
            previewRecord[fieldKey] = record.status_display || record[fieldKey] || '-'
          } else if (fieldKey === 'procurement_method') {
            previewRecord[fieldKey] = record.procurement_method_display || record[fieldKey] || '-'
          } else if (fieldKey === 'unit') {
            previewRecord[fieldKey] = record.unit_display || record[fieldKey] || '-'
          } else if (fieldKey === 'requirement_source') {
            previewRecord[fieldKey] = record.requirement_source || '-'
          } else if (fieldKey === 'item_category') {
            previewRecord[fieldKey] = record.item_category_display || record[fieldKey] || '-'
          } else if (fieldKey === 'fund_project') {
            previewRecord[fieldKey] = record.fund_project_display || record[fieldKey] || '-'
          } else if (fieldKey === 'urgency_level') {
            previewRecord[fieldKey] = record.urgency_level_display || record[fieldKey] || '-'
          } else if (['created_at', 'acceptance_date', 'purchase_date', 'submission_date', 'approved_at', 'reimbursement_date'].includes(fieldKey)) {
            previewRecord[fieldKey] = record[fieldKey] ? formatDateToYMD(record[fieldKey]) : '-'
          } else {
            previewRecord[fieldKey] = record[fieldKey] || '-'
          }
        })
        return previewRecord
      })

      console.log('Generated preview data:', previewData.value.length, 'records')
      previewVisible.value = true
    }

    // 执行导出
    const executeExport = async () => {
      if (selectedRecords.value.length === 0) {
        message.warning('请先选择要导出的记录')
        return
      }

      console.log('Executing export with records:', selectedRecords.value)
      console.log('Selected record data:', selectedRecordData.value)

      exporting.value = true
      try {
        // 构建导出参数，使用正确的字段名
        const params = {
          // 添加验收状态过滤
          status_in: 'pending_acceptance,accepted',
          // 添加选中的记录ID
          ids: selectedRecords.value.join(','),
          // 添加选中的字段
          fields: selectedFields.value.join(',')
        }

        // 添加其他筛选条件
        if (filters.value.department) {
          params.department = filters.value.department
        }
        if (filters.value.acceptor) {
          params.acceptor = filters.value.acceptor
        }
        if (filters.value.purchase_type) {
          params.purchase_type = filters.value.purchase_type
        }
        if (filters.value.dateRange && filters.value.dateRange.length === 2) {
          // 确保日期对象有format方法
          const startDate = filters.value.dateRange[0]
          const endDate = filters.value.dateRange[1]
          if (startDate && typeof startDate.format === 'function') {
            params.start_date = startDate.format('YYYY-MM-DD')
          }
          if (endDate && typeof endDate.format === 'function') {
            params.end_date = endDate.format('YYYY-MM-DD')
          }
        }

        const response = await api.acceptances.exportToExcel(params)

        // 创建下载链接 - 确保response.data是blob数据
        let blob
        if (response.data instanceof Blob) {
          blob = response.data
        } else if (response instanceof Blob) {
          blob = response
        } else {
          // 如果不是Blob，尝试创建Blob
          blob = new Blob([response.data || response], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })
        }
        // 创建Excel文件并下载
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `物资验收清单_${formatDateToYMD(new Date())}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        message.success(`导出成功，共导出 ${selectedRecords.value.length} 条记录`)
        emit('export-complete', selectedRecordData.value)
        emit('cancel')
      } catch (error) {
        // 导出失败：显示错误信息给用户
        message.error(`导出失败: ${error.message}`)
      } finally {
        exporting.value = false
      }
    }

    // 处理取消操作
    const handleCancel = () => {
      // 重置组件状态并触发取消事件
      resetComponent()
      emit('cancel')
    }

    // 重置组件状态
    const resetComponent = () => {
      // 清理定时器
      if (searchTimeout) {
        clearTimeout(searchTimeout)
        searchTimeout = null
      }

      records.value = []
      selectedRecords.value = []
      selectedRecordData.value = []
      previewVisible.value = false
      previewData.value = []
      exporting.value = false
      searchLoading.value = false
      console.log('Component state reset')
    }

    // 监听visible变化，初始化数据
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        initDepartmentOptions()
        // 自动请求数据，优化用户体验
        searchRecords()
      } else {
        // 模态框关闭时重置状态
        resetComponent()
      }
    })

    // 组件挂载时初始化
    onMounted(() => {
      if (props.visible) {
        initDepartmentOptions()
      }
    })

    // 组件卸载时清理定时器
    onUnmounted(() => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
        searchTimeout = null
      }
    })

    return {
      records,
      selectedRecords,
      selectedRecordData,
      departmentOptions,
      acceptorOptions,
      purchaseTypeOptions,
      exporting,
      previewVisible,
      previewData,
      searchLoading,
      filters,
      fieldsCollapseKey,
      recordPageSize,
      fieldCategories,
      selectedFields,
      recordColumns,
      recordRowSelection,
      previewColumns,
      initDepartmentOptions,
      searchRecords,
      debouncedSearch,
      resetFilters,
      filterOption,
      selectAllFields,
      resetFields,
      selectRequiredFields,
      onFieldsChange,
      onRecordPageSizeChange,
      getStatusColor,
      formatDateToYMD,
      previewExport,
      executeExport,
      handleCancel
    }
  }
}
</script>

<style scoped>
.export-config-container {
  padding: 0;
}

.filter-section {
  margin-bottom: 16px;
  padding: 16px 16px 0 16px;
  background: #fafafa;
  border-radius: 6px;
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.fields-section {
  margin-bottom: 16px;
}

/* 导出字段配置样式 */
.export-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
}

.export-field-category {
  flex: 1;
  min-width: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  overflow: hidden;
}

.export-category-header {
  background-color: #e6f7ff;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 12px;
}

.export-category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.export-category-fields {
  padding: 12px;
}

.export-field-option {
  margin-bottom: 8px;
}

.export-field-option:last-child {
  margin-bottom: 0;
}

.field-title {
  margin-right: 8px;
}

.filter-form-container {
  padding: 0;
}

.records-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.record-count {
  font-size: 12px;
  color: #666;
}

.actions-section {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.export-preview-content {
  height: 100%;
}

.preview-header {
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.empty-records-tip {
  padding: 40px 0;
  text-align: center;
  background: #fafafa;
  border-radius: 6px;
  margin-bottom: 16px;
}
</style>
