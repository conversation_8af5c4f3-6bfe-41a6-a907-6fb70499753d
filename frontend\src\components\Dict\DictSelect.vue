<template>
  <a-select
    v-bind="$attrs"
    :value="modelValue"
    :loading="loading"
    :options="options"
    :placeholder="placeholder || `请选择${dictTypeLabel}`"
    @update:value="handleChange"
    @focus="handleFocus"
  >
    <template v-if="$slots.default" #default>
      <slot />
    </template>
  </a-select>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import dictService from '@/services/dictService'

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: undefined
  },
  dictType: {
    type: String,
    required: true
  },
  placeholder: {
    type: String,
    default: ''
  },
  autoLoad: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'loaded'])

// 状态
const loading = ref(false)
const dictItems = ref([])

// 计算属性
const options = computed(() => {
  return dictItems.value.map(item => ({
    label: item.name,
    value: item.code,
    ...item
  }))
})

const dictTypeLabel = computed(() => {
  return props.dictType
})

// 方法
const loadDictData = async () => {
  if (loading.value) return
  
  try {
    loading.value = true
    const items = await dictService.getDict(props.dictType)
    dictItems.value = items
    emit('loaded', items)
  } catch (error) {
    console.error(`加载字典数据失败: ${props.dictType}`, error)
    dictItems.value = []
  } finally {
    loading.value = false
  }
}

const handleChange = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}

const handleFocus = () => {
  // 聚焦时如果没有数据则加载
  if (dictItems.value.length === 0) {
    loadDictData()
  }
}

// 监听字典类型变化
watch(() => props.dictType, () => {
  if (props.autoLoad) {
    loadDictData()
  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(() => {
  if (props.autoLoad) {
    loadDictData()
  }
})

// 暴露方法
defineExpose({
  loadDictData,
  refresh: loadDictData
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
