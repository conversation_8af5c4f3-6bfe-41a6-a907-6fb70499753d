# Generated by Django 5.2.4 on 2025-07-17 13:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0033_add_editable_field"),
    ]

    operations = [
        migrations.AddField(
            model_name="dictionary",
            name="type_code",
            field=models.CharField(
                blank=True,
                choices=[
                    ("status", "状态"),
                    ("purchase_type", "采购类型"),
                    ("procurement_method", "采购方式"),
                    ("fund_project", "经费项目"),
                    ("unit", "计量单位"),
                    ("item_category", "物品种类"),
                    ("requirement_source", "需求来源"),
                    ("urgency_level", "紧急程度"),
                    ("其他", "其他"),
                ],
                help_text="英文类型编码，用于代码匹配",
                max_length=50,
                null=True,
                verbose_name="字典类型编码",
            ),
        ),
        migrations.AddField(
            model_name="dictionary",
            name="type_name",
            field=models.CharField(
                blank=True,
                help_text="中文类型名称，用于界面显示",
                max_length=50,
                null=True,
                verbose_name="字典类型名称",
            ),
        ),
    ]
