/**
 * WebSocket 连接管理器
 * 用于实时通知功能
 */

class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.heartbeatInterval = 30000
    this.heartbeatTimer = null
    this.listeners = new Map()
    this.isConnecting = false
  }

  /**
   * 连接WebSocket
   */
  connect() {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return
    }

    this.isConnecting = true

    try {
      // 获取WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const token = localStorage.getItem('token')

      if (!token) {
        this.isConnecting = false
        return
      }

      // 根据环境确定WebSocket服务器地址
      let wsHost
      if (process.env.NODE_ENV === 'development') {
        // 开发环境：后端运行在8000端口
        wsHost = 'localhost:8000'
      } else {
        // 生产环境：使用当前host
        wsHost = window.location.host
      }

      // 确保token正确编码
      const encodedToken = encodeURIComponent(token)
      const wsUrl = `${protocol}//${wsHost}/ws/notifications/?token=${encodedToken}`
      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = this.onOpen.bind(this)
      this.ws.onmessage = this.onMessage.bind(this)
      this.ws.onclose = this.onClose.bind(this)
      this.ws.onerror = this.onError.bind(this)

    } catch (error) {
      this.isConnecting = false
      this.scheduleReconnect()
    }
  }

  /**
   * 连接成功处理
   */
  onOpen() {
    this.isConnecting = false
    this.reconnectAttempts = 0
    this.startHeartbeat()

    // 触发连接成功事件
    this.emit('connected')
  }

  /**
   * 接收消息处理
   */
  onMessage(event) {
    try {
      const data = JSON.parse(event.data)

      // 处理心跳响应
      if (data.type === 'pong') {
        return
      }

      // 处理通知消息
      if (data.type === 'notification') {
        this.emit('notification', data.data)
      }

      // 处理其他类型消息
      this.emit('message', data)

    } catch (error) {
      // 静默处理解析错误
    }
  }

  /**
   * 连接关闭处理
   */
  onClose(event) {
    const closeReasons = {
      1000: '正常关闭',
      1001: '端点离开',
      1002: '协议错误',
      1003: '不支持的数据类型',
      1005: '未收到状态码',
      1006: '连接异常关闭',
      1007: '数据格式错误',
      1008: '策略违规',
      1009: '消息过大',
      1010: '扩展协商失败',
      1011: '服务器错误',
      1015: 'TLS握手失败'
    }

    const reason = closeReasons[event.code] || `未知错误 (${event.code})`

    this.isConnecting = false
    this.stopHeartbeat()

    // 触发断开连接事件
    this.emit('disconnected', {
      code: event.code,
      reason: event.reason || reason,
      wasClean: event.wasClean
    })

    // 如果不是正常关闭，尝试重连
    if (event.code !== 1000) {
      this.scheduleReconnect()
    }
  }

  /**
   * 连接错误处理
   */
  onError(error) {
    this.isConnecting = false
    this.emit('error', error)
    this.scheduleReconnect()
  }

  /**
   * 发送消息
   */
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
      return true
    }
    return false
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.stopHeartbeat()

    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect')
      this.ws = null
    }
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat()

    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'ping' })
      }
    }, this.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('maxReconnectAttemptsReached')
      return
    }

    this.reconnectAttempts++

    setTimeout(() => {
      this.connect()
    }, this.reconnectInterval * this.reconnectAttempts)
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          // 静默处理回调错误
        }
      })
    }
  }

  /**
   * 获取连接状态
   */
  getReadyState() {
    return this.ws ? this.ws.readyState : WebSocket.CLOSED
  }

  /**
   * 是否已连接
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN
  }
}

// 创建全局实例
const wsManager = new WebSocketManager()

export default wsManager
