<template>
  <div class="acceptance-photo-upload">
    <div class="photo-upload-header">
      <h4>验收照片上传</h4>
      <p class="upload-description">请按要求上传3张照片，每张照片不超过10MB</p>
      <p class="debug-info" style="color: #999; font-size: 12px;">
        记录ID: {{ recordId || '未设置' }} |
        上传API: {{ uploadApi ? '已配置' : '未配置' }} |
        照片数量: {{ hasPhotos ? sortedPhotos.length : 0 }} |
        状态: {{ frontPhoto ? 'F' : '-' }}{{ sidePhoto ? 'S' : '-' }}{{ overallPhoto ? 'O' : '-' }}
      </p>
    </div>

    <div class="photo-upload-grid">
      <!-- 正面照片 -->
      <div class="photo-upload-item">
        <div class="photo-label">
          <span class="label-text">正面照片</span>
          <span class="required-mark">*</span>
        </div>
        <div class="photo-upload-area">
          <a-upload
            v-model:file-list="frontPhotoList"
            :custom-request="(options) => handleUpload(options, 'front')"
            list-type="picture-card"
            :max-count="1"
            :show-upload-list="true"
            accept="image/jpeg,image/png,image/jpg"
            :before-upload="(file) => beforeUpload(file, 'front')"
            @change="(info) => handleUploadChange(info, 'front')"
            @remove="() => removePhoto('front')"
          >
            <div v-if="frontPhotoList.length < 1" class="upload-placeholder">
              <PlusOutlined />
              <div class="upload-text">上传正面照片</div>
            </div>
          </a-upload>
        </div>
      </div>

      <!-- 侧面照片 -->
      <div class="photo-upload-item">
        <div class="photo-label">
          <span class="label-text">侧面照片</span>
          <span class="required-mark">*</span>
        </div>
        <div class="photo-upload-area">
          <a-upload
            v-model:file-list="sidePhotoList"
            :custom-request="(options) => handleUpload(options, 'side')"
            list-type="picture-card"
            :max-count="1"
            :show-upload-list="true"
            accept="image/jpeg,image/png,image/jpg"
            :before-upload="(file) => beforeUpload(file, 'side')"
            @change="(info) => handleUploadChange(info, 'side')"
            @remove="() => removePhoto('side')"
          >
            <div v-if="sidePhotoList.length < 1" class="upload-placeholder">
              <PlusOutlined />
              <div class="upload-text">上传侧面照片</div>
            </div>
          </a-upload>
        </div>
      </div>

      <!-- 整体照片 -->
      <div class="photo-upload-item">
        <div class="photo-label">
          <span class="label-text">整体照片</span>
          <span class="required-mark">*</span>
        </div>
        <div class="photo-upload-area">
          <a-upload
            v-model:file-list="overallPhotoList"
            :custom-request="(options) => handleUpload(options, 'overall')"
            list-type="picture-card"
            :max-count="1"
            :show-upload-list="true"
            accept="image/jpeg,image/png,image/jpg"
            :before-upload="(file) => beforeUpload(file, 'overall')"
            @change="(info) => handleUploadChange(info, 'overall')"
            @remove="() => removePhoto('overall')"
          >
            <div v-if="overallPhotoList.length < 1" class="upload-placeholder">
              <PlusOutlined />
              <div class="upload-text">上传整体照片</div>
            </div>
          </a-upload>
        </div>
      </div>
    </div>

    <!-- 上传状态显示 -->
    <div v-if="hasPhotos" class="upload-status">
      <h5>上传状态 ({{ uploadedCount }}/3 张完成)</h5>
      <div class="status-list">
        <div class="status-item" :class="{ completed: frontPhoto }">
          <span class="status-icon">{{ frontPhoto ? '✓' : '○' }}</span>
          <span class="status-text">正面照片</span>
          <span class="status-desc">{{ frontPhoto ? '已上传' : '待上传' }}</span>
        </div>
        <div class="status-item" :class="{ completed: sidePhoto }">
          <span class="status-icon">{{ sidePhoto ? '✓' : '○' }}</span>
          <span class="status-text">侧面照片</span>
          <span class="status-desc">{{ sidePhoto ? '已上传' : '待上传' }}</span>
        </div>
        <div class="status-item" :class="{ completed: overallPhoto }">
          <span class="status-icon">{{ overallPhoto ? '✓' : '○' }}</span>
          <span class="status-text">整体照片</span>
          <span class="status-desc">{{ overallPhoto ? '已上传' : '待上传' }}</span>
        </div>
      </div>
      <div class="upload-progress" v-if="uploadedCount < 3">
        <span class="progress-text">请继续上传剩余 {{ 3 - uploadedCount }} 张照片</span>
      </div>
    </div>

    <!-- 上传提示 -->
    <div class="upload-tips">
      <h5>上传要求：</h5>
      <ul>
        <li>支持 JPG、PNG 格式</li>
        <li>单张照片不超过 10MB</li>
        <li>必须上传正面、侧面、整体三张照片</li>
        <li>照片位置固定：第1张-正面照、第2张-侧面照、第3张-整体照</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined
} from '@ant-design/icons-vue'
import { processAcceptancePhoto, validateImageFile } from '@/utils/imageProcessor'
import { getPhotoUrl } from '@/utils/photo'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  recordId: {
    type: [String, Number],
    required: false,
    default: null
  },
  uploadApi: {
    type: Function,
    required: false,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 照片数据
const frontPhoto = ref(null)
const sidePhoto = ref(null)
const overallPhoto = ref(null)

// 上传列表（用于 a-upload 组件）
const frontPhotoList = ref([])
const sidePhotoList = ref([])
const overallPhotoList = ref([])

// 照片类型标签映射
const photoTypeLabels = {
  front: '正面照片',
  side: '侧面照片',
  overall: '整体照片'
}

// 计算属性
const hasPhotos = computed(() => {
  return frontPhoto.value || sidePhoto.value || overallPhoto.value
})

const uploadedCount = computed(() => {
  let count = 0
  if (frontPhoto.value) count++
  if (sidePhoto.value) count++
  if (overallPhoto.value) count++
  return count
})

const sortedPhotos = computed(() => {
  // 固定顺序：正面、侧面、整体
  const photos = []
  if (frontPhoto.value) photos.push({ ...frontPhoto.value, type: 'front', order: 0 })
  if (sidePhoto.value) photos.push({ ...sidePhoto.value, type: 'side', order: 1 })
  if (overallPhoto.value) photos.push({ ...overallPhoto.value, type: 'overall', order: 2 })

  return photos
})

// 方法
const getPhotoTypeLabel = (type) => {
  return photoTypeLabels[type] || type
}



const setPhotoByType = (type, photo) => {
  console.log(`🔄 设置${type}照片:`, photo)
  switch (type) {
    case 'front':
      frontPhoto.value = photo
      console.log('✅ 正面照片已设置:', frontPhoto.value)
      break
    case 'side':
      sidePhoto.value = photo
      console.log('✅ 侧面照片已设置:', sidePhoto.value)
      break
    case 'overall':
      overallPhoto.value = photo
      console.log('✅ 整体照片已设置:', overallPhoto.value)
      break
  }
}

// 上传处理
const handleUpload = async ({ file, onSuccess, onError }, type) => {
  try {
    console.log('🔄 开始上传照片:', { type, fileName: file.name, recordId: props.recordId })

    // 检查必要参数
    if (!props.recordId) {
      message.error('记录ID为空，无法上传照片')
      onError && onError(new Error('记录ID为空'))
      return
    }

    if (!props.uploadApi) {
      message.error('上传接口未配置')
      onError && onError(new Error('上传接口未配置'))
      return
    }

    // 文件验证
    const validation = validateImageFile(file)
    if (!validation.valid) {
      const errorMsg = validation.errors.join(', ')
      message.error(errorMsg)
      onError && onError(new Error(errorMsg))
      return
    }

    // 处理图片：自动缩放和压缩
    console.log('🖼️ 开始处理图片...')
    const processedFile = await processAcceptancePhoto(file, {
      targetWidth: 113,   // 3cm
      targetHeight: 133,  // 3.5cm
      quality: 0.8,
      maxFileSize: 500 * 1024 // 500KB
    })

    console.log('✅ 图片处理完成:', {
      原始大小: `${(file.size / 1024).toFixed(1)}KB`,
      处理后大小: `${(processedFile.size / 1024).toFixed(1)}KB`,
      压缩率: `${((1 - processedFile.size / file.size) * 100).toFixed(1)}%`
    })

    // 调用上传API
    const formData = new FormData()
    formData.append('file', processedFile)
    formData.append('photo_type', type)

    console.log('📤 调用上传API...')
    const response = await props.uploadApi(props.recordId, formData)
    console.log('📥 上传响应:', response)

    if (response.code === 200) {
      const photoData = {
        url: response.data.url,
        type: type,
        order: getFixedOrder(type),
        uid: file.uid,
        description: response.data.description
      }

      console.log('📸 照片数据:', photoData)
      console.log('📸 完整URL:', getPhotoUrl(response.data.url))

      // 更新照片数据
      setPhotoByType(type, photoData)

      // 更新对应的文件列表，确保 a-upload 组件状态正确
      const fileItem = {
        uid: file.uid,
        name: file.name,
        status: 'done',
        url: getPhotoUrl(response.data.url), // 使用完整URL
        thumbUrl: getPhotoUrl(response.data.url), // 缩略图URL
        response: response.data
      }

      switch (type) {
        case 'front':
          frontPhotoList.value = [fileItem]
          break
        case 'side':
          sidePhotoList.value = [fileItem]
          break
        case 'overall':
          overallPhotoList.value = [fileItem]
          break
      }

      message.success(`${getPhotoTypeLabel(type)}上传成功`)
      onSuccess && onSuccess(response.data, file)
      emitChange()
    } else {
      message.error(response.message || '上传失败')
      onError && onError(new Error(response.message || '上传失败'))
    }
  } catch (error) {
    console.error('❌ 上传失败:', error)
    message.error(`上传失败: ${error.message}`)
    onError && onError(error)
  }
}

const beforeUpload = (_file, type) => {
  console.log(`🔄 准备上传${type}照片，先清理旧照片`)

  // 如果已有照片，先清理
  const currentPhotoList = type === 'front' ? frontPhotoList.value :
                          type === 'side' ? sidePhotoList.value :
                          overallPhotoList.value

  if (currentPhotoList.length > 0) {
    console.log(`🗑️ 清理旧的${type}照片`)
    removePhoto(type)
  }

  return true // 继续上传
}

const handleUploadChange = (info, type) => {
  console.log(`${type} 照片上传状态变化:`, info)
}

const removePhoto = (type) => {
  console.log(`🗑️ 删除${type}照片`)

  // 清空照片数据
  setPhotoByType(type, null)

  // 清空对应的上传列表
  switch (type) {
    case 'front': frontPhotoList.value = []; break
    case 'side': sidePhotoList.value = []; break
    case 'overall': overallPhotoList.value = []; break
  }

  message.success(`${getPhotoTypeLabel(type)}已删除`)
  emitChange()
}

// 获取固定的照片顺序
const getFixedOrder = (type) => {
  const orderMap = {
    'front': 0,    // 正面照片固定为第1张
    'side': 1,     // 侧面照片固定为第2张
    'overall': 2   // 整体照片固定为第3张
  }
  return orderMap[type] || 0
}

const emitChange = () => {
  const photos = sortedPhotos.value
  emit('update:modelValue', photos)
  emit('change', photos)
}

// 初始化数据
const initPhotos = (photos) => {
  if (!photos || !Array.isArray(photos)) return

  // 清空现有照片和文件列表
  frontPhoto.value = null
  sidePhoto.value = null
  overallPhoto.value = null
  frontPhotoList.value = []
  sidePhotoList.value = []
  overallPhotoList.value = []

  photos.forEach(photo => {
    // 确保照片有正确的固定顺序
    const photoWithOrder = {
      ...photo,
      order: getFixedOrder(photo.type)
    }

    // 创建文件列表项
    const fileItem = {
      uid: photo.uid || photo.id || `${photo.type}_${Date.now()}`,
      name: `${photo.type}_photo.jpg`,
      status: 'done',
      url: getPhotoUrl(photo.url), // 使用完整URL
      thumbUrl: getPhotoUrl(photo.url) // 缩略图URL
    }

    // 设置照片数据和文件列表
    if (photo.type === 'front') {
      frontPhoto.value = photoWithOrder
      frontPhotoList.value = [fileItem]
    } else if (photo.type === 'side') {
      sidePhoto.value = photoWithOrder
      sidePhotoList.value = [fileItem]
    } else if (photo.type === 'overall') {
      overallPhoto.value = photoWithOrder
      overallPhotoList.value = [fileItem]
    }
  })
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  initPhotos(newValue)
}, { immediate: true })
</script>

<style scoped>
.acceptance-photo-upload {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.photo-upload-header {
  margin-bottom: 20px;
}

.photo-upload-header h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.upload-description {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.photo-upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.photo-upload-item {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8e8e8;
}

.photo-label {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.label-text {
  color: #262626;
  font-weight: 500;
  font-size: 14px;
}

.required-mark {
  color: #ff4d4f;
  margin-left: 4px;
}

.photo-upload-area {
  position: relative;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-placeholder:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-text {
  margin-top: 8px;
  color: #8c8c8c;
  font-size: 12px;
  text-align: center;
}

/* 使用原生 a-upload 的预览功能，不需要自定义样式 */

.remove-btn:hover {
  background: #ff4d4f;
}

.move-btn:hover:not(:disabled) {
  background: #1890ff;
}

.move-btn:disabled {
  background: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}

.upload-status {
  margin-bottom: 20px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.upload-status h5 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.status-item.completed {
  background: #d4edda;
  border-color: #c3e6cb;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  background: #6c757d;
  color: white;
}

.status-item.completed .status-icon {
  background: #28a745;
}

.status-text {
  flex: 1;
  color: #495057;
  font-weight: 500;
  font-size: 13px;
}

.status-desc {
  color: #6c757d;
  font-size: 12px;
}

.status-item.completed .status-desc {
  color: #155724;
}

.upload-progress {
  margin-top: 12px;
  padding: 8px 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
}

.progress-text {
  color: #856404;
  font-size: 12px;
  font-weight: 500;
}

.upload-tips {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.upload-tips h5 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.upload-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #595959;
  font-size: 13px;
}

.upload-tips li {
  margin-bottom: 4px;
}

.upload-tips li:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .photo-upload-grid {
    grid-template-columns: 1fr;
  }

  .photo-order-list {
    flex-direction: column;
    align-items: flex-start;
  }

  .photo-order-item {
    width: 100%;
  }
}
</style>
