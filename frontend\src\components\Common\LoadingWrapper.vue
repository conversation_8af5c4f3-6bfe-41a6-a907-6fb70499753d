<template>
  <div class="loading-wrapper" :class="wrapperClass">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container" :style="loadingStyle">
      <a-spin :size="spinSize" :tip="loadingText">
        <div class="loading-content" v-if="showLoadingContent">
          <slot name="loading"></slot>
        </div>
      </a-spin>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <a-result
        status="error"
        :title="errorTitle"
        :sub-title="errorMessage"
      >
        <template #extra>
          <a-button type="primary" @click="retry" v-if="showRetry">
            重试
          </a-button>
          <slot name="error-actions"></slot>
        </template>
      </a-result>
    </div>

    <!-- 空状态 -->
    <div v-else-if="empty" class="empty-container">
      <a-empty
        :image="emptyImage"
        :description="emptyDescription"
      >
        <template #description>
          <span>{{ emptyDescription }}</span>
        </template>
        <slot name="empty-actions"></slot>
      </a-empty>
    </div>

    <!-- 正常内容 -->
    <div v-else class="content-container">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'LoadingWrapper',
  props: {
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 错误状态
    error: {
      type: Boolean,
      default: false
    },
    // 空状态
    empty: {
      type: Boolean,
      default: false
    },
    // 加载文本
    loadingText: {
      type: String,
      default: '加载中...'
    },
    // 错误标题
    errorTitle: {
      type: String,
      default: '加载失败'
    },
    // 错误消息
    errorMessage: {
      type: String,
      default: '请检查网络连接后重试'
    },
    // 空状态描述
    emptyDescription: {
      type: String,
      default: '暂无数据'
    },
    // 空状态图片
    emptyImage: {
      type: String,
      default: 'default'
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      default: true
    },
    // 是否显示加载内容
    showLoadingContent: {
      type: Boolean,
      default: false
    },
    // 加载器大小
    spinSize: {
      type: String,
      default: 'default'
    },
    // 最小高度
    minHeight: {
      type: [String, Number],
      default: 200
    },
    // 是否全屏
    fullscreen: {
      type: Boolean,
      default: false
    }
  },
  emits: ['retry'],
  setup(props, { emit }) {
    // 包装器样式类
    const wrapperClass = computed(() => ({
      'loading-wrapper-fullscreen': props.fullscreen,
      'loading-wrapper-loading': props.loading,
      'loading-wrapper-error': props.error,
      'loading-wrapper-empty': props.empty
    }))

    // 加载容器样式
    const loadingStyle = computed(() => ({
      minHeight: typeof props.minHeight === 'number' ? `${props.minHeight}px` : props.minHeight
    }))

    // 重试方法
    const retry = () => {
      emit('retry')
    }

    return {
      wrapperClass,
      loadingStyle,
      retry
    }
  }
}
</script>

<style scoped>
.loading-wrapper {
  position: relative;
  width: 100%;
}

.loading-wrapper-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-content {
  margin-top: 16px;
}

.error-container,
.empty-container {
  padding: 40px 20px;
}

.content-container {
  width: 100%;
}

/* 加载状态动画 */
.loading-wrapper-loading .content-container {
  opacity: 0.6;
  pointer-events: none;
}

/* 移除响应式设计，保持固定布局 */

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .loading-wrapper-fullscreen {
    background: rgba(0, 0, 0, 0.9);
  }
}
</style>
