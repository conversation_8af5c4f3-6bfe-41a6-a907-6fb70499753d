"""
通用工具函数
减少重复代码，提供常用的工具方法
"""
import os
import re
import logging
from datetime import datetime
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response

logger = logging.getLogger(__name__)


def validate_file_type(file, allowed_types):
    """
    验证文件类型
    
    Args:
        file: 上传的文件对象
        allowed_types: 允许的文件类型列表
    
    Returns:
        bool: 是否为允许的文件类型
    """
    if not file:
        return False
    
    file_extension = os.path.splitext(file.name)[1].lower()
    return file_extension in allowed_types


def get_file_size_mb(file):
    """
    获取文件大小（MB）

    Args:
        file: 文件对象

    Returns:
        float: 文件大小（MB）
    """
    if not file:
        return 0

    return file.size / (1024 * 1024)


def cache_result(timeout=300):
    """
    缓存装饰器

    Args:
        timeout: 缓存超时时间（秒）
    """
    from functools import wraps
    from django.core.cache import cache
    import hashlib
    import json

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__module__}.{func.__name__}"
            if args:
                cache_key += f":{hashlib.md5(str(args).encode()).hexdigest()}"
            if kwargs:
                cache_key += f":{hashlib.md5(json.dumps(kwargs, sort_keys=True).encode()).hexdigest()}"

            # 尝试从缓存获取结果
            result = cache.get(cache_key)
            if result is not None:
                return result

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            return result

        return wrapper
    return decorator


def format_currency(amount, currency='¥'):
    """
    格式化货币显示

    Args:
        amount: 金额
        currency: 货币符号

    Returns:
        str: 格式化后的货币字符串
    """
    if amount is None:
        return f"{currency}0.00"

    return f"{currency}{amount:,.2f}"


def generate_order_number(prefix='PO', date_format='%Y%m%d'):
    """
    生成订单编号

    Args:
        prefix: 前缀
        date_format: 日期格式

    Returns:
        str: 订单编号
    """
    import random

    date_str = datetime.now().strftime(date_format)
    random_num = random.randint(1000, 9999)

    return f"{prefix}{date_str}{random_num}"


class StatusChoices:
    """状态选择常量"""
    DRAFT = 'draft'
    PENDING_APPROVAL = 'pending_approval'
    APPROVED = 'approved'
    REJECTED = 'rejected'
    RETURNED = 'returned'
    PENDING_PURCHASE = 'pending_purchase'
    PURCHASED = 'purchased'
    PENDING_ACCEPTANCE = 'pending_acceptance'
    ACCEPTED = 'accepted'
    PENDING_REIMBURSEMENT = 'pending_reimbursement'
    SETTLED = 'settled'

    CHOICES = [
        (DRAFT, '草稿'),
        (PENDING_APPROVAL, '待审批'),
        (APPROVED, '已审批'),
        (REJECTED, '已拒绝'),
        (RETURNED, '已退回'),
        (PENDING_PURCHASE, '待采购'),
        (PURCHASED, '已采购'),
        (PENDING_ACCEPTANCE, '待验收'),
        (ACCEPTED, '已验收'),
        (PENDING_REIMBURSEMENT, '待结算'),
        (SETTLED, '已结算'),
    ]

    @classmethod
    def get_display_name(cls, status):
        """获取状态显示名称"""
        for choice in cls.CHOICES:
            if choice[0] == status:
                return choice[1]
        return status


class ValidationUtils:
    """验证工具类"""

    @staticmethod
    def validate_phone(phone):
        """验证手机号"""
        if not phone:
            return True
        pattern = r'^1[3-9]\d{9}$'
        if not re.match(pattern, phone):
            raise ValidationError('请输入正确的手机号码')
        return True

    @staticmethod
    def validate_email(email):
        """验证邮箱"""
        if not email:
            return True
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, email):
            raise ValidationError('请输入正确的邮箱地址')
        return True

    @staticmethod
    def validate_positive_number(value, field_name='数值'):
        """验证正数"""
        if value is not None and value < 0:
            raise ValidationError(f'{field_name}必须为正数')
        return True

    @staticmethod
    def validate_date_range(start_date, end_date):
        """验证日期范围"""
        if start_date and end_date and start_date > end_date:
            raise ValidationError('开始日期不能晚于结束日期')
        return True


class ModelUtils:
    """模型工具类"""

    @staticmethod
    def get_or_none(model_class, **kwargs):
        """获取对象或返回None"""
        try:
            return model_class.objects.get(**kwargs)
        except model_class.DoesNotExist:
            return None
        except model_class.MultipleObjectsReturned:
            logger.warning(f"Multiple {model_class.__name__} objects returned for {kwargs}")
            return model_class.objects.filter(**kwargs).first()

    @staticmethod
    def safe_delete(instance):
        """安全删除对象"""
        try:
            instance.delete()
            return True
        except Exception as e:
            logger.error(f"删除{instance.__class__.__name__}失败: {e}")
            return False

    @staticmethod
    def bulk_update_status(queryset, new_status, reason=''):
        """批量更新状态"""
        updated_count = 0
        for instance in queryset:
            try:
                old_status = instance.status
                instance.status = new_status
                if hasattr(instance, 'status_reason'):
                    instance.status_reason = reason
                instance.save()
                updated_count += 1

                # 记录状态变更日志
                logger.info(f"{instance.__class__.__name__} {instance.id} 状态从 {old_status} 变更为 {new_status}")
            except Exception as e:
                logger.error(f"更新{instance.__class__.__name__} {instance.id}状态失败: {e}")

        return updated_count


class QueryUtils:
    """查询工具类"""

    @staticmethod
    def apply_date_filter(queryset, date_field, start_date=None, end_date=None):
        """应用日期范围过滤"""
        if start_date:
            queryset = queryset.filter(**{f'{date_field}__gte': start_date})
        if end_date:
            # 结束日期包含当天
            end_datetime = timezone.datetime.combine(end_date, timezone.datetime.max.time())
            queryset = queryset.filter(**{f'{date_field}__lte': end_datetime})
        return queryset

    @staticmethod
    def apply_search_filter(queryset, search_fields, search_term):
        """应用搜索过滤"""
        if not search_term:
            return queryset

        from django.db.models import Q
        query = Q()
        for field in search_fields:
            query |= Q(**{f'{field}__icontains': search_term})

        return queryset.filter(query)

    @staticmethod
    def apply_status_filter(queryset, status_list):
        """应用状态过滤"""
        if status_list:
            return queryset.filter(status__in=status_list)
        return queryset


class ResponseUtils:
    """响应工具类"""

    @staticmethod
    def success_response(data=None, message='操作成功', status_code=status.HTTP_200_OK):
        """成功响应"""
        return Response({
            'code': 200,
            'message': message,
            'data': data
        }, status=status_code)

    @staticmethod
    def error_response(message='操作失败', errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        """错误响应"""
        return Response({
            'code': status_code,
            'message': message,
            'data': errors
        }, status=status_code)
