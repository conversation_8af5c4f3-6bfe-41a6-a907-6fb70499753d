from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

from django.conf import settings

User = get_user_model()


class Department(models.Model):
    """
    部门表
    对应数据库表：sys_department
    支持两级部门结构（分公司-办事处）
    """
    dept_name = models.CharField('部门名称', max_length=50)
    parent_id = models.IntegerField('上级部门ID', null=True, blank=True)
    dept_code = models.CharField('部门编码', max_length=20, unique=True)
    hierarchy_path = models.CharField(
        '部门层级路径',
        max_length=200,
        help_text='例：分公司-办事处'
    )
    # 部门基本信息
    manager_name = models.CharField('部门负责人', max_length=50, blank=True, null=True)
    contact_phone = models.CharField('联系电话', max_length=20, blank=True, null=True)
    description = models.TextField('部门描述', blank=True, null=True)
    # 权限配置
    approval_limit = models.DecimalField(
        '采购审批额度限制',
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='该部门可审批的最大金额，为空表示无限制'
    )
    can_approve = models.BooleanField('可以审批', default=False, help_text='是否具有审批权限')
    can_accept = models.BooleanField('可以验收', default=False, help_text='是否具有验收权限')
    can_finance = models.BooleanField('可以财务结算', default=False, help_text='是否具有财务权限')

    status = models.BooleanField('启用状态', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'sys_department'
        verbose_name = '部门'
        verbose_name_plural = '部门管理'

    def __str__(self):
        return self.hierarchy_path or self.dept_name

    def save(self, *args, **kwargs):
        """保存时自动生成层级路径"""
        if self.parent_id:
            try:
                parent = Department.objects.get(id=self.parent_id)
                self.hierarchy_path = f"{parent.dept_name}-{self.dept_name}"
            except Department.DoesNotExist:
                self.hierarchy_path = self.dept_name
        else:
            self.hierarchy_path = self.dept_name
        super().save(*args, **kwargs)


class SystemLog(models.Model):
    """
    系统日志表
    """
    LOG_TYPES = [
        ('login', '用户登录'),
        ('logout', '用户登出'),
        ('create', '创建操作'),
        ('update', '更新操作'),
        ('delete', '删除操作'),
        ('approve', '审批操作'),
        ('reject', '驳回操作'),
        ('return', '退回操作'),
        ('purchase', '采购操作'),
        ('accept', '验收操作'),
        ('reimburse', '报销操作'),
        ('export', '导出操作'),
        ('import', '导入操作'),
        ('error', '错误日志'),
        ('warning', '警告日志'),
        ('info', '信息日志'),
        ('debug', '调试日志'),
        ('other', '其他操作'),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='操作用户')
    username = models.CharField(max_length=150, verbose_name='用户名', help_text='记录用户名，防止用户删除后无法追踪')
    log_type = models.CharField(max_length=20, choices=LOG_TYPES, verbose_name='日志类型')
    action = models.CharField(max_length=200, verbose_name='操作描述')
    target_model = models.CharField(max_length=100, blank=True, null=True, verbose_name='目标模型')
    target_id = models.CharField(max_length=50, blank=True, null=True, verbose_name='目标ID')
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, null=True, verbose_name='用户代理')
    request_data = models.JSONField(blank=True, null=True, verbose_name='请求数据')
    response_data = models.JSONField(blank=True, null=True, verbose_name='响应数据')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')

    class Meta:
        db_table = 'system_log'
        verbose_name = '系统日志'
        verbose_name_plural = '系统日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['log_type', 'created_at']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f'{self.username} - {self.get_log_type_display()} - {self.action}'

    @property
    def children(self):
        """获取子部门"""
        return Department.objects.filter(parent_id=self.id, status=True)

# UserSession 模型已删除 - 功能由 SystemLog 和 Django 内置会话管理替代
# 在线用户统计现在通过 django_session 和 SystemLog 实现


class Notification(models.Model):
    """
    系统通知表
    """
    NOTIFICATION_TYPES = [
        ('approval', '审批通知'),
        ('purchase', '采购通知'),
        ('acceptance', '验收通知'),
        ('reimbursement', '报销通知'),
        ('rejection', '驳回通知'),
        ('return', '退回通知'),
        ('system', '系统通知'),
    ]

    PRIORITY_LEVELS = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]

    title = models.CharField(max_length=200, verbose_name='通知标题')
    content = models.TextField(verbose_name='通知内容')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, verbose_name='通知类型')
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='normal', verbose_name='优先级')

    # 发送者和接收者
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
                              related_name='sent_notifications', verbose_name='发送者')
    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                                 related_name='received_notifications', verbose_name='接收者')

    # 关联的业务对象
    target_model = models.CharField(max_length=100, blank=True, null=True, verbose_name='目标模型')
    target_id = models.CharField(max_length=50, blank=True, null=True, verbose_name='目标ID')
    target_url = models.CharField(max_length=500, blank=True, null=True, verbose_name='跳转链接')

    # 状态字段
    is_read = models.BooleanField(default=False, verbose_name='是否已读')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')
    is_archived = models.BooleanField(default=False, verbose_name='是否归档')

    # 操作按钮配置（JSON格式）
    action_buttons = models.JSONField(default=list, blank=True, verbose_name='操作按钮')

    # 推送方式配置（JSON格式）
    delivery_methods = models.JSONField(default=list, blank=True, verbose_name='推送方式')

    # 推送状态（JSON格式）
    delivery_status = models.JSONField(default=dict, blank=True, verbose_name='推送状态')

    # 发送状态
    is_sent = models.BooleanField(default=False, verbose_name='是否已发送')
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name='发送时间')
    retry_count = models.IntegerField(default=0, verbose_name='重试次数')

    # 时间字段
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'system_notification'
        verbose_name = '系统通知'
        verbose_name_plural = '系统通知'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read', 'created_at']),
            models.Index(fields=['notification_type', 'created_at']),
            models.Index(fields=['target_model', 'target_id']),
        ]

    def __str__(self):
        return f'{self.title} - {self.recipient.username}'

    def mark_as_read(self):
        """标记为已读"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])

    @classmethod
    def create_notification(cls, title, content, notification_type, recipient, sender=None,
                          target_model=None, target_id=None, target_url=None, priority='normal'):
        """创建通知的便捷方法"""
        return cls.objects.create(
            title=title,
            content=content,
            notification_type=notification_type,
            priority=priority,
            sender=sender,
            recipient=recipient,
            target_model=target_model,
            target_id=target_id,
            target_url=target_url
        )


class Menu(models.Model):
    """
    系统菜单模型
    用于动态菜单系统，支持权限控制
    """
    MENU_TYPE_CHOICES = [
        ('directory', '目录'),
        ('page', '页面'),
        ('button', '按钮'),
    ]

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='父菜单',
        related_name='children'
    )
    menu_name = models.CharField('菜单名称', max_length=50)
    menu_code = models.CharField('菜单编码', max_length=50, unique=True)
    menu_type = models.CharField(
        '菜单类型',
        max_length=10,
        choices=MENU_TYPE_CHOICES,
        default='page'
    )
    route_path = models.CharField('路由路径', max_length=200, null=True, blank=True)
    component_path = models.CharField('组件路径', max_length=200, null=True, blank=True)
    icon = models.CharField('菜单图标', max_length=50, null=True, blank=True)
    sort_order = models.IntegerField('排序', default=0)
    is_visible = models.BooleanField('是否显示', default=True)
    is_active = models.BooleanField('是否启用', default=True)
    permission_code = models.CharField('权限标识', max_length=100, null=True, blank=True)
    business_status = models.CharField(
        '关联的业务状态',
        max_length=50,
        null=True,
        blank=True,
        help_text='采购流程专用，如：draft,pending_approval'
    )

    # 动态路由标准字段
    redirect = models.CharField(
        '重定向路径',
        max_length=200,
        null=True,
        blank=True,
        help_text="重定向路径，用于目录菜单"
    )
    hidden = models.BooleanField(
        '是否隐藏',
        default=False,
        help_text="是否隐藏：False-显示，True-隐藏"
    )
    always_show = models.BooleanField(
        '是否总是显示',
        default=False,
        help_text="是否总是显示：False-否，True-是"
    )
    keep_alive = models.BooleanField(
        '是否缓存路由',
        default=False,
        help_text="是否缓存路由：False-否，True-是"
    )

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'sys_menu'
        verbose_name = '系统菜单'
        verbose_name_plural = '系统菜单'
        ordering = ['sort_order', 'id']

    def __str__(self):
        return self.menu_name

