<template>
  <div class="permission-management">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <SafetyCertificateOutlined />
          权限管理
        </h1>
        <p class="page-subtitle">
          通过矩阵视图直观地查看和管理角色或用户的权限分配情况
        </p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalPermissions }}</span>
          <span class="stat-label">权限总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ moduleCount }}</span>
          <span class="stat-label">目录数量</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ pageCount }}</span>
          <span class="stat-label">页面数量</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ buttonCount }}</span>
          <span class="stat-label">按钮数量</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ roleCount }}</span>
          <span class="stat-label">系统角色</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ userCount }}</span>
          <span class="stat-label">系统用户</span>
        </div>
      </div>
    </div>

    <!-- 权限矩阵控制面板 -->
    <div class="business-card">
      <div class="matrix-control-panel">
        <div class="panel-left">
          <div class="panel-title">
            <SafetyCertificateOutlined class="title-icon" />
            权限矩阵管理
          </div>
          <div class="panel-description">
            管理系统角色和用户的权限分配，支持批量操作和数据导出
          </div>
        </div>
        <div class="panel-right">
          <a-radio-group
            v-model:value="matrixView"
            button-style="solid"
            class="view-switcher"
          >
            <a-radio-button value="roles">
              <TeamOutlined />
              角色视图
            </a-radio-button>
            <a-radio-button value="users">
              <UserOutlined />
              用户视图
            </a-radio-button>
          </a-radio-group>
          <a-space>
            <a-button
              @click="refreshData"
              :loading="loading"
              type="primary"
              class="primary-action-btn control-btn"
            >
              <ReloadOutlined />
              刷新数据
            </a-button>

            <a-button @click="exportMatrix" class="control-btn export-btn">
              <DownloadOutlined />
              导出矩阵
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 权限矩阵树形表格 -->
    <div class="table-section business-card">
      <a-table
        :columns="matrixColumns"
        :data-source="treeMatrixData"
        :row-key="(record) => record.key"
        :scroll="{ x: 1800, y: 'calc(100vh - 320px)' }"
        size="large"
        bordered
        class="unified-table matrix-table tree-table"
        :loading="loading"
        :pagination="false"
        :default-expand-all-rows="true"
        :expand-row-by-click="true"
        :indentSize="24"
      >
        <template #bodyCell="{ column, record }">
          <!-- 权限名称列 -->
          <template v-if="column.dataIndex === 'permission_name'">
            <div class="permission-cell" :class="{
              'directory-cell': record.type === 'directory',
              'page-cell': record.type === 'page',
              'button-cell': record.type === 'button',
              'module-cell': record.type === 'module'  // 兼容旧的模块类型
            }">
              <div class="permission-header">
                <component
                  :is="getTreeNodeIcon(record)"
                  class="permission-icon"
                  :class="{ 'module-icon': record.type === 'module' }"
                />
                <div class="permission-info">
                  <div class="permission-name-row">
                    <div class="permission-name" :class="{ 'module-name': record.type === 'module' }">
                      {{ record.permission_name }}
                    </div>
                    <!-- 添加类型标签，颜色与菜单管理保持一致 -->
                    <a-tag v-if="record.type === 'directory'" color="purple" size="small" class="type-tag">
                      目录
                      <span v-if="record.children && record.children.length > 0" class="count-badge">
                        {{ getChildPageCount(record) }}页面
                      </span>
                    </a-tag>
                    <a-tag v-else-if="record.type === 'page'" color="green" size="small" class="type-tag">
                      页面
                      <span v-if="record.children && record.children.length > 0" class="count-badge">
                        {{ getChildButtonCount(record) }}按钮
                      </span>
                    </a-tag>
                    <a-tag v-else-if="record.type === 'button'" color="blue" size="small" class="type-tag">
                      按钮
                    </a-tag>
                  </div>
                  <div class="permission-code" v-if="record.permission_key && record.type !== 'module'">
                    {{ record.permission_key }}
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 模块名称列 -->
          <template v-if="column.dataIndex === 'module_name'">
            <div class="module-name-cell" v-if="record.type === 'directory'">
              <div class="module-header">
                <component :is="getModuleIcon(record.permission_name)" class="module-icon" />
                <div class="module-info">
                  <div class="module-name-chinese">{{ record.permission_name }}</div>
                  <div class="module-name-english">{{ record.permission_key || record.module_name }}</div>
                </div>
              </div>
            </div>
            <div class="module-name-cell" v-else-if="record.type === 'page' || record.type === 'button'">
              <div class="module-header">
                <component :is="getModuleIcon(record.module_name || getModuleChineseName(record.permission_key?.split(':')[0]))" class="module-icon small" />
                <div class="module-info">
                  <div class="module-name-chinese">{{ record.module_name || getModuleChineseName(record.permission_key?.split(':')[0]) }}</div>
                  <div class="module-name-english">{{ record.permission_key?.split(':')[0] || 'unknown' }}</div>
                </div>
              </div>
            </div>
            <div class="module-name-cell" v-else>
              <div class="module-header">
                <component :is="getModuleIcon('未分类')" class="module-icon small" />
                <div class="module-info">
                  <div class="module-name-chinese">{{ record.module_name || '未分类' }}</div>
                  <div class="module-name-english">{{ record.permission_key?.split(':')[0] || 'other' }}</div>
                </div>
              </div>
            </div>
          </template>

          <!-- 实体权限复选框 -->
          <template v-if="column.entity">
            <a-checkbox
              v-if="record.type !== 'module' && record[column.dataIndex] !== null"
              :checked="record[column.dataIndex]"
              :indeterminate="getCheckboxIndeterminate(record, column)"
              @change="
                (e) => {
                  e.stopPropagation();
                  updateMatrixPermission(
                    column.entity,
                    record,
                    e.target.checked
                  );
                }
              "
              @click="(e) => e.stopPropagation()"
              :disabled="isPermissionDisabled(column.entity, record.permission_key) "
            />
            <span v-else class="module-placeholder">—</span>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<style scoped>
.permission-management {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

/* 页面标题区域样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
  flex: 1;
}

.page-title {
  color: white;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 业务卡片样式 */
.business-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.business-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* 权限矩阵控制面板样式 */
.matrix-control-panel {
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 1px solid #e8f4fd;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 32px;
}

.panel-left {
  flex: 1;
}

.panel-title {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 24px;
  color: #1890ff;
}

.panel-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.panel-right {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 20px;
}

.view-switcher {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8f4fd;
}

.view-switcher .ant-radio-button-wrapper {
  border: none;
  background: #d4dae0;
  color: #6b7280;
  font-weight: 500;
  padding: 8px 16px;
  height: 40px;
  gap: 8px;
}

.view-switcher .ant-radio-button-wrapper-checked {
  background: #1890ff;
  color: white;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.control-btn {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  border: 1px solid #d9d9d9;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: #666;
  transition: all 0.3s;
}

.control-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f0f8ff;
}
.primary-action-btn {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border: none;
  color: white;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.export-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  border: none;
  color: white;
}

.export-btn:hover {
  background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

/* 权限单元格样式 */
.permission-cell {
  padding: 8px 0;
}

.permission-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 6px;
}

.permission-icon {
  font-size: 18px;
  color: #1890ff;
  margin-top: 2px;
  flex-shrink: 0;
}

.permission-info {
  flex: 1;
  min-width: 0;
}

.permission-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.permission-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.3;
  word-break: break-all;
}

.permission-key {
  font-size: 11px;
  color: #9ca3af;
  font-family: "Courier New", monospace;
  background: #f8fafc;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

/* 矩阵表格样式 */
.matrix-table {
  border-radius: 0 0 12px 12px;
}

:deep(.matrix-table .ant-table-thead > tr > th) {
  background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
  border-bottom: 2px solid #e8f4fd;
  font-weight: 600;
  color: #2c3e50;
  padding: 12px 8px;
  font-size: 13px;
  text-align: center;
}

:deep(.matrix-table .ant-table-tbody > tr > td) {
  padding: 8px;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.3s;
  text-align: center;
}

:deep(.matrix-table .ant-table-tbody > tr:hover > td) {
  background: #f8fbff;
}

:deep(.matrix-table .ant-table-tbody > tr:nth-child(even)) {
  background: #fafbfc;
}

:deep(.matrix-table .ant-table-tbody > tr:nth-child(even):hover) {
  background: #f0f8ff;
}

/* 固定列样式 */
:deep(.matrix-table .ant-table-tbody > tr > td.ant-table-cell-fix-left) {
  background: #fafbfc;
  text-align: left;
}

:deep(.matrix-table .ant-table-tbody > tr:hover > td.ant-table-cell-fix-left) {
  background: #f0f8ff;
}

/* 模块名称样式 */
.module-name-cell {
  text-align: center;
}

.module-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.module-icon {
  font-size: 16px;
  color: #1890ff;
  flex-shrink: 0;

  &.small {
    font-size: 14px;
  }
}

.module-info {
  text-align: center;
}

.module-name-chinese {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
  line-height: 1.2;
}

.module-name-english {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.2;
  margin-top: 2px;
}

/* 权限信息基础样式 */
.permission-cell {
  padding: 8px 0;
}

.permission-header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.permission-icon {
  font-size: 16px;
  color: #1890ff;
  margin-top: 2px;
  flex-shrink: 0;
}

.permission-info {
  flex: 1;
  min-width: 0;
}

.permission-name-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
}

.permission-name {
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
}

.type-tag {
  margin-left: 4px;
  font-size: 11px;
  line-height: 1.2;
  padding: 1px 6px;
  border-radius: 2px;
}

.count-badge {
  margin-left: 4px;
  font-size: 10px;
  opacity: 0.8;
}

.permission-code {
  font-size: 11px;
  color: #bfbfbf;
  font-family: 'Courier New', monospace;
  line-height: 1.2;
}

/* 树形表格样式 */
.tree-table {
  .permission-cell {
    &.directory-cell {
      .permission-name {
        font-weight: 600;
        font-size: 16px;
        color: #722ed1;
      }
      .permission-icon {
        font-size: 18px;
        color: #722ed1;
      }
    }

    &.module-cell {
      .permission-name {
        font-weight: 600;
        font-size: 16px;
        color: #1890ff;
      }
      .permission-icon {
        font-size: 18px;
        color: #1890ff;
      }
    }

    &.page-cell {
      .permission-name {
        font-weight: 500;
        color: #262626;
        font-size: 14px;
      }
      .permission-code {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 2px;
        line-height: 1.2;
      }
      .permission-icon {
        color: #52c41a;
      }
    }

    &.button-cell {
      .permission-name {
        color: #595959;
        font-size: 13px;
      }
      .permission-code {
        font-size: 11px;
        color: #bfbfbf;
        margin-top: 2px;
        line-height: 1.2;
      }
      .permission-icon {
        color: #faad14;
      }
    }
  }
}

:deep(.tree-table .ant-table-tbody > tr.ant-table-row-level-0) {
  background: #f8f9fa;
  font-weight: 600;
}

:deep(.tree-table .ant-table-tbody > tr.ant-table-row-level-1) {
  background: #ffffff;
}

:deep(.tree-table .ant-table-tbody > tr.ant-table-row-level-0:hover) {
  background: #e6f7ff;
}

:deep(.tree-table .ant-table-tbody > tr.ant-table-row-level-1:hover) {
  background: #f6ffed;
}

.module-placeholder {
  color: #d9d9d9;
  font-size: 16px;
  display: inline-block;
  text-align: center;
  width: 100%;
}

/* 复选框样式 */
:deep(.ant-checkbox-wrapper) {
  margin: 0;
}

:deep(.ant-checkbox) {
  transform: scale(1.1);
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #52c41a;
  border-color: #52c41a;
}

:deep(.ant-checkbox:hover .ant-checkbox-inner) {
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permission-management {
    padding: 16px;
  }
  
  .page-header {
    padding: 20px;
    flex-direction: column;
    gap: 20px;
  }

  .header-stats {
    gap: 16px;
  }

  .stat-number {
    font-size: 24px;
  }
}
</style>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { message } from "ant-design-vue";
import {
  SafetyCertificateOutlined,
  TeamOutlined,
  UserOutlined,
  ReloadOutlined,
  DownloadOutlined,
  DashboardOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined,
  AccountBookOutlined,
  SettingOutlined,
} from "@ant-design/icons-vue";
import api from "@/api";

// 响应式数据
const matrixView = ref("roles");
const loading = ref(false);
const roles = ref([]);
const users = ref([]);
const permissions = ref([]); // 从后端获取的权限数据

// 权限树数据 - 从后端动态获取
const permissionTree = ref([]);




// 计算属性
const totalPermissions = computed(() => {
  // 统计所有有权限代码的节点（页面和按钮）
  let count = 0;
  const countPermissions = (nodes) => {
    nodes.forEach((node) => {
      // 统计有权限代码的页面和按钮
      if ((node.type === "page" || node.type === "button") && node.permission_code) {
        count++;
      }
      if (node.children) countPermissions(node.children);
    });
  };
  countPermissions(permissionTree.value);
  return count;
});

const moduleCount = computed(() => {
  // 统计目录数量
  let count = 0;
  const countModules = (nodes) => {
    nodes.forEach((node) => {
      if (node.type === "directory") {
        count++;
      }
      if (node.children) countModules(node.children);
    });
  };
  countModules(permissionTree.value);
  return count;
});

const roleCount = computed(() => {
  return roles.value.length;
});

const userCount = computed(() => {
  return users.value.length;
});

// 页面数量统计
const pageCount = computed(() => {
  let count = 0;
  const countPages = (nodes) => {
    nodes.forEach((node) => {
      if (node.type === "page") {
        count++;
      }
      if (node.children) countPages(node.children);
    });
  };
  countPages(permissionTree.value);
  return count;
});

// 按钮数量统计
const buttonCount = computed(() => {
  let count = 0;
  const countButtons = (nodes) => {
    nodes.forEach((node) => {
      if (node.type === "button") {
        count++;
      }
      if (node.children) countButtons(node.children);
    });
  };
  countButtons(permissionTree.value);
  return count;
});

// 图标组件映射
const iconComponents = {
  DashboardOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined,
  AccountBookOutlined,
  TeamOutlined,
  SafetyCertificateOutlined,
  SettingOutlined,
};

// 获取模块中文名称
const getModuleChineseName = (moduleCode) => {
  const moduleNameMap = {
    'dashboard': '仪表盘',
    'purchase': '采购管理',
    'system': '系统管理',
    'acceptance': '验收管理',
    'reimbursement': '报销管理',
    'user': '用户管理',
    'role': '角色管理',
    'permission': '权限管理',
    'menu': '菜单管理',
    'dept': '部门管理',
    'dict': '字典管理',
    'log': '日志管理',
    'report': '报表管理',
    'profile': '个人中心',
    'other': '其他'
  };
  return moduleNameMap[moduleCode] || moduleCode;
};

// 获取模块图标
const getModuleIcon = (moduleName) => {
  const iconMap = {
    仪表盘: "DashboardOutlined",
    采购管理: "ShoppingCartOutlined",
    验收管理: "CheckCircleOutlined",
    财务管理: "AccountBookOutlined",
    组织管理: "TeamOutlined",
    权限管理: "SafetyCertificateOutlined",
    系统配置: "SettingOutlined",
  };
  const iconName = iconMap[moduleName] || "SettingOutlined";
  return iconComponents[iconName] || SettingOutlined;
};

// 获取树形节点图标
const getTreeNodeIcon = (record) => {
  if (record.type === 'directory') {
    return 'FolderOutlined';
  } else if (record.type === 'page') {
    return 'FileOutlined';
  } else if (record.type === 'button') {
    return 'SettingOutlined';
  } else if (record.type === 'module') {
    // 兼容旧的模块类型
    return getModuleIcon(record.module_chinese_name);
  }
  return 'SettingOutlined';
};

// 获取目录下的页面数量
const getChildPageCount = (record) => {
  if (!record.children || record.children.length === 0) return 0;
  return record.children.filter(child => child.type === 'page').length;
};

// 获取页面下的按钮数量
const getChildButtonCount = (record) => {
  if (!record.children || record.children.length === 0) return 0;
  return record.children.filter(child => child.type === 'button').length;
};

// eslint-disable-next-line no-unused-vars
const matrixData = computed(() => {
  const permissionRows = [];

  // 基于实际权限数据生成矩阵
  permissions.value.forEach((permission) => {
    const module = permission.module || permission.codename?.split('.')[0] || permission.codename?.split(':')[0] || 'other';

    const permissionRow = {
      id: permission.id,
      permission_name: permission.name,
      permission_key: permission.codename || permission.code,
      permission_description: permission.description || permission.name,
      module_name: module,
      module_chinese_name: permission.module_name || getModuleChineseName(module),
      module_icon: getModuleIcon(module),
      category: permission.category || 'page',
    };

    // 为每个角色添加权限状态
    if (matrixView.value === "roles") {
      roles.value.forEach((role) => {
        permissionRow[`role_${role.id}`] = hasRolePermission(role, permission.codename || permission.code);
      });
    } else {
      // 用户视图暂时不支持
      users.value.forEach((user) => {
        permissionRow[`user_${user.id}`] = false; // 用户权限通过角色管理
      });
    }

    permissionRows.push(permissionRow);
  });

  return permissionRows;
});

// 树形矩阵数据
const treeMatrixData = computed(() => {
  if (!permissionTree.value || permissionTree.value.length === 0) {
    return [];
  }

  const buildTreeNode = (node, level = 0) => {
    const treeNode = {
      key: node.key,
      permission_name: node.title,
      permission_description: node.title,
      type: node.type,
      level: level,
      permission_code: node.permission_code,
      permission_key: node.permission_code  // 兼容旧的属性名
    };

    // 根据节点类型处理权限信息
    if (node.type === 'directory') {
      // 目录节点：通常不需要权限，但可能有查看权限
      if (node.permission_code) {
        treeNode.id = node.permission_id;
        // 为目录权限添加角色权限状态
        if (matrixView.value === "roles") {
          roles.value.forEach((role) => {
            treeNode[`role_${role.id}`] = hasRolePermission(role, node.permission_code);
          });
        } else {
          users.value.forEach((user) => {
            treeNode[`user_${user.id}`] = false;
          });
        }
      } else {
        // 没有权限的目录节点不显示复选框
        if (matrixView.value === "roles") {
          roles.value.forEach((role) => {
            treeNode[`role_${role.id}`] = null;
          });
        } else {
          users.value.forEach((user) => {
            treeNode[`user_${user.id}`] = null;
          });
        }
      }
    } else if (node.type === 'page' || node.type === 'button') {
      // 页面和按钮节点：有具体的权限
      if (node.permission_code) {
        treeNode.id = node.permission_id;

        // 为权限节点添加角色权限状态
        if (matrixView.value === "roles") {
          roles.value.forEach((role) => {
            treeNode[`role_${role.id}`] = hasRolePermission(role, node.permission_code);
          });
        } else {
          users.value.forEach((user) => {
            treeNode[`user_${user.id}`] = false;
          });
        }
      } else {
        // 没有权限代码的节点不显示复选框
        if (matrixView.value === "roles") {
          roles.value.forEach((role) => {
            treeNode[`role_${role.id}`] = null;
          });
        } else {
          users.value.forEach((user) => {
            treeNode[`user_${user.id}`] = null;
          });
        }
      }
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      treeNode.children = node.children.map(child => buildTreeNode(child, level + 1));
    }

    return treeNode;
  };

  return permissionTree.value.map(module => buildTreeNode(module));
});

// 检查角色是否有指定权限
const hasRolePermission = (role, permissionCode) => {
  if (!role || !permissionCode) {
    return false;
  }

  // 首先检查permission_ids数组
  if (role.permission_ids && Array.isArray(role.permission_ids)) {
    const permission = permissions.value.find(p => p.code === permissionCode || p.codename === permissionCode);
    if (permission) {
      return role.permission_ids.includes(permission.id);
    }
  }

  // 备用检查：使用permissions数组
  if (role.permissions && Array.isArray(role.permissions)) {
    return role.permissions.some(perm =>
      perm.codename === permissionCode ||
      perm.code === permissionCode ||
      perm.id === permissionCode ||
      perm.key === permissionCode
    );
  }

  return false;
};

const matrixColumns = computed(() => {
  const baseColumns = [
    {
      title: "权限信息",
      dataIndex: "permission_name",
      width: 320,
      fixed: "left",
    },
    {
      title: "功能模块",
      dataIndex: "module_name",
      width: 140,
      fixed: "left",
    },
  ];

  // 添加角色/用户列
  const entities = matrixView.value === "roles" ? roles.value : users.value;
  const prefix = matrixView.value === "roles" ? "role" : "user";

  entities.forEach((entity) => {
    baseColumns.push({
      title: entity.name || entity.username,
      width: 100,
      align: "center",
      entity: entity,
      dataIndex: `${prefix}_${entity.id}`,
    });
  });

  return baseColumns;
});








const isPermissionDisabled = (entity) => {
  // 用户视图下，如果用户未激活则禁用
  if (matrixView.value === "users" && !entity.is_active) {
    return true;
  }

  // 角色视图下，如果角色未激活则禁用
  if (matrixView.value === "roles" && !entity.is_active) {
    return true;
  }

  return false;
};

// 权限更新函数（带层级联动）
// eslint-disable-next-line no-unused-vars
const updateRolePermission = async (roleId, permissionKey, enabled) => {
  try {
    // 找到对应的角色和权限
    const role = roles.value.find(r => r.id === roleId);
    const permission = permissions.value.find(p => p.code === permissionKey || p.codename === permissionKey);

    if (!role) {
      message.error(`角色不存在`);
      return;
    }

    if (!permission) {
      message.error(`权限 ${permissionKey} 不存在`);
      return;
    }

    // 从本地数据获取当前权限列表
    let permissionIds = [...(role.permission_ids || [])];

    // 简化权限更新逻辑：直接更新目标权限，后端会处理联动
    if (enabled) {
      if (!permissionIds.includes(permission.id)) {
        permissionIds.push(permission.id);
      }
    } else {
      permissionIds = permissionIds.filter(id => id !== permission.id);
    }

    // 使用权限更新API
    const response = await api.roles.updatePermissions(roleId, {
      permission_ids: permissionIds
    });

    if (response.code === 200) {
      const responseData = response.data || {};
      const finalPermissionIds = responseData.final_permissions || permissionIds;
      const autoAddedCount = responseData.auto_added_count || 0;

      let successMessage = "权限更新成功";
      if (autoAddedCount > 0) {
        successMessage += `，自动关联了 ${autoAddedCount} 个相关权限`;
      }
      message.success(successMessage);

      // 立即更新本地数据，使用后端返回的最终权限列表
      const roleIndex = roles.value.findIndex(r => r.id === roleId);
      if (roleIndex !== -1) {
        // 更新角色的权限数据
        roles.value[roleIndex].permission_ids = finalPermissionIds;
        roles.value[roleIndex].permissions = finalPermissionIds.map(id => {
          const perm = permissions.value.find(p => p.id === id);
          return perm ? { id: perm.id, codename: perm.code, name: perm.name } : null;
        }).filter(Boolean);
      }

      // 树形矩阵数据会自动更新（计算属性）

      // 强制刷新用户菜单缓存
      try {
        await api.menus.refreshUserMenus();
        console.log("✅ 用户菜单缓存已刷新");

        // 清除前端所有菜单缓存
        localStorage.removeItem('user_menus');
        localStorage.removeItem('dynamic_routes_cache');
        localStorage.removeItem('permission_menus');
        localStorage.removeItem('has_routes');
        sessionStorage.removeItem('user_menus');
        sessionStorage.removeItem('permission_menus');
        console.log("🗑️ 已清除前端菜单缓存");

        // 触发全局权限变更事件
        window.dispatchEvent(new CustomEvent('permission-changed', {
          detail: { roleId, permissionKey, enabled }
        }));
        console.log("📡 已触发权限变更事件");

        // 提示用户刷新页面
        message.info("权限已更新，建议刷新页面以查看最新菜单", 5);
      } catch (error) {
        console.warn("⚠️ 刷新菜单缓存失败:", error);
      }
    } else {
      message.error(response.message || "权限更新失败");
    }
  } catch (error) {
    console.error("更新角色权限失败:", error);
    message.error("权限更新失败");
  }
};

// 权限联动逻辑已移至后端处理，简化前端代码

const updateUserPermission = async () => {
  message.warning(
    "当前系统基于角色权限管理，请通过角色管理页面为用户分配角色来管理权限"
  );
};

// 获取复选框的半选状态
const getCheckboxIndeterminate = (record, column) => {
  // 只有页面类型的节点才可能有半选状态
  if (record.type !== 'page' || !record.children || record.children.length === 0) {
    return false;
  }

  const entityKey = `${matrixView.value === "roles" ? "role" : "user"}_${column.entity.id}`;

  // 如果页面本身已经被勾选，不显示半选状态
  if (record[entityKey] === true) {
    return false;
  }

  // 检查子节点的勾选状态
  const childStates = record.children.map(child => child[entityKey]);
  const checkedCount = childStates.filter(state => state === true).length;
  const totalCount = childStates.filter(state => state !== null).length;

  // 只有在页面未勾选但有子按钮勾选时才显示半选状态
  return checkedCount > 0 && checkedCount < totalCount && record[entityKey] !== true;
};

// 查找按钮的父页面
const findParentPage = (buttonRecord) => {
  // 在树形数据中查找父页面
  const findInTree = (nodes) => {
    for (const node of nodes) {
      if (node.children) {
        for (const child of node.children) {
          if (child.key === buttonRecord.key) {
            return node.type === 'page' ? node : null;
          }
        }
        const result = findInTree(node.children);
        if (result) return result;
      }
    }
    return null;
  };

  return findInTree(treeMatrixData.value);
};

// 单个权限更新（无级联）
// eslint-disable-next-line no-unused-vars
const updateSingleRolePermission = async (roleId, record, enabled) => {
  try {
    if (!record.permission_key) {
      message.error('权限代码不存在');
      return;
    }

    const role = roles.value.find(r => r.id === roleId);
    const permission = permissions.value.find(p => p.code === record.permission_key || p.codename === record.permission_key);

    if (!role) {
      message.error('角色不存在');
      return;
    }

    if (!permission) {
      message.error(`权限 ${record.permission_key} 不存在`);
      return;
    }

    // 获取当前权限列表
    let currentPermissionIds = [...(role.permission_ids || [])];

    // 只更新当前权限，不做任何级联
    if (enabled) {
      if (!currentPermissionIds.includes(permission.id)) {
        currentPermissionIds.push(permission.id);
      }
    } else {
      currentPermissionIds = currentPermissionIds.filter(id => id !== permission.id);
    }

    // 调用API更新权限
    const response = await api.roles.updatePermissions(roleId, {
      permission_ids: currentPermissionIds
    });

    if (response.code === 200) {
      message.success("权限更新成功");

      // 更新本地数据
      const roleIndex = roles.value.findIndex(r => r.id === roleId);
      if (roleIndex !== -1) {
        const responseData = response.data || {};
        const finalPermissionIds = responseData.final_permissions || currentPermissionIds;

        roles.value[roleIndex].permission_ids = finalPermissionIds;
        roles.value[roleIndex].permissions = finalPermissionIds.map(id => {
          const perm = permissions.value.find(p => p.id === id);
          return perm ? { id: perm.id, codename: perm.code, name: perm.name } : null;
        }).filter(Boolean);
      }

      // 刷新缓存
      await refreshUserMenuCache(roleId, record.permission_key, enabled);
    } else {
      message.error(response.message || "权限更新失败");
    }
  } catch (error) {
    console.error("权限更新失败:", error);
    message.error("权限更新失败");
  }
};

// 优化的级联权限更新逻辑（区分主动和被动勾选）
const updateRolePermissionWithNewCascade = async (roleId, record, enabled, isPassive = false) => {
  try {
    const permissionsToUpdate = [];

    if (record.type === 'page') {
      // 页面权限处理
      if (record.permission_key) {
        permissionsToUpdate.push({ key: record.permission_key, enabled });
      }

      if (enabled && !isPassive) {
        // 主动勾选页面权限：勾选所有子按钮权限
        if (record.children && record.children.length > 0) {
          record.children.forEach(child => {
            if (child.permission_key && child.type === 'button') {
              permissionsToUpdate.push({ key: child.permission_key, enabled: true });
            }
          });
        }
      } else if (!enabled) {
        // 取消页面权限：取消所有子按钮权限
        if (record.children && record.children.length > 0) {
          record.children.forEach(child => {
            if (child.permission_key && child.type === 'button') {
              permissionsToUpdate.push({ key: child.permission_key, enabled: false });
            }
          });
        }
      }
      // 被动勾选页面权限时（isPassive=true），不影响子按钮权限

    } else if (record.type === 'button') {
      // 按钮权限处理
      if (record.permission_key) {
        permissionsToUpdate.push({ key: record.permission_key, enabled });
      }

      // 勾选子按钮权限：自动勾选父页面权限（被动勾选）
      if (enabled) {
        const parentPage = findParentPage(record);
        if (parentPage && parentPage.permission_key) {
          const entityKey = `role_${roleId}`;
          // 检查父页面是否已经勾选
          if (!parentPage[entityKey]) {
            // 被动勾选父页面，不影响其他子按钮
            permissionsToUpdate.push({ key: parentPage.permission_key, enabled: true });
          }
        }
      }
      // 取消勾选子按钮权限时，不影响父页面或任何同级按钮权限

    } else if (record.type === 'directory') {
      // 目录权限：简单更新，不做级联
      if (record.permission_key) {
        permissionsToUpdate.push({ key: record.permission_key, enabled });
      }
    }

    // 批量更新权限
    await batchUpdateRolePermissions(roleId, permissionsToUpdate);

  } catch (error) {
    console.error("权限更新失败:", error);
    message.error("权限更新失败");
  }
};

// 批量更新角色权限
const batchUpdateRolePermissions = async (roleId, permissionsToUpdate) => {
  if (!permissionsToUpdate || permissionsToUpdate.length === 0) {
    return;
  }

  try {
    const role = roles.value.find(r => r.id === roleId);
    if (!role) {
      message.error('角色不存在');
      return;
    }

    // 获取当前权限列表
    let currentPermissionIds = [...(role.permission_ids || [])];

    // 批量处理权限变更
    for (const { key, enabled } of permissionsToUpdate) {
      const permission = permissions.value.find(p => p.code === key || p.codename === key);
      if (permission) {
        if (enabled) {
          if (!currentPermissionIds.includes(permission.id)) {
            currentPermissionIds.push(permission.id);
          }
        } else {
          currentPermissionIds = currentPermissionIds.filter(id => id !== permission.id);
        }
      }
    }

    // 一次性更新所有权限
    const response = await api.roles.updatePermissions(roleId, {
      permission_ids: currentPermissionIds
    });

    if (response.code === 200) {
      message.success("权限更新成功");

      // 更新本地数据
      const roleIndex = roles.value.findIndex(r => r.id === roleId);
      if (roleIndex !== -1) {
        const responseData = response.data || {};
        const finalPermissionIds = responseData.final_permissions || currentPermissionIds;

        roles.value[roleIndex].permission_ids = finalPermissionIds;
        roles.value[roleIndex].permissions = finalPermissionIds.map(id => {
          const perm = permissions.value.find(p => p.id === id);
          return perm ? { id: perm.id, codename: perm.code, name: perm.name } : null;
        }).filter(Boolean);
      }

      // 刷新缓存
      await refreshUserMenuCache(roleId, permissionsToUpdate[0]?.key, permissionsToUpdate[0]?.enabled);
    } else {
      message.error(response.message || "权限更新失败");
    }
  } catch (error) {
    console.error("批量权限更新失败:", error);
    message.error("权限更新失败");
  }
};

// 权限更新主函数（优化的级联逻辑）
const updateMatrixPermission = async (item, record, enabled) => {
  if (matrixView.value === "roles") {
    // 用户直接点击，属于主动操作
    await updateRolePermissionWithNewCascade(item.id, record, enabled, false);
  } else {
    await updateUserPermission();
  }
};



// 刷新用户菜单缓存
const refreshUserMenuCache = async (roleId, permissionKey, enabled) => {
  try {
    await api.menus.refreshUserMenus();
    console.log("✅ 用户菜单缓存已刷新");

    // 清除前端所有菜单缓存
    localStorage.removeItem('user_menus');
    localStorage.removeItem('dynamic_routes_cache');
    localStorage.removeItem('permission_menus');
    localStorage.removeItem('has_routes');
    sessionStorage.removeItem('user_menus');
    sessionStorage.removeItem('permission_menus');
    console.log("🗑️ 已清除前端菜单缓存");

    // 触发全局权限变更事件
    window.dispatchEvent(new CustomEvent('permission-changed', {
      detail: { roleId, permissionKey, enabled }
    }));
    console.log("📡 已触发权限变更事件");

    // 提示用户刷新页面
    message.info("权限已更新，建议刷新页面以查看最新菜单", 5);
  } catch (error) {
    console.warn("⚠️ 刷新菜单缓存失败:", error);
  }
};







const getUsers = async () => {
  try {
    loading.value = true;
    const response = await api.users.getList();
    if (response.code === 200) {
      const userList = response.data.results || response.data || [];

      // 批量获取用户权限信息，避免多次请求
      const userPermissionPromises = userList.map(async (user) => {
        try {
          const permResponse = await api.permissions.getUserPermissionsList(user.id);
          if (permResponse.code === 200) {
            user.permissions = permResponse.data || [];
          } else {
            user.permissions = [];
          }
        } catch (error) {
          console.warn(`获取用户 ${user.id} 权限失败:`, error);
          user.permissions = [];
        }

        // 如果用户有角色信息，批量获取角色权限
        if (user.roles && user.roles.length > 0) {
          const rolePermissionPromises = user.roles.map(async (role) => {
            try {
              const rolePermResponse = await api.roles.getPermissions(role.id);
              if (rolePermResponse.code === 200) {
                role.permissions = rolePermResponse.data || [];
              } else {
                role.permissions = [];
              }
            } catch (error) {
              console.warn(`获取角色 ${role.id} 权限失败:`, error);
              role.permissions = [];
            }
            return role;
          });
          await Promise.all(rolePermissionPromises);
        }
        return user;
      });

      // 等待所有用户权限请求完成
      await Promise.all(userPermissionPromises);
      users.value = userList;
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
    message.error("获取用户数据失败");
  } finally {
    loading.value = false;
  }
};



// 获取菜单类型图标
const getMenuTypeIcon = (menuType) => {
  const iconMap = {
    'directory': 'FolderOutlined',
    'page': 'FileOutlined',
    'button': 'SettingOutlined'
  };
  return iconMap[menuType] || 'SettingOutlined';
};

// 递归构建子节点
const buildTreeChildren = (children) => {
  if (!children || children.length === 0) return [];

  return children.map(child => ({
    title: child.name,
    key: child.key,
    type: child.type,
    menu_id: child.menu_id,
    menu_code: child.menu_code,
    permission_id: child.id,
    permission_code: child.code,
    permission_name: child.permission_name || child.name,
    children: buildTreeChildren(child.children)
  }));
};

// 从树形数据中提取权限列表
const extractPermissionsFromTree = (treeData) => {
  const permissions = [];

  const extractFromNode = (node) => {
    // 如果节点有权限代码，添加到权限列表
    if (node.code) {
      permissions.push({
        id: node.id,
        name: node.name || node.permission_name,
        code: node.code,
        category: node.type === 'button' ? 'button' : 'page',
        module: node.code.split(':')[0] || 'system',
        module_name: getModuleChineseName(node.code.split(':')[0] || 'system'),
        description: node.description || node.name
      });
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => extractFromNode(child));
    }
  };

  treeData.forEach(rootNode => extractFromNode(rootNode));
  return permissions;
};

// 使用统一权限矩阵API获取所有数据
const loadMatrixData = async () => {
  try {
    loading.value = true;

    // 调用统一权限矩阵API
    const response = await api.permissions.getMatrixData();

    if (response.code === 200) {
      const data = response.data;

      // 从tree_data中提取权限数据
      permissions.value = extractPermissionsFromTree(data.tree_data || []);

      // 设置角色数据
      roles.value = (data.roles || []).map(role => ({
        ...role,
        permissions: (role.permission_ids || []).map(id => {
          const perm = permissions.value.find(p => p.id === id);
          return perm ? { id: perm.id, codename: perm.code, name: perm.name } : null;
        }).filter(Boolean)
      }));

      // 设置基于菜单树的权限数据
      if (data.tree_data) {
        permissionTree.value = data.tree_data.map(menuNode => ({
          title: menuNode.name,
          key: menuNode.key,
          type: menuNode.type,
          menu_id: menuNode.menu_id,
          menu_code: menuNode.menu_code,
          permission_id: menuNode.id,
          permission_code: menuNode.code,
          permission_name: menuNode.permission_name || menuNode.name,
          icon: getMenuTypeIcon(menuNode.type),
          children: buildTreeChildren(menuNode.children)
        }));
      }



      console.log("权限矩阵数据加载成功:", {
        permissions: permissions.value.length,
        roles: roles.value.length,
        tree: permissionTree.value.length
      });

    } else {
      message.error(response.message || "获取权限矩阵数据失败");
    }
  } catch (error) {
    console.error("加载权限矩阵数据失败:", error);
    message.error("加载权限数据失败");
  } finally {
    loading.value = false;
  }
};

// 防抖刷新函数
let refreshTimer = null;
const refreshData = async (force = false) => {
  // 防抖处理，避免频繁刷新
  if (refreshTimer) {
    clearTimeout(refreshTimer);
  }

  refreshTimer = setTimeout(async () => {
    await loadMatrixData();

    if (force) {
      message.success("数据刷新成功");
    }
  }, force ? 0 : 300); // 强制刷新时立即执行，否则延迟300ms
};

const exportMatrix = () => {
  try {
    // 准备导出数据
    const exportData = [];
    const entities = matrixView.value === "roles" ? roles.value : users.value;
    const entityPrefix = matrixView.value === "roles" ? "role" : "user";

    // 添加表头
    const headers = ["权限类型", "权限名称", "功能模块", "权限代码"];
    entities.forEach((entity) => {
      headers.push(entity.name || entity.username);
    });
    exportData.push(headers);

    // 递归收集所有权限数据
    const collectPermissionData = (nodes, level = 0) => {
      nodes.forEach((node) => {
        // 添加当前节点数据
        const indent = "  ".repeat(level);
        const typeLabel = node.type === 'directory' ? '📁目录' :
                         node.type === 'page' ? '📄页面' :
                         node.type === 'button' ? '🔘按钮' : '其他';

        const row = [
          typeLabel,
          indent + node.permission_name,
          node.type === 'directory' ? node.permission_name :
          getModuleChineseName(node.permission_key?.split(':')[0]) || '未分类',
          node.permission_key || ''
        ];

        // 添加每个实体的权限状态
        entities.forEach((entity) => {
          const fieldName = `${entityPrefix}_${entity.id}`;
          const hasPermission = node[fieldName];
          row.push(hasPermission === true ? "✓" : hasPermission === false ? "✗" : "—");
        });

        exportData.push(row);

        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          collectPermissionData(node.children, level + 1);
        }
      });
    };

    // 从树形数据收集权限
    collectPermissionData(treeMatrixData.value);

    // 转换为CSV格式
    const csvContent = exportData
      .map((row) => row.map((cell) => `"${cell || ""}"`).join(","))
      .join("\n");

    // 创建下载链接
    const blob = new Blob(["\uFEFF" + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `权限矩阵_${
        matrixView.value === "roles" ? "角色视图" : "用户视图"
      }_${new Date().toISOString().slice(0, 10)}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success("权限矩阵导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    message.error("导出失败，请稍后重试");
  }
};

// 监听视图切换，优化数据加载
watch(matrixView, async (newView, oldView) => {
  if (newView !== oldView) {
    // 角色视图数据已经在loadMatrixData中加载
    // 用户视图需要单独加载
    if (newView === "users" && users.value.length === 0) {
      await getUsers();
    }
  }
});

// 页面初始化
onMounted(async () => {
  await loadMatrixData();
});
</script>

<style scoped>
.permission-management {
  padding: 0;
  background: transparent;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-md);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  letter-spacing: 0.5px;
}

.page-title .anticon {
  font-size: var(--text-3xl);
}

.page-subtitle {
  font-size: var(--text-base);
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: var(--space-xl);
}

.stat-item {
  text-align: center;
  padding: var(--space-md) var(--space-lg);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.9;
  font-weight: 500;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) var(--space-xl);
  margin-bottom: var(--space-md);
  flex-shrink: 0;
}

.action-left {
  display: flex;
  gap: var(--space-md);
}

.action-right {
  display: flex;
  align-items: center;
}

.primary-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.secondary-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.secondary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-overlay);
}

/* 表格区域 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 表格样式优化 */
:deep(.business-table) {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

:deep(.business-table .ant-table-thead > tr > th) {
  background: linear-gradient(
    135deg,
    var(--bg-tertiary) 0%,
    var(--bg-primary) 100%
  );
  border-bottom: 2px solid var(--border-light);
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--text-sm);
  letter-spacing: 0.3px;
  padding: var(--space-lg) var(--space-md);
}

:deep(.business-table .ant-table-tbody > tr:hover > td) {
  background: var(--bg-overlay);
}

:deep(.business-table .ant-table-tbody > tr > td) {
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-light);
  font-size: var(--text-sm);
  color: var(--text-primary);
}

/* 权限矩阵特殊样式 */
.matrix-table {
  border-radius: var(--radius-lg);
  flex: 1;
}

/* 自定义滚动条样式 */
:deep(.ant-table-body) {
  /* 默认隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

:deep(.ant-table-body::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.ant-table-body::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.ant-table-body::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 4px;
  transition: background 0.3s ease;
}

/* 鼠标悬停时显示滚动条 */
:deep(.ant-table-body:hover::-webkit-scrollbar-thumb) {
  background: rgba(0, 0, 0, 0.2);
}

:deep(.ant-table-body:hover::-webkit-scrollbar-thumb:hover) {
  background: rgba(0, 0, 0, 0.4);
}

/* 表格容器样式 */
:deep(.ant-table-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.ant-table-content) {
  flex: 1;
  overflow: auto;
}

:deep(.matrix-table .ant-table-thead > tr > th) {
  text-align: center;
  font-size: var(--text-xs);
  padding: var(--space-sm) var(--space-xs);
  min-width: 80px;
}

:deep(.matrix-table .ant-table-tbody > tr > td) {
  text-align: center;
  padding: var(--space-sm) var(--space-xs);
}

.name-cell {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  text-align: left;
}

.name-info {
  flex: 1;
}

.name-primary {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.name-secondary {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-top: 2px;
}

/* 复选框样式 */
:deep(.ant-checkbox-wrapper) {
  display: flex;
  justify-content: center;
}

:deep(.ant-checkbox) {
  transform: scale(1.2);
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.ant-checkbox:hover .ant-checkbox-inner) {
  border-color: var(--primary-color);
}

/* 单选按钮组样式 */
:deep(.ant-radio-group) {
  border-radius: var(--radius-sm);
  overflow: hidden;
}

:deep(.ant-radio-button-wrapper) {
  border-color: var(--border-medium);
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.ant-radio-button-wrapper:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

:deep(.ant-radio-button-wrapper-checked) {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  color: var(--text-inverse);
}

:deep(.ant-radio-button-wrapper-checked:hover) {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
  color: var(--text-inverse);
}

/* 分页样式 */
:deep(.ant-pagination) {
  margin: var(--space-xl) 0 0 0;
  text-align: center;
}

:deep(.ant-pagination .ant-pagination-item) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s;
}

:deep(.ant-pagination .ant-pagination-item:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

:deep(.ant-pagination .ant-pagination-item-active) {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
}

:deep(.ant-pagination .ant-pagination-item-active a) {
  color: var(--text-inverse);
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-lg);
  }

  .header-stats {
    justify-content: center;
  }

  .action-bar {
    flex-direction: column;
    gap: var(--space-md);
  }

  .action-left {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .permission-management {
    height: 100vh;
    padding: 0;
  }

  .action-bar {
    padding: var(--space-sm) var(--space-md);
    margin-bottom: var(--space-sm);
  }

  .page-header {
    padding: var(--space-md);
    margin-bottom: var(--space-sm);
  }

  .page-title {
    font-size: var(--text-2xl);
  }

  .header-stats {
    gap: var(--space-md);
  }

  .stat-item {
    padding: var(--space-md) var(--space-md);
    min-width: 80px;
  }

  :deep(.matrix-table .ant-table-thead > tr > th) {
    font-size: 10px;
    padding: var(--space-xs);
    min-width: 60px;
  }

  .table-section {
    margin: 0 var(--space-xs);
  }
}
</style>
