# Generated by Django 5.2.4 on 2025-07-26 12:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system", "0019_auto_20250726_1929"),
    ]

    operations = [
        # 注释掉删除不存在字段的操作
        # migrations.RemoveField(
        #     model_name="permission",
        #     name="menu",
        # ),
        # migrations.RemoveField(
        #     model_name="permission",
        #     name="permission_type",
        # ),
        migrations.AddField(
            model_name="menu",
            name="always_show",
            field=models.BooleanField(
                default=False,
                help_text="是否总是显示：False-否，True-是",
                verbose_name="是否总是显示",
            ),
        ),
        migrations.AddField(
            model_name="menu",
            name="hidden",
            field=models.BooleanField(
                default=False,
                help_text="是否隐藏：False-显示，True-隐藏",
                verbose_name="是否隐藏",
            ),
        ),
        migrations.AddField(
            model_name="menu",
            name="keep_alive",
            field=models.BooleanField(
                default=False,
                help_text="是否缓存路由：False-否，True-是",
                verbose_name="是否缓存路由",
            ),
        ),
        migrations.AddField(
            model_name="menu",
            name="redirect",
            field=models.CharField(
                blank=True,
                help_text="重定向路径，用于目录菜单",
                max_length=200,
                null=True,
                verbose_name="重定向路径",
            ),
        ),
        migrations.AddField(
            model_name="permission",
            name="category",
            field=models.CharField(
                choices=[
                    ("page", "页面权限"),
                    ("button", "按钮权限"),
                    ("api", "API权限"),
                    ("data", "数据权限"),
                ],
                default="page",
                max_length=20,
                verbose_name="权限类型",
            ),
        ),
        migrations.AddField(
            model_name="permission",
            name="is_system",
            field=models.BooleanField(default=False, verbose_name="是否系统权限"),
        ),
        migrations.AddField(
            model_name="permission",
            name="module_name",
            field=models.CharField(
                blank=True, max_length=100, null=True, verbose_name="模块中文名称"
            ),
        ),
        migrations.AlterModelTable(
            name="rolepermission",
            table="sys_role_permission",
        ),
    ]
