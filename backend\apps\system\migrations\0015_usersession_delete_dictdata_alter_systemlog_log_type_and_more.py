# Generated by Django 5.2.4 on 2025-07-21 13:15

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system", "0014_notification_retry_count"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "session_key",
                    models.CharField(max_length=40, unique=True, verbose_name="会话键"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(blank=True, null=True, verbose_name="用户代理"),
                ),
                (
                    "last_activity",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="最后活动时间"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否活跃"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="创建时间"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户会话",
                "verbose_name_plural": "用户会话",
                "db_table": "user_session",
                "ordering": ["-last_activity"],
            },
        ),

        migrations.AlterField(
            model_name="systemlog",
            name="log_type",
            field=models.CharField(
                choices=[
                    ("login", "用户登录"),
                    ("logout", "用户登出"),
                    ("create", "创建操作"),
                    ("update", "更新操作"),
                    ("delete", "删除操作"),
                    ("approve", "审批操作"),
                    ("reject", "驳回操作"),
                    ("return", "退回操作"),
                    ("purchase", "采购操作"),
                    ("accept", "验收操作"),
                    ("reimburse", "报销操作"),
                    ("export", "导出操作"),
                    ("import", "导入操作"),
                    ("error", "错误日志"),
                    ("warning", "警告日志"),
                    ("info", "信息日志"),
                    ("debug", "调试日志"),
                    ("other", "其他操作"),
                ],
                max_length=20,
                verbose_name="日志类型",
            ),
        ),
        migrations.AddIndex(
            model_name="usersession",
            index=models.Index(
                fields=["user", "is_active"], name="user_sessio_user_id_4f7411_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="usersession",
            index=models.Index(
                fields=["last_activity", "is_active"],
                name="user_sessio_last_ac_40fe9f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="usersession",
            index=models.Index(
                fields=["session_key"], name="user_sessio_session_ab559f_idx"
            ),
        ),
    ]
