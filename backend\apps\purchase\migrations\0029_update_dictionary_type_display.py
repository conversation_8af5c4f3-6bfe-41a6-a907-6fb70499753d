# Generated by Django 5.2.4 on 2025-07-17 11:27

from django.db import migrations


def update_dictionary_type_display(apps, schema_editor):
    """更新字典类型显示名称并清理重复数据"""
    Dictionary = apps.get_model('purchase', 'Dictionary')

    # 类型映射表
    type_display_mapping = {
        'status': '状态',
        'purchase_type': '采购类型',
        'procurement_method': '采购方式',
        'fund_project': '经费项目',
        'unit': '计量单位',
        'item_category': '物品种类',
        'requirement_source': '需求来源',
        'urgency_level': '紧急程度',
        # 处理旧的中文类型名称
        '单位': '计量单位',
        '物品分类': '物品种类',
        '经费来源': '经费项目',
        '采购方式': '采购方式',
    }

    # 类型名称标准化映射（将旧的中文类型名称转换为英文）
    type_normalize_mapping = {
        '单位': 'unit',
        '物品分类': 'item_category',
        '经费来源': 'fund_project',
        '采购方式': 'procurement_method',
    }

    # 先标准化类型名称（将中文类型名称转换为英文）
    for old_type, new_type in type_normalize_mapping.items():
        Dictionary.objects.filter(type=old_type).update(type=new_type)

    # 更新type_display字段
    for type_code, display_name in type_display_mapping.items():
        Dictionary.objects.filter(type=type_code).update(type_display=display_name)

    # 清理重复数据 - 保留最新的记录
    from django.db.models import Count

    # 找出重复的记录
    duplicates = Dictionary.objects.values('type', 'code').annotate(
        count=Count('id')
    ).filter(count__gt=1)

    for duplicate in duplicates:
        # 获取所有重复记录，按创建时间排序，保留最新的
        duplicate_records = Dictionary.objects.filter(
            type=duplicate['type'],
            code=duplicate['code']
        ).order_by('created_at')

        # 删除除最新记录外的所有重复记录
        for record in duplicate_records[:-1]:
            record.delete()


def reverse_update_dictionary_type_display(apps, schema_editor):
    """回滚操作"""
    Dictionary = apps.get_model('purchase', 'Dictionary')
    Dictionary.objects.all().update(type_display='未分类')


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0028_optimize_dictionary_structure"),
    ]

    operations = [
        migrations.RunPython(
            update_dictionary_type_display,
            reverse_update_dictionary_type_display
        ),
    ]
