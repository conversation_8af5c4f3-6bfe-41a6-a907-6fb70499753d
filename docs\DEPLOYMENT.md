# 采购管理系统部署指南

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)   │    │  后端 (Django)  │    │  数据库 (MySQL) │
│   Port: 8080    │───▶│   Port: 8000    │───▶│   Port: 3306    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  缓存 (Redis)   │
                       │   Port: 6379    │
                       └─────────────────┘
```

## 环境要求

### 开发环境
- Python 3.8+
- Node.js 14+
- MySQL 8.0+
- Redis 6.0+

### 生产环境
- Ubuntu 20.04+ / CentOS 8+
- Nginx 1.18+
- Gunicorn
- Supervisor (进程管理)

## 快速部署

### 1. 使用Docker Compose（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd purchase

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 2. 手动部署

#### 后端部署

```bash
# 1. 创建项目目录
sudo mkdir -p /var/www/purchase
cd /var/www/purchase

# 2. 克隆代码
git clone <repository-url> .

# 3. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 4. 安装依赖
cd backend
pip install -r requirements.txt

# 5. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库等信息

# 6. 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 7. 创建超级用户
python manage.py createsuperuser

# 8. 收集静态文件
python manage.py collectstatic --noinput

# 9. 初始化数据
python scripts/init_data.py
```

#### 前端部署

```bash
# 1. 安装Node.js依赖
cd frontend
npm install

# 2. 构建生产版本
npm run build

# 3. 部署到Web服务器
sudo cp -r dist/* /var/www/html/
```

## 生产环境配置

### 1. Nginx配置

创建 `/etc/nginx/sites-available/purchase`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 管理后台
    location /admin/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件
    location /static/ {
        alias /var/www/purchase/backend/staticfiles/;
    }
    
    # 媒体文件
    location /media/ {
        alias /var/www/purchase/backend/media/;
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/purchase /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 2. Gunicorn配置

创建 `/var/www/purchase/backend/gunicorn.conf.py`：

```python
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
user = "www-data"
group = "www-data"
tmp_upload_dir = None
errorlog = "/var/log/gunicorn/error.log"
accesslog = "/var/log/gunicorn/access.log"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
```

### 3. Supervisor配置

创建 `/etc/supervisor/conf.d/purchase.conf`：

```ini
[program:purchase-backend]
command=/var/www/purchase/venv/bin/gunicorn purchase_system.wsgi:application -c gunicorn.conf.py
directory=/var/www/purchase/backend
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/purchase-backend.log

[program:purchase-celery]
command=/var/www/purchase/venv/bin/celery -A purchase_system worker -l info
directory=/var/www/purchase/backend
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/purchase-celery.log
```

启动服务：
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start purchase-backend
sudo supervisorctl start purchase-celery
```

### 4. 数据库优化

MySQL配置优化 (`/etc/mysql/mysql.conf.d/mysqld.cnf`)：

```ini
[mysqld]
# 基本配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 性能优化
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 连接配置
max_connections = 200
max_connect_errors = 10000
```

### 5. Redis配置

Redis配置优化 (`/etc/redis/redis.conf`)：

```
# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 安全配置
requirepass your-redis-password
```

## 监控和维护

### 1. 日志管理

```bash
# 创建日志目录
sudo mkdir -p /var/log/purchase
sudo chown www-data:www-data /var/log/purchase

# 配置日志轮转
sudo tee /etc/logrotate.d/purchase << EOF
/var/log/purchase/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF
```

### 2. 备份策略

创建备份脚本 `/usr/local/bin/backup-purchase.sh`：

```bash
#!/bin/bash
BACKUP_DIR="/backup/purchase"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u root -p purchase_system > $BACKUP_DIR/db_$DATE.sql

# 媒体文件备份
tar -czf $BACKUP_DIR/media_$DATE.tar.gz /var/www/purchase/backend/media/

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

设置定时任务：
```bash
sudo crontab -e
# 每天凌晨2点备份
0 2 * * * /usr/local/bin/backup-purchase.sh
```

### 3. 性能监控

安装监控工具：
```bash
# 安装htop
sudo apt install htop

# 安装iotop
sudo apt install iotop

# 安装MySQL监控
sudo apt install mytop
```

### 4. 安全配置

```bash
# 配置防火墙
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# 禁用不必要的服务
sudo systemctl disable apache2
sudo systemctl stop apache2

# 配置fail2ban
sudo apt install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   sudo systemctl status mysql
   
   # 检查连接配置
   mysql -u root -p -e "SHOW DATABASES;"
   ```

2. **静态文件404**
   ```bash
   # 重新收集静态文件
   cd /var/www/purchase/backend
   source ../venv/bin/activate
   python manage.py collectstatic --noinput
   ```

3. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R www-data:www-data /var/www/purchase
   sudo chmod -R 755 /var/www/purchase
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 重启服务释放内存
   sudo supervisorctl restart purchase-backend
   ```

### 日志查看

```bash
# Django应用日志
tail -f /var/www/purchase/backend/logs/django.log

# Nginx日志
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log

# Supervisor日志
tail -f /var/log/supervisor/purchase-backend.log

# 系统日志
journalctl -u nginx -f
```

## 更新部署

```bash
# 1. 备份当前版本
cp -r /var/www/purchase /var/www/purchase_backup_$(date +%Y%m%d)

# 2. 拉取最新代码
cd /var/www/purchase
git pull origin main

# 3. 更新后端
cd backend
source ../venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py collectstatic --noinput

# 4. 更新前端
cd ../frontend
npm install
npm run build
sudo cp -r dist/* /var/www/html/

# 5. 重启服务
sudo supervisorctl restart purchase-backend
sudo systemctl reload nginx
```

## 性能优化建议

1. **数据库优化**
   - 定期分析慢查询日志
   - 添加适当的索引
   - 定期优化表结构

2. **缓存策略**
   - 使用Redis缓存频繁查询的数据
   - 实施页面缓存
   - 使用CDN加速静态资源

3. **代码优化**
   - 使用数据库连接池
   - 优化查询语句
   - 实施异步任务处理

4. **服务器优化**
   - 调整Gunicorn worker数量
   - 优化Nginx配置
   - 监控系统资源使用情况
