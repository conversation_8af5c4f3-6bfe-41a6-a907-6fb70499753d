/**
 * 采购管理系统 - 基础路由配置
 * 
 * 只包含基础页面路由，业务页面通过动态路由加载
 */
import Login from '@/views/auth/Login.vue'
import Layout from '@/views/layout/Layout.vue'

export default [
  // 登录页面
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  
  // 主布局
  {
    path: '/',
    name: 'Layout',
    component: Layout,
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      // 仪表盘（基础页面）
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Dashboard.vue'),
        meta: {
          title: '仪表盘',
          requiresAuth: true
        }
      },
      // 个人中心已移动到动态路由
      // 采购详情页面（复用页面，支持所有采购阶段）
      {
        path: 'purchase/requests/:id',
        name: 'PurchaseRequestDetail',
        component: () => import('@/views/purchase/requests/Detail.vue'),
        meta: {
          title: '采购详情',
          requiresAuth: true
        }
      }, // 采购编辑页面（复用页面，支持所有采购阶段）
      {
        path: 'purchase/requests/new',
        name: 'PurchaseRequestForm',
        component: () => import('@/views/purchase/requests/Form.vue'),
        meta: {
          title: '新建采购',
          requiresAuth: true
        }
      }
    ]
  },
  
  // 403 禁止访问
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '禁止访问',
      requiresAuth: false
    }
  },
  
  // 注意：404通配符路由移到动态路由添加后再添加，避免优先级问题
]
