# 采购管理系统后端

启动前端：
cd frontend;npm run serve
npm run serve
启动后端：
cd backend;daphne -b 0.0.0.0 -p 8000 purchase_system.asgi:application
daphne -b 0.0.0.0 -p 8000 purchase_system.asgi:application

        uvicorn purchase_system.asgi:application --host 0.0.0.0 --port 8000 --log-level info





基于Django + Django REST Framework开发的企业采购流程管理系统后端API。

## 功能特性

### 核心模块
- **认证系统** - JWT认证、用户管理、权限控制
- **部门管理** - 两级部门结构（分公司-办事处）
- **数据字典** - 采购方式、经费来源等配置管理
- **采购需求** - 需求提交、审批、跟踪、导出
- **物品验收** - 验收记录、照片上传、水印处理
- **结算报销** - 财务结算、凭证管理

### 技术特性
- RESTful API设计
- 统一响应格式
- 分页和筛选支持
- Excel导入导出
- 图片水印处理
- 权限控制
- API文档自动生成

## 技术栈

- **框架**: Django 4.2 + Django REST Framework 3.14
- **数据库**: MySQL 8.0
- **认证**: JWT (Simple JWT)
- **缓存**: Redis
- **任务队列**: Celery
- **文档**: drf-spectacular (Swagger)
- **图片处理**: Pillow
- **Excel处理**: xlsxwriter, openpyxl

## 项目结构

```
backend/
├── purchase_system/          # Django项目配置
│   ├── settings.py          # 项目设置
│   ├── urls.py              # 主URL配置
│   └── wsgi.py              # WSGI配置
├── apps/                    # 应用模块
│   ├── authentication/      # 认证模块
│   ├── system/              # 系统管理模块
│   ├── purchase/            # 采购管理模块
│   └── common/              # 通用工具模块
├── scripts/                 # 脚本文件
│   └── init_data.py         # 初始化数据脚本
├── media/                   # 媒体文件目录
├── static/                  # 静态文件目录
├── logs/                    # 日志目录
├── requirements.txt         # Python依赖
├── manage.py               # Django管理脚本
└── start.py                # 项目启动脚本
```

## 快速开始

### 1. 环境要求

- Python 3.8+
- MySQL 8.0+
- Redis 6.0+

### 2. 安装依赖

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置数据库

```bash
# 创建MySQL数据库
mysql -u root -p
CREATE DATABASE purchase_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
# 修改数据库连接信息等配置
```

### 5. 启动项目

```bash
# 使用启动脚本（推荐）
python start.py

# 或手动启动
python manage.py makemigrations
python manage.py migrate
python manage.py collectstatic --noinput
python scripts/init_data.py
python manage.py runserver 0.0.0.0:8000
```

### 6. 访问系统

- API文档: http://localhost:8000/api/docs/
- 管理后台: http://localhost:8000/admin/
- 默认管理员: admin / admin123

## API接口

### 认证相关
- `POST /api/auth/login/` - 用户登录
- `POST /api/auth/logout/` - 用户登出
- `GET /api/auth/user/` - 获取当前用户信息
- `GET /api/auth/users/` - 用户列表
- `POST /api/auth/users/` - 创建用户

### 系统管理
- `GET /api/system/departments/` - 部门列表
- `GET /api/system/departments/tree/` - 部门树形结构
- `GET /api/system/dicts/` - 数据字典列表
- `GET /api/system/dict/{type}/` - 根据类型获取字典

### 采购管理
- `GET /api/requests/` - 采购需求列表
- `POST /api/requests/` - 创建采购需求
- `POST /api/requests/{id}/approve/` - 审批采购需求
- `GET /api/requests/export/` - 导出采购清单
- `GET /api/requests/template/` - 下载导入模板

### 验收管理
- `GET /api/acceptances/` - 验收列表
- `POST /api/acceptances/` - 创建验收记录

### 报销管理
- `GET /api/reimbursements/` - 报销列表
- `POST /api/reimbursements/{id}/settle/` - 结算报销

## 数据库设计

### 主要数据表

1. **sys_user** - 用户表
2. **sys_department** - 部门表
3. **purchase_dict_data** - 数据字典表
4. **purchase_purchase_request** - 采购需求表
5. **purchase_acceptance** - 物品验收表
6. **purchase_reimbursement** - 结算报销表

详细的数据库设计请参考 `docs/purchase.md` 文档。

## 开发指南

### 添加新的API接口

1. 在对应的app中创建serializer
2. 在views.py中创建视图类或函数
3. 在urls.py中添加URL配置
4. 编写测试用例

### 权限控制

系统使用基于角色的权限控制：
- `admin` - 管理员，拥有所有权限
- `approver` - 审批人，可以审批采购需求
- `acceptor` - 验收人，可以进行物品验收
- `finance` - 财务，可以进行结算报销
- `user` - 普通用户，可以提交采购需求

### 文件上传

支持的文件类型：
- 图片：jpg, jpeg, png, gif
- 文档：pdf, doc, docx, xls, xlsx

文件存储路径：
- 快递单照片：`media/receipts/`
- 物品照片：`media/items/`
- 审批附件：`media/approvals/`

## 部署指南

### 生产环境配置

1. 设置环境变量 `DEBUG=False`
2. 配置正式的数据库连接
3. 配置Redis缓存
4. 设置静态文件服务
5. 配置日志记录
6. 启用HTTPS

### Docker部署

```bash
# 构建镜像
docker build -t purchase-backend .

# 运行容器
docker run -d -p 8000:8000 purchase-backend
```

## 常见问题

### Q: 数据库连接失败
A: 检查MySQL服务是否启动，数据库配置是否正确

### Q: 图片上传失败
A: 检查media目录权限，确保Django有写入权限

### Q: Excel导出乱码
A: 确保数据库字符集为utf8mb4

## 贡献指南

1. Fork项目"申请部门"
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License
