import json
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from urllib.parse import parse_qs
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class NotificationConsumer(AsyncWebsocketConsumer):
    """
    通知WebSocket消费者
    """
    
    async def connect(self):
        """连接处理（优化版本）"""
        try:
            # 设置连接超时
            import asyncio

            # 从查询参数中获取token
            query_string = self.scope['query_string'].decode()
            query_params = parse_qs(query_string)
            token = query_params.get('token', [None])[0]

            if not token:
                logger.warning("WebSocket connection rejected: No token provided")
                await self.close()
                return

            # 验证token并获取用户（添加超时）
            try:
                user = await asyncio.wait_for(
                    self.get_user_from_token(token),
                    timeout=5.0  # 5秒超时
                )
            except asyncio.TimeoutError:
                logger.warning("WebSocket connection rejected: Token validation timeout")
                await self.close()
                return

            if not user or user.is_anonymous:
                logger.warning("WebSocket connection rejected: Invalid token")
                await self.close()
                return

            self.user = user
            self.user_group_name = f"user_{user.id}"

            # 加入用户组（添加超时）
            try:
                await asyncio.wait_for(
                    self.channel_layer.group_add(
                        self.user_group_name,
                        self.channel_name
                    ),
                    timeout=3.0  # 3秒超时
                )
            except asyncio.TimeoutError:
                logger.warning("WebSocket connection rejected: Group add timeout")
                await self.close()
                return

            await self.accept()
            logger.info(f"WebSocket connected for user {user.username}")

            # 发送连接成功消息
            await self.send(text_data=json.dumps({
                'type': 'connection_established',
                'message': 'WebSocket connected successfully'
            }))

        except Exception as e:
            logger.error(f"WebSocket connection error: {str(e)}")
            await self.close()

    async def disconnect(self, close_code):
        """断开连接处理"""
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )
            logger.info(f"WebSocket disconnected for user {getattr(self, 'user', 'unknown')}")

    async def receive(self, text_data):
        """接收消息处理"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'ping':
                # 心跳响应
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': asyncio.get_event_loop().time()
                }))
            elif message_type == 'mark_read':
                # 标记通知为已读
                notification_id = data.get('notification_id')
                if notification_id:
                    await self.mark_notification_read(notification_id)
            
        except json.JSONDecodeError:
            logger.error("Invalid JSON received in WebSocket")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {str(e)}")

    async def notification_message(self, event):
        """发送通知消息"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'data': event['data']
        }))

    @database_sync_to_async
    def get_user_from_token(self, token):
        """从token获取用户"""
        try:
            # 验证token
            UntypedToken(token)
            
            # 解码token获取用户ID
            from rest_framework_simplejwt.tokens import AccessToken
            access_token = AccessToken(token)
            user_id = access_token['user_id']
            
            # 获取用户
            user = User.objects.get(id=user_id)
            return user
            
        except (InvalidToken, TokenError, User.DoesNotExist) as e:
            logger.error(f"Token validation error: {str(e)}")
            return AnonymousUser()

    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """标记通知为已读"""
        try:
            from .models import Notification
            notification = Notification.objects.get(
                id=notification_id,
                recipient=self.user
            )
            notification.mark_as_read()
            return True
        except Notification.DoesNotExist:
            return False


class SystemConsumer(AsyncWebsocketConsumer):
    """
    系统级WebSocket消费者
    用于系统广播消息
    """
    
    async def connect(self):
        """连接处理"""
        self.group_name = "system_broadcast"
        
        # 加入系统广播组
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info("System WebSocket connected")

    async def disconnect(self, close_code):
        """断开连接处理"""
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
        logger.info("System WebSocket disconnected")

    async def receive(self, text_data):
        """接收消息处理"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': asyncio.get_event_loop().time()
                }))
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received in System WebSocket")

    async def system_message(self, event):
        """发送系统消息"""
        await self.send(text_data=json.dumps({
            'type': 'system_message',
            'data': event['data']
        }))


# WebSocket工具函数
async def send_notification_to_user(user_id, notification_data):
    """
    向指定用户发送通知
    """
    from channels.layers import get_channel_layer
    
    channel_layer = get_channel_layer()
    if channel_layer:
        await channel_layer.group_send(
            f"user_{user_id}",
            {
                'type': 'notification_message',
                'data': notification_data
            }
        )

async def send_system_broadcast(message_data):
    """
    发送系统广播消息
    """
    from channels.layers import get_channel_layer
    
    channel_layer = get_channel_layer()
    if channel_layer:
        await channel_layer.group_send(
            "system_broadcast",
            {
                'type': 'system_message',
                'data': message_data
            }
        )


# 同步版本的通知发送函数
def send_notification_to_user_sync(user_id, notification_data):
    """
    同步版本：向指定用户发送通知
    """
    import asyncio
    from channels.layers import get_channel_layer
    
    channel_layer = get_channel_layer()
    if channel_layer:
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        loop.run_until_complete(send_notification_to_user(user_id, notification_data))

def send_system_broadcast_sync(message_data):
    """
    同步版本：发送系统广播消息
    """
    import asyncio
    from channels.layers import get_channel_layer
    
    channel_layer = get_channel_layer()
    if channel_layer:
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        loop.run_until_complete(send_system_broadcast(message_data))
