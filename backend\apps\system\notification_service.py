"""
通知服务模块
用于处理业务流程中的通知创建和发送
"""
from django.conf import settings
from django.utils import timezone
from django.template.loader import render_to_string
from .models import Notification
from apps.authentication.models import User
import logging

logger = logging.getLogger(__name__)


class NotificationService:
    """通知服务类"""
    
    @staticmethod
    def create_notification(title, content, notification_type, recipient, sender=None, 
                          target_model=None, target_id=None, target_url=None, 
                          priority='normal', action_buttons=None):
        """
        创建通知
        
        Args:
            title: 通知标题
            content: 通知内容
            notification_type: 通知类型
            recipient: 接收者（User对象或用户ID）
            sender: 发送者（User对象或用户ID，可选）
            target_model: 目标模型名称
            target_id: 目标对象ID
            target_url: 跳转链接
            priority: 优先级（low, normal, high, urgent）
            action_buttons: 操作按钮配置
        """
        try:
            # 处理接收者
            if isinstance(recipient, int):
                recipient = User.objects.get(id=recipient)
            elif isinstance(recipient, str):
                recipient = User.objects.get(username=recipient)
            
            # 处理发送者
            if sender:
                if isinstance(sender, int):
                    sender = User.objects.get(id=sender)
                elif isinstance(sender, str):
                    sender = User.objects.get(username=sender)
            
            # 创建通知
            notification = Notification.objects.create(
                title=title,
                content=content,
                notification_type=notification_type,
                priority=priority,
                sender=sender,
                recipient=recipient,
                target_model=target_model,
                target_id=str(target_id) if target_id else None,
                target_url=target_url,
                action_buttons=action_buttons or []
            )
            
            logger.info(f"通知创建成功: {notification.id} - {title}")
            return notification
            
        except Exception as e:
            logger.error(f"创建通知失败: {e}")
            return None
    
    @staticmethod
    def notify_approval_passed(purchase_request, approver):
        """采购申请审批通过通知"""
        title = f"采购申请审批通过 - {purchase_request.item_name}"
        content = f"您的采购申请「{purchase_request.item_name}」已通过审批，可以进行采购。\n审批人：{approver.real_name}\n审批时间：{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return NotificationService.create_notification(
            title=title,
            content=content,
            notification_type='approval',
            recipient=purchase_request.requester,
            sender=approver,
            target_model='PurchaseRequest',
            target_id=purchase_request.id,
            target_url=f'/purchase/requests/{purchase_request.id}',
            priority='high',
            action_buttons=[
                {'text': '查看详情', 'action': 'view', 'url': f'/purchase/requests/{purchase_request.id}'}
            ]
        )
    
    @staticmethod
    def notify_approval_rejected(purchase_request, approver, reason):
        """采购申请审批驳回通知"""
        title = f"采购申请被驳回 - {purchase_request.item_name}"
        content = f"您的采购申请「{purchase_request.item_name}」已被驳回。\n驳回原因：{reason}\n审批人：{approver.real_name}\n驳回时间：{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return NotificationService.create_notification(
            title=title,
            content=content,
            notification_type='rejection',
            recipient=purchase_request.requester,
            sender=approver,
            target_model='PurchaseRequest',
            target_id=purchase_request.id,
            target_url=f'/purchase/requests/{purchase_request.id}',
            priority='high',
            action_buttons=[
                {'text': '查看详情', 'action': 'view', 'url': f'/purchase/requests/{purchase_request.id}'},
                {'text': '重新申请', 'action': 'reapply', 'url': f'/purchase/requests/create?copy_from={purchase_request.id}'}
            ]
        )
    
    @staticmethod
    def notify_purchase_completed(purchase_request, purchaser):
        """采购完成通知"""
        title = f"采购已完成 - {purchase_request.item_name}"
        content = f"您申请的「{purchase_request.item_name}」采购已完成，等待验收。\n采购人：{purchaser.real_name}\n完成时间：{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return NotificationService.create_notification(
            title=title,
            content=content,
            notification_type='purchase',
            recipient=purchase_request.requester,
            sender=purchaser,
            target_model='PurchaseRequest',
            target_id=purchase_request.id,
            target_url=f'/purchase/requests/{purchase_request.id}',
            priority='normal',
            action_buttons=[
                {'text': '查看详情', 'action': 'view', 'url': f'/purchase/requests/{purchase_request.id}'}
            ]
        )
    
    @staticmethod
    def notify_acceptance_completed(purchase_request, acceptor):
        """验收完成通知"""
        title = f"验收已完成 - {purchase_request.item_name}"
        content = f"您申请的「{purchase_request.item_name}」验收已完成，可以进行报销。\n验收人：{acceptor.real_name}\n验收时间：{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return NotificationService.create_notification(
            title=title,
            content=content,
            notification_type='acceptance',
            recipient=purchase_request.requester,
            sender=acceptor,
            target_model='PurchaseRequest',
            target_id=purchase_request.id,
            target_url=f'/purchase/requests/{purchase_request.id}',
            priority='normal',
            action_buttons=[
                {'text': '查看详情', 'action': 'view', 'url': f'/purchase/requests/{purchase_request.id}'},
                {'text': '申请报销', 'action': 'reimburse', 'url': f'/purchase/reimbursement/create?request_id={purchase_request.id}'}
            ]
        )
    
    @staticmethod
    def notify_reimbursement_completed(purchase_request, reimburser):
        """报销完成通知"""
        title = f"报销已完成 - {purchase_request.item_name}"
        content = f"您申请的「{purchase_request.item_name}」报销已完成。\n报销人：{reimburser.real_name}\n完成时间：{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return NotificationService.create_notification(
            title=title,
            content=content,
            notification_type='reimbursement',
            recipient=purchase_request.requester,
            sender=reimburser,
            target_model='PurchaseRequest',
            target_id=purchase_request.id,
            target_url=f'/purchase/requests/{purchase_request.id}',
            priority='normal',
            action_buttons=[
                {'text': '查看详情', 'action': 'view', 'url': f'/purchase/requests/{purchase_request.id}'}
            ]
        )

    @staticmethod
    def notify_finance_return_to_acceptance(purchase_request, finance_user):
        """财务退回重新验收通知"""
        title = f"财务退回重新验收 - {purchase_request.item_name}"
        content = f"财务人员要求重新验收「{purchase_request.item_name}」。\n退回原因：{purchase_request.finance_return_reason}\n财务人员：{finance_user.real_name}\n退回时间：{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # 通知申请人
        NotificationService.create_notification(
            title=title,
            content=content,
            notification_type='finance_return',
            recipient=purchase_request.requester,
            sender=finance_user,
            target_model='PurchaseRequest',
            target_id=purchase_request.id,
            target_url=f'/purchase/requests/{purchase_request.id}',
            priority='high',
            action_buttons=[
                {'text': '查看详情', 'action': 'view', 'url': f'/purchase/requests/{purchase_request.id}'},
                {'text': '重新验收', 'action': 'accept', 'url': f'/purchase/acceptance?request_id={purchase_request.id}'}
            ]
        )

        # 通知验收人员（如果有的话）
        if hasattr(purchase_request, 'acceptor') and purchase_request.acceptor:
            NotificationService.create_notification(
                title=title,
                content=content,
                notification_type='finance_return',
                recipient=purchase_request.acceptor,
                sender=finance_user,
                target_model='PurchaseRequest',
                target_id=purchase_request.id,
                target_url=f'/purchase/requests/{purchase_request.id}',
                priority='high',
                action_buttons=[
                    {'text': '查看详情', 'action': 'view', 'url': f'/purchase/requests/{purchase_request.id}'},
                    {'text': '重新验收', 'action': 'accept', 'url': f'/purchase/acceptance?request_id={purchase_request.id}'}
                ]
            )

    @staticmethod
    def notify_pending_approval(purchase_request, approver):
        """待审批通知"""
        title = f"待审批采购申请 - {purchase_request.item_name}"
        content = f"有新的采购申请需要您审批。\n申请人：{purchase_request.requester.real_name}\n申请时间：{purchase_request.submission_date.strftime('%Y-%m-%d %H:%M:%S') if purchase_request.submission_date else '未知'}"
        
        return NotificationService.create_notification(
            title=title,
            content=content,
            notification_type='approval',
            recipient=approver,
            sender=purchase_request.requester,
            target_model='PurchaseRequest',
            target_id=purchase_request.id,
            target_url=f'/purchase/approval/{purchase_request.id}',
            priority='high',
            action_buttons=[
                {'text': '立即审批', 'action': 'approve', 'url': f'/purchase/approval/{purchase_request.id}'}
            ]
        )
    
    @staticmethod
    def notify_system_maintenance(title, content, users=None, priority='normal'):
        """系统维护通知"""
        if users is None:
            users = User.objects.filter(is_active=True)
        
        notifications = []
        for user in users:
            notification = NotificationService.create_notification(
                title=title,
                content=content,
                notification_type='system',
                recipient=user,
                priority=priority
            )
            if notification:
                notifications.append(notification)
        
        return notifications
    
    @staticmethod
    def batch_notify(notifications_data):
        """批量创建通知"""
        notifications = []
        for data in notifications_data:
            notification = NotificationService.create_notification(**data)
            if notification:
                notifications.append(notification)
        return notifications


# 便捷函数
def notify_approval_passed(purchase_request, approver):
    """审批通过通知的便捷函数"""
    return NotificationService.notify_approval_passed(purchase_request, approver)

def notify_approval_rejected(purchase_request, approver, reason):
    """审批驳回通知的便捷函数"""
    return NotificationService.notify_approval_rejected(purchase_request, approver, reason)

def notify_purchase_completed(purchase_request, purchaser):
    """采购完成通知的便捷函数"""
    return NotificationService.notify_purchase_completed(purchase_request, purchaser)

def notify_acceptance_completed(purchase_request, acceptor):
    """验收完成通知的便捷函数"""
    return NotificationService.notify_acceptance_completed(purchase_request, acceptor)

def notify_reimbursement_completed(purchase_request, reimburser):
    """报销完成通知的便捷函数"""
    return NotificationService.notify_reimbursement_completed(purchase_request, reimburser)

def notify_pending_approval(purchase_request, approver):
    """待审批通知的便捷函数"""
    return NotificationService.notify_pending_approval(purchase_request, approver)

def notify_finance_return_to_acceptance(purchase_request, finance_user):
    """财务退回重新验收通知的便捷函数"""
    return NotificationService.notify_finance_return_to_acceptance(purchase_request, finance_user)
