<template>
  <div class="acceptance-print-preview">
    <div v-for="(record, index) in records" :key="record.id" class="print-page">
      <!-- 页面头部 -->
      <div class="page-header">
        <h2>物资验收单</h2>
        <div class="page-info">
          第 {{ index + 1 }} 页，共 {{ records.length }} 页 | 本页 1 条，总计 {{ records.length }} 条记录
        </div>
      </div>

      <!-- 主要内容区域 - 左右分栏 -->
      <div class="page-content">
        <!-- 左侧：验收信息 -->
        <div class="left-section">
          <div class="section-title">验收单 #{{ record.id }}</div>
          
          <div class="info-group">
            <div class="info-item">
              <span class="label">物品名称:</span>
              <span class="value">{{ record.item_name || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">规格型号:</span>
              <span class="value">{{ record.specification || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">部门层级路径:</span>
              <span class="value">{{ record.hierarchy_path || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">验收人姓名:</span>
              <span class="value">{{ record.acceptor_name || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">采购数量:</span>
              <span class="value">{{ record.purchase_quantity || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">供应商名称:</span>
              <span class="value">{{ record.supplier_name || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">验收数量:</span>
              <span class="value">{{ record.acceptance_quantity || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">验收时间:</span>
              <span class="value">{{ formatDate(record.acceptance_date) }}</span>
            </div>
          </div>
        </div>

        <!-- 右侧：验收照片 -->
        <div class="right-section">
          <div class="section-title">验收照片</div>
          
          <div class="photos-container">
            <div v-for="(photo, photoIndex) in getOrderedPhotos(record)" :key="photoIndex" class="photo-item">
              <div class="photo-wrapper">
                <img 
                  :src="getPhotoUrl(photo.url)" 
                  :alt="photo.description"
                  class="photo-image"
                  @error="handleImageError"
                />
              </div>
              <div class="photo-label">{{ getPhotoLabel(photo.type) }}</div>
            </div>
            
            <!-- 如果照片不足3张，显示占位符 -->
            <div v-for="i in (3 - getOrderedPhotos(record).length)" :key="`placeholder-${i}`" class="photo-item placeholder">
              <div class="photo-wrapper">
                <div class="photo-placeholder">
                  <span>暂无照片</span>
                </div>
              </div>
              <div class="photo-label">{{ getPlaceholderLabel(getOrderedPhotos(record).length + i - 1) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面底部 -->
      <div class="page-footer">
        <div class="footer-item">
          <span class="label">制表人:</span>
          <span class="value">{{ record.acceptor_name || '系统管理员' }}</span>
        </div>
        <div class="footer-item">
          <span class="label">制表时间:</span>
          <span class="value">{{ formatDate(new Date()) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
defineProps({
  records: {
    type: Array,
    default: () => []
  }
})

// 获取照片URL
const getPhotoUrl = (url) => {
  if (!url) return ''
  if (url.startsWith('http')) return url
  return `${process.env.VUE_APP_API_URL || 'http://localhost:8000'}${url}`
}

// 获取有序的照片列表
const getOrderedPhotos = (record) => {
  if (!record.acceptance_photos || !Array.isArray(record.acceptance_photos)) {
    return []
  }
  
  // 按固定顺序排列：正面、侧面、整体
  const typeOrder = { 'front': 0, 'side': 1, 'overall': 2 }
  return record.acceptance_photos
    .filter(photo => photo && photo.url)
    .sort((a, b) => (typeOrder[a.type] || 999) - (typeOrder[b.type] || 999))
    .slice(0, 3) // 最多3张
}

// 获取照片标签
const getPhotoLabel = (type) => {
  const labels = {
    'front': '正面照片',
    'side': '侧面照片', 
    'overall': '整体照片'
  }
  return labels[type] || '照片'
}

// 获取占位符标签
const getPlaceholderLabel = (index) => {
  const labels = ['正面照片', '侧面照片', '整体照片']
  return labels[index] || '照片'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  try {
    return new Date(date).toLocaleDateString('zh-CN')
  } catch {
    return '-'
  }
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none'
  const placeholder = event.target.parentNode.querySelector('.error-placeholder')
  if (!placeholder) {
    const errorDiv = document.createElement('div')
    errorDiv.className = 'error-placeholder'
    errorDiv.textContent = '图片加载失败'
    event.target.parentNode.appendChild(errorDiv)
  }
}
</script>

<style scoped>
.acceptance-print-preview {
  background: white;
}

.print-page {
  width: 210mm;
  min-height: 297mm;
  margin: 0 auto 20px;
  padding: 15mm;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  page-break-after: always;
  box-sizing: border-box;
}

.print-page:last-child {
  page-break-after: avoid;
  margin-bottom: 0;
}

.page-header {
  text-align: center;
  margin-bottom: 20mm;
  border-bottom: 2px solid #333;
  padding-bottom: 5mm;
  background: transparent !important;
  border-bottom: none;
}

.page-header h2 {
  margin: 0 0 5mm 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.page-info {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.page-content {
  display: flex;
  gap: 10mm;
  min-height: min-content;
}

.left-section {
  width: 65mm;
  padding-right: 1mm;
}

.right-section {
  padding-left: 5mm;
  /* border-left: 1px solid #ddd; */
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10mm;
  text-align: center;
  padding: 3mm 0;
  border-bottom: 1px solid #ddd;
}

.info-group {
  display: flex;
  flex-direction: column;

}

.info-item {
  display: flex;
  align-items: flex-start;
  padding: 2mm 0;
 
}

.info-item .label {
  width: 30mm;
  font-weight: 600;
  color: #333;
  flex-shrink: 0;
}

.info-item .value {
  flex: 1;
  color: #555;
  word-break: break-all;
}

.photos-container {
  display: flex;
  gap: 4mm;
  margin-top: 20mm;
}

.photo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.photo-wrapper {
  width: 30mm;
  height: 35mm;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.photo-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.photo-placeholder,
.error-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #999;
  font-size: 12px;
  text-align: center;
}

.photo-label {
  margin-top: 2mm;
  font-size: 12px;
  color: #333;
  text-align: center;
  font-weight: 500;
}

.page-footer {
  margin-top: 15mm;
  padding-top: 5mm;
  border-top: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
}

.footer-item {
  display: flex;
  align-items: center;
  gap: 5mm;
}

.footer-item .label {
  font-weight: 600;
  color: #333;
}

.footer-item .value {
  color: #555;
}

/* 打印样式 */
@media print {
  @page {
    size: A4;
    margin: 0;
  }
  
  .acceptance-print-preview {
    background: white;
  }
  
  .print-page {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 15mm;
    box-shadow: none;
    page-break-after: always;
  }
  
  .print-page:last-child {
    page-break-after: avoid;
  }
  
  .page-header h2 {
    color: #000 !important;
  }
  
  .section-title,
  .info-item .label,
  .footer-item .label {
    color: #000 !important;
  }
}
</style>
