"""
采购管理系统 - 数据序列化器

功能说明：
1. 定义API数据的序列化和反序列化规则
2. 实现数据验证和格式转换
3. 提供不同场景下的数据视图
4. 支持嵌套关系和关联数据处理

技术特点：
- 基于Django REST Framework序列化器
- 分阶段的数据验证和权限控制
- 自动化的关联数据处理
- 灵活的字段配置和输出格式

<AUTHOR>
@version 1.0.0
@since 2025-01-08
"""
from rest_framework import serializers
from .models import PurchaseRequest, Dictionary


class DictionarySerializer(serializers.ModelSerializer):
    """数据字典序列化器"""
    class Meta:
        model = Dictionary
        fields = [
            'id', 'type_code', 'type_name', 'code', 'name', 'description', 'order', 'status', 'editable',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class PurchaseRequestSerializer(serializers.ModelSerializer):
    """采购需求序列化器（整合验收和报销功能）"""
    requester_name = serializers.CharField(source='requester.real_name', read_only=True)
    purchaser_name = serializers.CharField(source='purchaser.real_name', read_only=True)
    acceptor_name = serializers.CharField(source='acceptor.real_name', read_only=True)
    reimburser_name = serializers.CharField(source='reimburser.real_name', read_only=True)
    approver_name = serializers.CharField(source='approver.real_name', read_only=True)
    exception_approver_name = serializers.CharField(source='exception_approver.real_name', read_only=True)
    is_price_warning = serializers.ReadOnlyField()
    can_auto_approve_acceptance = serializers.ReadOnlyField()

    # 兼容性字段 - 为了向后兼容，提供旧字段名的访问
    quantity = serializers.IntegerField(source='budget_quantity', read_only=True)
    unit_price = serializers.DecimalField(source='budget_unit_price', max_digits=12, decimal_places=2, read_only=True)
    total_amount = serializers.DecimalField(source='budget_total_amount', max_digits=15, decimal_places=2, read_only=True)
    actual_quantity = serializers.IntegerField(source='purchase_quantity', read_only=True)
    actual_unit_price = serializers.DecimalField(source='purchase_unit_price', max_digits=12, decimal_places=2, read_only=True)
    actual_total_amount = serializers.DecimalField(source='purchase_total_amount', max_digits=15, decimal_places=2, read_only=True)
    accepted_quantity = serializers.IntegerField(source='acceptance_quantity', read_only=True)

    # 验收照片字段
    acceptance_photos = serializers.SerializerMethodField()

    # 字典字段的显示名称（用于前端显示）
    status_display = serializers.SerializerMethodField()
    item_category_display = serializers.SerializerMethodField()
    unit_display = serializers.SerializerMethodField()
    procurement_method_display = serializers.SerializerMethodField()
    fund_project_display = serializers.SerializerMethodField()
    purchase_type_display = serializers.SerializerMethodField()

    # 添加字段用于返回大写编码给前端
    status_code = serializers.SerializerMethodField()
    purchase_type_code = serializers.SerializerMethodField()

    class Meta:
        model = PurchaseRequest
        fields = [
            # 基本信息
            'id', 'item_category', 'item_name', 'specification', 'unit', 'budget_quantity',
            'budget_unit_price', 'budget_total_amount', 'dept_id', 'hierarchy_path',
            'procurement_method', 'requirement_source', 'fund_project',
            'purchase_type', 'remarks', 'status', 'submission_date', 'order_number',

            # 字典字段显示名称
            'status_display', 'item_category_display', 'unit_display',
            'procurement_method_display', 'fund_project_display', 'purchase_type_display',

            # 字典字段编码（用于前端）
            'status_code', 'purchase_type_code',

            # 采购信息
            'purchase_quantity', 'purchase_unit_price', 'purchase_total_amount', 'supplier_name',
            'purchase_remarks', 'shipping_date',

            # 人员信息（包含关联字段和显示名称）
            'requester', 'requester_name',
            'purchaser', 'purchaser_name', 'purchase_date',
            'acceptor', 'acceptor_name', 'acceptance_date',
            'reimburser', 'reimburser_name', 'reimbursement_date',
            'approver', 'approver_name', 'approved_at', 'approval_comment',
            'exception_approver', 'exception_approver_name', 'exception_approved_at',
            'rejection_reason',

            # 验收信息
            'courier_company', 'tracking_number', 'shipping_origin', 'courier_receipt_photo', 'item_photo',
            'acceptance_photos', 'acceptance_quantity', 'quantity_variance', 'variance_rate',
            'has_exception', 'exception_reason', 'exception_approved',
            'exception_approver', 'exception_approver_name', 'exception_approved_at', 'acceptance_remarks',

            # 结算信息
            'settlement_amount', 'voucher_number', 'financial_serial', 'transaction_number',
            'payee_account', 'payee_name', 'settlement_remarks',

            # 退回信息
            'returner', 'return_date', 'return_reason',

            # 财务退回信息
            're_acceptance_required', 'finance_return_reason', 'finance_returner',
            'finance_return_date', 're_acceptance_count',

            # 附件信息
            'requirement_attachment', 'approval_attachment',

            # 历史数据和其他
            'history_purchase_count', 'history_avg_price', 'history_max_price',
            'history_min_price', 'order_number', 'is_price_warning',
            'can_auto_approve_acceptance', 'created_at', 'updated_at',

            # 兼容性字段（旧字段名）
            'quantity', 'unit_price', 'total_amount', 'actual_quantity',
            'actual_unit_price', 'actual_total_amount', 'accepted_quantity'
        ]
        read_only_fields = [
            'id', 'budget_total_amount', 'purchase_total_amount', 'requester', 'requester_name',
            'purchaser', 'purchaser_name', 'purchase_date', 'shipping_date',
            'acceptor', 'acceptor_name', 'acceptance_date',
            'reimburser', 'reimburser_name', 'reimbursement_date',
            'approver', 'approver_name', 'approved_at',
            'quantity_variance', 'variance_rate', 'has_exception', 'exception_reason',
            'is_price_warning', 'can_auto_approve_acceptance', 'order_number',
            'created_at', 'updated_at'
        ]

    def get_status_display(self, obj):
        """获取状态显示名称"""
        from .services.dict_service import DictService
        return DictService.get_text('status', obj.status)

    def get_item_category_display(self, obj):
        """获取物品种类显示名称"""
        from .services.dict_service import DictService
        return DictService.get_text('item_category', obj.item_category)

    def get_unit_display(self, obj):
        """获取单位显示名称"""
        from .services.dict_service import DictService
        return DictService.get_text('unit', obj.unit)

    def get_procurement_method_display(self, obj):
        """获取采购方式显示名称"""
        from .services.dict_service import DictService
        return DictService.get_text('procurement_method', obj.procurement_method)

    def get_fund_project_display(self, obj):
        """获取经费项目显示名称"""
        from .services.dict_service import DictService
        return DictService.get_text('fund_project', obj.fund_project)

    def get_purchase_type_display(self, obj):
        """获取采购类型显示名称"""
        from .services.dict_service import DictService
        return DictService.get_text('purchase_type', obj.purchase_type)



    def get_status_code(self, obj):
        """获取状态编码（统一使用小写）"""
        return obj.status

    def get_purchase_type_code(self, obj):
        """获取采购类型编码（统一使用小写）"""
        return obj.purchase_type

    def create(self, validated_data):
        """创建采购需求时设置申请人"""
        # 移除只读字段，防止前端传递
        validated_data.pop('budget_total_amount', None)
        validated_data.pop('purchase_total_amount', None)
        validated_data.pop('order_number', None)  # 确保不设置订单号

        validated_data['requester'] = self.context['request'].user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """更新采购需求时移除只读字段"""
        # 移除只读字段，防止前端传递
        validated_data.pop('budget_total_amount', None)
        validated_data.pop('purchase_total_amount', None)
        validated_data.pop('order_number', None)  # 确保不修改订单号

        return super().update(instance, validated_data)

    def validate_budget_quantity(self, value):
        """验证预算数量"""
        if value < 1:
            raise serializers.ValidationError('预算数量必须大于0')
        return value

    def validate_budget_unit_price(self, value):
        """验证预算单价"""
        if value < 0:
            raise serializers.ValidationError('预算单价不能为负数')
        return value

    def validate_status(self, value):
        """验证状态字段（统一使用小写编码）"""
        if not value:
            return value

        # 强制转换为小写
        value = value.lower()

        # 验证是否为有效状态
        valid_statuses = [choice[0] for choice in PurchaseRequest.STATUS_CHOICES]
        if value in valid_statuses:
            return value

        # 无效的状态值
        raise serializers.ValidationError(f'无效的状态值: {value}')

    def validate_purchase_type(self, value):
        """验证采购类型字段（统一使用小写编码）"""
        if not value:
            return value
        return value.lower()

    def validate_item_category(self, value):
        """验证物品种类字段（统一使用小写编码）"""
        if not value:
            return value
        return value.lower()

    def validate_unit(self, value):
        """验证计量单位字段（统一使用小写编码）"""
        if not value:
            return value
        return value.lower()

    def validate_procurement_method(self, value):
        """验证采购方式字段（统一使用小写编码）"""
        if not value:
            return value
        return value.lower()

    def validate_fund_project(self, value):
        """验证经费项目字段（统一使用小写编码）"""
        if not value:
            return value
        return value.lower()

    def validate_requirement_source(self, value):
        """验证需求来源字段（统一使用小写编码）"""
        if not value:
            return value
        return value.lower()

    def validate_purchase_type(self, value):
        """验证采购类型字段 - 统一使用小写编码"""
        if not value:
            return value

        # 统一转换为小写
        value = value.lower()

        # 验证是否为有效的采购类型
        valid_types = [choice[0] for choice in PurchaseRequest.PURCHASE_TYPE_CHOICES]
        if value in valid_types:
            return value

        # 无效的采购类型值
        raise serializers.ValidationError(f'无效的采购类型值: {value}')

    def get_acceptance_photos(self, obj):
        """获取验收照片列表 - 简化类型处理，直接返回三种固定类型的照片"""
        photos = []

        # 从新的多照片模型获取所有照片
        all_photos = obj.acceptance_photos_new.all().order_by('-upload_time')

        # 按类型分组，每种类型只保留最新的一张
        type_photos = {}
        for photo in all_photos:
            photo_type = photo.photo_type

            # 兼容旧的类型映射
            if photo_type == 'item':
                photo_type = 'front'
            elif photo_type == 'package':
                photo_type = 'side'
            elif photo_type == 'courier':
                photo_type = 'overall'

            # 只保留三种标准类型
            if photo_type in ['front', 'side', 'overall']:
                if photo_type not in type_photos:
                    type_photos[photo_type] = photo

        # 转换为前端需要的格式
        for photo_type, photo in type_photos.items():
            # 获取照片类型的中文标签
            type_labels = {
                'front': '正面照片',
                'side': '侧面照片',
                'overall': '整体照片'
            }

            photos.append({
                'url': photo.photo.url,
                'description': photo.description or type_labels.get(photo_type, photo_type),
                'type': photo_type,
                'photo_type': photo_type,
                'order': 0,  # 固定顺序，不需要排序功能
                'upload_time': photo.upload_time.isoformat() if photo.upload_time else None,
                'id': photo.id
            })

        # 兼容旧的单张照片字段（如果新照片模型中没有对应类型的照片）
        existing_types = [p['type'] for p in photos]

        if obj.courier_receipt_photo and 'overall' not in existing_types:
            photos.append({
                'url': obj.courier_receipt_photo.url,
                'description': '整体照片',
                'type': 'overall',
                'photo_type': 'overall',
                'order': 0,
                'upload_time': None,
                'id': f'legacy_courier_{obj.id}'
            })
        if obj.item_photo and 'front' not in existing_types:
            photos.append({
                'url': obj.item_photo.url,
                'description': '正面照片',
                'type': 'front',
                'photo_type': 'front',
                'order': 0,
                'upload_time': None,
                'id': f'legacy_item_{obj.id}'
            })

        # 按固定顺序排序：front, side, overall
        type_order = {'front': 0, 'side': 1, 'overall': 2}
        photos.sort(key=lambda x: type_order.get(x['type'], 999))

        # 重新分配order（固定位置）
        for i, photo in enumerate(photos):
            photo['order'] = i

        return photos


class PurchaseRequestCreateSerializer(PurchaseRequestSerializer):
    """采购需求创建序列化器"""
    
    def validate(self, attrs):
        """创建时的额外验证"""
        # 可以在这里添加创建时的特殊验证逻辑
        return attrs


class PurchaseRequestUpdateSerializer(PurchaseRequestSerializer):
    """采购需求更新序列化器"""

    def validate(self, attrs):
        """更新时的验证"""
        # 如果状态不是草稿、退回或驳回状态，则不允许修改基本信息
        if self.instance and self.instance.status not in ['draft', 'returned', 'rejected']:
            protected_fields = [
                'item_category', 'item_name', 'specification', 'unit', 'budget_quantity',
                'budget_unit_price', 'dept_id', 'procurement_method',
                'requirement_source', 'fund_project'
            ]
            for field in protected_fields:
                if field in attrs and attrs[field] != getattr(self.instance, field):
                    raise serializers.ValidationError(f'需求已提交，无法修改{field}')

        return attrs

    def validate_item_photo(self, value):
        """验证物品照片字段"""
        # 如果传入的是URL字符串，说明前端没有上传新文件，忽略此字段
        if isinstance(value, str):
            return None
        return value

    def validate_courier_receipt_photo(self, value):
        """验证快递单照片字段"""
        # 如果传入的是URL字符串，说明前端没有上传新文件，忽略此字段
        if isinstance(value, str):
            return None
        return value


class PurchaseRequestApprovalSerializer(serializers.Serializer):
    """采购需求审批序列化器"""
    action = serializers.ChoiceField(choices=['approve', 'reject'], required=True)
    comment = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        """验证审批操作"""
        if attrs['action'] == 'reject' and not attrs.get('comment'):
            raise serializers.ValidationError('驳回时必须填写审批意见')
        return attrs


class PurchaseRequestAcceptanceSerializer(serializers.Serializer):
    """采购需求验收序列化器"""
    actual_quantity = serializers.IntegerField(required=False)
    courier_company = serializers.CharField(max_length=100, required=False, allow_blank=True)
    tracking_number = serializers.CharField(max_length=100, required=False, allow_blank=True)
    shipping_origin = serializers.CharField(max_length=200, required=False, allow_blank=True)
    courier_receipt_photo = serializers.ImageField(required=False, allow_null=True)
    item_photo = serializers.ImageField(required=False, allow_null=True)


class PurchaseRequestReimbursementSerializer(serializers.Serializer):
    """采购需求报销序列化器"""
    voucher_number = serializers.CharField(max_length=50, required=False, allow_blank=True)
    financial_serial = serializers.CharField(max_length=50, required=False, allow_blank=True)
    settlement_remarks = serializers.CharField(required=False, allow_blank=True)
