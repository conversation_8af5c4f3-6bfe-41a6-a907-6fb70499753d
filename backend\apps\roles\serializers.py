from rest_framework import serializers
from apps.system.models_permission import Permission
from .models import Role


class PermissionSerializer(serializers.ModelSerializer):
    """权限序列化器"""

    class Meta:
        model = Permission
        fields = ['id', 'name', 'code', 'category', 'module']


class RoleSerializer(serializers.ModelSerializer):
    """角色序列化器"""
    permissions = serializers.SerializerMethodField()
    permission_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    user_count = serializers.SerializerMethodField()
    permission_count = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = [
            'id', 'name', 'code', 'description', 'permissions', 'permission_ids',
            'is_active', 'is_system', 'user_count', 'permission_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'user_count', 'permission_count']

    def get_permissions(self, obj):
        """获取角色关联的权限列表"""
        from apps.system.models_permission import RolePermission
        role_permissions = RolePermission.objects.filter(role=obj).select_related('permission')
        return [
            {
                'id': rp.permission.id,
                'name': rp.permission.name,
                'codename': rp.permission.code,
                'content_type': None  # 兼容字段
            }
            for rp in role_permissions
        ]

    def get_user_count(self, obj):
        """获取角色关联的用户数量"""
        from django.contrib.auth import get_user_model
        User = get_user_model()
        return User.objects.filter(role=obj.code).count()

    def get_permission_count(self, obj):
        """获取角色关联的权限数量"""
        from apps.system.models_permission import RolePermission
        return RolePermission.objects.filter(role=obj).count()

    def create(self, validated_data):
        permission_ids = validated_data.pop('permission_ids', [])
        role = Role.objects.create(**validated_data)

        if permission_ids:
            from apps.system.models_permission import Permission, RolePermission
            permissions = Permission.objects.filter(id__in=permission_ids)
            for permission in permissions:
                RolePermission.objects.get_or_create(
                    role=role,
                    permission=permission
                )

        return role

    def update(self, instance, validated_data):
        permission_ids = validated_data.pop('permission_ids', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if permission_ids is not None:
            from apps.system.models_permission import Permission, RolePermission
            # 删除现有权限关联
            RolePermission.objects.filter(role=instance).delete()
            # 添加新的权限关联
            permissions = Permission.objects.filter(id__in=permission_ids)
            for permission in permissions:
                RolePermission.objects.create(
                    role=instance,
                    permission=permission
                )

        return instance


# UserRoleSerializer已删除 - 改用User.role字段


class RoleSimpleSerializer(serializers.ModelSerializer):
    """角色简单序列化器（用于下拉选择）"""
    
    class Meta:
        model = Role
        fields = ['id', 'name', 'code', 'description', 'is_active']
