"""
采购管理过滤器（整合验收和报销功能）
"""
import django_filters
from django.db.models import Q, F
from .models import PurchaseRequest, Dictionary


class PurchaseRequestFilter(django_filters.FilterSet):
    """采购需求过滤器（整合验收和报销功能）"""

    # ID精确搜索
    id = django_filters.NumberFilter(
        field_name='id',
        lookup_expr='exact',
        help_text='采购需求ID'
    )

    # 时间范围过滤 - 支持前端参数格式
    submission_date__gte = django_filters.DateTimeFilter(
        field_name='submission_date',
        lookup_expr='gte',
        help_text='提交开始时间'
    )
    submission_date__lte = django_filters.DateTimeFilter(
        field_name='submission_date',
        lookup_expr='lte',
        help_text='提交结束时间'
    )

    # 采购时间过滤
    purchase_date__gte = django_filters.DateTimeFilter(
        field_name='purchase_date',
        lookup_expr='gte',
        help_text='采购开始时间'
    )
    purchase_date__lte = django_filters.DateTimeFilter(
        field_name='purchase_date',
        lookup_expr='lte',
        help_text='采购结束时间'
    )

    # 验收时间过滤
    acceptance_date__gte = django_filters.DateTimeFilter(
        field_name='acceptance_date',
        lookup_expr='gte',
        help_text='验收开始时间'
    )
    acceptance_date__lte = django_filters.DateTimeFilter(
        field_name='acceptance_date',
        lookup_expr='lte',
        help_text='验收结束时间'
    )

    # 报销时间过滤
    reimbursement_date__gte = django_filters.DateTimeFilter(
        field_name='reimbursement_date',
        lookup_expr='gte',
        help_text='报销开始时间'
    )
    reimbursement_date__lte = django_filters.DateTimeFilter(
        field_name='reimbursement_date',
        lookup_expr='lte',
        help_text='报销结束时间'
    )

    # 金额范围过滤 - 支持前端参数格式
    budget_total_amount__gte = django_filters.NumberFilter(
        field_name='budget_total_amount',
        lookup_expr='gte',
        help_text='预算金额最小值'
    )
    budget_total_amount__lte = django_filters.NumberFilter(
        field_name='budget_total_amount',
        lookup_expr='lte',
        help_text='预算金额最大值'
    )

    # 采购金额范围过滤
    purchase_total_amount__gte = django_filters.NumberFilter(
        field_name='purchase_total_amount',
        lookup_expr='gte',
        help_text='采购金额最小值'
    )
    purchase_total_amount__lte = django_filters.NumberFilter(
        field_name='purchase_total_amount',
        lookup_expr='lte',
        help_text='采购金额最大值'
    )

    # 结算金额范围过滤
    settlement_amount__gte = django_filters.NumberFilter(
        field_name='settlement_amount',
        lookup_expr='gte',
        help_text='结算金额最小值'
    )
    settlement_amount__lte = django_filters.NumberFilter(
        field_name='settlement_amount',
        lookup_expr='lte',
        help_text='结算金额最大值'
    )

    # 模糊搜索
    item_name_contains = django_filters.CharFilter(
        field_name='item_name',
        lookup_expr='icontains',
        help_text='物品名称包含'
    )
    spec_contains = django_filters.CharFilter(
        field_name='spec',
        lookup_expr='icontains',
        help_text='规格型号包含'
    )

    # 快递信息过滤
    courier_company_contains = django_filters.CharFilter(
        field_name='courier_company',
        lookup_expr='icontains',
        help_text='快递公司包含'
    )
    tracking_number_contains = django_filters.CharFilter(
        field_name='tracking_number',
        lookup_expr='icontains',
        help_text='快递单号包含'
    )

    # 报销凭证过滤
    voucher_no_contains = django_filters.CharFilter(
        field_name='reimbursement_voucher_no',
        lookup_expr='icontains',
        help_text='报销凭证号包含'
    )

    # 多状态过滤
    status_in = django_filters.BaseInFilter(
        field_name='status',
        help_text='状态列表，用逗号分隔'
    )

    # 申请人过滤
    requester_name = django_filters.CharFilter(
        method='filter_requester_name',
        help_text='申请人姓名'
    )

    # 验收人过滤
    acceptor_name = django_filters.CharFilter(
        method='filter_acceptor_name',
        help_text='验收人姓名'
    )

    # 报销人过滤
    reimburser_name = django_filters.CharFilter(
        method='filter_reimburser_name',
        help_text='报销人姓名'
    )

    # 价格预警过滤
    has_price_warning = django_filters.BooleanFilter(
        method='filter_price_warning',
        help_text='是否有价格预警'
    )

    # 部门层级路径过滤
    hierarchy_path__icontains = django_filters.CharFilter(
        field_name='hierarchy_path',
        lookup_expr='icontains',
        help_text='部门层级路径包含'
    )

    # 需求来源模糊搜索
    requirement_source__icontains = django_filters.CharFilter(
        field_name='requirement_source',
        lookup_expr='icontains',
        help_text='需求来源包含'
    )

    class Meta:
        model = PurchaseRequest
        fields = [
            'status', 'dept_id', 'item_category', 'procurement_method',
            'requirement_source', 'fund_project', 'unit',
            'has_exception', 'purchase_type'
        ]

    def filter_requester_name(self, queryset, name, value):
        """根据申请人姓名过滤"""
        return queryset.filter(
            Q(requester__real_name__icontains=value) |
            Q(requester__username__icontains=value)
        )

    def filter_acceptor_name(self, queryset, name, value):
        """根据验收人姓名过滤"""
        return queryset.filter(
            Q(acceptor__real_name__icontains=value) |
            Q(acceptor__username__icontains=value)
        )

    def filter_reimburser_name(self, queryset, name, value):
        """根据报销人姓名过滤"""
        return queryset.filter(
            Q(reimburser__real_name__icontains=value) |
            Q(reimburser__username__icontains=value)
        )

    def filter_price_warning(self, queryset, name, value):
        """过滤有价格预警的记录"""
        if value:
            return queryset.filter(
                unit_price__gt=F('history_avg_price') * 1.2,
                history_avg_price__gt=0
            )
        return queryset



