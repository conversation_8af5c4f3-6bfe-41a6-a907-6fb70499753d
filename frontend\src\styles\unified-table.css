/* 统一表格样式 - 基于需求提报页的表格样式 */

/* 表格基础样式 */
.unified-table {
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  min-height: 400px; /* 设置最小高度，防止空数据时表头挤压 */
}

/* 表格空状态样式 */
.unified-table .ant-table-placeholder {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保表格在空数据时保持列宽 */
.unified-table .ant-table-thead > tr > th {
  min-width: 80px; /* 设置最小列宽 */
}

/* 表格布局固定，防止空数据时列宽变化 */
.unified-table .ant-table {
  table-layout: fixed;
}

.unified-table .ant-table-thead > tr > th {
  background: var(--bg-secondary);
  border-bottom: 2px solid var(--border-light);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--text-sm);
  padding: 12px 16px;
}

.unified-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  font-size: var(--text-sm);
  border-bottom: 1px solid var(--border-light);
}

.unified-table .ant-table-tbody > tr:hover > td {
  background: var(--bg-hover);
}

/* 表格标题样式 */
.table-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  justify-content: flex-start;
}

.table-title .anticon {
  color: var(--primary-color);
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.table-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
  word-break: break-word;
}

/* 统一筛选区域样式 */
.unified-filter-section {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker,
.unified-filter-section .amount-input .ant-input-number,
.unified-filter-section .amount-input {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus,
.unified-filter-section .amount-input .ant-input-number:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover,
.unified-filter-section .amount-input .ant-input-number:hover {
  border-color: var(--primary-light);
}

.filter-result-text {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  margin-left: var(--space-lg);
}

/* 操作按钮区域样式 */
.action-buttons-section {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  color: var(--text-inverse);
  height: var(--button-height-lg);
  border-radius: var(--radius-sm);
  font-weight: 500;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  font-size: var(--text-sm);
  padding: 0 var(--space-xl);
  min-width: 140px;
}

.primary-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: var(--text-inverse);
}

.primary-action-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.secondary-action-btn {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  height: var(--button-height-lg);
  border-radius: var(--radius-sm);
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: var(--text-sm);
  padding: 0 var(--space-xl);
  min-width: 140px;
}

.secondary-action-btn:hover {
  border-color: var(--primary-light);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* 表格操作按钮样式 */
.action-buttons {
  display: flex;
  gap: var(--space-xs);
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.action-btn {
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition-normal);
  border-radius: var(--radius-sm);
  padding: 4px 8px;
  font-size: var(--text-sm);
}

.action-btn:hover {
  color: var(--primary-dark);
  background: rgba(30, 58, 138, 0.1);
}

/* 统一表格中的操作按钮样式 */
.unified-table .ant-btn-link {
  font-size: var(--text-sm) !important;
  font-weight: 500;
  padding: 4px 8px;
}

/* 统一表格中的状态标签样式 */
.unified-table .ant-tag {
  font-size: var(--text-sm) !important;
  font-weight: 500;
  border-radius: var(--radius-sm);
}

/* 统一表格中的角色类型标签样式 */
.unified-table .role-type-tag {
  font-size: var(--text-sm) !important;
  font-weight: 500;
}

/* 表格容器样式 */
.table-section {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  width: 100%;
  box-sizing: border-box;
}

/* 表格滚动优化 */
.table-container {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

.business-table .ant-table-container {
  overflow-x: auto;
  overflow-y: auto;
}

.business-table .ant-table-body {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* 字段筛选模态框样式 */
.column-filter-modal .ant-modal {
  width: auto ;
  min-width: 600px;
  max-width: 95vw !important;
}

.column-filter-modal .ant-modal-header {
  padding:20px 20px 0 20px;
  margin-bottom: 0px;
}

.column-filter-modal .ant-modal-content {
  padding: 0;
}

.column-filter-modal .ant-modal-body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 字段筛选面板统一样式 - 优化为横向一行布局 */
.column-filter-panel {
  width: 100%;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

/* 字段分类容器 - 使用flex横向一行布局，不换行 */
.field-categories-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 20px;
  align-items: flex-start;
  overflow-x: auto;
  padding: 20px;
}

/* 单个字段分类 - 固定宽度，不换行 */
.field-category-section {
  flex: 0 0 auto;
  width: 200px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 分类标题 */
.field-category-section .category-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 6px;
}


/* 分类字段容器 */
.field-category-section .category-fields {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.field-category-section .category-fields .ant-checkbox-wrapper{
  margin-bottom: 5px;
}

/* 单个字段选项 */
.field-category-section .column-option {
  display: flex;
  align-items: center;
  padding: 2px 0;
}

/* 字段标题 */
.field-category-section .column-title {
  font-size: 13px;
  color: #595959;
}

/* 必选标签 */
.field-category-section .ant-tag {
  margin-left: 8px;
  font-size: 10px;
  padding: 0 4px;
  height: 18px;
  line-height: 18px;
}

/* 响应式设计 - 字段筛选面板 */
@media (max-width: 1200px) {
  .column-filter-panel {
    min-width: 500px;
  }

  .field-categories-container {
    gap: 16px;
  }

  .field-category-section {
    width: 160px;
  }
}

@media (max-width: 992px) {
  .column-filter-panel {
    min-width: 400px;
  }

  .field-categories-container {
    gap: 12px;
  }

  .field-category-section {
    width: 140px;
    padding: 10px;
  }
}

/* 移除响应式样式，保持固定布局 */

/* 表格列宽调节 */
.business-table .ant-table-thead > tr > th {
  position: relative;
  resize: horizontal;
  overflow: hidden;
}

.business-table .ant-table-thead > tr > th:hover {
  cursor: col-resize;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header {
    padding: var(--space-md) var(--space-lg);
  }

  .table-title {
    font-size: var(--text-md);
    gap: var(--space-xs);
  }

  .table-title .anticon {
    font-size: var(--text-md);
  }

  .table-subtitle {
    font-size: var(--text-xs);
  }

  .unified-filter-section {
    padding: var(--space-md) var(--space-lg);
  }

  .action-buttons-section {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-sm);
  }

  .action-buttons-section .ant-space {
    justify-content: center;
  }

  .unified-table .ant-table-thead > tr > th,
  .unified-table .ant-table-tbody > tr > td {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-xs);
  }
}

@media (max-width: 480px) {
  .table-header {
    padding: var(--space-sm) var(--space-md);
    text-align: center;
  }

  .table-title {
    font-size: var(--text-sm);
    justify-content: center;
    gap: var(--space-xs);
  }

  .table-title .anticon {
    font-size: var(--text-sm);
  }

  .table-subtitle {
    font-size: 11px;
    line-height: 1.4;
    margin-top: var(--space-xs);
  }

  .unified-filter-section {
    padding: var(--space-sm);
  }

  .action-buttons-section {
    padding: var(--space-sm);
  }

  .unified-table {
    font-size: var(--text-xs);
  }

  .unified-table .ant-table-thead > tr > th {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
  }

  .unified-table .ant-table-tbody > tr > td {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
  }
}

@media (max-width: 360px) {
  .table-header {
    padding: 6px 8px;
  }

  .table-title {
    font-size: 11px;
    gap: 1px;
  }

  .table-title .anticon {
    font-size: 12px;
  }

  .table-subtitle {
    font-size: 9px;
    line-height: 1.2;
  }

  .unified-filter-section {
    padding: 6px 8px;
  }

  .action-buttons-section {
    padding: 6px 8px;
  }

  .primary-action-btn,
  .secondary-action-btn {
    height: 40px;
    font-size: var(--text-xs);
  }
}
