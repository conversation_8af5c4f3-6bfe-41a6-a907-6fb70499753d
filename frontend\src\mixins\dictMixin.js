/**
 * 字典混入
 * 为组件提供统一的字典功能
 */

import { ref, reactive, onMounted } from 'vue'
import dictService from '@/services/dictService'

export function useDictMixin(dictTypes = []) {
  // 字典数据存储
  const dictData = reactive({})
  const dictLoading = ref(false)
  const dictMaps = reactive({})

  // 加载指定的字典数据
  const loadDicts = async (types = dictTypes) => {
    if (!types || types.length === 0) return

    try {
      dictLoading.value = true
      const batchData = await dictService.getBatchDicts(types)

      // 更新字典数据
      Object.keys(batchData).forEach(type => {
        dictData[type] = batchData[type]
        // 创建映射
        dictMaps[type] = {}
        batchData[type].forEach(item => {
          dictMaps[type][item.code] = item.name
        })
      })
    } catch (error) {
      // 加载字典数据失败：静默处理
    } finally {
      dictLoading.value = false
    }
  }

  // 获取字典选项（用于下拉框）
  const getDictOptions = (dictType) => {
    const items = dictData[dictType] || []
    return items.map(item => ({
      label: item.name,
      value: item.code,
      ...item
    }))
  }

  // 获取字典文本
  const getDictText = (dictType, code, defaultText = '-') => {
    if (!code) return defaultText
    const map = dictMaps[dictType] || {}
    return map[code] || code || defaultText
  }

  // 刷新字典数据
  const refreshDicts = async (types = dictTypes) => {
    // 清除缓存
    types.forEach(type => {
      dictService.clearCache(type)
    })
    // 重新加载
    await loadDicts(types)
  }

  // 预定义的字典转换函数
  const getStatusText = (status) => getDictText('status', status)
  const getPurchaseTypeText = (type) => getDictText('purchase_type', type)
  const getProcurementMethodText = (method) => getDictText('procurement_method', method)
  const getFundProjectText = (project) => getDictText('fund_project', project)
  const getUnitText = (unit) => getDictText('unit', unit)
  const getItemCategoryText = (category) => getDictText('item_category', category)
  const getUrgencyLevelText = (level) => getDictText('urgency_level', level)

  // 获取状态颜色（支持中文和英文状态，统一颜色映射）
  const getStatusColor = (status) => {
    // 统一的状态颜色映射（中英文对应相同颜色）
    const colorMap = {
      // 中文状态
      '草稿': 'default',
      '待审批': 'processing',
      '已审批': 'success',
      '已驳回': 'error',
      '待采购': 'warning',
      '已采购': 'success',
      '已退回': 'error',
      '待验收': 'processing',
      '已验收': 'success',
      '待结算': 'warning',
      '已结算': 'success',

      // 英文状态（对应相同颜色）
      'draft': 'default',
      'pending_approval': 'processing',
      'approved': 'success',
      'rejected': 'error',
      'pending_purchase': 'warning',
      'purchased': 'success',
      'returned': 'error',
      'pending_acceptance': 'processing',
      'accepted': 'success',
      'pending_reimbursement': 'warning',
      'settled': 'success'
    }

    return colorMap[status] || 'default'
  }

  // 格式化日期函数
  const formatDate = (date, format = 'YYYY-MM-DD') => {
    if (!date) return '-'

    const d = new Date(date)
    if (isNaN(d.getTime())) return '-'

    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  }

  // 格式化日期为年/月/日格式（统一实现）
  const formatDateToYMD = (dateString) => {
    if (!dateString) return '-'
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return '-'

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    } catch (error) {
      // 日期格式化错误：返回原始值
      return '-'
    }
  }

  // 格式化日期为年-月-日 时-分-秒格式（详情页面专用）
  const formatDateTimeDetail = (dateString) => {
    if (!dateString) return '无'
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return '无'

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    } catch (error) {
      // 日期时间格式化错误：返回原始值
      return '无'
    }
  }

  // 组件挂载时自动加载字典
  onMounted(() => {
    if (dictTypes && dictTypes.length > 0) {
      loadDicts()
    }
  })

  return {
    // 数据
    dictData,
    dictLoading,
    dictMaps,

    // 方法
    loadDicts,
    getDictOptions,
    getDictText,
    refreshDicts,

    // 预定义转换函数
    getStatusText,
    getPurchaseTypeText,
    getProcurementMethodText,
    getFundProjectText,
    getUnitText,
    getItemCategoryText,
    getUrgencyLevelText,
    getStatusColor,
    formatDate,
    formatDateToYMD,
    formatDateTimeDetail
  }
}

// 导出常用字典类型常量（使用英文编码）
export const DICT_TYPES = {
  STATUS: 'status',
  PURCHASE_TYPE: 'purchase_type',
  PROCUREMENT_METHOD: 'procurement_method',
  FUND_PROJECT: 'fund_project',
  UNIT: 'unit',
  ITEM_CATEGORY: 'item_category',
  URGENCY_LEVEL: 'urgency_level'
}

// 导出所有字典类型数组
export const ALL_DICT_TYPES = Object.values(DICT_TYPES)

// 导出页面常用字典类型组合
export const PAGE_DICT_TYPES = {
  // 需求提报页面
  REQUIREMENT: [
    DICT_TYPES.STATUS,
    DICT_TYPES.PURCHASE_TYPE,
    DICT_TYPES.PROCUREMENT_METHOD,
    DICT_TYPES.FUND_PROJECT,
    DICT_TYPES.UNIT,
    DICT_TYPES.ITEM_CATEGORY,
    DICT_TYPES.REQUIREMENT_SOURCE,
    DICT_TYPES.URGENCY_LEVEL
  ],
  // 审批页面
  APPROVAL: [
    DICT_TYPES.STATUS,
    DICT_TYPES.PURCHASE_TYPE,
    DICT_TYPES.PROCUREMENT_METHOD
  ],
  // 采购页面
  PROCUREMENT: [
    DICT_TYPES.STATUS,
    DICT_TYPES.PROCUREMENT_METHOD,
    DICT_TYPES.UNIT
  ],
  // 验收页面
  ACCEPTANCE: [
    DICT_TYPES.STATUS,
    DICT_TYPES.UNIT
  ],
  // 结算页面
  SETTLEMENT: [
    DICT_TYPES.STATUS
  ]
}
