# Generated by Django 5.2.4 on 2025-07-30 01:39

import django.contrib.auth.validators
import django.utils.timezone
from django.db import migrations, models, connection
import apps.authentication.models


def remove_django_groups_tables(apps, schema_editor):
    """安全地移除Django内置的groups和user_permissions相关表"""
    with connection.cursor() as cursor:
        # 检查并删除可能存在的表
        tables_to_check = [
            'sys_user_groups',
            'sys_user_user_permissions'
        ]

        for table_name in tables_to_check:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
                print(f"✅ 删除表 {table_name}")
            except Exception as e:
                print(f"⚠️ 删除表 {table_name} 时出错: {e}")


def reverse_remove_django_groups_tables(apps, schema_editor):
    """回滚操作 - 无法恢复已删除的表"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0001_initial"),
    ]

    operations = [
        # 使用Python函数安全地移除相关表
        migrations.RunPython(
            remove_django_groups_tables,
            reverse_remove_django_groups_tables
        ),
        migrations.AlterModelOptions(
            name="user",
            options={
                "default_permissions": (),
                "verbose_name": "用户",
                "verbose_name_plural": "用户管理",
            },
        ),
        migrations.AlterModelManagers(
            name="user",
            managers=[
                ("objects", apps.authentication.models.UserManager()),
            ],
        ),
        migrations.AlterField(
            model_name="user",
            name="date_joined",
            field=models.DateTimeField(
                default=django.utils.timezone.now, verbose_name="注册时间"
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="email",
            field=models.EmailField(
                blank=True, max_length=254, verbose_name="邮箱地址"
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="first_name",
            field=models.CharField(blank=True, max_length=150, verbose_name="名字"),
        ),
        migrations.AlterField(
            model_name="user",
            name="is_active",
            field=models.BooleanField(
                default=True,
                help_text="指定是否应将此用户视为活动用户。取消选择此项而不是删除帐户。",
                verbose_name="激活状态",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="is_staff",
            field=models.BooleanField(
                default=False,
                help_text="指定用户是否可以登录管理站点。",
                verbose_name="管理员状态",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="last_name",
            field=models.CharField(blank=True, max_length=150, verbose_name="姓氏"),
        ),
        migrations.AlterField(
            model_name="user",
            name="role",
            field=models.CharField(
                blank=True,
                help_text="用户角色代码，如admin/approver/acceptor/finance等",
                max_length=20,
                verbose_name="角色",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="username",
            field=models.CharField(
                error_messages={"unique": "该用户名已存在。"},
                help_text="必填。150个字符以内。只能包含字母、数字和@/./+/-/_字符。",
                max_length=150,
                unique=True,
                validators=[django.contrib.auth.validators.UnicodeUsernameValidator()],
                verbose_name="用户名",
            ),
        ),
    ]
