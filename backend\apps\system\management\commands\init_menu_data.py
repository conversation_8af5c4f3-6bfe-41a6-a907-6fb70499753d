from django.core.management.base import BaseCommand
from django.db import transaction
from apps.system.models import Menu
from apps.roles.models import Role


class Command(BaseCommand):
    help = '初始化菜单数据和角色菜单关联'

    def handle(self, *args, **options):
        self.stdout.write("🚀 开始初始化菜单数据...")
        
        try:
            with transaction.atomic():
                # 清空现有数据
                Menu.objects.all().delete()

                # 创建菜单数据
                self.create_menus()

                # 注意：角色菜单关联现在通过权限系统管理
                
                self.stdout.write(self.style.SUCCESS("✅ 菜单数据初始化完成！"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ 初始化失败: {str(e)}"))
            raise

    def create_menus(self):
        """创建菜单数据"""
        self.stdout.write("📋 创建菜单数据...")
        
        # 系统管理菜单
        system_menu = Menu.objects.create(
            id=1, menu_name='系统管理', menu_code='system_management',
            route_path='/settings', icon='SettingOutlined', sort_order=1,
            permission_code='system:view'
        )
        
        Menu.objects.create(
            id=2, parent=system_menu, menu_name='用户管理', menu_code='user_management',
            route_path='/settings/users', component_path='settings/users/Users',
            icon='UserOutlined', sort_order=1, permission_code='user:view'
        )
        
        Menu.objects.create(
            id=3, parent=system_menu, menu_name='角色管理', menu_code='role_management',
            route_path='/settings/roles', component_path='settings/roles/Roles',
            icon='SafetyCertificateOutlined', sort_order=2, permission_code='role:view'
        )
        
        Menu.objects.create(
            id=4, parent=system_menu, menu_name='权限管理', menu_code='permission_management',
            route_path='/settings/permissions', component_path='settings/permissions/PermissionsMatrix',
            icon='KeyOutlined', sort_order=3, permission_code='permission:view'
        )
        
        Menu.objects.create(
            id=5, parent=system_menu, menu_name='数据字典', menu_code='dict_management',
            route_path='/settings/dicts', component_path='settings/dicts/Dicts',
            icon='BookOutlined', sort_order=4, permission_code='dict:view'
        )
        
        Menu.objects.create(
            id=6, parent=system_menu, menu_name='日志管理', menu_code='log_management',
            route_path='/settings/logs', component_path='settings/logs/LogManagement',
            icon='FileSearchOutlined', sort_order=5, permission_code='log:view'
        )
        
        Menu.objects.create(
            id=7, parent=system_menu, menu_name='通知管理', menu_code='notification_management',
            route_path='/settings/notifications', component_path='settings/notifications/NotificationList',
            icon='BellOutlined', sort_order=6, permission_code='notification:view'
        )
        
        # 采购管理菜单
        purchase_menu = Menu.objects.create(
            id=10, menu_name='采购管理', menu_code='purchase_management',
            route_path='/purchase', icon='ShoppingCartOutlined', sort_order=2,
            permission_code='purchase:view'
        )
        
        Menu.objects.create(
            id=11, parent=purchase_menu, menu_name='采购概览', menu_code='purchase_overview',
            route_path='/purchase/overview', component_path='purchase/overview/Overview',
            icon='BarChartOutlined', sort_order=1, permission_code='purchase:view'
        )
        
        Menu.objects.create(
            id=12, parent=purchase_menu, menu_name='需求提报', menu_code='purchase_requests',
            route_path='/purchase-requests', component_path='purchase/requests/List',
            icon='FileTextOutlined', sort_order=2, permission_code='purchase:request',
            business_status='draft,pending_approval'
        )
        
        Menu.objects.create(
            id=13, parent=purchase_menu, menu_name='需求审批', menu_code='purchase_approval',
            route_path='/purchase/approval', component_path='purchase/approval/Approval',
            icon='AuditOutlined', sort_order=3, permission_code='purchase:approve',
            business_status='pending_approval'
        )
        
        Menu.objects.create(
            id=14, parent=purchase_menu, menu_name='物资采购', menu_code='purchase_procurement',
            route_path='/purchase/procurement', component_path='purchase/procurement/Procurement',
            icon='ShoppingOutlined', sort_order=4, permission_code='purchase:execute',
            business_status='approved,pending_purchase'
        )
        
        Menu.objects.create(
            id=15, parent=purchase_menu, menu_name='物资验收', menu_code='purchase_acceptance',
            route_path='/acceptances', component_path='purchase/acceptance/List',
            icon='CheckCircleOutlined', sort_order=5, permission_code='purchase:accept',
            business_status='purchased,pending_acceptance'
        )
        
        Menu.objects.create(
            id=16, parent=purchase_menu, menu_name='结算报销', menu_code='purchase_reimbursement',
            route_path='/reimbursements', component_path='purchase/reimbursement/List',
            icon='AccountBookOutlined', sort_order=6, permission_code='purchase:reimburse',
            business_status='accepted,pending_reimbursement'
        )
        
        # 个人中心菜单
        Menu.objects.create(
            id=20, menu_name='个人中心', menu_code='personal_center',
            route_path='/profile', component_path='profile/UserProfile',
            icon='UserOutlined', sort_order=3, permission_code='profile:view'
        )
        
        self.stdout.write("✅ 菜单数据创建完成")



    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新初始化，清空现有数据',
        )
