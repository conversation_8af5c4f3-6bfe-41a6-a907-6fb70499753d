import store from '@/store'

/**
 * 权限指令
 * 用法：
 * v-permission="'user:add'"
 * v-permission="['user:add', 'user:edit']"
 * v-permission:role="'admin'"
 */
export default {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    checkPermission(el, binding)
  }
}

function checkPermission(el, binding) {
  const { value, arg, modifiers } = binding

  let hasPermission = false

  // 检查角色权限
  if (arg === 'role') {
    hasPermission = store.getters.hasRole(value)
  } else {
    // 检查功能权限
    hasPermission = store.getters.hasPermission(value)
  }

  // 根据修饰符决定处理方式
  if (!hasPermission) {
    if (modifiers.hide) {
      // v-permission.hide - 隐藏元素
      el.style.display = 'none'
    } else if (modifiers.disable) {
      // v-permission.disable - 禁用元素
      el.setAttribute('disabled', 'disabled')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
    } else {
      // 默认行为：隐藏元素
      el.style.display = 'none'
      el.setAttribute('disabled', 'disabled')
    }

    // 添加权限不足的提示
    if (modifiers.tooltip) {
      el.setAttribute('title', '权限不足')
    }
  } else {
    // 有权限时恢复元素状态
    el.style.display = ''
    el.style.opacity = ''
    el.style.cursor = ''
    el.removeAttribute('disabled')
    el.removeAttribute('title')
  }
}

/**
 * 权限检查工具函数
 */
export const hasPermission = (permission) => {
  const userPermissions = store.getters.userPermissions || []
  
  if (Array.isArray(permission)) {
    return permission.some(p => 
      userPermissions.includes('*') || userPermissions.includes(p)
    )
  }
  
  return userPermissions.includes('*') || userPermissions.includes(permission)
}

/**
 * 角色检查工具函数
 */
export const hasRole = (role) => {
  const userRole = store.getters.userRole?.code
  
  if (Array.isArray(role)) {
    return role.includes(userRole)
  }
  
  return userRole === role
}

/**
 * 权限检查混入
 */
export const permissionMixin = {
  methods: {
    $hasPermission: hasPermission,
    $hasRole: hasRole,
    
    // 检查采购流程状态权限
    $canOperateStatus(status) {
      const userRole = store.getters.userRole?.code
      
      const statusPermissions = {
        'draft': ['requester', 'super_admin'],
        'pending_approval': ['approver', 'manager_approver', 'super_admin'],
        'approved': ['purchaser', 'super_admin'],
        'purchased': ['acceptor', 'super_admin'],
        'accepted': ['finance', 'super_admin'],
        'pending_purchase': ['purchaser', 'super_admin'],
        'pending_acceptance': ['acceptor', 'super_admin'],
        'pending_reimbursement': ['finance', 'super_admin']
      }
      
      return statusPermissions[status]?.includes(userRole) || false
    }
  }
}
