"""
ASGI config for purchase_system project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
import django
from channels.auth import AuthMiddlewareStack
from channels.routing import ProtocolTypeRouter, URLRouter
from django.core.asgi import get_asgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'purchase_system.settings')

# 初始化Django
django.setup()

# 初始化Django ASGI应用
django_asgi_app = get_asgi_application()

# 导入WebSocket路由（在Django初始化之后）
import apps.system.routing

# 配置ASGI应用
application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": AuthMiddlewareStack(
        URLRouter(
            apps.system.routing.websocket_urlpatterns
        )
    ),
})
