<template>
  <div class="menu-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <MenuOutlined />
          菜单管理
        </h1>
        <p class="page-subtitle">
          管理系统菜单结构，配置菜单权限和显示顺序
        </p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalCount }}</span>
          <span class="stat-label">菜单总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ pageCount }}</span>
          <span class="stat-label">主页面数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ buttonCount }}</span>
          <span class="stat-label">按钮总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ activeCount }}</span>
          <span class="stat-label">启用个数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ inactiveCount }}</span>
          <span class="stat-label">禁用个数</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="unified-filter-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-input v-model:value="searchTerm" placeholder="菜单名称、路径、权限" allow-clear @pressEnter="handleSearch"
            class="filter-input">
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="statusFilter" placeholder="选择状态" allowClear @change="handleStatusFilterChange"
            class="filter-select">
            <a-select-option value="1">启用</a-select-option>
            <a-select-option value="0">禁用</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="typeFilter" placeholder="菜单类型" allowClear @change="handleTypeFilterChange"
            class="filter-select">
            <a-select-option value="directory">目录</a-select-option>
            <a-select-option value="page">页面</a-select-option>
            <a-select-option value="button">按钮</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button type="primary" @click="handleSearch" class="filter-button">
            <SearchOutlined />
            搜索
          </a-button>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="resetFilters" class="filter-button">
            <ClearOutlined />
            重置
          </a-button>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button type="primary" @click="showAddModal" class="filter-button">
            <PlusOutlined />
            新增菜单
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 菜单树表格 -->
    <div class="business-card">
      <a-table
        :columns="columns"
        :data-source="menuTreeData"
        :loading="loading"
        :pagination="false"
        row-key="id"
        :default-expand-all-rows="true"
        :child-row-key="'children'"
        :indent-size="20"
        class="menu-tree-table"
      >
        <!-- 菜单名称列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'menu_name'">
            <div class="menu-name-cell">
              <component :is="getIconComponent(record.icon)" v-if="record.icon" />
              <span>{{ record.menu_name }}</span>
              <a-tag v-if="record.menu_type === 'directory'" color="purple" size="small">目录</a-tag>
              <a-tag v-else-if="record.menu_type === 'page'" color="green" size="small">页面</a-tag>
              <a-tag v-else-if="record.menu_type === 'button'" color="blue" size="small">按钮</a-tag>
            </div>
          </template>
          
          <!-- 状态列 -->
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.is_active ? 'green' : 'red'">
              {{ record.is_active ? '启用' : '禁用' }}
            </a-tag>
            <a-tag v-if="!record.is_visible" color="orange">隐藏</a-tag>
          </template>
          
          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-tooltip title="查看详情">
                <a-button type="link" size="small" @click="viewMenuDetail(record)">
                  <EyeOutlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="编辑菜单">
                <a-button type="link" size="small" @click="editMenu(record)">
                  <EditOutlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="添加子菜单" v-if="record.menu_type === 'directory' || record.menu_type === 'page'">
                <a-button type="link" size="small" @click="addChildMenu(record)">
                  <PlusOutlined />
                </a-button>
              </a-tooltip>
              <a-tooltip :title="record.is_active ? '点击禁用' : '点击启用'">
                <a-switch
                  :checked="record.is_active"
                  @change="(checked) => handleSwitchChange(record, checked)"
                  size="small"
                  :checked-children="'启用'"
                  :un-checked-children="'禁用'"
                />
              </a-tooltip>
              <a-tooltip title="删除菜单">
                <a-popconfirm
                  title="确定要删除这个菜单吗？"
                  @confirm="deleteMenu(record)"
                >
                  <a-button type="link" size="small" danger>
                    <DeleteOutlined />
                  </a-button>
                </a-popconfirm>
              </a-tooltip>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 菜单编辑模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <template #footer>
        <a-button @click="handleCancel">
          {{ isViewMode ? '关闭' : '取消' }}
        </a-button>
        <a-button v-if="!isViewMode" type="primary" @click="handleSubmit">
          保存
        </a-button>
      </template>
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="菜单名称" name="menu_name">
              <a-input v-model:value="formData.menu_name" placeholder="请输入菜单名称" :disabled="isViewMode" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="菜单编码" name="menu_code">
              <a-input v-model:value="formData.menu_code" placeholder="请输入菜单编码" :disabled="isViewMode" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="菜单类型" name="menu_type">
              <a-select v-model:value="formData.menu_type" :disabled="isViewMode" @change="handleMenuTypeChange">
                <a-select-option value="directory">目录</a-select-option>
                <a-select-option value="page">页面</a-select-option>
                <a-select-option value="button">按钮</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="父菜单" name="parent">
              <a-tree-select
                v-model:value="formData.parent"
                :tree-data="parentMenuOptions"
                placeholder="请选择父菜单"
                allow-clear
                tree-default-expand-all
                :disabled="isViewMode"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" v-if="formData.menu_type !== 'button'">
          <a-col :span="12">
            <a-form-item
              :label="formData.menu_type === 'directory' ? '重定向路径' : '路由路径'"
              name="route_path"
            >
              <a-input
                v-model:value="formData.route_path"
                :placeholder="formData.menu_type === 'directory' ? '如：/settings/users（可选）' : '如：/settings/users'"
                :disabled="isViewMode"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="formData.menu_type === 'page'">
            <a-form-item label="组件路径" name="component_path">
              <a-input v-model:value="formData.component_path" placeholder="如：settings/users/Users" :disabled="isViewMode" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="菜单图标" name="icon">
              <a-input v-model:value="formData.icon" placeholder="如：UserOutlined" :disabled="isViewMode" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序" name="sort_order">
              <a-input-number v-model:value="formData.sort_order" :min="0" style="width: 100%" :disabled="isViewMode" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="权限标识" name="permission_code">
          <a-input v-model:value="formData.permission_code" placeholder="如：user:view" :disabled="isViewMode" />
        </a-form-item>

        <a-form-item label="业务状态" name="business_status">
          <a-input v-model:value="formData.business_status" placeholder="如：draft,pending_approval" :disabled="isViewMode" />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="is_visible">
              <a-checkbox v-model:checked="formData.is_visible" :disabled="isViewMode">是否显示</a-checkbox>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="is_active">
              <a-checkbox v-model:checked="formData.is_active" :disabled="isViewMode">是否启用</a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  MenuOutlined,
  PlusOutlined,
  ReloadOutlined,
  UserOutlined,
  SettingOutlined,
  ShoppingCartOutlined,
  DashboardOutlined,
  SafetyCertificateOutlined,
  KeyOutlined,
  BookOutlined,
  FileSearchOutlined,
  BellOutlined,
  BarChartOutlined,
  FileTextOutlined,
  AuditOutlined,
  ShoppingOutlined,
  CheckCircleOutlined,
  AccountBookOutlined,
  SearchOutlined,
  ClearOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons-vue'
import api from '@/api'

export default {
  name: 'MenuManagement',
  components: {
    MenuOutlined,
    PlusOutlined,
    ReloadOutlined,
    UserOutlined,
    SettingOutlined,
    ShoppingCartOutlined,
    DashboardOutlined,
    SafetyCertificateOutlined,
    KeyOutlined,
    BookOutlined,
    FileSearchOutlined,
    BellOutlined,
    BarChartOutlined,
    FileTextOutlined,
    AuditOutlined,
    ShoppingOutlined,
    CheckCircleOutlined,
    AccountBookOutlined,
    SearchOutlined,
    ClearOutlined,
    EditOutlined,
    DeleteOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,

  },
  setup() {
    
    // 响应式数据
    const loading = ref(false)
    const menuTreeData = ref([])
    const modalVisible = ref(false)
    const modalTitle = ref('')
    const formRef = ref()
    const isEdit = ref(false)

    // 搜索和筛选
    const searchTerm = ref('')
    const statusFilter = ref(undefined)
    const typeFilter = ref(undefined)

    // 统计数据
    const statisticsData = ref({
      total_count: 0,
      active_count: 0,
      inactive_count: 0,
      page_count: 0,
      button_count: 0,
      top_level_count: 0,
      sub_menu_count: 0
    })
    
    // 表单数据
    const formData = reactive({
      id: null,
      parent: null,
      menu_name: '',
      menu_code: '',
      menu_type: 'page',
      route_path: '',
      component_path: '',
      icon: '',
      sort_order: 0,
      is_visible: true,
      is_active: true,
      permission_code: '',
      business_status: ''
    })
    
    // 表单验证规则 - 动态根据菜单类型调整
    const formRules = computed(() => {
      const baseRules = {
        menu_name: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' }
        ],
        menu_code: [
          { required: true, message: '请输入菜单编码', trigger: 'blur' }
        ],
        menu_type: [
          { required: true, message: '请选择菜单类型', trigger: 'change' }
        ]
      }

      // 根据菜单类型添加额外的验证规则
      if (formData.menu_type === 'page') {
        // 页面类型需要路由路径和组件路径
        baseRules.route_path = [
          { required: true, message: '请输入路由路径', trigger: 'blur' }
        ]
        baseRules.component_path = [
          { required: true, message: '请输入组件路径', trigger: 'blur' }
        ]
      } else if (formData.menu_type === 'directory') {
        // 目录类型可选路由路径（用于重定向，通常不需要，但为了统一性，按钮也需要）（按钮通常不需要）
        baseRules.route_path = [
          { required: false }
        ]
      }
      // 按钮类型不需要路由路径和组件路径

      return baseRules
    })
    
    // 表格列定义
    const columns = [
      {
        title: '菜单名称',
        dataIndex: 'menu_name',
        key: 'menu_name',
        width: 200
      },
      {
        title: '菜单编码',
        dataIndex: 'menu_code',
        key: 'menu_code',
        width: 150
      },
      {
        title: '路由路径',
        dataIndex: 'route_path',
        key: 'route_path',
        width: 200
      },
      {
        title: '权限标识',
        dataIndex: 'permission_code',
        key: 'permission_code',
        width: 150
      },
      {
        title: '排序',
        dataIndex: 'sort_order',
        key: 'sort_order',
        width: 80
      },
      {
        title: '状态',
        key: 'status',
        width: 120
      },
      {
        title: '操作',
        key: 'action',
        width: 280,
        fixed: 'right'
      }
    ]
    
    // 计算属性
    const parentMenuOptions = computed(() => {
      const buildOptions = (menus, level = 0) => {
        return menus.map(menu => ({
          title: menu.menu_name,
          value: menu.id,
          key: menu.id,
          disabled: menu.menu_type === 'button' || (formData.menu_type === 'button' && menu.menu_type !== 'page'),
          children: menu.children ? buildOptions(menu.children, level + 1) : undefined
        }))
      }

      return buildOptions(menuTreeData.value)
    })

    // 统计数据计算 - 使用后端真实数据，带默认值
    const totalCount = computed(() => statisticsData.value.total_count || 0)
    const activeCount = computed(() => statisticsData.value.active_count || 0)
    const inactiveCount = computed(() => statisticsData.value.inactive_count || 0)
    const pageCount = computed(() => statisticsData.value.page_count || 0)
    const buttonCount = computed(() => statisticsData.value.button_count || 0)
    const topLevelCount = computed(() => statisticsData.value.top_level_count || 0)
    const subMenuCount = computed(() => statisticsData.value.sub_menu_count || 0)
    
    // 获取图标组件
    const getIconComponent = (iconName) => {
      const iconMap = {
        'UserOutlined': UserOutlined,
        'SettingOutlined': SettingOutlined,
        'ShoppingCartOutlined': ShoppingCartOutlined,
        'DashboardOutlined': DashboardOutlined,
        'SafetyCertificateOutlined': SafetyCertificateOutlined,
        'KeyOutlined': KeyOutlined,
        'BookOutlined': BookOutlined,
        'FileSearchOutlined': FileSearchOutlined,
        'BellOutlined': BellOutlined,
        'BarChartOutlined': BarChartOutlined,
        'FileTextOutlined': FileTextOutlined,
        'AuditOutlined': AuditOutlined,
        'ShoppingOutlined': ShoppingOutlined,
        'CheckCircleOutlined': CheckCircleOutlined,
        'AccountBookOutlined': AccountBookOutlined
      }
      
      return iconMap[iconName] || UserOutlined
    }
    
    // 原始菜单数据（未筛选）
    const originalMenuData = ref([])

    // 数据获取函数
    const loadMenuData = async () => {
      loading.value = true
      try {
        // 同时获取菜单数据和统计数据（使用管理模式获取所有菜单）
        const [menuResponse, statsResponse] = await Promise.all([
          api.menus.getTree(true), // 管理模式：获取所有菜单包括禁用的
          api.menus.getStatistics()
        ])

        if (menuResponse.code === 200) {
          originalMenuData.value = menuResponse.data || []
          // 应用筛选
          applyFilters()
        } else {
          message.error('获取菜单数据失败')
        }

        if (statsResponse.code === 200) {
          statisticsData.value = statsResponse.data || {}
          console.log('📊 统计数据加载成功:', statisticsData.value)
        } else {
          console.error('❌ 获取统计数据失败:', statsResponse)
          message.error('获取统计数据失败')
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        message.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }

    // 应用筛选
    const applyFilters = () => {
      let filteredData = [...originalMenuData.value]

      // 递归筛选函数
      const filterMenus = (menus) => {
        return menus.filter(menu => {
          // 搜索词筛选
          if (searchTerm.value) {
            const searchLower = searchTerm.value.toLowerCase()
            const matchesSearch =
              menu.menu_name.toLowerCase().includes(searchLower) ||
              (menu.route_path && menu.route_path.toLowerCase().includes(searchLower)) ||
              (menu.permission_code && menu.permission_code.toLowerCase().includes(searchLower)) ||
              (menu.menu_code && menu.menu_code.toLowerCase().includes(searchLower))

            if (!matchesSearch) {
              // 检查子菜单是否匹配
              if (menu.children && menu.children.length > 0) {
                const filteredChildren = filterMenus(menu.children)
                if (filteredChildren.length === 0) {
                  return false
                }
                menu.children = filteredChildren
              } else {
                return false
              }
            }
          }

          // 状态筛选
          if (statusFilter.value !== undefined) {
            const isActive = statusFilter.value === '1'
            if (menu.is_active !== isActive) {
              return false
            }
          }

          // 类型筛选
          if (typeFilter.value !== undefined) {
            if (menu.menu_type !== typeFilter.value) {
              return false
            }
          }

          // 递归处理子菜单
          if (menu.children && menu.children.length > 0) {
            menu.children = filterMenus(menu.children)
          }

          return true
        })
      }

      menuTreeData.value = filterMenus(filteredData)
    }

    // 刷新数据
    const refreshData = () => {
      loadMenuData()
    }

    // 显示新增模态框
    const showAddModal = () => {
      isEdit.value = false
      isViewMode.value = false
      modalTitle.value = '新增菜单'
      resetForm()
      modalVisible.value = true
    }

    // 编辑菜单
    const editMenu = (record) => {
      isEdit.value = true
      isViewMode.value = false
      modalTitle.value = '编辑菜单'

      // 复制菜单数据，特别处理父级菜单
      Object.assign(formData, {
        ...record,
        parent: record.parent_id || null // 使用parent_id而不是parent对象
      })

      modalVisible.value = true
    }

    // 添加子菜单
    const addChildMenu = (record) => {
      isEdit.value = false
      isViewMode.value = false
      modalTitle.value = '新增子菜单'
      resetForm()
      formData.parent = record.id
      modalVisible.value = true
    }

    // 查看菜单详情
    const isViewMode = ref(false)
    const viewMenuDetail = (record) => {
      // 创建一个只读的详情模态框
      isEdit.value = false
      isViewMode.value = true
      modalTitle.value = '菜单详情'
      Object.assign(formData, record)
      modalVisible.value = true
    }

    // 切换菜单状态（启用/禁用）
    const toggleMenuStatus = async (record) => {
      try {
        const newStatus = !record.is_active
        const response = await api.menus.update(record.id, {
          ...record,
          is_active: newStatus
        })
        if (response.code === 200) {
          message.success(`${newStatus ? '启用' : '禁用'}成功`)
          loadMenuData()
        } else {
          message.error(`${newStatus ? '启用' : '禁用'}失败`)
        }
      } catch (error) {
        console.error('切换菜单状态失败:', error)
        message.error('操作失败')
      }
    }

    // 处理开关变更（带确认提示）
    const handleSwitchChange = (record, checked) => {
      Modal.confirm({
        title: '确认操作',
        content: `确定要${checked ? '启用' : '禁用'}菜单"${record.menu_name}"吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            const response = await api.menus.update(record.id, {
              ...record,
              is_active: checked
            })
            if (response.code === 200) {
              message.success(`${checked ? '启用' : '禁用'}成功`)
              loadMenuData()
            } else {
              message.error(`${checked ? '启用' : '禁用'}失败`)
            }
          } catch (error) {
            console.error('切换菜单状态失败:', error)
            message.error('操作失败')
          }
        }
      })
    }

    // 删除菜单
    const deleteMenu = async (record) => {
      try {
        const response = await api.menus.delete(record.id)
        if (response.code === 200) {
          message.success('删除成功')
          loadMenuData()
        } else {
          message.error('删除失败')
        }
      } catch (error) {
        console.error('删除菜单失败:', error)
        message.error('删除失败')
      }
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
        id: null,
        parent: null,
        menu_name: '',
        menu_code: '',
        menu_type: 'page',
        route_path: '',
        component_path: '',
        icon: '',
        sort_order: 0,
        is_visible: true,
        is_active: true,
        permission_code: '',
        business_status: ''
      })
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()

        // 准备提交数据，确保父级菜单字段正确
        const submitData = {
          ...formData,
          parent: formData.parent || null // 确保空值时为null而不是undefined
        }

        const apiCall = isEdit.value
          ? api.menus.update(submitData.id, submitData)
          : api.menus.create(submitData)

        const response = await apiCall
        if (response.code === 200) {
          message.success(isEdit.value ? '更新成功' : '创建成功')
          modalVisible.value = false
          loadMenuData()
        } else {
          message.error(isEdit.value ? '更新失败' : '创建失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        message.error('操作失败')
      }
    }

    // 取消操作
    const handleCancel = () => {
      modalVisible.value = false
      resetForm()
    }

    // 搜索和筛选方法
    const handleSearch = () => {
      applyFilters()
    }

    const handleStatusFilterChange = () => {
      applyFilters()
    }

    const handleTypeFilterChange = () => {
      applyFilters()
    }

    // 菜单类型变化处理
    const handleMenuTypeChange = (value) => {
      // 根据菜单类型调整必填字段
      if (value === 'directory') {
        // 目录不需要组件路径，但需要路由路径（用于重定向）
        formData.component_path = ''
      } else if (value === 'button') {
        // 按钮不需要路由路径和组件路径
        formData.route_path = ''
        formData.component_path = ''
      }
      // 页面类型需要路由路径和组件路径，保持原值
    }

    const resetFilters = () => {
      searchTerm.value = ''
      statusFilter.value = undefined
      typeFilter.value = undefined
      applyFilters()
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadMenuData()
    })

    return {
      loading,
      menuTreeData,
      originalMenuData,
      modalVisible,
      modalTitle,
      formRef,
      isEdit,
      isViewMode,
      formData,
      formRules,
      columns,
      parentMenuOptions,

      // 搜索和筛选
      searchTerm,
      statusFilter,
      typeFilter,
      handleSearch,
      handleStatusFilterChange,
      handleTypeFilterChange,
      handleMenuTypeChange,
      resetFilters,
      applyFilters,

      // 统计数据
      statisticsData,
      totalCount,
      activeCount,
      inactiveCount,
      pageCount,
      buttonCount,
      topLevelCount,
      subMenuCount,

      // 方法
      getIconComponent,
      refreshData,
      showAddModal,
      viewMenuDetail,
      editMenu,
      addChildMenu,
      toggleMenuStatus,
      handleSwitchChange,
      deleteMenu,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.menu-management {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部样式 - 蓝色背景 */
.menu-management .page-header {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
  background: var(--primary-gradient) !important;
  color: white !important;
  border-radius: 12px !important;
  margin-bottom: 24px !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  box-shadow: 0 4px 20px rgba(74, 144, 226, 0.3) !important;
  border: none !important;
  position: relative !important;
}

.menu-management .page-header::before {
  display: none !important;
}

.menu-management .header-content {
  flex: 1 !important;
}

.menu-management .page-title {
  font-size: 28px !important;
  font-weight: 700 !important;
  margin: 0 0 12px 0 !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.menu-management .page-title .anticon {
  font-size: 32px !important;
  color: white !important;
}

.menu-management .page-subtitle {
  font-size: 16px !important;
  margin: 0 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  line-height: 1.5 !important;
}

/* 统计数据样式 */
.menu-management .header-stats {
  display: flex !important;
  gap: 24px !important;
  flex-wrap: wrap !important;
}

.menu-management .stat-item {
  text-align: center !important;
  background: rgba(255, 255, 255, 0.15) !important;
  padding: 16px 20px !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  min-width: 100px !important;
  transition: all 0.3s ease !important;
}

.menu-management .stat-item:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  transform: translateY(-2px) !important;
}

.menu-management .stat-number {
  display: block !important;
  font-size: 32px !important;
  font-weight: 700 !important;
  color: white !important;
  line-height: 1 !important;
  margin-bottom: 8px !important;
}

.menu-management .stat-label {
  display: block !important;
  font-size: 14px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
}

/* 筛选区域样式 */
.unified-filter-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.filter-input,
.filter-select,
.filter-button {
  width: 100%;
  height: 40px;
}

/* 表格样式 */
.business-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.menu-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-tree-table {
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
  }

  .header-stats {
    width: 100%;
    justify-content: center;
  }

  .stat-item {
    flex: 1;
    min-width: 80px;
  }
}
</style>
