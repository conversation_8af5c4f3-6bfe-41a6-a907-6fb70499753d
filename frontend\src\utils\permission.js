/**
 * 权限控制管理器
 * 基于用户角色和菜单权限的权限验证
 */

import store from '@/store'

/**
 * 权限验证类
 */
class PermissionManager {
  constructor() {
    this.userPermissions = new Set()
    this.userRole = null
    this.menuPermissions = new Map()
  }

  /**
   * 初始化用户权限
   */
  initUserPermissions() {
    const user = store.getters.currentUser
    if (!user) {
      this.clearPermissions()
      return
    }

    // 设置用户角色
    this.userRole = user.role

    // 设置用户权限
    const permissions = user.role_permissions || []
    this.userPermissions = new Set(permissions)

    console.log('🔐 用户权限初始化:', {
      role: this.userRole,
      permissions: Array.from(this.userPermissions)
    })
  }

  /**
   * 清除权限缓存
   */
  clearPermissions() {
    this.userPermissions.clear()
    this.userRole = null
    this.menuPermissions.clear()
    console.log('🗑️ 权限缓存已清除')
  }

  /**
   * 检查是否有指定权限
   */
  hasPermission(permission) {
    if (!permission) return true
    
    // 超级管理员拥有所有权限
    if (this.userRole === 'super_admin') {
      return true
    }

    return this.userPermissions.has(permission)
  }

  /**
   * 检查是否有指定角色
   */
  hasRole(role) {
    if (!role) return true
    
    if (Array.isArray(role)) {
      return role.includes(this.userRole)
    }
    
    return this.userRole === role
  }

  /**
   * 检查菜单访问权限
   */
  canAccessMenu(menu) {
    // 检查菜单是否隐藏
    if (menu.hidden) {
      return false
    }

    // 检查菜单权限
    if (menu.permission_code) {
      return this.hasPermission(menu.permission_code)
    }

    // 如果没有权限要求，默认允许访问
    return true
  }

  /**
   * 过滤用户可访问的菜单
   */
  filterAccessibleMenus(menus) {
    const filterMenus = (menuList) => {
      return menuList.filter(menu => {
        // 检查当前菜单权限
        if (!this.canAccessMenu(menu)) {
          return false
        }

        // 递归过滤子菜单
        if (menu.children && menu.children.length > 0) {
          menu.children = filterMenus(menu.children)
          
          // 如果是目录菜单且没有可访问的子菜单，则隐藏
          if (!menu.route_path && menu.children.length === 0) {
            return false
          }
        }

        return true
      })
    }

    return filterMenus(menus)
  }

  /**
   * 检查路由访问权限
   */
  canAccessRoute(route) {
    // 检查路由元信息中的权限要求
    const meta = route.meta || {}
    
    // 检查是否需要认证
    if (meta.requiresAuth && !store.getters.isAuthenticated) {
      return false
    }

    // 检查角色权限
    if (meta.roles && !this.hasRole(meta.roles)) {
      return false
    }

    // 检查具体权限
    if (meta.permission && !this.hasPermission(meta.permission)) {
      return false
    }

    return true
  }

  /**
   * 获取用户权限信息
   */
  getUserPermissionInfo() {
    return {
      role: this.userRole,
      permissions: Array.from(this.userPermissions),
      isAdmin: this.userRole === 'super_admin',
      permissionCount: this.userPermissions.size
    }
  }
}

// 创建全局权限管理器实例
const permissionManager = new PermissionManager()

// 导出权限检查函数
export const hasPermission = (permission) => permissionManager.hasPermission(permission)
export const hasRole = (role) => permissionManager.hasRole(role)
export const canAccessMenu = (menu) => permissionManager.canAccessMenu(menu)
export const canAccessRoute = (route) => permissionManager.canAccessRoute(route)
export const filterAccessibleMenus = (menus) => permissionManager.filterAccessibleMenus(menus)
export const initUserPermissions = () => permissionManager.initUserPermissions()
export const clearPermissions = () => permissionManager.clearPermissions()
export const getUserPermissionInfo = () => permissionManager.getUserPermissionInfo()

export default permissionManager
