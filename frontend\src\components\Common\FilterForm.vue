<template>
  <a-form
    :model="formData"
    layout="inline"
    class="filter-form"
    @finish="handleSubmit"
  >
    <slot :form-data="formData" :reset-form="resetForm"></slot>
  </a-form>
</template>

<script>
import { reactive, watch } from 'vue'

export default {
  name: 'FilterForm',
  props: {
    // 初始表单数据
    initialValues: {
      type: Object,
      default: () => ({})
    },
    // 是否自动提交
    autoSubmit: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'reset', 'change'],
  setup(props, { emit }) {
    // 表单数据
    const formData = reactive({ ...props.initialValues })

    // 重置表单
    const resetForm = () => {
      Object.keys(formData).forEach(key => {
        if (props.initialValues[key] !== undefined) {
          formData[key] = props.initialValues[key]
        } else {
          formData[key] = undefined
        }
      })
      emit('reset', { ...formData })
    }

    // 提交表单
    const handleSubmit = () => {
      emit('submit', { ...formData })
    }

    // 监听表单数据变化
    watch(
      formData,
      (newData) => {
        emit('change', { ...newData })
        if (props.autoSubmit) {
          handleSubmit()
        }
      },
      { deep: true }
    )

    return {
      formData,
      resetForm,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.filter-form {
  width: 100%;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
  margin-right: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
  color: #262626;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  border-radius: 6px;
  border-color: #d9d9d9;
  transition: all 0.3s;
}

:deep(.ant-input:hover),
:deep(.ant-select-selector:hover),
:deep(.ant-picker:hover) {
  border-color: #40a9ff;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>
