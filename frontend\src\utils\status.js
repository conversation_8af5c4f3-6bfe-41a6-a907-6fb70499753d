/**
 * 采购管理系统状态配置和流转逻辑
 * 统一的状态定义、流转规则、显示配置
 */

// 状态流转映射 - 定义每个状态可以转换到的下一个状态
const STATUS_TRANSITIONS = {
  'draft': ['pending_approval'],
  'pending_approval': ['approved', 'rejected'],
  'approved': ['pending_purchase'],
  'rejected': ['draft'],
  'pending_purchase': ['purchased', 'returned'],
  'purchased': ['pending_acceptance'],
  'returned': ['draft'],
  'pending_acceptance': ['accepted'],
  'accepted': ['pending_reimbursement'],
  'pending_reimbursement': ['settled', 'pending_acceptance'],  // 允许财务退回到待验收
  'settled': []
}

// 统一的状态配置 - 包含显示文本、颜色、图标、描述等
const STATUS_CONFIG = {
  'draft': {
    text: '草稿',
    color: 'default',
    chartColor: '#d9d9d9',
    icon: 'FileTextOutlined',
    description: '需求草稿，可编辑修改',
    category: 'draft',
    order: 1
  },
  'pending_approval': {
    text: '待审批',
    color: 'orange',
    chartColor: '#1890ff',
    icon: 'ClockCircleOutlined',
    description: '等待审批人审批',
    category: 'approval',
    order: 2
  },
  'approved': {
    text: '已审批',
    color: 'blue',
    chartColor: '#13c2c2',
    icon: 'CheckCircleOutlined',
    description: '审批通过，等待采购',
    category: 'approval',
    order: 3
  },
  'rejected': {
    text: '已驳回',
    color: 'red',
    chartColor: '#f5222d',
    icon: 'CloseCircleOutlined',
    description: '审批驳回，需要修改',
    category: 'approval',
    order: 4
  },
  'pending_purchase': {
    text: '待采购',
    color: 'purple',
    chartColor: '#52c41a',
    icon: 'ShoppingCartOutlined',
    description: '等待采购人员采购',
    category: 'purchase',
    order: 5
  },
  'purchased': {
    text: '已采购',
    color: 'cyan',
    chartColor: '#722ed1',
    icon: 'SyncOutlined',
    description: '采购完成，等待验收',
    category: 'purchase',
    order: 6
  },
  'returned': {
    text: '已退回',
    color: 'volcano',
    chartColor: '#fa8c16',
    icon: 'RollbackOutlined',
    description: '采购退回，需要重新处理',
    category: 'purchase',
    order: 7
  },
  'pending_acceptance': {
    text: '待验收',
    color: 'geekblue',
    chartColor: '#faad14',
    icon: 'ExclamationCircleOutlined',
    description: '等待验收人员验收',
    category: 'acceptance',
    order: 8
  },
  'accepted': {
    text: '已验收',
    color: 'green',
    chartColor: '#eb2f96',
    icon: 'CheckCircleOutlined',
    description: '验收通过，等待结算',
    category: 'acceptance',
    order: 9
  },
  'pending_reimbursement': {
    text: '待结算',
    color: 'gold',
    chartColor: '#f759ab',
    icon: 'DollarCircleOutlined',
    description: '等待财务结算报销',
    category: 'settlement',
    order: 10
  },
  'settled': {
    text: '已结算',
    color: 'success',
    chartColor: '#389e0d',
    icon: 'CheckCircleOutlined',
    description: '结算完成，流程结束',
    category: 'settlement',
    order: 11
  }
}

// 状态分类
const STATUS_CATEGORIES = {
  'draft': '草稿阶段',
  'approval': '审批阶段',
  'purchase': '采购阶段',
  'acceptance': '验收阶段',
  'settlement': '结算阶段'
}

/**
 * 获取状态可转换的下一个状态列表
 * @param {string} currentStatus 当前状态
 * @returns {Array} 可转换的状态列表
 */
export function getStatusTransitions(currentStatus) {
  return STATUS_TRANSITIONS[currentStatus] || []
}

/**
 * 验证状态转换是否合法
 * @param {string} from 源状态
 * @param {string} to 目标状态
 * @returns {boolean} 是否允许转换
 */
export function validateStatusTransition(from, to) {
  const allowedTransitions = getStatusTransitions(from)
  return allowedTransitions.includes(to)
}

/**
 * 获取状态配置信息
 * @param {string} status 状态值
 * @returns {Object} 状态配置对象
 */
export function getStatusConfig(status) {
  return STATUS_CONFIG[status] || {
    text: '未知状态',
    color: 'default',
    icon: 'QuestionCircleOutlined',
    description: '未知状态',
    category: 'unknown',
    order: 999
  }
}

/**
 * 获取所有状态配置
 * @returns {Object} 完整的状态配置映射
 */
export function getAllStatusConfig() {
  return STATUS_CONFIG
}

/**
 * 获取状态分类
 * @returns {Object} 状态分类映射
 */
export function getStatusCategories() {
  return STATUS_CATEGORIES
}

/**
 * 根据分类获取状态列表
 * @param {string} category 分类名称
 * @returns {Array} 该分类下的状态列表
 */
export function getStatusByCategory(category) {
  return Object.entries(STATUS_CONFIG)
    .filter(([, config]) => config.category === category)
    .map(([status]) => status)
    .sort((a, b) => STATUS_CONFIG[a].order - STATUS_CONFIG[b].order)
}

/**
 * 获取状态流程的完整路径
 * @param {string} startStatus 起始状态
 * @param {string} endStatus 结束状态
 * @returns {Array} 状态流程路径
 */
export function getStatusPath(startStatus, endStatus) {
  const visited = new Set()

  function findPath(current, target, currentPath) {
    if (current === target) {
      return [...currentPath, current]
    }

    if (visited.has(current)) {
      return null
    }

    visited.add(current)
    const transitions = getStatusTransitions(current)

    for (const next of transitions) {
      const result = findPath(next, target, [...currentPath, current])
      if (result) {
        return result
      }
    }

    visited.delete(current)
    return null
  }

  return findPath(startStatus, endStatus, []) || []
}

/**
 * 获取状态的图表颜色
 * @param {string} status 状态值
 * @returns {string} 图表颜色值
 */
export function getStatusChartColor(status) {
  const config = getStatusConfig(status)
  return config.chartColor || '#666666'
}

/**
 * 获取所有状态的图表颜色映射
 * @returns {Object} 状态到颜色的映射
 */
export function getAllStatusChartColors() {
  const colors = {}
  Object.keys(STATUS_CONFIG).forEach(status => {
    colors[status] = STATUS_CONFIG[status].chartColor
  })
  return colors
}

/**
 * 按采购流程顺序获取状态列表
 * @returns {Array} 按order排序的状态列表
 */
export function getStatusInOrder() {
  return Object.entries(STATUS_CONFIG)
    .sort(([, a], [, b]) => a.order - b.order)
    .map(([status, config]) => ({
      status,
      ...config
    }))
}

// 导出常量
export {
  STATUS_TRANSITIONS,
  STATUS_CONFIG,
  STATUS_CATEGORIES
}