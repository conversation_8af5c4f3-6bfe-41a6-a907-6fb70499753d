/**
 * 照片URL处理工具函数
 */

// 后端服务器地址
const BACKEND_BASE_URL = process.env.VUE_APP_BACKEND_URL || 'http://localhost:8000'

/**
 * 获取完整的照片URL
 * @param {string} url - 相对路径或完整URL
 * @returns {string} 完整的照片URL
 */
export function getPhotoUrl(url) {
  if (!url) return ''
  if (url.startsWith('http')) return url
  return `${BACKEND_BASE_URL}${url}`
}

/**
 * 获取照片缩略图URL
 * @param {string} url - 相对路径或完整URL
 * @param {number} _width - 缩略图宽度（暂未使用）
 * @param {number} _height - 缩略图高度（暂未使用）
 * @returns {string} 缩略图URL
 */
// eslint-disable-next-line no-unused-vars
export function getThumbnailUrl(url, _width = 200, _height = 200) {
  const fullUrl = getPhotoUrl(url)
  if (!fullUrl) return ''

  // 这里可以扩展为调用后端的缩略图生成API
  // 目前直接返回原图URL
  return fullUrl
}

/**
 * 验证照片URL是否有效
 * @param {string} url - 照片URL
 * @returns {Promise<boolean>} 是否有效
 */
export async function validatePhotoUrl(url) {
  if (!url) return false
  
  try {
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    console.warn('照片URL验证失败:', url, error)
    return false
  }
}

/**
 * 格式化照片数据
 * @param {Array} photos - 照片数组
 * @returns {Array} 格式化后的照片数组
 */
export function formatPhotos(photos) {
  if (!Array.isArray(photos)) return []
  
  return photos.map(photo => ({
    ...photo,
    url: getPhotoUrl(photo.url),
    thumbnailUrl: getThumbnailUrl(photo.url)
  }))
}
