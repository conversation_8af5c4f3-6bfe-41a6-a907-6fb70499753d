# 采购管理系统 Purchase Management System

企业采购流程管理系统，实现从需求提交到结算报销的全流程数字化管理。

## 系统概述

本系统用于企业采购流程的管理，提高采购效率，降低采购成本。支持全流程状态跟踪和历史数据查询统计。

## 功能模块

### 1. 采购需求管理
- 需求提交与审批
- 价格预警机制
- 历史数据分析
- 批量导入导出
- 清单打印

### 2. 物品验收管理
- 验收记录管理
- 照片上传与水印
- 异常处理流程

### 3. 结算报销管理
- 财务结算流程
- 电子凭证管理
- 对账功能

### 4. 系统管理
- 部门管理（两级结构）
- 用户权限管理
- 数据字典配置

## 技术架构

### 前端
- **框架**: Vue 3
- **UI库**: Ant Design Vue
- **状态管理**: Vuex
- **网络请求**: Axios

### 后端
- **框架**: Django 4.2 + Django REST Framework
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **认证**: JWT
- **任务队列**: Celery

## 项目结构

```
purchase/
├── frontend/                # Vue前端项目
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 通用组件
│   │   ├── router/         # 路由配置
│   │   └── store/          # 状态管理
│   └── package.json
├── backend/                 # Django后端项目
│   ├── apps/               # 应用模块
│   │   ├── authentication/ # 认证模块
│   │   ├── system/         # 系统管理
│   │   ├── purchase/       # 采购管理
│   │   └── common/         # 通用工具
│   ├── purchase_system/    # 项目配置
│   └── requirements.txt
├── docs/                   # 文档目录
│   └── purchase.md         # 需求文档
├── mysql/                  # 数据库初始化
└── docker-compose.yml      # Docker编排
```

## 快速开始

### 方式一：Docker部署（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd purchase

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

访问地址：
- 前端应用: http://localhost:8080
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/api/docs/

### 方式二：本地开发

#### 后端启动

```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 配置数据库（需要先创建MySQL数据库）
cp .env.example .env
# 编辑.env文件配置数据库连接

# 启动项目
python start.py
```

#### 前端启动

```bash
cd frontend

# 安装依赖
npm install
# 或 yarn install

# 启动开发服务器
npm run serve
# 或 yarn serve
```

## 默认账号

- **管理员**: admin / admin123
- **角色**: 系统管理员，拥有所有权限

## 系统流程

```
需求提交 → 上级审批 → 生成订单 → 物品验收 → 财务结算
```

### 状态流转
- 草稿 → 待审批 → 已审批/已驳回 → 已采购 → 待验收 → 已验收 → 待结算 → 已结算

## API文档

系统提供完整的RESTful API，支持：
- 统一响应格式
- 分页和筛选
- 权限控制
- 文件上传下载
- Excel导入导出

详细API文档访问：http://localhost:8000/api/docs/

## 开发指南

### 前端开发
- 基于Vue 3 Composition API
- 使用Ant Design Vue组件库
- 支持响应式设计
- 统一的错误处理机制

### 后端开发
- 遵循Django最佳实践
- 使用DRF构建API
- 基于角色的权限控制
- 完整的单元测试

## 部署指南

### 生产环境部署

1. **环境准备**
   - Python 3.8+
   - Node.js 14+
   - MySQL 8.0+
   - Redis 6.0+

2. **后端部署**
   ```bash
   # 设置生产环境变量
   export DEBUG=False
   export ALLOWED_HOSTS=your-domain.com
   
   # 收集静态文件
   python manage.py collectstatic
   
   # 使用Gunicorn启动
   gunicorn purchase_system.wsgi:application
   ```

3. **前端部署**
   ```bash
   # 构建生产版本
   npm run build
   
   # 部署到Web服务器
   cp -r dist/* /var/www/html/
   ```

### Docker生产部署

```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml up -d
```

## 监控和维护

### 日志管理
- 应用日志：`backend/logs/django.log`
- 访问日志：Web服务器日志
- 错误监控：集成Sentry（可选）

### 数据备份
```bash
# 数据库备份
mysqldump -u root -p purchase_system > backup.sql

# 媒体文件备份
tar -czf media_backup.tar.gz backend/media/
```

## 常见问题

### Q: 如何重置管理员密码？
A: 使用Django命令：`python manage.py changepassword admin`

### Q: 如何添加新的数据字典类型？
A: 在系统管理-数据字典中添加，或通过API接口添加

### Q: 文件上传大小限制？
A: 默认10MB，可在settings.py中修改FILE_UPLOAD_MAX_MEMORY_SIZE

## 贡献指南

1. Fork项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。
