"""
数据字典迁移脚本
将硬编码的字典数据迁移到数据库中
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.purchase.models import Dictionary


class Command(BaseCommand):
    help = '迁移硬编码字典数据到数据库'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制覆盖已存在的字典数据',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        # 定义所有字典数据
        dict_data = {
            'status': [
                ('draft', '草稿', 1),
                ('pending_approval', '待审批', 2),
                ('approved', '已审批', 3),
                ('rejected', '已驳回', 4),
                ('pending_purchase', '待采购', 5),
                ('purchased', '已采购', 6),
                ('returned', '已退回', 7),
                ('pending_acceptance', '待验收', 8),
                ('accepted', '已验收', 9),
                ('pending_reimbursement', '待结算', 10),
                ('settled', '已结算', 11),
            ],
            'purchase_type': [
                ('unified', '统一采购', 1),
                ('self', '自行采购', 2),
                ('centralized', '统一采购', 3),  # 兼容旧值
                ('urgent', '紧急采购', 4),
                ('normal', '正常采购', 5),
            ],
            'procurement_method': [
                ('public_bidding', '公开招标', 1),
                ('invited_bidding', '邀请招标', 2),
                ('competitive_negotiation', '竞争性谈判', 3),
                ('single_source', '单一来源', 4),
                ('inquiry', '询价', 5),
                ('direct_purchase', '直接采购', 6),
            ],
            'fund_project': [
                ('basic_operation', '基本运行费', 1),
                ('special_project', '专项资金', 2),
                ('research_fund', '科研经费', 3),
                ('teaching_fund', '教学经费', 4),
                ('infrastructure', '基础设施建设', 5),
                ('equipment_purchase', '设备采购', 6),
                ('maintenance_fund', '维护费用', 7),
                ('emergency_fund', '应急资金', 8),
            ],
            'unit': [
                ('piece', '个', 1),
                ('set', '套', 2),
                ('box', '箱', 3),
                ('bottle', '瓶', 4),
                ('pack', '包', 5),
                ('kg', '千克', 6),
                ('g', '克', 7),
                ('meter', '米', 8),
                ('cm', '厘米', 9),
                ('liter', '升', 10),
                ('ml', '毫升', 11),
                ('pair', '对', 12),
                ('dozen', '打', 13),
                ('roll', '卷', 14),
                ('sheet', '张', 15),
            ],
            'item_category': [
                ('office_supplies', '办公用品', 1),
                ('computer_equipment', '计算机设备', 2),
                ('laboratory_equipment', '实验设备', 3),
                ('furniture', '家具用品', 4),
                ('consumables', '耗材用品', 5),
                ('books_materials', '图书资料', 6),
                ('software', '软件产品', 7),
                ('maintenance_parts', '维修配件', 8),
                ('cleaning_supplies', '清洁用品', 9),
                ('safety_equipment', '安全设备', 10),
            ],
            'requirement_source': [
                ('teaching', '教学需求', 1),
                ('research', '科研需求', 2),
                ('administration', '行政需求', 3),
                ('infrastructure', '基础设施', 4),
                ('maintenance', '维护需求', 5),
                ('emergency', '应急需求', 6),
            ],
            'urgency_level': [
                ('low', '一般', 1),
                ('medium', '紧急', 2),
                ('high', '特急', 3),
            ],
        }

        try:
            with transaction.atomic():
                total_created = 0
                total_updated = 0
                
                for dict_type, items in dict_data.items():
                    self.stdout.write(f'处理字典类型: {dict_type}')
                    
                    for code, name, order in items:
                        # 检查是否已存在
                        existing = Dictionary.objects.filter(
                            type=dict_type,
                            code=code
                        ).first()
                        
                        if existing:
                            if force:
                                existing.name = name
                                existing.order = order
                                existing.status = True
                                existing.save()
                                total_updated += 1
                                self.stdout.write(f'  更新: {code} -> {name}')
                            else:
                                self.stdout.write(f'  跳过已存在: {code} -> {name}')
                        else:
                            Dictionary.objects.create(
                                type=dict_type,
                                code=code,
                                name=name,
                                value=name,  # value字段设为与name相同
                                order=order,
                                status=True,
                                description=f'{dict_type}字典项: {name}'
                            )
                            total_created += 1
                            self.stdout.write(f'  创建: {code} -> {name}')
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'迁移完成! 创建 {total_created} 条，更新 {total_updated} 条记录'
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'迁移失败: {str(e)}')
            )
            raise
