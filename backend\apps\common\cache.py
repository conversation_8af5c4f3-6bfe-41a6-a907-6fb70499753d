"""
缓存工具类
"""
from django.core.cache import cache
from django.conf import settings
import hashlib
import json
from functools import wraps


class CacheManager:
    """缓存管理器"""
    
    # 默认缓存时间（秒）
    DEFAULT_TIMEOUT = 300  # 5分钟
    
    # 缓存键前缀
    KEY_PREFIX = 'purchase_system'
    
    @classmethod
    def _generate_key(cls, key_parts):
        """生成缓存键"""
        if isinstance(key_parts, (list, tuple)):
            key_str = ':'.join(str(part) for part in key_parts)
        else:
            key_str = str(key_parts)
        
        # 使用MD5哈希避免键过长
        key_hash = hashlib.md5(key_str.encode('utf-8')).hexdigest()
        return f"{cls.KEY_PREFIX}:{key_hash}"
    
    @classmethod
    def get(cls, key_parts, default=None):
        """获取缓存"""
        cache_key = cls._generate_key(key_parts)
        return cache.get(cache_key, default)
    
    @classmethod
    def set(cls, key_parts, value, timeout=None):
        """设置缓存"""
        if timeout is None:
            timeout = cls.DEFAULT_TIMEOUT
        
        cache_key = cls._generate_key(key_parts)
        return cache.set(cache_key, value, timeout)
    
    @classmethod
    def delete(cls, key_parts):
        """删除缓存"""
        cache_key = cls._generate_key(key_parts)
        return cache.delete(cache_key)
    
    @classmethod
    def clear_pattern(cls, pattern):
        """清除匹配模式的缓存"""
        # 注意：这个方法需要Redis支持
        try:
            from django.core.cache.backends.redis import RedisCache
            if isinstance(cache, RedisCache):
                keys = cache._cache.get_client().keys(f"{cls.KEY_PREFIX}:*{pattern}*")
                if keys:
                    cache._cache.get_client().delete(*keys)
        except ImportError:
            # 如果不是Redis缓存，跳过
            pass


def cache_result(timeout=None, key_func=None):
    """
    缓存装饰器
    
    Args:
        timeout: 缓存超时时间（秒）
        key_func: 生成缓存键的函数，接收函数参数，返回键的组成部分
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                key_parts = key_func(*args, **kwargs)
            else:
                # 默认使用函数名和参数生成键
                key_parts = [
                    func.__module__,
                    func.__name__,
                    json.dumps(args, sort_keys=True, default=str),
                    json.dumps(kwargs, sort_keys=True, default=str)
                ]
            
            # 尝试从缓存获取
            result = CacheManager.get(key_parts)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            CacheManager.set(key_parts, result, timeout)
            
            return result
        
        return wrapper
    return decorator


# 常用缓存键生成函数
def dept_cache_key(dept_id=None):
    """部门缓存键"""
    return ['departments', dept_id or 'all']


def dict_cache_key(dict_type=None, enabled=None):
    """数据字典缓存键"""
    return ['dictionaries', dict_type or 'all', enabled]


def user_cache_key(user_id=None):
    """用户缓存键"""
    return ['users', user_id or 'all']


def stats_cache_key(date_range=None, filters=None):
    """统计数据缓存键"""
    return ['statistics', date_range or 'default', json.dumps(filters or {}, sort_keys=True)]


# 缓存清理函数
def clear_dept_cache():
    """清理部门相关缓存"""
    CacheManager.clear_pattern('departments')


def clear_dict_cache():
    """清理数据字典相关缓存"""
    CacheManager.clear_pattern('dictionaries')


def clear_user_cache():
    """清理用户相关缓存"""
    CacheManager.clear_pattern('users')


def clear_stats_cache():
    """清理统计相关缓存"""
    CacheManager.clear_pattern('statistics')


# 缓存预热函数
def warm_up_cache():
    """缓存预热"""
    from apps.system.models import Department, User
    from apps.purchase.models import Dictionary
    
    try:
        # 预热部门数据
        departments = list(Department.objects.all())
        CacheManager.set(dept_cache_key(), departments, 3600)  # 1小时
        
        # 预热数据字典
        dicts = list(Dictionary.objects.filter(status='active'))
        CacheManager.set(dict_cache_key(enabled=True), dicts, 3600)
        
        # 预热活跃用户
        active_users = list(User.objects.filter(is_active=True))
        CacheManager.set(user_cache_key(), active_users, 1800)  # 30分钟
        
        return True
    except Exception as e:
        print(f"缓存预热失败: {e}")
        return False


# 缓存监控
class CacheMonitor:
    """缓存监控"""
    
    @staticmethod
    def get_cache_stats():
        """获取缓存统计信息"""
        try:
            from django.core.cache.backends.redis import RedisCache
            if isinstance(cache, RedisCache):
                client = cache._cache.get_client()
                info = client.info()
                return {
                    'used_memory': info.get('used_memory_human', 'N/A'),
                    'connected_clients': info.get('connected_clients', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'hit_rate': round(
                        info.get('keyspace_hits', 0) / 
                        max(info.get('keyspace_hits', 0) + info.get('keyspace_misses', 0), 1) * 100, 
                        2
                    )
                }
        except ImportError:
            pass
        
        return {'message': '缓存统计信息不可用'}
    
    @staticmethod
    def get_cache_keys_count():
        """获取缓存键数量"""
        try:
            from django.core.cache.backends.redis import RedisCache
            if isinstance(cache, RedisCache):
                client = cache._cache.get_client()
                return len(client.keys(f"{CacheManager.KEY_PREFIX}:*"))
        except ImportError:
            pass
        
        return 0
