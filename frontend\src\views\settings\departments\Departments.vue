<template>
  <div class="department-management">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <ApartmentOutlined />
          部门管理
        </h1>
        <p class="page-subtitle">
          管理企业组织架构，维护部门层级关系和权限配置
        </p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalCount }}</span>
          <span class="stat-label">部门总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ activeCount }}</span>
          <span class="stat-label">启用部门</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ topLevelCount }}</span>
          <span class="stat-label">一级部门</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ subLevelCount }}</span>
          <span class="stat-label">二级部门</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ userCount }}</span>
          <span class="stat-label">关联用户</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="unified-filter-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-input v-model:value="searchTerm" placeholder="部门名称、编码、负责人" allow-clear @pressEnter="searchDepartments"
            class="filter-input">
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="levelFilter" placeholder="选择层级" allowClear @change="handleLevelFilterChange"
            class="filter-select">
            <a-select-option value="">全部层级</a-select-option>
            <a-select-option value="1">一级部门</a-select-option>
            <a-select-option value="2">二级部门</a-select-option>
            <a-select-option value="3">三级部门</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-select v-model:value="statusFilter" placeholder="选择状态" allowClear @change="handleStatusFilterChange"
            class="filter-select">
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">停用</a-select-option>
          </a-select>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="searchDepartments" class="secondary-action-btn">
            <SearchOutlined />
            搜索
          </a-button>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
          <a-button @click="resetSearch" class="secondary-action-btn">
            <ReloadOutlined />
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 操作栏 -->
    <div class="action-buttons-section business-card">
      <a-row :gutter="[16, 16]" align="middle">
        <a-col>
          <a-button type="primary" @click="showAddModal" class="primary-action-btn">
            <PlusOutlined />
            新增部门
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="showImportModal" class="secondary-action-btn">
            <UploadOutlined />
            批量导入
          </a-button>
        </a-col>
        <a-col>
          <a-button @click="exportDepartments" class="secondary-action-btn">
            <DownloadOutlined />
            导出
          </a-button>
        </a-col>

        <!-- 批量操作按钮 -->
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button type="primary" @click="batchEnable" class="primary-action-btn">
            <CheckCircleOutlined />
            批量启用 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button @click="batchDisable" class="secondary-action-btn">
            <StopOutlined />
            批量停用 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
        <a-col v-if="selectedRowKeys.length > 0">
          <a-button danger @click="batchDelete" class="danger-action-btn">
            <DeleteOutlined />
            批量删除 ({{ selectedRowKeys.length }})
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 部门表格 -->
    <div class="table-section business-card">
      <a-table :key="tableKey" :columns="columns" :data-source="departments" :row-key="(record) => record.id"
        :row-selection="rowSelection" :pagination="pagination" @change="handleTableChange" class="unified-table"
        size="large" :scroll="{ x: 1000 }">
        <template #bodyCell="{ column, record }">
          <!-- 层级路径显示 -->
          <template v-if="column.dataIndex === 'hierarchy_path'">
            <div class="hierarchy-path">
              <span v-for="(path, index) in record.hierarchy_path.split('-')" :key="index" class="path-item">
                <a-badge status="processing" />
                {{ path }}
                <RightOutlined v-if="index < record.hierarchy_path.split('-').length - 1" class="path-separator" />
              </span>
            </div>
          </template>

          <!-- 负责人显示 -->
          <template v-if="column.dataIndex === 'manager_name'">
            <span v-if="record.manager_name">{{ record.manager_name }}</span>
            <span v-else>-</span>
          </template>

          <!-- 联系电话显示 -->
          <template v-if="column.dataIndex === 'contact_phone'">
            <span v-if="record.contact_phone">{{ record.contact_phone }}</span>
            <span v-else>-</span>
          </template>

          <!-- 部门描述显示 -->
          <template v-if="column.dataIndex === 'description'">
            <a-tooltip v-if="record.description && record.description.length > 20" :title="record.description">
              <span>{{ record.description.substring(0, 20) }}...</span>
            </a-tooltip>
            <span v-else-if="record.description">{{ record.description }}</span>
            <span v-else class="text-muted">暂无描述</span>
          </template>

          <!-- 用户数量显示 -->
          <template v-if="column.dataIndex === 'user_count'">
            <a-badge :count="record.user_count || 0" :number-style="{ backgroundColor: '#1890ff' }" />
          </template>

          <!-- 状态显示 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="record.status ? 'success' : 'error'">
              <CheckCircleOutlined v-if="record.status" />
              <StopOutlined v-else />
              {{ record.status ? "启用" : "停用" }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" @click="editDepartment(record)" size="small">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="link" @click="toggleStatus(record)" size="small">
                <SwapOutlined />
                切换状态
              </a-button>
              <a-popconfirm title="确定要删除这个部门吗？" @confirm="deleteDepartment(record)" ok-text="确定" cancel-text="取消">
                <a-button type="link" danger size="small">
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增/编辑部门模态框 -->
    <a-modal v-model:open="modalVisible" :title="isEditMode ? '编辑部门' : '新增部门'" @ok="handleSubmit" @cancel="handleCancel"
      width="600px" class="department-modal" ok-text="确定" cancel-text="取消">
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical" class="department-form">
        <a-form-item label="部门名称" name="dept_name">
          <a-input v-model:value="formData.dept_name" placeholder="请输入部门名称" size="large" />
        </a-form-item>
        <a-form-item label="部门编码" name="dept_code">
          <a-input v-model:value="formData.dept_code" placeholder="请输入部门编码" size="large" />
        </a-form-item>
        <a-form-item label="上级部门" name="parent_id">
          <a-select v-model:value="formData.parent_id" placeholder="请选择上级部门（可选）" style="width: 100%" size="large"
            allow-clear>
            <a-select-option :value="null">无上级部门</a-select-option>
            <a-select-option v-for="dept in allDepartments" :key="dept.id" :value="dept.id"
              :disabled="isEditMode && dept.id === formData.id">
              {{ dept.dept_name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="负责人" name="manager_name">
          <a-input v-model:value="formData.manager_name" placeholder="请输入部门负责人姓名" size="large" />
        </a-form-item>

        <a-form-item label="联系电话" name="contact_phone">
          <a-input v-model:value="formData.contact_phone" placeholder="请输入联系电话" size="large" />
        </a-form-item>

        <a-form-item label="部门描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入部门描述信息" :rows="3" size="large" />
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formData.status" checked-children="启用" un-checked-children="停用" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量导入模态框 -->
    <a-modal v-model:open="importModalVisible" title="批量导入部门" @ok="handleImport" @cancel="handleImportCancel"
      width="600px" :confirm-loading="importLoading" ok-text="开始导入" cancel-text="取消">
      <div class="import-section">
        <a-alert message="导入说明" description="请下载模板文件，按照模板格式填写部门信息后上传。支持Excel格式(.xlsx)文件。" type="info" show-icon
          style="margin-bottom: 16px" />

        <div style="margin-bottom: 16px">
          <a-button @click="downloadTemplate" type="link">
            <DownloadOutlined />
            下载部门导入模板
          </a-button>
        </div>

        <a-upload-dragger v-model:fileList="importFileList" :before-upload="beforeUpload" :remove="handleRemoveFile"
          accept=".xlsx,.xls" :multiple="false">
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持单个文件上传，仅支持Excel格式(.xlsx, .xls)
          </p>
        </a-upload-dragger>

        <div v-if="importResult" class="import-result" style="margin-top: 16px">
          <a-alert :message="importResult.success ? '导入成功' : '导入失败'" :description="importResult.message"
            :type="importResult.success ? 'success' : 'error'" show-icon />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import {
  SearchOutlined,
  UploadOutlined,
  DownloadOutlined,
  InboxOutlined,
  RightOutlined,
  CheckCircleOutlined,
  StopOutlined,
  EditOutlined,
  SwapOutlined,
  DeleteOutlined,
} from "@ant-design/icons-vue";
import api from "@/api";

// 搜索关键词
const searchTerm = ref("");
// 层级筛选
const levelFilter = ref("");
// 状态筛选
const statusFilter = ref("");

// 批量操作相关
const selectedRowKeys = ref([]);

// 表格刷新key
const tableKey = ref(0);

// 表单引用
const formRef = ref(null);

// 模态框控制
const modalVisible = ref(false);
// 是否为编辑模式
const isEditMode = ref(false);

// 导入相关
const importModalVisible = ref(false);
const importLoading = ref(false);
const importFileList = ref([]);
const importResult = ref(null);

// 表单数据
const formData = ref({
  dept_name: "",
  dept_code: "",
  parent_id: null,
  manager_name: "",
  contact_phone: "",
  description: "",
  status: true,
});

// 部门列表
const departments = ref([]);
// 所有部门列表（用于上级部门下拉选项，不受分页影响）
const allDepartments = ref([]);
// 部门树数据
const departmentTree = ref([{ value: null, title: "根部门", children: [] }]);

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
  hideOnSinglePage: false,
  pageSizeOptions: ["10", "20", "50", "100"],
});

// 统计信息
const totalCount = ref(0);
const activeCount = ref(0);
const topLevelCount = ref(0);
const subLevelCount = ref(0);
const userCount = ref(0);

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: () => ({
    disabled: false, // 可以根据需要设置禁用条件
  }),
};

// 表格列定义
const columns = [
  {
    title: "部门名称",
    dataIndex: "dept_name",
    width: 140,
    sorter: true,
  },
  {
    title: "部门编码",
    dataIndex: "dept_code",
    width: 100,
  },
  {
    title: "层级路径",
    dataIndex: "hierarchy_path",
    width: 250,
  },
  {
    title: "负责人",
    dataIndex: "manager_name",
    width: 120,
  },
  {
    title: "联系电话",
    dataIndex: "contact_phone",
    width: 140,
  },
  {
    title: "部门描述",
    dataIndex: "description",
    width: 200,
    ellipsis: true,
  },
  {
    title: "用户数量",
    dataIndex: "user_count",
    width: 80,
    sorter: true,
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 80,
  },
  {
    title: "操作",
    dataIndex: "action",
    width: 200,
    fixed: "right",
  },
];

// 电话号码验证函数
const validatePhone = (_, value) => {
  if (!value) {
    // 联系电话是可选的，如果为空则通过验证
    return Promise.resolve();
  }

  // 手机号码格式：11位数字，以1开头
  const mobilePattern = /^1[3-9]\d{9}$/;

  // 6位电话号码格式：6位数字
  const phonePattern = /^\d{6}$/;

  // 区号+6位电话号码格式：3-4位区号 + 连字符 + 6位电话号码
  const areaPhonePattern = /^\d{3,4}-\d{6}$/;

  // 固定电话格式：区号(3-4位) + 电话号码(7-8位)，可选连字符
  const landlinePattern = /^\d{3,4}-?\d{7,8}$/;

  if (mobilePattern.test(value) ||
    phonePattern.test(value) ||
    areaPhonePattern.test(value) ||
    landlinePattern.test(value)) {
    return Promise.resolve();
  }

  return Promise.reject(new Error('请输入正确的电话号码格式：手机号(11位)、电话号码(6位)、区号-电话号码(如：0571-123456)'));
};

// 表单验证规则
const rules = {
  dept_name: [
    { required: true, message: "请输入部门名称" },
    { min: 2, max: 50, message: "部门名称长度在2-50个字符" },
  ],
  dept_code: [
    { required: true, message: "请输入部门编码" },
    {
      pattern: /^[A-Z0-9_]{2,20}$/,
      message: "编码只能包含大写字母、数字和下划线，长度2-20位",
    },
  ],
  contact_phone: [
    { validator: validatePhone, trigger: 'blur' }
  ],
};

// 获取部门列表
const getDepartments = async () => {
  try {
    const params = {
      page: pagination.value.current,
      page_size: pagination.value.pageSize,
    };

    // 添加搜索参数
    if (searchTerm.value) {
      params.search = searchTerm.value;
    }

    // 添加筛选参数
    if (levelFilter.value) {
      params.level = levelFilter.value;
    }
    if (statusFilter.value !== "") {
      params.status = statusFilter.value;
    }
    const response = await api.departments.getList(params);

    if (response.code === 200) {
      const departmentData = response.data.results || response.data || [];
      // 确保数据是数组格式
      if (Array.isArray(departmentData)) {
        departments.value = departmentData;

        // 设置总数 - 优先使用total，其次使用count
        pagination.value.total =
          response.data.total || response.data.count || departmentData.length;

        // 构建部门树（用于新增/编辑时的上级部门选择）
        await buildDepartmentTreeForSelect();
      } else {
        departments.value = [];
        pagination.value.total = 0;
      }
    } else {
      message.error(response.message || "获取部门列表失败");
      departments.value = [];
      pagination.value.total = 0;
    }
  } catch (error) {
    // 如果是404错误且当前页大于1，尝试跳转到第一页
    if (error.response?.status === 404 && pagination.value.current > 1) {
      pagination.value.current = 1;
      getDepartments();
      return;
    }

    message.error("获取部门列表失败");
    departments.value = [];
    pagination.value.total = 0;
  }
};

// 获取所有部门（用于上级部门下拉选项）
const getAllDepartments = async () => {
  try {
    // 获取所有部门，不分页
    const response = await api.departments.getList({
      page: 1,
      page_size: 1000, // 获取足够多的数据
      status: true // 只获取启用的部门
    });

    if (response.code === 200) {
      const departmentData = response.data.results || response.data || [];
      if (Array.isArray(departmentData)) {
        allDepartments.value = departmentData;
      } else {
        allDepartments.value = [];
      }
    } else {
      allDepartments.value = [];
    }
  } catch (error) {
    console.error("获取所有部门失败:", error);
    allDepartments.value = [];
  }
};

// 获取统计信息
const getStatistics = async () => {
  try {
    const response = await api.departments.getStatistics();
    if (response.code === 200) {
      const data = response.data;

      // 更新统计信息
      totalCount.value = data.total_count || 0;
      activeCount.value = data.active_count || 0;
      topLevelCount.value = data.top_level_count || 0;
      subLevelCount.value = data.sub_level_count || 0;
      userCount.value = data.user_count || 0;
    }
  } catch (error) {
    console.warn("获取部门统计信息失败:", error);
  }
};

// 处理parent_id的有效性
const getValidParentId = (parentId) => {
  // 如果是null、undefined或空字符串，返回null
  if (parentId === null || parentId === undefined || parentId === "") {
    return null;
  }

  // 如果是数字且大于0
  if (typeof parentId === "number" && parentId > 0) {
    return parentId;
  }

  // 如果是字符串数字
  if (typeof parentId === "string") {
    const numId = parseInt(parentId);
    if (!isNaN(numId) && numId > 0) {
      return numId;
    }
  }
  return null;
};

// 获取所有部门数据用于上级部门选择 - 使用专门的无分页接口
const getAllDepartmentsForSelect = async () => {
  try {
    const response = await api.departments.getAll();
    if (response.code === 200) {
      return response.data || [];
    }
    return [];
  } catch (error) {
    return [];
  }
};

// 构建部门树（用于上级部门选择）
const buildDepartmentTreeForSelect = async () => {
  const allDepartments = await getAllDepartmentsForSelect();
  departmentTree.value = [
    { value: null, title: "无上级部门", children: [] },
    ...allDepartments.map((dept) => ({
      value: dept.id,
      title: dept.dept_name,
      children: [],
    })),
  ];
};

// 显示新增部门模态框
const showAddModal = async () => {
  isEditMode.value = false;
  formData.value = {
    dept_name: "",
    dept_code: "",
    parent_id: null,
    manager_name: "",
    contact_phone: "",
    description: "",
    status: true,
  };
  // 获取所有部门用于上级部门选择
  await getAllDepartments();
  modalVisible.value = true;
};

// 编辑部门
const editDepartment = async (record) => {
  isEditMode.value = true;
  formData.value = {
    id: record.id,
    dept_name: record.dept_name,
    dept_code: record.dept_code,
    parent_id: record.parent_id,
    manager_name: record.manager_name || "",
    contact_phone: record.contact_phone || "",
    description: record.description || "",
    status: record.status,
  };
  // 获取所有部门用于上级部门选择
  await getAllDepartments();
  modalVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    console.log("开始提交表单，编辑模式:", isEditMode.value);
    const values = await formRef.value.validateFields();
    console.log("表单验证通过，提交数据:", values);

    // 构建提交数据，确保包含所有必需字段
    const submitData = {
      dept_name: values.dept_name,
      dept_code: values.dept_code,
      parent_id: getValidParentId(values.parent_id),
      description: values.description || "",
      status: values.status !== undefined ? values.status : true,
      // 使用表单中的实际值
      manager_name: values.manager_name || "",
      contact_phone: values.contact_phone || "",
      approval_limit: null,
      can_approve: false,
      can_accept: false,
      can_finance: false,
    };

    console.log("最终提交数据:", submitData);

    let response;
    if (isEditMode.value) {
      // 更新部门信息
      console.log("更新部门，ID:", formData.value.id);
      response = await api.departments.update(formData.value.id, submitData);
    } else {
      // 创建新部门
      console.log("创建新部门");
      response = await api.departments.create(submitData);
    }

    console.log("API响应:", response);

    if (response.code === 200) {
      message.success(isEditMode.value ? "更新成功" : "创建成功");
      modalVisible.value = false;
      console.log("开始刷新部门数据...");
      await getDepartments();
      await getStatistics(); // 更新统计信息
      console.log("部门数据刷新完成");
    } else {
      console.error("API返回错误:", response);
      message.error(response.message || "操作失败");
    }
  } catch (error) {
    console.error("表单提交失败：", error);
    console.error("错误详情:", error.response?.data);
    if (error.response?.data?.data) {
      console.error("验证错误详情:", error.response.data.data);
      // 显示具体的字段错误
      Object.keys(error.response.data.data).forEach((field) => {
        console.error(`${field}字段错误:`, error.response.data.data[field]);
      });
    }
    message.error("操作失败");
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
};

// 切换部门状态
const toggleStatus = async (record) => {
  try {
    const newStatus = !record.status;
    const response = await api.departments.update(record.id, {
      ...record,
      status: newStatus,
    });

    if (response.code === 200) {
      message.success(`部门已${newStatus ? "启用" : "停用"}`);
      await getDepartments();
      await getStatistics(); // 更新统计信息
    }
  } catch (error) {
    console.error("状态切换失败:", error);
    message.error("状态切换失败");
  }
};

// 删除部门
const deleteDepartment = async (record) => {
  try {
    const response = await api.departments.delete(record.id);
    if (response.code === 200) {
      message.success("删除成功");
      await getDepartments();
      await getStatistics(); // 更新统计信息
    } else {
      message.error(response.message || "删除失败");
    }
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("删除失败:", error);
    }
    if (error.response && error.response.data && error.response.data.message) {
      message.error(error.response.data.message);
    } else {
      message.error("删除失败，请稍后重试");
    }
  }
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  getDepartments();
};

// 搜索部门
const searchDepartments = () => {
  // 重置到第一页
  pagination.value.current = 1;
  getDepartments();
};

// 重置搜索
const resetSearch = () => {
  searchTerm.value = "";
  levelFilter.value = "";
  statusFilter.value = "";
  pagination.value.current = 1;
  getDepartments();
};

// 层级筛选处理
const handleLevelFilterChange = () => {
  pagination.value.current = 1;
  getDepartments();
};

// 状态筛选处理
const handleStatusFilterChange = () => {
  pagination.value.current = 1;
  getDepartments();
};

// 显示导入模态框
const showImportModal = () => {
  importModalVisible.value = true;
  importFileList.value = [];
  importResult.value = null;
};

// 下载模板
const downloadTemplate = async () => {
  try {
    const response = await api.departments.downloadTemplate();

    // 检查响应数据
    if (!response || !response.data) {
      throw new Error("响应数据为空");
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob =
      response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error("下载的文件为空");
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "部门导入模板.xlsx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    message.success("模板下载成功");
  } catch (error) {
    console.error("下载模板失败:", error);
    message.error(`下载模板失败: ${error.message || "未知错误"}`);
  }
};

// 文件上传前检查
const beforeUpload = (file) => {
  const isExcel =
    file.type ===
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    file.type === "application/vnd.ms-excel";
  if (!isExcel) {
    message.error("只能上传Excel文件!");
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error("文件大小不能超过10MB!");
    return false;
  }
  return false; // 阻止自动上传
};

// 移除文件
const handleRemoveFile = () => {
  importFileList.value = [];
  importResult.value = null;
};

// 处理导入
const handleImport = async () => {
  if (importFileList.value.length === 0) {
    message.warning("请选择要导入的文件");
    return;
  }

  importLoading.value = true;
  try {
    const formData = new FormData();
    formData.append("file", importFileList.value[0].originFileObj);

    const response = await api.departments.importFromExcel(formData);
    if (response.code === 200) {
      importResult.value = {
        success: true,
        message: `导入成功！共导入 ${response.data.success_count} 条记录`,
      };
      message.success("导入成功");
      await getDepartments(); // 刷新列表
      await getStatistics(); // 更新统计信息
      setTimeout(() => {
        importModalVisible.value = false;
      }, 2000);
    } else {
      importResult.value = {
        success: false,
        message: response.message || "导入失败",
      };
    }
  } catch (error) {
    console.error("导入失败:", error);
    importResult.value = {
      success: false,
      message: "导入失败，请检查文件格式",
    };
    message.error("导入失败");
  } finally {
    importLoading.value = false;
  }
};

// 取消导入
const handleImportCancel = () => {
  importModalVisible.value = false;
  importFileList.value = [];
  importResult.value = null;
};

// 导出部门数据
const exportDepartments = async () => {
  try {
    const response = await api.departments.export({
      level: levelFilter.value,
      status: statusFilter.value,
      search: searchTerm.value,
    });

    // 检查响应数据
    if (!response || !response.data) {
      throw new Error("响应数据为空");
    }

    // response.data 在 responseType: 'blob' 时已经是 Blob 对象，不需要再包装
    const blob =
      response.data instanceof Blob ? response.data : new Blob([response.data]);

    // 验证文件大小
    if (blob.size === 0) {
      throw new Error("导出的文件为空");
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `部门信息_${new Date().toLocaleDateString()}.xlsx`
    );
    document.body.appendChild(link);
    link.click();
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    message.success("导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    message.error(`导出失败: ${error.message || "未知错误"}`);
  }
};

// 批量启用
const batchEnable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要启用的部门");
    return;
  }

  try {
    const promises = selectedRowKeys.value.map(async (id) => {
      const dept = departments.value.find((d) => d.id === id);
      if (!dept) {
        throw new Error(`未找到ID为${id}的部门`);
      }

      // 只更新状态字段，避免发送不必要的数据
      const updateData = {
        dept_name: dept.dept_name,
        dept_code: dept.dept_code,
        parent_id: dept.parent_id,
        manager_name: dept.manager_name || "",
        contact_phone: dept.contact_phone || "",
        description: dept.description || "",
        status: true,
      };

      const response = await api.departments.update(id, updateData);
      return response;
    });

    await Promise.all(promises);
    message.success(`成功启用 ${selectedRowKeys.value.length} 个部门`);
    selectedRowKeys.value = [];
    await getDepartments();
    await getStatistics(); // 更新统计信息
  } catch (error) {
    console.error("批量启用失败:", error);
    message.error(`批量启用失败: ${error.message || "未知错误"}`);
  }
};

// 批量停用
const batchDisable = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要停用的部门");
    return;
  }

  try {
    const promises = selectedRowKeys.value.map(async (id) => {
      const dept = departments.value.find((d) => d.id === id);
      if (!dept) {
        throw new Error(`未找到ID为${id}的部门`);
      }

      // 只更新状态字段，避免发送不必要的数据
      const updateData = {
        dept_name: dept.dept_name,
        dept_code: dept.dept_code,
        parent_id: dept.parent_id,
        manager_name: dept.manager_name || "",
        contact_phone: dept.contact_phone || "",
        description: dept.description || "",
        status: false,
      };

      const response = await api.departments.update(id, updateData);
      return response;
    });

    await Promise.all(promises);
    message.success(`成功停用 ${selectedRowKeys.value.length} 个部门`);
    selectedRowKeys.value = [];
    await getDepartments();
    await getStatistics(); // 更新统计信息
  } catch (error) {
    console.error("批量停用失败:", error);
    message.error(`批量停用失败: ${error.message || "未知错误"}`);
  }
};

// 批量删除
const batchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要删除的部门");
    return;
  }

  // 检查是否有部门包含用户
  const departmentsWithUsers = selectedRowKeys.value.filter((id) => {
    const dept = departments.value.find((d) => d.id === id);
    return dept && dept.user_count > 0;
  });

  if (departmentsWithUsers.length > 0) {
    message.error("选中的部门中有包含用户的部门，无法删除");
    return;
  }

  Modal.confirm({
    title: "批量删除确认",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个部门吗？此操作不可恢复。`,
    okText: "确定删除",
    cancelText: "取消",
    okType: "danger",
    onOk: async () => {
      try {
        const promises = selectedRowKeys.value.map(async (id) => {
          const response = await api.departments.delete(id);
          return response;
        });

        await Promise.all(promises);
        message.success(`成功删除 ${selectedRowKeys.value.length} 个部门`);
        selectedRowKeys.value = [];
        await getDepartments();
        await getStatistics(); // 更新统计信息
      } catch (error) {
        console.error("批量删除失败:", error);
        message.error(
          `批量删除失败: ${error.response?.data?.message || error.message || "未知错误"
          }`
        );
      }
    },
  });
};

// 页面加载时初始化
onMounted(async () => {
  await getDepartments();
  await getStatistics();
});

// 页面加载时初始化
onMounted(async () => {
  await getDepartments();
  await getStatistics();
});
</script>

<style scoped>
.department-management {
  padding: 0;
  background: transparent;
}

/* 页面标题区域 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  letter-spacing: 0.5px;
}

.page-title .anticon {
  font-size: var(--text-3xl);
}

.page-subtitle {
  font-size: var(--text-base);
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: var(--space-xl);
}

.stat-item {
  text-align: center;
  padding: var(--space-md) var(--space-lg);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: var(--text-sm);
  opacity: 0.9;
  font-weight: 500;
}

/* 搜索和筛选区域 */
.filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
}

.filter-form {
  gap: var(--space-lg);
}

:deep(.filter-form .ant-form-item) {
  margin-bottom: 0;
}

:deep(.filter-form .ant-form-item-label) {
  font-weight: 500;
  color: var(--text-secondary);
}

/* 搜索输入框样式 */
.search-input {
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.search-input .ant-input) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: all 0.3s;
}

:deep(.search-input .ant-input:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

:deep(.search-input .ant-input:hover) {
  border-color: var(--primary-light);
}

/* 按钮样式 */
.search-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.search-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.reset-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  background: var(--bg-primary);
  transition: all 0.3s;
}

.reset-btn:hover {
  border-color: var(--border-dark);
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
}

.action-left {
  display: flex;
  gap: var(--space-md);
}

.primary-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-inverse);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.secondary-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.secondary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-overlay);
}

.danger-btn {
  border: 2px solid var(--error-color);
  color: var(--error-color);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: all 0.3s;
}

.danger-btn:hover {
  border-color: #dc2626;
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 表格区域 */
.table-section {
  padding: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* 表格样式已在统一样式文件中定义 */

/* 层级路径样式 */
.hierarchy-path {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  flex-wrap: wrap;
}

.path-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.path-separator {
  color: var(--text-tertiary);
  font-size: 10px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

:deep(.action-buttons .ant-btn-link) {
  color: var(--primary-color);
  font-weight: 500;
  transition: all 0.3s;
  border-radius: var(--radius-sm);
  padding: var(--space-xs) var(--space-sm);
  height: auto;
  font-size: var(--text-xs);
}

:deep(.action-buttons .ant-btn-link:hover) {
  color: var(--primary-dark);
  background: var(--bg-overlay);
}

:deep(.action-buttons .ant-btn-link.ant-btn-dangerous) {
  color: var(--error-color);
}

:deep(.action-buttons .ant-btn-link.ant-btn-dangerous:hover) {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

/* 表单帮助文字 */
.form-help {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: 4px;
}

/* 状态标签优化 */
:deep(.ant-tag) {
  border-radius: 20px;
  font-weight: 500;
  font-size: var(--text-sm);
  padding: 4px var(--space-md);
  border: none;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 模态框样式 */
:deep(.department-modal .ant-modal-header) {
  background: var(--primary-gradient);
  border-bottom: none;
}

:deep(.department-modal .ant-modal-title) {
  color: var(--text-inverse);
  font-weight: 600;
}

:deep(.department-modal .ant-modal-close) {
  color: var(--text-inverse);
}

:deep(.department-modal .ant-modal-close:hover) {
  color: rgba(255, 255, 255, 0.8);
}

.department-form {
  padding: var(--space-lg) 0;
}

:deep(.department-form .ant-form-item-label) {
  font-weight: 500;
  color: var(--text-primary);
}

:deep(.department-form .ant-input) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.department-form .ant-input:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

:deep(.department-form .ant-select-selector) {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

:deep(.department-form .ant-select-focused .ant-select-selector) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-lg);
  }

  .header-stats {
    justify-content: center;
  }

  .action-bar {
    flex-direction: column;
    gap: var(--space-md);
  }

  .action-left {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .filter-section {
    padding: var(--space-md) var(--space-lg);
  }

  .action-bar {
    padding: var(--space-md) var(--space-lg);
  }

  .page-header {
    padding: var(--space-lg) var(--space-lg);
  }

  .page-title {
    font-size: var(--text-2xl);
  }

  .header-stats {
    gap: var(--space-md);
  }

  .stat-item {
    padding: var(--space-md) var(--space-md);
    min-width: 80px;
  }
}

/* 统一筛选区样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg,
      var(--bg-secondary) 0%,
      var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

.unified-filter-section .filter-select .ant-select-selector {
  height: 32px !important;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  transition: all 0.3s;
}

.unified-filter-section .filter-select:hover .ant-select-selector {
  border-color: var(--primary-light);
}

.unified-filter-section .filter-select.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* 统一按钮样式 */
.action-buttons-section {
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.primary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: white;
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.secondary-action-btn:hover {
  border-color: var(--primary-light);
  color: var(--primary-color);
}

.danger-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--error-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--error-color);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s;
}

.danger-action-btn:hover {
  border-color: #dc2626;
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}
</style>
