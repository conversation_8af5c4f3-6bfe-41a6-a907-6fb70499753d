<template>
  <div class="time-warnings">
    <!-- 预警统计卡片 -->
    <a-row :gutter="16" class="warning-stats">
      <a-col :span="8">
        <a-card size="small">
          <a-statistic
            title="总预警数"
            :value="warningStats.total_count"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card size="small">
          <a-statistic
            title="高优先级"
            :value="warningStats.high_priority_count"
            :value-style="{ color: '#f5222d' }"
          />
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card size="small">
          <a-statistic
            title="中优先级"
            :value="warningStats.medium_priority_count"
            :value-style="{ color: '#fa8c16' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 预警列表 -->
    <a-card title="时间预警列表" class="warning-list" :loading="loading">
      <template #extra>
        <a-space>
          <a-button @click="refreshWarnings" :loading="loading">
            <ReloadOutlined />
            刷新
          </a-button>
          <a-select
            v-model:value="filterType"
            placeholder="筛选类型"
            style="width: 150px"
            @change="filterWarnings"
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="acceptance_overdue">验收超期</a-select-option>
            <a-select-option value="settlement_overdue">结算超期</a-select-option>
            <a-select-option value="acceptance_warning">验收预警</a-select-option>
            <a-select-option value="settlement_warning">结算预警</a-select-option>
          </a-select>
          <a-select
            v-model:value="filterPriority"
            placeholder="筛选优先级"
            style="width: 120px"
            @change="filterWarnings"
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="high">高优先级</a-select-option>
            <a-select-option value="medium">中优先级</a-select-option>
          </a-select>
        </a-space>
      </template>

      <a-list
        :data-source="filteredWarnings"
        :pagination="{ pageSize: 10, showSizeChanger: true }"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <template #actions>
              <a-button type="link" @click="handleWarning(item)">
                处理
              </a-button>
              <a-button type="link" @click="viewDetail(item)">
                查看详情
              </a-button>
            </template>
            
            <a-list-item-meta>
              <template #title>
                <a-space>
                  <a-tag :color="getPriorityColor(item.priority)">
                    {{ getPriorityText(item.priority) }}
                  </a-tag>
                  <a-tag :color="getTypeColor(item.type)">
                    {{ getTypeText(item.type) }}
                  </a-tag>
                  {{ item.title }}
                </a-space>
              </template>
              <template #description>
                <div class="warning-description">
                  <p>{{ item.message }}</p>
                  <div class="warning-details">
                    <a-space wrap>
                      <span><strong>物品:</strong> {{ item.details.item_name }}</span>
                      <span><strong>规格:</strong> {{ item.details.spec }}</span>
                      <span><strong>需求单位:</strong> {{ item.details.hierarchy_path }}</span>
                      <span v-if="item.details.days_overdue">
                        <strong>超期天数:</strong> 
                        <a-tag color="red">{{ item.details.days_overdue }}天</a-tag>
                      </span>
                      <span v-if="item.details.days_remaining">
                        <strong>剩余天数:</strong> 
                        <a-tag color="orange">{{ item.details.days_remaining }}天</a-tag>
                      </span>
                    </a-space>
                  </div>
                </div>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 预警详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      title="预警详情"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedWarning" class="warning-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="预警类型">
            <a-tag :color="getTypeColor(selectedWarning.type)">
              {{ getTypeText(selectedWarning.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(selectedWarning.priority)">
              {{ getPriorityText(selectedWarning.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="物品名称">
            {{ selectedWarning.details.item_name }}
          </a-descriptions-item>
          <a-descriptions-item label="规格型号">
            {{ selectedWarning.details.spec }}
          </a-descriptions-item>
          <a-descriptions-item label="需求单位" :span="2">
            {{ selectedWarning.details.hierarchy_path }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人">
            {{ selectedWarning.details.requester_name }}
          </a-descriptions-item>
          <a-descriptions-item label="采购人" v-if="selectedWarning.details.purchaser_name">
            {{ selectedWarning.details.purchaser_name }}
          </a-descriptions-item>
          <a-descriptions-item label="验收人" v-if="selectedWarning.details.acceptor_name">
            {{ selectedWarning.details.acceptor_name }}
          </a-descriptions-item>
          <a-descriptions-item label="采购日期" v-if="selectedWarning.details.purchase_date">
            {{ selectedWarning.details.purchase_date }}
          </a-descriptions-item>
          <a-descriptions-item label="验收日期" v-if="selectedWarning.details.acceptance_date">
            {{ selectedWarning.details.acceptance_date }}
          </a-descriptions-item>
          <a-descriptions-item label="金额" v-if="selectedWarning.details.amount">
            ¥{{ selectedWarning.details.amount }}
          </a-descriptions-item>
          <a-descriptions-item label="超期天数" v-if="selectedWarning.details.days_overdue">
            <a-tag color="red">{{ selectedWarning.details.days_overdue }}天</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="剩余天数" v-if="selectedWarning.details.days_remaining">
            <a-tag color="orange">{{ selectedWarning.details.days_remaining }}天</a-tag>
          </a-descriptions-item>
        </a-descriptions>
        
        <div class="warning-actions" style="margin-top: 16px; text-align: right;">
          <a-space>
            <a-button @click="showDetailModal = false">关闭</a-button>
            <a-button type="primary" @click="handleWarning(selectedWarning)">
              立即处理
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import api from '@/api'

export default {
  name: 'TimeWarnings',
  components: {
    ReloadOutlined
  },
  emits: ['navigate'],
  setup(props, { emit }) {
    const loading = ref(false)
    const warnings = ref([])
    const warningStats = reactive({
      total_count: 0,
      high_priority_count: 0,
      medium_priority_count: 0
    })
    
    // 筛选条件
    const filterType = ref('')
    const filterPriority = ref('')
    
    // 模态框
    const showDetailModal = ref(false)
    const selectedWarning = ref(null)
    
    // 筛选后的预警列表
    const filteredWarnings = computed(() => {
      let result = warnings.value
      
      if (filterType.value) {
        result = result.filter(item => item.type === filterType.value)
      }
      
      if (filterPriority.value) {
        result = result.filter(item => item.priority === filterPriority.value)
      }
      
      return result
    })
    
    // 获取预警信息
    const getWarnings = async () => {
      loading.value = true
      try {
        const response = await api.purchaseRequests.getTimeWarnings()
        if (response.code === 200) {
          warnings.value = response.data.warnings || []
          Object.assign(warningStats, {
            total_count: response.data.total_count || 0,
            high_priority_count: response.data.high_priority_count || 0,
            medium_priority_count: response.data.medium_priority_count || 0
          })
        }
      } catch (error) {
        message.error('获取预警信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 刷新预警
    const refreshWarnings = () => {
      getWarnings()
    }
    
    // 筛选预警
    const filterWarnings = () => {
      // 筛选逻辑在computed中处理
    }
    
    // 查看详情
    const viewDetail = (warning) => {
      selectedWarning.value = warning
      showDetailModal.value = true
    }
    
    // 处理预警
    const handleWarning = (warning) => {
      // 根据预警类型跳转到相应页面
      const routes = {
        'acceptance_overdue': '/purchase/acceptance',
        'acceptance_warning': '/purchase/acceptance',
        'settlement_overdue': '/purchase/reimbursement',
        'settlement_warning': '/purchase/reimbursement'
      }
      
      const targetRoute = routes[warning.type]
      if (targetRoute) {
        // 通过emit事件通知父组件进行路由跳转
        emit('navigate', { path: targetRoute, query: { id: warning.id } })
      }
    }
    
    // 获取优先级颜色
    const getPriorityColor = (priority) => {
      const colors = {
        'high': 'red',
        'medium': 'orange'
      }
      return colors[priority] || 'default'
    }
    
    // 获取优先级文本
    const getPriorityText = (priority) => {
      const texts = {
        'high': '高优先级',
        'medium': '中优先级'
      }
      return texts[priority] || priority
    }
    
    // 获取类型颜色
    const getTypeColor = (type) => {
      const colors = {
        'acceptance_overdue': 'red',
        'settlement_overdue': 'red',
        'acceptance_warning': 'orange',
        'settlement_warning': 'orange'
      }
      return colors[type] || 'default'
    }
    
    // 获取类型文本
    const getTypeText = (type) => {
      const texts = {
        'acceptance_overdue': '验收超期',
        'settlement_overdue': '结算超期',
        'acceptance_warning': '验收预警',
        'settlement_warning': '结算预警'
      }
      return texts[type] || type
    }
    
    // 初始化
    onMounted(() => {
      getWarnings()
    })
    
    return {
      loading,
      warnings,
      warningStats,
      filteredWarnings,
      filterType,
      filterPriority,
      showDetailModal,
      selectedWarning,
      getWarnings,
      refreshWarnings,
      filterWarnings,
      viewDetail,
      handleWarning,
      getPriorityColor,
      getPriorityText,
      getTypeColor,
      getTypeText
    }
  }
}
</script>

<style scoped>
.time-warnings {
  padding: 16px;
}

.warning-stats {
  margin-bottom: 16px;
}

.warning-list {
  background: white;
}

.warning-description {
  color: #666;
}

.warning-details {
  margin-top: 8px;
  font-size: 12px;
}

.warning-detail .ant-descriptions {
  margin-bottom: 16px;
}
</style>
