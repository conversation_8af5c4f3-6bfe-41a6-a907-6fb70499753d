# Generated by Django 5.2.3 on 2025-06-18 18:01

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0004_add_system_log'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='通知标题')),
                ('content', models.TextField(verbose_name='通知内容')),
                ('notification_type', models.CharField(choices=[('approval', '审批通知'), ('purchase', '采购通知'), ('acceptance', '验收通知'), ('reimbursement', '报销通知'), ('rejection', '驳回通知'), ('return', '退回通知'), ('system', '系统通知')], max_length=20, verbose_name='通知类型')),
                ('priority', models.CharField(choices=[('low', '低'), ('normal', '普通'), ('high', '高'), ('urgent', '紧急')], default='normal', max_length=10, verbose_name='优先级')),
                ('target_model', models.CharField(blank=True, max_length=100, null=True, verbose_name='目标模型')),
                ('target_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='目标ID')),
                ('target_url', models.CharField(blank=True, max_length=500, null=True, verbose_name='跳转链接')),
                ('is_read', models.BooleanField(default=False, verbose_name='是否已读')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='阅读时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_notifications', to=settings.AUTH_USER_MODEL, verbose_name='接收者')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL, verbose_name='发送者')),
            ],
            options={
                'verbose_name': '系统通知',
                'verbose_name_plural': '系统通知',
                'db_table': 'system_notification',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['recipient', 'is_read', 'created_at'], name='system_noti_recipie_77c36f_idx'), models.Index(fields=['notification_type', 'created_at'], name='system_noti_notific_ab4810_idx'), models.Index(fields=['target_model', 'target_id'], name='system_noti_target__aafdf7_idx')],
            },
        ),
    ]
