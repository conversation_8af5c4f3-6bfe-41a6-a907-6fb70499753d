<template>
  <div>
    <div class="purchase-approval">
      <!-- 页面标题区域 -->
      <div class="page-header business-card">
        <div class="header-content">
          <h1 class="page-title">
            <CheckCircleOutlined />
            需求审核
          </h1>
          <p class="page-subtitle">审批采购需求，管理审批流程</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <span class="stat-number">{{ pendingCount }}</span>
            <span class="stat-label">待审批</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ approvedCount }}</span>
            <span class="stat-label">已审批</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ rejectedCount }}</span>
            <span class="stat-label">已驳回</span>
          </div>
        </div>
      </div>

      <!-- 同一个面板内包括上方按钮区、下方表格区 -->
      <div class="department-tables business-card">


        <!-- 表格区域 -->
        <div class="table-section">
          <!-- 表格标题和按钮区域 - 使用flex布局，两端对齐 -->
          <div class="table-header-flex">
            <div class="table-title-section">
              <div class="table-title">
                <span>需求审核列表</span>
                <span class="table-subtitle">共 {{ totalCount }} 条记录</span>
              </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="table-actions">
              <a-space :size="8">
                <!-- 主要业务操作按钮 -->
                <a-button v-if="selectedRowKeys.length > 0 && hasPendingApprovals && permissions.canBatchApprove"
                  type="primary" @click="batchApprove"
                  :disabled="selectedRowKeys.length === 0" size="small" class="compact-action-btn"
                  style="background: #52c41a; border-color: #52c41a;">
                  <CheckOutlined />
                  批量审批 ({{ selectedRowKeys.length }})
                </a-button>

                <!-- 分隔线 -->
                <a-divider v-if="selectedRowKeys.length > 0 && hasPendingApprovals" type="vertical" />

                <!-- 筛选和视图操作按钮 -->
                <a-button @click="toggleFilters" size="small" class="compact-action-btn">
                  <template #icon>
                    <component :is="showFilters ? 'UpOutlined' : 'DownOutlined'" />
                  </template>
                  {{ showFilters ? '收起筛选' : '展开筛选' }}
                </a-button>
                <a-button @click="resetFilters" size="small" class="compact-action-btn">重置筛选</a-button>
                <a-button v-if="permissions.canExport" @click="showExportModal = true" size="small" class="compact-action-btn">
                  <DownloadOutlined />
                  导出
                </a-button>
                <a-button size="small" class="compact-action-btn" @click="showColumnFilter = true">
                  <template #icon>
                    <SettingOutlined />
                  </template>
                  字段筛选
                  <a-badge :count="selectedColumns.length"
                    :number-style="{ backgroundColor: '#52c41a', fontSize: '10px' }" />
                </a-button>

                <!-- 字段筛选模态框 -->
                <a-modal v-model:open="showColumnFilter" title="字段筛选配置" :footer="null" width="auto" :centered="true"
                  :mask-closable="true" :destroy-on-close="true" wrap-class-name="column-filter-modal">
                  <template #closeIcon>
                    <CloseOutlined />
                  </template>
                  <div class="column-filter-panel">
                    <div class="preset-section">

                      <a-dropdown v-model:open="presetDropdownOpen" :trigger="['click']" placement="bottomRight"
                        :overlay-style="{ zIndex: 9999 }" @click.stop>
                        <a-button class="preset-trigger" @click.stop size="large">
                          <template #icon>
                            <SettingOutlined />
                          </template>
                          预设配置
                          <DownOutlined />
                        </a-button>
                        <template #overlay>
                          <a-menu @click="handlePresetClick" @click.stop>
                            <template v-for="item in presetMenuItems" :key="item.key || 'divider'">
                              <a-menu-divider v-if="item.type === 'divider'" />
                              <a-menu-item v-else :key="item.key">{{ item.title }}</a-menu-item>
                            </template>
                          </a-menu>
                        </template>
                      </a-dropdown>
                      <div>
                        <a-button type="primary" size="large" @click="handleSelectAll"
                          style="margin-left: 12px;">全选</a-button>
                        <a-button size="large" @click="handleReset" style="margin-left: 8px;">重置</a-button>
                      </div>
                    </div>

                    <div class="filter-tip">
                      <span>已选择 {{ selectedColumns.length }} / {{ columnOptions.length }} 个字段</span>
                    </div>

                    <a-checkbox-group v-model:value="selectedColumns" @change="handleColumnChange">
                      <!-- 动态字段分类 - 使用flex横向布局 -->
                      <div class="field-categories-container">
                        <div v-for="category in fieldCategories" :key="category.key" class="field-category-section">
                          <h5 class="category-title">{{ category.title }}</h5>
                          <div class="category-fields">
                            <div v-for="option in columnOptions.filter(opt => opt.category === category.key)"
                              :key="option.key" class="column-option">
                              <a-checkbox :value="option.key" :disabled="option.required" @click.stop>
                                <span class="column-title">{{ option.title }}</span>
                                <a-tag v-if="option.required" size="small" color="blue">必选</a-tag>
                              </a-checkbox>
                            </div>
                          </div>
                        </div>
                      </div>
                    </a-checkbox-group>
                  </div>
                </a-modal>
              </a-space>
            </div>
          </div>

          <!-- 详细筛选控件区域 - 分两行布局，与页面同宽 -->
          <div v-show="showFilters" class="detailed-filters-fullwidth">
            <!-- 第一行：基础筛选项 -->
            <div class="filter-row">
              <a-row :gutter="[16, 12]" align="middle">
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.status" placeholder="状态" allowClear @change="handleFilterChange"
                    class="filter-select">
                    <a-select-option value="">全部状态</a-select-option>
                    <a-select-option value="pending_approval">待审批</a-select-option>
                    <a-select-option value="approved">已审批</a-select-option>
                    <a-select-option value="rejected">已驳回</a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.purchaseType" placeholder="采购类型" allowClear
                    @change="handleFilterChange" class="filter-select">
                    <a-select-option value="">全部类型</a-select-option>
                    <a-select-option v-for="type in purchaseTypes" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.department" placeholder="需求单位" allowClear
                    @change="handleFilterChange" class="filter-select" show-search :filter-option="filterOption">
                    <a-select-option value="">全部需求单位</a-select-option>
                    <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                      {{ dept.dept_name }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.requester" placeholder="申请人" allowClear @change="handleFilterChange"
                    class="filter-select" show-search :filter-option="filterOption">
                    <a-select-option value="">全部申请人</a-select-option>
                    <a-select-option v-for="user in users" :key="user.id" :value="user.id">
                      {{ user.username }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-select v-model:value="filters.approver" placeholder="审批人" allowClear @change="handleFilterChange"
                    class="filter-select" show-search :filter-option="filterOption">
                    <a-select-option value="">全部审批人</a-select-option>
                    <a-select-option v-for="user in users" :key="user.id" :value="user.id">
                      {{ user.username }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.minAmount" placeholder="最小金额" @input="debouncedFilterChange"
                    class="filter-input" type="number" :min="0">
                    <template #prefix>
                      ¥
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.maxAmount" placeholder="最大金额" @input="debouncedFilterChange"
                    class="filter-input" type="number" :min="0">
                    <template #prefix>
                      ¥
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-range-picker v-model:value="filters.approvalTimeRange" :placeholder="['开始时间', '结束时间']"
                    @change="handleFilterChange" class="filter-date-picker" />
                </a-col>
              </a-row>
            </div>

            <!-- 第二行：搜索和其他筛选项 -->
            <div class="filter-row">
              <a-row :gutter="[16, 12]" align="middle">
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.id" placeholder="ID" @input="debouncedFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.itemName" placeholder="搜索物品名称" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.spec" placeholder="搜索规格型号" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.procurementMethod" placeholder="采购方式" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.requirementSource" placeholder="需求来源" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.fundProject" placeholder="经费项目" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.unit" placeholder="计量单位" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.itemCategory" placeholder="物品种类" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
                  <a-input v-model:value="filters.remarks" placeholder="需求备注" @change="handleFilterChange"
                    class="filter-input">
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                </a-col>
              </a-row>
            </div>
          </div>


        </div>

        <div class="table-container">
          <a-table :columns="filteredColumns" :data-source="requests" :row-key="record => record.id"
            :row-selection="rowSelection" :pagination="{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: (page, pageSize) => handleTableChange({ current: page, pageSize }),
              onShowSizeChange: (current, size) => handleTableChange({ current, pageSize: size })
            }" :loading="loading" @change="handleTableChange" :scroll="{ x: 'max-content', y: 'calc(100vh - 400px)' }"
            :virtual="false" :row-height="54" bordered class="unified-table">
            <template #bodyCell="{ column, record }">
              <!-- 状态标签 -->
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ record.status_display || record.status }}
                </a-tag>
              </template>

              <!-- 金额显示 -->
              <template v-if="column.dataIndex === 'amount'">
                ¥{{ record.amount }}
              </template>

              <!-- 操作列 -->
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button v-if="permissions.canViewDetail" type="link" size="small" @click="viewDetail(record)">
                    详情
                  </a-button>
                  <a-button v-if="(record._originalStatus || record.status) === 'pending_approval' && permissions.canApprove"
                    type="link" size="small" @click="approveRequest(record)" style="color: #52c41a;">
                    审批
                  </a-button>
                  <a-button v-if="(record._originalStatus || record.status) === 'pending_approval' && permissions.canReject"
                    type="link" size="small" @click="rejectRequest(record)" danger>
                    驳回
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <!-- 审批对话框 -->
    <a-modal v-model:open="showApprovalModal" title="审批需求" width="600px" :maskClosable="false" class="approval-modal">
      <template #title>
        <div class="modal-title">
          <CheckCircleOutlined style="color: #52c41a; margin-right: 8px;" />
          审批需求
        </div>
      </template>

      <div class="modal-content">
        <!-- 需求信息 -->
        <div class="request-info" v-if="currentRequest">
          <a-descriptions :column="2" size="small" bordered>
            <a-descriptions-item label="需求编号">{{ currentRequest.id || currentRequest.request_number
            }}</a-descriptions-item>
            <a-descriptions-item label="物品名称">{{ currentRequest.item_name }}</a-descriptions-item>
            <a-descriptions-item label="申请人">{{ currentRequest.requester_name }}</a-descriptions-item>
            <a-descriptions-item label="预算金额">{{ currentRequest.budget_total_amount || currentRequest.amount
            }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <a-divider />

        <!-- 审批表单 -->
        <a-form layout="vertical" :model="approvalForm">
          <a-form-item label="审批意见" name="comment" :rules="[{ required: false, message: '请输入审批意见' }]">
            <a-textarea v-model:value="approvalForm.comment" placeholder="请输入审批意见（可选）" :rows="4" show-count
              :maxlength="500" />
          </a-form-item>
        </a-form>
      </div>

      <template #footer>
        <div class="modal-footer">
          <a-button @click="cancelApproval" size="large">
            取消
          </a-button>
          <a-button v-if="permissions.canApprove" type="primary" @click="handleApproval" :loading="approvalLoading" size="large"
            style="background: #52c41a; border-color: #52c41a;">
            <CheckOutlined />
            确认审批
          </a-button>
        </div>
      </template>
    </a-modal>

    <!-- 驳回对话框 -->
    <a-modal v-model:open="showRejectModal" title="驳回需求" width="600px" :maskClosable="false" class="reject-modal">
      <template #title>
        <div class="modal-title">
          <CloseCircleOutlined style="color: #ff4d4f; margin-right: 8px;" />
          驳回需求
        </div>
      </template>

      <div class="modal-content">
        <!-- 需求信息 -->
        <div class="request-info" v-if="currentRequest">
          <a-descriptions :column="2" size="small" bordered>
            <a-descriptions-item label="需求编号">{{ currentRequest.id || currentRequest.request_number
              }}</a-descriptions-item>
            <a-descriptions-item label="物品名称">{{ currentRequest.item_name }}</a-descriptions-item>
            <a-descriptions-item label="申请人">{{ currentRequest.requester_name }}</a-descriptions-item>
            <a-descriptions-item label="预算金额">{{ currentRequest.budget_total_amount || currentRequest.amount
              }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <a-divider />

        <!-- 驳回表单 -->
        <a-form layout="vertical" :model="rejectForm">
          <a-form-item label="驳回原因" name="reasons" :rules="[{ required: true, message: '请选择驳回原因' }]">
            <a-checkbox-group v-model:value="rejectForm.reasons" class="reject-reasons">
              <a-row :gutter="[16, 16]">
                <a-col :span="12">
                  <a-checkbox value="需求不合理">需求不合理</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="比价手续不全">比价手续不全</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="重复采购">重复采购</a-checkbox>
                </a-col>
                <a-col :span="12">
                  <a-checkbox value="价格虚高">价格虚高</a-checkbox>
                </a-col>
                <a-col :span="24">
                  <a-checkbox value="其他">其他</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>

          <a-form-item label="详细说明" name="detail"
            v-if="rejectForm.reasons.includes('其他') || rejectForm.reasons.length > 0">
            <a-textarea v-model:value="rejectForm.detail" placeholder="请详细说明驳回原因" :rows="4" show-count
              :maxlength="500" />
          </a-form-item>
        </a-form>
      </div>

      <template #footer>
        <div class="modal-footer">
          <a-button @click="cancelReject" size="large">
            取消
          </a-button>
          <a-button v-if="permissions.canReject" type="primary" danger @click="handleReject" :loading="rejectLoading" size="large">
            <CloseOutlined />
            确认驳回
          </a-button>
        </div>
      </template>
    </a-modal>

    <!-- 导出配置对话框 -->
    <a-modal v-model:open="showExportModal" title="导出Excel配置" width="80%" :footer="null" :destroy-on-close="true">
      <div class="export-config-container">
        <!-- 筛选条件 -->
        <div class="filter-section compact">
          <h4>筛选条件</h4>
          <a-form :model="exportFilters" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-row :gutter="[12, 6]">
              <a-col :xs="24" :sm="12" :md="6" :lg="5">
                <a-form-item label="审批状态" label-align="right">
                  <a-select v-model:value="exportFilters.status" placeholder="选择状态" allowClear mode="multiple"
                    size="small">
                    <a-select-option value="pending_approval">待审批</a-select-option>
                    <a-select-option value="approved">已审批</a-select-option>
                    <a-select-option value="rejected">已驳回</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="5">
                <a-form-item label="需求单位" label-align="right">
                  <a-select v-model:value="exportFilters.department" placeholder="选择需求单位" allowClear show-search
                    mode="multiple" size="small">
                    <a-select-option v-for="dept in departments" :key="dept.id" :value="dept.id">
                      {{ dept.hierarchy_path || dept.dept_name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="5" :lg="4">
                <a-form-item label="审批人" label-align="right">
                  <a-select v-model:value="exportFilters.approver" placeholder="选择审批人" allowClear show-search
                    size="small">
                    <a-select-option v-for="approver in approvers" :key="approver.id" :value="approver.id">
                      {{ approver.real_name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="5" :lg="6">
                <a-form-item label="日期范围" label-align="right">
                  <a-range-picker v-model:value="exportFilters.dateRange" style="width: 100%" size="small"
                    :placeholder="['开始日期', '结束日期']" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="2" :lg="4">
                <a-form-item :wrapper-col="{ span: 24 }">
                  <a-space size="small">
                    <a-button @click="resetExportFilters" size="small">重置</a-button>
                    <a-button type="primary" @click="searchExportRecords" size="small">搜索</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 导出字段配置 -->
        <div class="fields-section" style="border-radius: 6px; margin: 16px 0;">
          <a-collapse v-model:activeKey="fieldsCollapseKey" size="small">
            <a-collapse-panel key="1" header="导出字段配置">
              <template #extra>
                <a-space>
                  <a-button @click.stop="selectAllExportFields" size="small">全选</a-button>
                  <a-button @click.stop="resetExportFields" size="small">重置</a-button>
                  <a-button @click.stop="selectRequiredExportFields" size="small">仅必选</a-button>
                </a-space>
              </template>
              <a-checkbox-group v-model:value="selectedExportFields" @change="onExportFieldsChange">
                <div class="export-fields-container">
                  <div v-for="category in exportFieldCategories" :key="category.key" class="export-field-category">
                    <div class="export-category-header">
                      <h5 class="export-category-title">{{ category.title }}</h5>
                    </div>
                    <div class="export-category-fields">
                      <div v-for="field in exportFieldOptions.filter(opt => opt.category === category.key)"
                        :key="field.value" class="export-field-option">
                        <a-checkbox :value="field.value" :disabled="field.required">
                          <span class="field-title">{{ field.label }}</span>
                          <a-tag v-if="field.required" size="small" color="blue">必选</a-tag>
                        </a-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </a-checkbox-group>
            </a-collapse-panel>
          </a-collapse>
        </div>

        <!-- 选择导出记录 -->
        <div class="records-section">
          <div class="section-header">
            <h4>选择导出记录</h4>
            <div>
              <span class="record-count">共 {{ exportRecords.length }} 条记录，已选择 {{ selectedExportRecords.length }}
                条</span>
              <!-- <a-space>
                <a-select v-model:value="exportRecordPageSize" @change="onExportRecordPageSizeChange" size="small"
                  style="width: 80px">
                  <a-select-option :value="10">10</a-select-option>
                  <a-select-option :value="20">20</a-select-option>
                  <a-select-option :value="50">50</a-select-option>
                  <a-select-option :value="100">100</a-select-option>
                </a-select>
                <a-button @click="searchExportRecords" size="small" :loading="exportSearchLoading">
                  <ReloadOutlined />
                  刷新
                </a-button>
              </a-space> -->
            </div>
          </div>
          <a-table :columns="exportRecordColumns" :data-source="exportRecords" :row-selection="exportRecordRowSelection"
            :row-key="record => record.id" :pagination="{
              current: exportRecordPagination.current,
              total: exportRecordPagination.total,
              pageSize: exportRecordPageSize,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: onExportRecordPageChange
            }" size="small" :scroll="{ x: 800 }" bordered class="export-records-table">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ record.status_display || record.status }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'purchase_type'">
                {{ record.purchase_type_display || record.purchase_type }}
              </template>
              <template v-else-if="column.key === 'procurement_method'">
                {{ record.procurement_method_display || record.procurement_method }}
              </template>
              <template v-else-if="column.key === 'budget_total_amount'">
                ¥{{ record.budget_total_amount?.toLocaleString() || '0' }}
              </template>
              <template v-else-if="column.key === 'created_at'">
                {{ record.created_at ? formatDateToYMD(record.created_at) : '-' }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 导出预览和操作 -->
        <div class="actions-section">
          <a-space>
            <a-button @click="previewExportData" :disabled="selectedExportRecords.length === 0">
              <EyeOutlined />
              预览 ({{ selectedExportRecords.length }}条)
            </a-button>
            <a-button v-if="permissions.canExport" type="primary" @click="executeExport"
              :disabled="selectedExportRecords.length === 0" :loading="exportLoading">
              <DownloadOutlined />
              导出Excel ({{ selectedExportRecords.length }}条)
            </a-button>
            <a-button @click="handleExportCancel">取消</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
    <!-- 预览模态框 -->
    <a-modal v-model:open="showPreviewModal" title="导出预览" width="90%" :footer="null" :centered="true"
      :mask-closable="true" :destroy-on-close="true" wrap-class-name="preview-modal-wrapper"
      :body-style="{ height: '80vh', overflow: 'auto' }">
      <div class="preview-container">
        <div class="preview-header">
          <a-space>
            <span>预览数据 (共 {{ previewTotal }} 条记录)</span>
          </a-space>
        </div>

        <a-table :columns="previewColumns" :data-source="previewData" :loading="previewLoading" :pagination="{
          total: previewTotal,
          pageSize: 50,
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
        }" :scroll="{ x: 'max-content' }" size="small">
          <template #bodyCell="{ column, record }">
            <template
              v-if="column.dataIndex === 'created_at' || column.dataIndex === 'submission_date' || column.dataIndex === 'approved_at'">
              {{ record[column.dataIndex] ? formatDateToYMD(record[column.dataIndex]) : '-' }}
            </template>
            <template v-else-if="column.dataIndex === 'budget_quantity' || column.dataIndex === 'purchase_quantity'">
              {{ record[column.dataIndex] || 0 }}
            </template>
            <template v-else>
              {{ record[column.dataIndex] || '-' }}
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
</div>
</template>

<script>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { debounce } from '@/utils/debounce'
import { useDictMixin, PAGE_DICT_TYPES } from '@/mixins/dictMixin'
import { usePermission } from '@/utils/permissionHelper'
import {
  CheckCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  CloseCircleOutlined,
  DownloadOutlined,
  SettingOutlined,
  DownOutlined,
  UpOutlined,
  SearchOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
import api from '@/api'
import { getPageColumnOptions, getPageColumnPresets, getPageFieldCategories, getPagePresetMenuItems, getPageExportFieldOptions, getPageDefaultExportFields, getPageDefaultColumns, sortColumnsByOrder } from '@/utils/validation'

export default {
  name: 'PurchaseApproval',
  components: {
    CheckCircleOutlined,
    CheckOutlined,
    CloseOutlined,
    CloseCircleOutlined,
    DownloadOutlined,
    SettingOutlined,
    DownOutlined,
    UpOutlined,
    SearchOutlined,
    EyeOutlined
  },
  setup() {
    const router = useRouter()

    // 使用字典混入 - 只保留必要的功能
    const {
      getDictOptions,
      getStatusColor,
      formatDateToYMD
    } = useDictMixin(PAGE_DICT_TYPES.APPROVAL)

    // 使用权限检查
    const {
      hasPermission,
      PURCHASE_PERMISSIONS: PERMS
    } = usePermission()

    const loading = ref(false)
    const requests = ref([])
    const selectedRowKeys = ref([])
    const showApprovalModal = ref(false)
    const showRejectModal = ref(false)
    const currentRequest = ref(null)
    const approvalLoading = ref(false)
    const rejectLoading = ref(false)

    // 部门列表
    const departments = ref([])

    // 筛选选项数据源 - 使用字典服务
    const purchaseTypes = computed(() => getDictOptions('purchase_type'))
    const applicants = ref([])
    const approvers = ref([])

    // 筛选控件显示状态
    const showFilters = ref(false)
    const showColumnFilter = ref(false)
    const presetDropdownOpen = ref(false)
    const showExportModal = ref(false)

    // 导出相关状态
    const exportActiveTab = ref('fields')
    const exportLoading = ref(false)
    const previewLoading = ref(false)
    const previewData = ref([])
    const previewTotal = ref(0)
    const showPreviewModal = ref(false)

    // 基于业务流程的导出字段配置（审批阶段）
    const exportFieldOptions = getPageExportFieldOptions('approval')
    const exportFieldCategories = getPageFieldCategories('approval')
    const selectedExportFields = ref(getPageDefaultExportFields('approval'))
    const fieldsCollapseKey = ref([])

    // 导出筛选条件
    const exportFilters = reactive({
      status: [],
      department: [],
      itemName: '',
      requester: '',
      approver: '',
      minAmount: '',
      maxAmount: '',
      dateRange: [],
      // 新增导出筛选字段
      procurementMethod: '',
      requirementSource: '',
      fundProject: '',
      unit: '',
      itemCategory: '',
      remarks: ''
    })

    // 导出记录选择相关
    const exportRecords = ref([])
    const selectedExportRecords = ref([])
    const selectedExportRecordData = ref([])
    const exportSearchLoading = ref(false)
    const exportRecordPageSize = ref(20)
    const exportRecordPagination = ref({
      current: 1,
      total: 0
    })

    // 筛选条件
    const filters = reactive({
      status: '',
      id: '',
      itemName: '',
      spec: '',
      department: '',
      applicant: '',
      approver: '',
      purchaseType: '',
      approvalTimeRange: [],
      minAmount: '',
      maxAmount: '',
      dateRange: [],
      // 新增缺失的筛选字段
      procurementMethod: '',     // 采购方式
      requirementSource: '',     // 需求来源
      fundProject: '',          // 经费项目
      unit: '',                 // 计量单位
      itemCategory: '',         // 物品种类
      remarks: ''               // 需求备注
    })

    // 基于业务流程的字段筛选配置（审批阶段，排除操作列）
    const columnOptions = getPageColumnOptions('approval').filter(
      (opt) => opt.key !== 'action'
    )

    // 从本地存储读取字段配置
    const getStoredColumns = () => {
      const COLUMNS_VERSION = '2.1' // 版本号，更新时清除旧缓存
      const STORAGE_KEY = `purchase-approval-columns-v${COLUMNS_VERSION}`

      try {
        // 清除旧版本的缓存
        const oldKeys = ['purchase-approval-columns', 'purchase-approval-columns-v2.0']
        oldKeys.forEach(key => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key)
          }
        })

        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const parsed = JSON.parse(stored)
          // 确保必选字段始终被包含，排除操作列
          const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)
          return [...new Set([...parsed, ...requiredColumns])].filter(col => col !== 'action')
        }
      } catch (error) {
        console.warn('读取字段配置失败:', error)
      }
      // 使用默认字段配置（按顺序排列），排除操作列
      const defaultColumns = getPageDefaultColumns('approval').filter(col => col !== 'action')
      return defaultColumns
    }

    // 保存字段配置到本地存储
    const saveColumnsToStorage = (columns) => {
      const COLUMNS_VERSION = '2.1'
      const STORAGE_KEY = `purchase-approval-columns-v${COLUMNS_VERSION}`
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(columns))
      } catch (error) {
        console.warn('保存字段配置失败:', error)
      }
    }

    const selectedColumns = ref(getStoredColumns())

    // 基于业务流程的预设配置（审批阶段）
    const columnPresets = getPageColumnPresets('approval')

    // 基于业务流程的字段分类配置（审批阶段）
    const fieldCategories = getPageFieldCategories('approval')

    // 基于业务流程的预设菜单选项（审批阶段）
    const presetMenuItems = getPagePresetMenuItems('approval')

    // 审批表单
    const approvalForm = reactive({
      comment: ''
    })

    // 驳回表单
    const rejectForm = reactive({
      reasons: [],
      detail: ''
    })

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
      pageSizeOptions: ['10', '20', '50', '100']
    })

    // 行选择配置
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      },
      getCheckboxProps: (record) => ({
        disabled: (record._originalStatus || record.status) !== 'pending_approval',
        name: record.item_name,
      }),
    }

    // 计算属性（使用原始状态编码进行统计）
    const pendingCount = computed(() => {
      return requests.value.filter(item => (item._originalStatus || item.status) === 'pending_approval').length
    })

    const approvedCount = computed(() => {
      return requests.value.filter(item => (item._originalStatus || item.status) === 'approved').length
    })

    const rejectedCount = computed(() => {
      return requests.value.filter(item => (item._originalStatus || item.status) === 'rejected').length
    })

    const hasPendingRequests = computed(() => {
      return selectedRowKeys.value.some(id => {
        const record = requests.value.find(r => r.id === id)
        return record && (record._originalStatus || record.status) === 'pending_approval'
      })
    })

    // 权限检查计算属性
    const permissions = computed(() => {
      return {
        canApprove: hasPermission(PERMS.APPROVAL.APPROVE),
        canReject: hasPermission(PERMS.APPROVAL.REJECT),
        canBatchApprove: hasPermission(PERMS.APPROVAL.BATCH),
        canExport: hasPermission(PERMS.APPROVAL.EXPORT),
        canViewDetail: hasPermission(PERMS.APPROVAL.VIEW_DETAIL)
      }
    })



    // 所有可用的表格列定义
    const allColumns = [
      {
        title: '物品名称',
        dataIndex: 'item_name',
        key: 'item_name',
        width: 150,
        sorter: true,
        align: 'center'
      },
      {
        title: '规格型号',
        dataIndex: 'specification',
        key: 'specification',
        width: 200,
        sorter: true,
        align: 'center'
      },
      {
        title: '预算数量',
        dataIndex: 'budget_quantity',
        key: 'budget_quantity',
        width: 80,
        sorter: true,
        align: 'center'
      },
      {
        title: '预算单价',
        dataIndex: 'budget_unit_price',
        key: 'budget_unit_price',
        width: 120,
        sorter: true,
        align: 'center'
      },
      {
        title: '预算金额',
        dataIndex: 'budget_total_amount',
        key: 'budget_total_amount',
        width: 120,
        sorter: true,
        align: 'center'
      },
      {
        title: '需求单位',
        dataIndex: 'hierarchy_path',
        key: 'hierarchy_path',
        width: 150,
        sorter: true,
        align: 'center',
        customRender: ({ text }) => {
          // 显示完整的层级路径（分公司-办事处）
          return text || '未知单位'
        }
      },
      {
        title: '需求来源',
        dataIndex: 'requirement_source',
        key: 'requirement_source',
        width: 120,
        sorter: true,
        align: 'center'
      },
      {
        title: '申请人',
        dataIndex: 'requester_name',
        key: 'requester_name',
        width: 100,
        sorter: true,
        align: 'center'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        sorter: true,
        align: 'center'
      },
      {
        title: '采购类型',
        dataIndex: 'purchase_type',
        key: 'purchase_type',
        width: 100,
        sorter: true,
        align: 'center',
        customRender: ({ text }) => {
          return text || '-'
        }
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 150,
        sorter: true,
        customRender: ({ text }) => {
          return text ? formatDateToYMD(text) : '-'
        }
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        width: 200,
        sorter: true
      },
      {
        title: '提交时间',
        dataIndex: 'submission_date',
        key: 'submission_date',
        width: 150,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : '-'
        }
      },
      // 审批相关
      {
        title: '审批人',
        dataIndex: 'approver_name',
        key: 'approver_name',
        width: 100
      },
      {
        title: '审批时间',
        dataIndex: 'approved_at',
        key: 'approved_at',
        width: 150,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : '-'
        }
      },
      {
        title: '审批意见',
        dataIndex: 'approval_comment',
        key: 'approval_comment',
        width: 200
      },
      // 采购相关
      {
        title: '采购人',
        dataIndex: 'purchaser_name',
        key: 'purchaser_name',
        width: 100
      },
      {
        title: '采购时间',
        dataIndex: 'purchase_date',
        key: 'purchase_date',
        width: 150,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : '-'
        }
      },
      {
        title: '实际单价',
        dataIndex: 'actual_unit_price',
        key: 'actual_unit_price',
        width: 120,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : '-'
        }
      },
      {
        title: '采购金额',
        dataIndex: 'purchase_amount',
        key: 'purchase_amount',
        width: 120,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : '-'
        }
      },
      {
        title: '供应商',
        dataIndex: 'supplier',
        key: 'supplier',
        width: 150
      },
      // 验收相关
      {
        title: '验收人',
        dataIndex: 'acceptor_name',
        key: 'acceptor_name',
        width: 100
      },
      {
        title: '验收时间',
        dataIndex: 'acceptance_date',
        key: 'acceptance_date',
        width: 150,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : '-'
        }
      },
      {
        title: '实际数量',
        dataIndex: 'actual_quantity',
        key: 'actual_quantity',
        width: 80
      },
      // 结算相关
      {
        title: '结算金额',
        dataIndex: 'settlement_amount',
        key: 'settlement_amount',
        width: 120,
        customRender: ({ text }) => {
          return text ? `¥${parseFloat(text).toFixed(2)}` : '-'
        }
      },
      {
        title: '结算人',
        dataIndex: 'settler_name',
        key: 'settler_name',
        width: 100
      },
      {
        title: '结算时间',
        dataIndex: 'settled_at',
        key: 'settled_at',
        width: 150,
        customRender: ({ text }) => {
          return text ? new Date(text).toLocaleDateString() : '-'
        }
      },
      {
        title: '交易流水号',
        dataIndex: 'transaction_number',
        key: 'transaction_number',
        width: 150
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 200,
        fixed: 'right',
        align: 'center'
      }
    ]

    // 为所有列添加居中对齐（如果还没有的话）
    allColumns.forEach(column => {
      if (!column.align) {
        column.align = 'center'
      }
    })

    // 动态筛选后的列
    const filteredColumns = computed(() => {
      // 构建选中的列（排除操作列）
      const selectedCols = selectedColumns.value
        .filter(columnKey => columnKey !== 'action') // 排除操作列
        .map(columnKey => {
          // 从 allColumns 中找到对应的列定义
          const columnDef = allColumns.find(col => col.key === columnKey)
          if (columnDef) {
            return columnDef
          }
          // 如果在 allColumns 中找不到，则创建一个基本的列定义
          const option = columnOptions.find(opt => opt.key === columnKey)
          if (option) {
            return {
              title: option.title,
              dataIndex: columnKey,
              key: columnKey,
              width: 120,
              ellipsis: true,
              align: 'center' // 所有列居中显示
            }
          }
          return null
        }).filter(Boolean) // 过滤掉 null 值

      // 始终在最右侧添加操作列
      const actionColumn = {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 200,
        fixed: 'right',
        align: 'center'
      }

      return [...selectedCols, actionColumn]
    })

    // 获取部门列表 - 用于筛选选项，使用专门的无分页接口
    const getDepartments = async () => {
      try {
        const response = await api.departments.getAll()
        if (response.code === 200) {
          departments.value = response.data || []
        }
      } catch (error) {
        console.error('获取部门列表失败:', error)
      }
    }

    // 构建查询参数的辅助函数
    const buildQueryParams = () => {
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        page_type: 'approval', // 指定页面类型
        // 审核页面默认状态
        status_in: filters.status || 'pending_approval,approved,rejected'
      }

      // 优化筛选参数构建
      const filterMappings = {
        itemName: 'search',
        requester: 'requester__username__icontains',
        purchaseType: 'purchase_type',
        requirementSource: 'requirement_source__icontains'
      }

      // 批量处理简单筛选参数
      Object.entries(filterMappings).forEach(([key, mapping]) => {
        if (filters[key]) {
          params[mapping] = filters[key]
        }
      })

      // 处理特殊筛选参数
      if (filters.id) {
        const idNum = parseInt(filters.id)
        if (!isNaN(idNum)) params.id = idNum
      }

      if (filters.minAmount) {
        const minAmount = parseFloat(filters.minAmount)
        if (!isNaN(minAmount)) params.budget_total_amount__gte = minAmount
      }

      if (filters.maxAmount) {
        const maxAmount = parseFloat(filters.maxAmount)
        if (!isNaN(maxAmount)) params.budget_total_amount__lte = maxAmount
      }

      // 处理部门筛选
      if (filters.department) {
        const selectedDept = departments.value.find(d => d.id == filters.department)
        if (selectedDept) {
          params.hierarchy_path__icontains = selectedDept.parent_id
            ? selectedDept.hierarchy_path
            : selectedDept.dept_name
        }
      }

      // 处理日期范围
      if (filters.dateRange?.length === 2) {
        params.created_at__gte = filters.dateRange[0].format('YYYY-MM-DD')
        params.created_at__lte = filters.dateRange[1].format('YYYY-MM-DD')
      }

      // 处理排序
      if (sortField.value && sortOrder.value) {
        params.ordering = `${sortOrder.value === 'descend' ? '-' : ''}${sortField.value}`
      }

      return params
    }

    // 获取采购需求列表 - 优化版本
    const getPurchaseRequests = async () => {
      // 防止重复请求
      if (loading.value) return

      loading.value = true
      try {
        const params = buildQueryParams()

        // 添加额外的筛选参数
        const additionalFilters = {
          procurementMethod: 'procurement_method__icontains',
          fundProject: 'fund_project__icontains',
          unit: 'unit__icontains',
          itemCategory: 'item_category__icontains',
          remarks: 'remarks__icontains'
        }

        Object.entries(additionalFilters).forEach(([key, mapping]) => {
          if (filters[key]) {
            params[mapping] = filters[key]
          }
        })

        const response = await api.purchaseRequests.getList(params)

        if (response.code === 200) {
          const rawRequests = response.data.results || []

          // 使用后端提供的display字段，同时保留原始编码用于逻辑判断
          const convertedRequests = rawRequests.map(request => ({
            ...request,
            // 使用后端提供的display字段进行显示
            status: request.status_display || request.status,
            item_category: request.item_category_display || request.item_category,
            unit: request.unit_display || request.unit,
            procurement_method: request.procurement_method_display || request.procurement_method,
            fund_project: request.fund_project_display || request.fund_project,
            purchase_type: request.purchase_type_display || request.purchase_type,
            // 保存原始编码用于逻辑判断
            _originalStatus: request.status,
            _originalItemCategory: request.item_category,
            _originalUnit: request.unit,
            _originalProcurementMethod: request.procurement_method,
            _originalFundProject: request.fund_project,
            _originalPurchaseType: request.purchase_type
          }))

          requests.value = convertedRequests
          pagination.total = response.data.count || 0
        } else {
          throw new Error(`API返回错误: ${response.message}`)
        }
      } catch (error) {
        console.error('获取采购需求失败:', error)

        // 优化错误处理
        if (error.response?.status === 404) {
          if (pagination.current > 1) {
            pagination.current = 1
            return getPurchaseRequests()
          } else {
            requests.value = []
            pagination.total = 0
            return
          }
        }

        // 其他错误显示提示
        message.error(error.message || '获取数据失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 筛选变化处理
    const handleFilterChange = () => {
      pagination.current = 1
      getPurchaseRequests()
    }

    // 防抖筛选变化处理
    const debouncedFilterChange = debounce(handleFilterChange, 500)

    // 判断选中的记录是否全部为已审批状态
    const hasPendingApprovals = computed(() => {
      if (selectedRowKeys.value.length === 0) return false;
      return selectedRowKeys.value.every((id) => {
        const record = requests.value.find((r) => r.id === id);
        return record && (record._originalStatus || record.status) === "approved";
      });
    });

    // 重置筛选
    const resetFilters = () => {
      Object.assign(filters, {
        status: '',
        id: '',
        itemName: '',
        spec: '',
        department: '',
        applicant: '',
        approver: '',
        purchaseType: '',
        approvalTimeRange: [],
        minAmount: '',
        maxAmount: '',
        dateRange: [],
        // 重置新增的筛选字段
        procurementMethod: '',
        requirementSource: '',
        fundProject: '',
        unit: '',
        itemCategory: '',
        remarks: ''
      })
      handleFilterChange()
    }

    // 获取筛选选项数据
    const getFilterOptions = async () => {
      try {
        // 获取用户列表（申请人、审批人）
        const usersResponse = await api.users.getList({ page_size: 100 })
        if (usersResponse.code === 200) {
          const users = usersResponse.data.results || usersResponse.data
          applicants.value = users // 所有用户都可以是申请人
          // 所有用户都可以作为审批人，因为审批权限由系统动态控制
          approvers.value = users
        }
      } catch (error) {
        console.error('获取筛选选项失败:', error)
      }
    }

    // 筛选选项过滤方法
    const filterOption = (input, option) => {
      return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 展开/收起筛选控件
    const toggleFilters = () => {
      showFilters.value = !showFilters.value
    }

    // 字段筛选方法
    const onColumnChange = (checkedValues) => {
      // 确保必选字段始终被选中
      const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)
      const newSelectedColumns = [...new Set([...checkedValues, ...requiredColumns])]
      selectedColumns.value = newSelectedColumns
      saveColumnsToStorage(newSelectedColumns)
    }

    // 处理字段变更（阻止面板关闭）
    const handleColumnChange = (checkedValues) => {
      onColumnChange(checkedValues)
    }

    // 处理预设点击（阻止面板关闭）
    const handlePresetClick = ({ key }) => {
      applyPreset({ key })
      // 保持dropdown打开状态
      presetDropdownOpen.value = true
    }

    // 处理全选按钮点击
    const handleSelectAll = (e) => {
      e.stopPropagation()
      selectAllColumns()
    }

    // 处理重置按钮点击
    const handleReset = (e) => {
      e.stopPropagation()
      resetColumns()
    }

    const selectAllColumns = () => {
      const sortedOptions = sortColumnsByOrder(columnOptions)
      const allColumns = sortedOptions.map(opt => opt.key)
      selectedColumns.value = allColumns
      saveColumnsToStorage(allColumns)
    }

    const resetColumns = () => {
      // 重置到系统默认配置
      const defaultColumns = getPageDefaultColumns('approval')
      selectedColumns.value = defaultColumns
      saveColumnsToStorage(defaultColumns)
    }

    // 应用预设
    const applyPreset = ({ key }) => {
      const presetColumns = columnPresets[key]
      if (presetColumns) {
        // 确保必选字段始终被包含
        const requiredColumns = columnOptions.filter(opt => opt.required).map(opt => opt.key)

        // 合并预设字段和必选字段
        const combinedColumns = [...new Set([...presetColumns, ...requiredColumns])]

        // 按照标准顺序排列字段
        const sortedColumns = sortColumnsByOrder(combinedColumns, columnOptions)

        selectedColumns.value = sortedColumns
        saveColumnsToStorage(sortedColumns)
      }
    }

    // 计算总记录数
    const totalCount = computed(() => {
      return pagination.total
    })



    // 导出字段选择相关计算属性
    const exportFieldsIndeterminate = computed(() => {
      return selectedExportFields.value.length > 0 && selectedExportFields.value.length < exportFieldOptions.length
    })

    const exportFieldsCheckAll = computed(() => {
      return selectedExportFields.value.length === exportFieldOptions.length
    })

    // 导出记录表格列 - 根据选中字段动态生成
    const exportRecordColumns = computed(() => {
      // 基础列（始终显示）
      const baseColumns = [
        { title: 'ID', dataIndex: 'id', width: 80, fixed: 'left' }
      ]

      // 根据选中字段生成动态列
      const dynamicColumns = selectedExportFields.value.map(fieldKey => {
        const field = exportFieldOptions.find(f => f.value === fieldKey)

        if (fieldKey === 'id') return null // ID列已在基础列中

        return {
          title: field ? field.label : fieldKey,
          dataIndex: fieldKey,
          width: getExportColumnWidth(fieldKey),
          ellipsis: true,
          align: 'center',
          customRender: ({ text }) => {
            // 时间字段格式化为年/月/日
            if (['created_at', 'updated_at', 'submission_date', 'approval_date'].includes(fieldKey)) {
              return formatDateToYMD(text)
            }
            return text || '-'
          }
        }
      }).filter(Boolean)

      return [...baseColumns, ...dynamicColumns]
    })

    // 获取导出列宽度
    const getExportColumnWidth = (fieldKey) => {
      const widthMap = {
        'request_number': 120,
        'item_name': 150,
        'specification': 120,
        'quantity': 80,
        'budget_total_amount': 100,
        'status': 100,
        'dept_name': 120,
        'requester_name': 100,
        'created_at': 120,
        'purchase_type': 100,
        'procurement_method': 120,
        'requirement_source': 120,
        'fund_project': 120,
        'unit': 80,
        'item_category': 100,
        'approver_name': 100,
        'approval_date': 120
      }
      return widthMap[fieldKey] || 100
    }

    // 导出记录行选择 - 修复复选框功能
    const exportRecordRowSelection = computed(() => ({
      selectedRowKeys: selectedExportRecords.value,
      onChange: (selectedRowKeys, selectedRows) => {
        // 直接更新选中状态，不支持跨页
        selectedExportRecords.value = [...selectedRowKeys]
        selectedExportRecordData.value = [...selectedRows]
      },
      onSelectAll: (selected, selectedRows, changeRows) => {
        if (selected) {
          // 全选当前页 - 将当前页的所有记录添加到已选择列表
          const currentPageIds = changeRows.map(row => row.id)
          const existingIds = selectedExportRecords.value.filter(id => !currentPageIds.includes(id))
          selectedExportRecords.value = [...existingIds, ...currentPageIds]

          const existingData = selectedExportRecordData.value.filter(row => !currentPageIds.includes(row.id))
          selectedExportRecordData.value = [...existingData, ...changeRows]
        } else {
          // 取消全选当前页 - 从已选择列表中移除当前页的记录
          const currentPageIds = changeRows.map(row => row.id)
          selectedExportRecords.value = selectedExportRecords.value.filter(id => !currentPageIds.includes(id))
          selectedExportRecordData.value = selectedExportRecordData.value.filter(row => !currentPageIds.includes(row.id))
        }
      }
    }))

    // 预览表格列
    const previewColumns = computed(() => {
      return selectedExportFields.value.map(field => {
        const option = exportFieldOptions.find(opt => opt.value === field)
        return {
          title: option?.label || field,
          dataIndex: field,
          key: field,
          width: 120,
          ellipsis: true
        }
      })
    })

    // 导出字段全选处理
    const onExportFieldsCheckAllChange = (e) => {
      if (e.target.checked) {
        selectedExportFields.value = exportFieldOptions.map(option => option.value)
      } else {
        selectedExportFields.value = []
      }
    }

    // 导出字段选择变化
    const onExportFieldsChange = (checkedList) => {
      selectedExportFields.value = checkedList
    }

    // 使用dictMixin中的formatDateToYMD方法

    // 导出记录分页相关
    const onExportRecordPageSizeChange = (value) => {
      exportRecordPageSize.value = value
      searchExportRecords()
    }

    const onExportRecordPageChange = (page) => {
      exportRecordPagination.value.current = page
      searchExportRecords()
    }

    // 重置导出筛选条件
    const resetExportFilters = () => {
      Object.assign(exportFilters, {
        status: [],
        department: [],
        approver: [],
        dateRange: []
      })
    }

    // 搜索导出记录
    const searchExportRecords = async () => {
      exportSearchLoading.value = true
      try {
        const params = {
          page: exportRecordPagination.value.current,
          page_size: exportRecordPageSize.value,
          page_type: 'approval' // 添加页面类型参数，确保后端正确筛选状态
        }

        // 添加筛选条件
        if (exportFilters.status && exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(',')
        } else {
          // 如果没有指定状态，使用审批页面的默认状态
          params.status_in = 'pending_approval,approved,rejected'
        }

        // 修复部门筛选逻辑，使用后端支持的参数名
        if (exportFilters.department && exportFilters.department.length > 0) {
          params.department_in = exportFilters.department.join(',')
        }

        if (exportFilters.approver) {
          params.approver_id = exportFilters.approver
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.created_at__gte = exportFilters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = exportFilters.dateRange[1].format('YYYY-MM-DD')
        }

        const response = await api.purchaseRequests.getList(params)
        if (response.code === 200) {
          const rawRecords = response.data.results || response.data || []
          // 使用后端提供的display字段进行显示
          const processedRecords = rawRecords.map(record => ({
            ...record,
            status: record.status_display || record.status,
            item_category: record.item_category_display || record.item_category,
            unit: record.unit_display || record.unit,
            procurement_method: record.procurement_method_display || record.procurement_method,
            fund_project: record.fund_project_display || record.fund_project,
            purchase_type: record.purchase_type_display || record.purchase_type
          }))
          exportRecords.value = processedRecords
          exportRecordPagination.value.total = response.data.count || exportRecords.value.length
        }
      } catch (error) {
        console.error('搜索导出记录失败:', error)
        message.error('搜索导出记录失败')
      } finally {
        exportSearchLoading.value = false
      }
    }

    // 全选导出字段
    const selectAllExportFields = () => {
      selectedExportFields.value = exportFieldOptions.map(field => field.value)
      // 确保折叠面板展开
      if (!fieldsCollapseKey.value.includes('1')) {
        fieldsCollapseKey.value = ['1']
      }
    }

    // 重置导出字段
    const resetExportFields = () => {
      selectedExportFields.value = getPageDefaultExportFields('approval')
      // 确保折叠面板展开
      if (!fieldsCollapseKey.value.includes('1')) {
        fieldsCollapseKey.value = ['1']
      }
    }

    // 仅选择必选字段
    const selectRequiredExportFields = () => {
      selectedExportFields.value = exportFieldOptions
        .filter(field => field.required)
        .map(field => field.value)
      // 确保折叠面板展开
      if (!fieldsCollapseKey.value.includes('1')) {
        fieldsCollapseKey.value = ['1']
      }
    }

    // 预览导出数据
    const previewExportData = () => {
      if (selectedExportRecords.value.length === 0) {
        message.warning('请先选择要导出的记录')
        return
      }
      if (selectedExportFields.value.length === 0) {
        message.warning('请至少选择一个导出字段')
        return
      }

      // 根据选中的记录ID获取完整的记录数据
      const selectedRecordIds = selectedExportRecords.value
      const selectedRecordData = exportRecords.value.filter((record) =>
        selectedRecordIds.includes(record.id)
      )

      // 设置预览数据为选中的完整记录
      previewData.value = selectedRecordData
      previewTotal.value = selectedRecordData.length
      showPreviewModal.value = true
    }

    // 重置导出配置
    const resetExportConfig = () => {
      selectedExportFields.value = getPageDefaultExportFields('approval')
      Object.assign(exportFilters, {
        status: [],
        department: [],
        itemName: '',
        requester: '',
        approver: '',
        minAmount: '',
        maxAmount: '',
        dateRange: [],
        // 重置新增的导出筛选字段
        procurementMethod: '',
        requirementSource: '',
        fundProject: '',
        unit: '',
        itemCategory: '',
        remarks: ''
      })
      exportActiveTab.value = 'fields'
      previewData.value = []
      previewTotal.value = 0
    }

    // 加载预览数据
    const loadPreviewData = async () => {
      if (selectedExportFields.value.length === 0) {
        message.warning('请至少选择一个导出字段')
        return
      }

      previewLoading.value = true
      try {
        const params = {
          page: 1,
          page_size: 10,
          fields: selectedExportFields.value.join(','),
          page_type: 'approval' // 添加页面类型参数
        }

        // 应用导出筛选条件（使用与搜索记录相同的参数格式）
        if (exportFilters.status && exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(',')
        } else {
          // 如果没有指定状态，使用审批页面的默认状态
          params.status_in = 'pending_approval,approved,rejected'
        }

        // 修复部门筛选逻辑，使用后端支持的参数名
        if (exportFilters.department && exportFilters.department.length > 0) {
          params.department_in = exportFilters.department.join(',')
        }

        if (exportFilters.itemName) {
          params.search = exportFilters.itemName
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester
        }
        if (exportFilters.approver) {
          params.approver_id = exportFilters.approver
        }
        if (exportFilters.minAmount) {
          params.budget_total_amount__gte = parseFloat(exportFilters.minAmount)
        }
        if (exportFilters.maxAmount) {
          params.budget_total_amount__lte = parseFloat(exportFilters.maxAmount)
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.created_at__gte = exportFilters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = exportFilters.dateRange[1].format('YYYY-MM-DD')
        }
        // 新增导出筛选参数
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains = exportFilters.procurementMethod
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains = exportFilters.requirementSource
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks
        }

        const response = await api.purchaseRequests.getList(params)
        if (response.code === 200) {
          const rawPreviewData = response.data.results || response.data || []
          // 使用后端提供的display字段进行显示
          const processedPreviewData = rawPreviewData.map(record => ({
            ...record,
            status: record.status_display || record.status,
            item_category: record.item_category_display || record.item_category,
            unit: record.unit_display || record.unit,
            procurement_method: record.procurement_method_display || record.procurement_method,
            fund_project: record.fund_project_display || record.fund_project,
            purchase_type: record.purchase_type_display || record.purchase_type
          }))
          previewData.value = processedPreviewData
          previewTotal.value = response.data.count || previewData.value.length
        }
      } catch (error) {
        console.error('加载预览数据失败:', error)
        message.error('加载预览数据失败')
      } finally {
        previewLoading.value = false
      }
    }

    // 处理导出取消操作
    const handleExportCancel = () => {
      showExportModal.value = false
      // 重置选择
      selectedExportRecords.value = []
      selectedExportRecordData.value = []
      exportRecords.value = []
    }



    // 执行导出
    const executeExport = async () => {
      if (selectedExportRecords.value.length === 0) {
        message.warning('请先选择要导出的记录')
        return
      }
      if (selectedExportFields.value.length === 0) {
        message.warning('请至少选择一个导出字段')
        return
      }

      exportLoading.value = true
      try {
        const params = {
          fields: selectedExportFields.value.join(','),
          ids: selectedExportRecords.value.join(',')
        }

        // 应用导出筛选条件（使用与搜索记录相同的参数格式）
        if (exportFilters.status && exportFilters.status.length > 0) {
          params.status_in = exportFilters.status.join(',')
        }

        // 修复部门筛选逻辑，使用后端支持的参数名
        if (exportFilters.department && exportFilters.department.length > 0) {
          params.department_in = exportFilters.department.join(',')
        }

        if (exportFilters.itemName) {
          params.search = exportFilters.itemName
        }
        if (exportFilters.requester) {
          params.requester__username__icontains = exportFilters.requester
        }
        if (exportFilters.approver) {
          params.approver_id = exportFilters.approver
        }
        if (exportFilters.minAmount) {
          params.budget_total_amount__gte = parseFloat(exportFilters.minAmount)
        }
        if (exportFilters.maxAmount) {
          params.budget_total_amount__lte = parseFloat(exportFilters.maxAmount)
        }
        if (exportFilters.dateRange && exportFilters.dateRange.length === 2) {
          params.created_at__gte = exportFilters.dateRange[0].format('YYYY-MM-DD')
          params.created_at__lte = exportFilters.dateRange[1].format('YYYY-MM-DD')
        }
        // 新增导出筛选参数
        if (exportFilters.procurementMethod) {
          params.procurement_method__icontains = exportFilters.procurementMethod
        }
        if (exportFilters.requirementSource) {
          params.requirement_source__icontains = exportFilters.requirementSource
        }
        if (exportFilters.fundProject) {
          params.fund_project__icontains = exportFilters.fundProject
        }
        if (exportFilters.unit) {
          params.unit__icontains = exportFilters.unit
        }
        if (exportFilters.itemCategory) {
          params.item_category__icontains = exportFilters.itemCategory
        }
        if (exportFilters.remarks) {
          params.remarks__icontains = exportFilters.remarks
        }

        console.log('导出请求参数:', params)
        const response = await api.purchaseRequests.exportToExcel(params)
        console.log('导出响应:', response)
        console.log('响应类型:', typeof response)
        console.log('响应是否为Blob:', response instanceof Blob)

        // 创建下载链接
        // 检查response类型并正确处理
        let blob
        if (response instanceof Blob) {
          blob = response
        } else if (response.data instanceof Blob) {
          blob = response.data
        } else {
          // 如果都不是Blob，尝试创建Blob
          blob = new Blob([response.data || response], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })
        }

        console.log('最终Blob对象:', blob)
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `采购审批清单_${new Date().toLocaleDateString()}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        message.success(`导出成功，共导出 ${selectedExportRecords.value.length} 条记录`)
        handleExportCancel()
      } catch (error) {
        console.error('导出失败详细信息:', error)
        console.error('错误响应:', error.response)
        console.error('请求配置:', error.config)

        let errorMessage = '导出失败'
        if (error.response) {
          errorMessage = `导出失败: ${error.response.status} ${error.response.statusText}`
        } else if (error.request) {
          errorMessage = '导出失败: 网络请求失败'
        } else {
          errorMessage = `导出失败: ${error.message}`
        }
        message.error(errorMessage)
      } finally {
        exportLoading.value = false
      }
    }

    // 查看详情
    const viewDetail = (record) => {
      router.push(`/purchase/requests/${record.id}`)
    }

    // 审批需求
    const approveRequest = (record) => {
      currentRequest.value = record
      approvalForm.comment = ''
      showApprovalModal.value = true
    }

    // 驳回需求
    const rejectRequest = (record) => {
      currentRequest.value = record
      rejectForm.reasons = []
      rejectForm.detail = ''
      showRejectModal.value = true
    }

    // 处理审批
    const handleApproval = async () => {
      approvalLoading.value = true
      try {
        const response = await api.purchaseRequests.approve(currentRequest.value.id, {
          action: 'approve',
          comment: approvalForm.comment
        })
        if (response.code === 200) {
          message.success('审批成功')
          showApprovalModal.value = false
          getPurchaseRequests()
        }
      } catch (error) {
        console.error('审批失败:', error)
        message.error('审批失败')
      } finally {
        approvalLoading.value = false
      }
    }

    // 处理驳回
    const handleReject = async () => {
      if (rejectForm.reasons.length === 0) {
        message.warning('请选择驳回原因')
        return
      }

      if (rejectForm.reasons.includes('其他') && !rejectForm.detail.trim()) {
        message.warning('选择"其他"时请填写详细说明')
        return
      }

      rejectLoading.value = true
      try {
        // 组合驳回原因
        let reason = rejectForm.reasons.join('、')
        if (rejectForm.detail.trim()) {
          reason += `：${rejectForm.detail}`
        }

        // 使用专门的驳回API
        const response = await api.purchaseRequests.reject(currentRequest.value.id, {
          reason: reason
        })
        if (response.code === 200) {
          message.success('驳回成功')
          showRejectModal.value = false
          getPurchaseRequests()
        }
      } catch (error) {
        console.error('驳回失败:', error)
        message.error('驳回失败')
      } finally {
        rejectLoading.value = false
      }
    }

    // 取消审批
    const cancelApproval = () => {
      showApprovalModal.value = false
      currentRequest.value = null
    }

    // 取消驳回
    const cancelReject = () => {
      showRejectModal.value = false
      currentRequest.value = null
    }

    // 批量审批
    const batchApprove = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要审批的需求')
        return
      }

      const pendingRequests = requests.value.filter(item =>
        selectedRowKeys.value.includes(item.id) && (item._originalStatus || item.status) === 'pending_approval'
      )

      if (pendingRequests.length === 0) {
        message.warning('所选项目中没有待审批的需求')
        return
      }

      Modal.confirm({
        title: '批量审批确认',
        content: `确定要批量审批选中的 ${pendingRequests.length} 个需求吗？`,
        okText: "确定",
        cancelText: "取消",
        onOk: async () => {
          try {
            const requestIds = pendingRequests.map(req => req.id)
            const response = await api.purchaseRequests.batchApprove(requestIds, '批量审批通过')
            if (response.code === 200) {
              message.success(`批量审批成功，共审批 ${pendingRequests.length} 个需求`)
              selectedRowKeys.value = []
              getPurchaseRequests()
            }
          } catch (error) {
            console.error('批量审批失败:', error)
            message.error('批量审批失败')
          }
        }
      })
    }





    // 排序状态
    const sortField = ref('')
    const sortOrder = ref('')

    // 表格变化处理 - 支持分页和排序
    const handleTableChange = (pag, filters, sorter) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize

      // 处理排序
      if (sorter && sorter.field) {
        sortField.value = sorter.field
        sortOrder.value = sorter.order
      } else {
        sortField.value = ''
        sortOrder.value = ''
      }

      getPurchaseRequests()
    }

    // 监听导出模态框打开
    watch(() => showExportModal.value, (newVal) => {
      if (newVal) {
        searchExportRecords()
      }
    })

    onMounted(async () => {
      await getDepartments()
      await getFilterOptions()
      await getPurchaseRequests()
    })

    return {
      loading,
      requests,
      selectedRowKeys,
      departments,
      purchaseTypes,
      applicants,
      approvers,
      showFilters,
      showColumnFilter,
      presetDropdownOpen,
      showExportModal,
      filters,
      columnOptions,
      selectedColumns,
      // 导出相关
      exportActiveTab,
      exportLoading,
      previewLoading,
      previewData,
      previewTotal,
      showPreviewModal,
      selectedExportFields,
      exportFieldOptions,
      exportFieldCategories,
      exportFilters,
      exportFieldsIndeterminate,
      exportFieldsCheckAll,
      previewColumns,
      onExportFieldsCheckAllChange,
      onExportFieldsChange,
      resetExportConfig,
      loadPreviewData,
      executeExport,
      resetExportFilters,
      searchExportRecords,
      selectAllExportFields,
      resetExportFields,
      selectRequiredExportFields,
      previewExportData,
      fieldsCollapseKey,
      fieldCategories,
      presetMenuItems,
      approvalForm,
      rejectForm,
      pagination,
      rowSelection,
      filteredColumns,
      showApprovalModal,
      showRejectModal,
      currentRequest,
      pendingCount,
      approvedCount,
      rejectedCount,
      totalCount,
      hasPendingRequests,
      // 方法
      getDepartments,
      getFilterOptions,
      filterOption,
      toggleFilters,
      onColumnChange,
      handleColumnChange,
      handlePresetClick,
      handleSelectAll,
      handleReset,
      selectAllColumns,
      resetColumns,
      applyPreset,
      getPurchaseRequests,
      handleFilterChange,
      debouncedFilterChange,
      resetFilters,
      hasPendingApprovals,
      getStatusColor,
      viewDetail,
      approveRequest,
      rejectRequest,
      handleApproval,
      handleReject,
      cancelApproval,
      cancelReject,
      batchApprove,
      handleTableChange,
      sortField,
      sortOrder,
      // 导出记录选择相关
      exportRecords,
      selectedExportRecords,
      selectedExportRecordData,
      exportSearchLoading,
      exportRecordPageSize,
      exportRecordPagination,
      exportRecordColumns,
      exportRecordRowSelection,
      // 格式化函数
      formatDateToYMD,
      // 导出记录操作函数
      onExportRecordPageSizeChange,
      onExportRecordPageChange,
      handleExportCancel,
      // 权限控制
      permissions
    }
  }
}
</script>

<style scoped>
@import '@/styles/business-panels.css';

.purchase-approval {
  padding: 0px;
}

/* 表格头部flex布局 - 与采购总览页面保持一致 */
.table-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-light);
  gap: 24px;
}

.table-title-section {
  flex-shrink: 0;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: 13px;
  font-weight: 400;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

/* 移除响应式布局，保持固定布局 */

/* 表格标题区域样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
}

.table-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-title h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.record-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.table-filters {
  margin-top: 8px;
  padding: 12px 0;
  border-top: 1px solid var(--border-light);
}

.table-actions {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

/* 详细筛选控件样式 */
.detailed-filters {
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.filter-select,
.filter-input,
.filter-date-picker {
  height: 32px;
  font-size: var(--text-sm);
}

.budget-range-input {
  width: 200px;
}

.budget-range-input .ant-input {
  height: 32px;
  font-size: var(--text-sm);
}



/* 紧凑型操作按钮样式 */
.compact-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-section {
  margin: 24px 0;
  padding: 24px;
}

.action-buttons-section {
  margin: 24px 0;
  text-align: center;
}

.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  color: white;
}

/* 统一筛选区域样式 */
.unified-filter-section {
  padding: var(--space-lg) var(--space-xl);
  margin-bottom: var(--space-lg);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
}

.unified-filter-section .filter-select,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker {
  width: 100%;
}

.unified-filter-section .filter-select .ant-select-selector,
.unified-filter-section .filter-input,
.unified-filter-section .date-picker .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-filter-section .filter-select .ant-select-focused .ant-select-selector,
.unified-filter-section .filter-input:focus,
.unified-filter-section .date-picker .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-filter-section .filter-select .ant-select-selector:hover,
.unified-filter-section .filter-input:hover,
.unified-filter-section .date-picker .ant-picker:hover {
  border-color: var(--primary-light);
}

/* 次要操作按钮样式 */
.secondary-action-btn {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 48px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 140px;
}

.secondary-action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
  transform: translateY(-1px);
}

.danger-action-btn {
  background: #ff4d4f;
  border: none;
  color: white;
}



/* 筛选区按钮高度对齐 */
.unified-filter-section .secondary-action-btn {
  height: 32px;
  line-height: 30px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-filter-section .secondary-action-btn:hover {
  border-color: var(--primary-light);
}

/* 对话框样式 */
:deep(.approval-modal .ant-modal-header),
:deep(.reject-modal .ant-modal-header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  border-radius: 8px 8px 0 0;
}

:deep(.approval-modal .ant-modal-title),
:deep(.reject-modal .ant-modal-title) {
  color: white;
  font-weight: 600;
}

.modal-title {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.modal-content {
  padding: 20px 0;
}

.request-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.reject-reasons {
  width: 100%;
}

.reject-reasons .ant-checkbox-wrapper {
  margin-bottom: 12px;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.3s;
  width: 100%;
  display: flex;
  align-items: center;
}

.reject-reasons .ant-checkbox-wrapper:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.reject-reasons .ant-checkbox-wrapper-checked {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-modal-footer) {
  border-top: none;
  padding: 0;
}

.field-category-section {
  margin-bottom: 12px;
}

.category-title {
  margin: 0 0 6px 0;
  font-size: 13px;
  font-weight: 600;
  color: #666;
}

.category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.column-option {
  display: flex;
  align-items: center;
}

.column-title {
  font-size: 12px;
}

/* 导出字段选择样式 - 与字段筛选保持一致 */
.export-field-category {
  margin-bottom: 12px;
}

.export-category-title {
  margin: 0 0 6px 0;
  font-weight: 600;
  font-size: 13px;
  color: #666;
}

.export-category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.export-field-option {
  display: flex;
  align-items: center;
}

/* 表格标题区域样式 */
.table-header {
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  width: 100%;
  box-sizing: border-box;
}

.table-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-title span:first-child {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: normal;
}

.table-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

/* 导出配置样式 */
.export-config-container {
  padding: 0;
}

.filter-section {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: none;
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.filter-section.compact {
  padding: 8px 12px;
  margin-bottom: 12px;
}

.filter-section.compact h4 {
  margin-bottom: 6px;
  font-size: 13px;
}

.filter-section.compact .ant-form-item {
  margin-bottom: 6px;
}

.fields-section {
  margin-bottom: 16px;
}

.export-field-category {
  margin-bottom: 16px;
}

.export-category-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #595959;
}

.export-category-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 6px;
}

.export-field-option {
  padding: 4px 0;
}

/* 导出记录表格样式 */
.export-records-table {
  .ant-table-tbody>tr>td {
    text-align: center;
  }

  .ant-table-thead>tr>th {
    text-align: center;
  }
}

/* 记录选择部分样式 */
.records-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-count {
  font-size: 13px;
  color: #666;
}

.actions-section {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.table-container {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 修复操作列透明背景问题 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5 !important;
}

/* 导出字段配置样式 */
.export-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
}

.export-field-category {
  flex: 1;
  min-width: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  overflow: hidden;
}

.export-category-header {
  background-color: #e6f7ff;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 12px;
}

.export-category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
  text-align: center;
}

.export-category-fields {
  padding: 12px;
  background-color: #ffffff;
}

.export-field-option {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.export-field-option:last-child {
  margin-bottom: 0;
}

.field-title {
  flex: 1;
  margin-right: 8px;
}

/* 必选字段样式 */
.export-field-option :deep(.ant-checkbox-disabled) {
  opacity: 0.8;
}

.export-field-option :deep(.ant-checkbox-disabled + .field-title) {
  color: #1890ff;
  font-weight: 500;
}

:deep(.ant-table-tbody > tr:hover > td.ant-table-cell-fix-right) {
  background-color: #f5f5f5 !important;
}

/* 预览模态框样式 */
:deep(.preview-modal-wrapper .ant-modal) {
  height: 80vh;
  top: 10vh;
  margin: 0;
}

:deep(.preview-modal-wrapper .ant-modal-content) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.preview-modal-wrapper .ant-modal-body) {
  flex: 1;
  overflow: hidden;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.preview-container .ant-table-wrapper {
  flex: 1;
  overflow: auto;
}
</style>
