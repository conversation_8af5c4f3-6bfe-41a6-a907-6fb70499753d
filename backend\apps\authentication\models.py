from django.contrib.auth.models import AbstractUser
from django.db import models


class User(AbstractUser):
    """
    自定义用户模型
    对应数据库表：sys_user
    """

    # 基础字段继承自AbstractUser: username, password, email, first_name, last_name, is_active, date_joined

    real_name = models.CharField('真实姓名', max_length=50, blank=True)
    phone = models.CharField('联系电话', max_length=20, blank=True)
    dept_id = models.IntegerField('所属部门ID', null=True, blank=True, help_text='关联部门表sys_department')
    role = models.CharField(
        '角色',
        max_length=20,
        blank=True,
        help_text='用户角色代码，如admin/approver/acceptor/finance等'
    )

    # 注意：已移除Django内置的groups和user_permissions字段
    # 现在统一使用自定义权限系统 (sys_permission, sys_role_permission, sys_user_permission)

    class Meta:
        db_table = 'sys_user'
        verbose_name = '用户'
        verbose_name_plural = '用户管理'
        default_permissions = ()  # 禁用Django自动权限创建
        
    def __str__(self):
        return f"{self.real_name or self.username}"
    
    @property
    def dept_hierarchy_path(self):
        """获取部门层级路径（如分公司-办事处）"""
        if self.dept_id:
            from apps.system.models import Department
            try:
                dept = Department.objects.get(id=self.dept_id)
                return dept.hierarchy_path
            except Department.DoesNotExist:
                return ""
        return ""

    @property
    def role_name(self):
        """获取用户角色名称 - 简化版，直接使用role字段"""
        if not self.role:
            return '普通用户'

        try:
            from apps.roles.models import Role
            role_obj = Role.objects.filter(code=self.role, is_active=True).first()
            if role_obj:
                return role_obj.name
        except ImportError:
            pass

        return self.role

    @property
    def role_code(self):
        """获取用户角色代码"""
        return self.role

    def has_role(self, role_code):
        """检查用户是否有指定角色"""
        return self.role == role_code

    def set_role(self, role_code):
        """设置用户角色"""
        self.role = role_code
        self.save(update_fields=['role'])
