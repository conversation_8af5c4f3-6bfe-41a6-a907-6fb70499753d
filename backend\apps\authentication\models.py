from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.contrib.auth.validators import UnicodeUsernameValidator
from django.db import models
from django.utils import timezone


class UserManager(BaseUserManager):
    """自定义用户管理器"""

    def create_user(self, username, email=None, password=None, **extra_fields):
        """创建普通用户"""
        if not username:
            raise ValueError('用户名不能为空')

        email = self.normalize_email(email) if email else ''
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, email=None, password=None, **extra_fields):
        """创建超级用户"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须设置is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须设置is_superuser=True')

        return self.create_user(username, email, password, **extra_fields)


class User(AbstractBaseUser):
    """
    自定义用户模型 - 基于AbstractBaseUser，完全自定义权限系统
    对应数据库表：sys_user
    """

    # 基础认证字段
    username_validator = UnicodeUsernameValidator()
    username = models.CharField(
        '用户名',
        max_length=150,
        unique=True,
        help_text='必填。150个字符以内。只能包含字母、数字和@/./+/-/_字符。',
        validators=[username_validator],
        error_messages={
            'unique': "该用户名已存在。",
        },
    )
    email = models.EmailField('邮箱地址', blank=True)
    first_name = models.CharField('名字', max_length=150, blank=True)
    last_name = models.CharField('姓氏', max_length=150, blank=True)

    # 状态字段
    is_staff = models.BooleanField(
        '管理员状态',
        default=False,
        help_text='指定用户是否可以登录管理站点。',
    )
    is_active = models.BooleanField(
        '激活状态',
        default=True,
        help_text='指定是否应将此用户视为活动用户。取消选择此项而不是删除帐户。',
    )
    is_superuser = models.BooleanField(
        '超级用户状态',
        default=False,
        help_text='指定该用户拥有所有权限而无需显式分配。',
    )
    date_joined = models.DateTimeField('注册时间', default=timezone.now)

    # 扩展字段
    real_name = models.CharField('真实姓名', max_length=50, blank=True)
    phone = models.CharField('联系电话', max_length=20, blank=True)
    dept_id = models.IntegerField('所属部门ID', null=True, blank=True, help_text='关联部门表sys_department')
    role = models.CharField(
        '角色',
        max_length=20,
        blank=True,
        help_text='用户角色代码，如admin/approver/acceptor/finance等'
    )

    # 指定用户名字段和管理器
    USERNAME_FIELD = 'username'
    EMAIL_FIELD = 'email'
    REQUIRED_FIELDS = ['email']

    objects = UserManager()

    class Meta:
        db_table = 'sys_user'
        verbose_name = '用户'
        verbose_name_plural = '用户管理'
        default_permissions = ()  # 禁用Django自动权限创建
        
    def __str__(self):
        return f"{self.real_name or self.username}"
    
    @property
    def dept_hierarchy_path(self):
        """获取部门层级路径（如分公司-办事处）"""
        if self.dept_id:
            from apps.system.models import Department
            try:
                dept = Department.objects.get(id=self.dept_id)
                return dept.hierarchy_path
            except Department.DoesNotExist:
                return ""
        return ""

    @property
    def role_name(self):
        """获取用户角色名称 - 简化版，直接使用role字段"""
        if not self.role:
            return '普通用户'

        try:
            from apps.roles.models import Role
            role_obj = Role.objects.filter(code=self.role, is_active=True).first()
            if role_obj:
                return role_obj.name
        except ImportError:
            pass

        return self.role

    @property
    def role_code(self):
        """获取用户角色代码"""
        return self.role

    def has_role(self, role_code):
        """检查用户是否有指定角色"""
        return self.role == role_code

    # 重写PermissionsMixin的方法，使用自定义权限系统
    def get_user_permissions(self, obj=None):
        """获取用户权限 - 重写以使用自定义权限系统"""
        from apps.system.permissions import get_user_permissions
        return set(get_user_permissions(self))

    def get_group_permissions(self, obj=None):
        """获取组权限 - 返回空集合，因为不使用Django组"""
        return set()

    def get_all_permissions(self, obj=None):
        """获取所有权限 - 使用自定义权限系统"""
        return self.get_user_permissions(obj)

    def has_perm(self, perm, obj=None):
        """检查权限 - 使用自定义权限系统"""
        if self.is_active and self.is_superuser:
            return True
        from apps.system.permissions import has_permission
        return has_permission(self, perm)

    def has_perms(self, perm_list, obj=None):
        """检查多个权限"""
        return all(self.has_perm(perm, obj) for perm in perm_list)

    def has_module_perms(self, app_label):
        """检查模块权限 - 超级用户有所有权限"""
        if self.is_active and self.is_superuser:
            return True
        # 对于普通用户，检查是否有该模块的任何权限
        from apps.system.permissions import get_user_permissions
        user_perms = get_user_permissions(self)
        return any(perm.startswith(f'{app_label}:') for perm in user_perms)

    def set_role(self, role_code):
        """设置用户角色"""
        self.role = role_code
        self.save(update_fields=['role'])
