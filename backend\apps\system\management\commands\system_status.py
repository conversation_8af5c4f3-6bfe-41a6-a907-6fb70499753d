"""
系统状态检查命令 - 优化终端操作性能
"""
from django.core.management.base import BaseCommand
from apps.system.models import Menu
from apps.system.models_permission import Permission
from apps.roles.models import Role
from apps.authentication.models import User
import json


class Command(BaseCommand):
    help = '快速检查系统状态'

    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            choices=['text', 'json'],
            default='text',
            help='输出格式'
        )
        parser.add_argument(
            '--check',
            choices=['all', 'data', 'permissions', 'services'],
            default='all',
            help='检查类型'
        )

    def handle(self, *args, **options):
        format_type = options['format']
        check_type = options['check']
        
        results = {}
        
        if check_type in ['all', 'data']:
            results['data'] = self.check_data_status()
        
        if check_type in ['all', 'permissions']:
            results['permissions'] = self.check_permissions_status()
            
        if check_type in ['all', 'services']:
            results['services'] = self.check_services_status()
        
        if format_type == 'json':
            self.stdout.write(json.dumps(results, ensure_ascii=False, indent=2))
        else:
            self.output_text_format(results)

    def check_data_status(self):
        """检查数据状态"""
        return {
            'menus': {
                'total': Menu.objects.count(),
                'directories': Menu.objects.filter(menu_type='directory').count(),
                'pages': Menu.objects.filter(menu_type='page').count(),
                'buttons': Menu.objects.filter(menu_type='button').count(),
                'active': Menu.objects.filter(is_active=True).count()
            },
            'permissions': {
                'total': Permission.objects.count(),
                'page_permissions': Permission.objects.filter(category='page').count(),
                'button_permissions': Permission.objects.filter(category='button').count()
            },
            'roles': {
                'total': Role.objects.count(),
                'active': Role.objects.filter(is_active=True).count()
            },
            'users': {
                'total': User.objects.count(),
                'active': User.objects.filter(is_active=True).count(),
                'superusers': User.objects.filter(is_superuser=True).count()
            }
        }

    def check_permissions_status(self):
        """检查权限状态"""
        # 检查权限一致性
        menu_codes = set(Menu.objects.filter(
            permission_code__isnull=False
        ).values_list('permission_code', flat=True))
        
        perm_codes = set(Permission.objects.values_list('code', flat=True))
        
        # 检查需求提报页面
        request_page = Menu.objects.filter(
            menu_name='需求提报', 
            menu_type='page'
        ).first()
        
        request_info = None
        if request_page:
            child_buttons = Menu.objects.filter(parent=request_page, menu_type='button')
            related_perms = Permission.objects.filter(code__startswith='purchase:request:')
            
            request_info = {
                'page_code': request_page.permission_code,
                'child_buttons': child_buttons.count(),
                'related_permissions': related_perms.count(),
                'consistency': child_buttons.count() + 1 == related_perms.count()
            }
        
        return {
            'menu_permission_codes': len(menu_codes),
            'permission_codes': len(perm_codes),
            'orphan_permissions': len(perm_codes - menu_codes),
            'orphan_menus': len(menu_codes - perm_codes),
            'request_page_status': request_info,
            'consistency_check': len(menu_codes & perm_codes) == len(menu_codes)
        }

    def check_services_status(self):
        """检查服务状态"""
        import socket
        
        def check_port(host, port):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                sock.close()
                return result == 0
            except:
                return False
        
        return {
            'backend_service': check_port('localhost', 8000),
            'frontend_service': check_port('localhost', 8080),
            'database_connection': self.check_database_connection()
        }

    def check_database_connection(self):
        """检查数据库连接"""
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return True
        except:
            return False

    def output_text_format(self, results):
        """文本格式输出"""
        self.stdout.write("🔍 系统状态检查报告")
        self.stdout.write("=" * 50)
        
        if 'data' in results:
            data = results['data']
            self.stdout.write("\n📊 数据状态:")
            self.stdout.write(f"  菜单: {data['menus']['total']} (目录:{data['menus']['directories']}, 页面:{data['menus']['pages']}, 按钮:{data['menus']['buttons']})")
            self.stdout.write(f"  权限: {data['permissions']['total']} (页面:{data['permissions']['page_permissions']}, 按钮:{data['permissions']['button_permissions']})")
            self.stdout.write(f"  角色: {data['roles']['total']} (活跃:{data['roles']['active']})")
            self.stdout.write(f"  用户: {data['users']['total']} (活跃:{data['users']['active']}, 管理员:{data['users']['superusers']})")
        
        if 'permissions' in results:
            perms = results['permissions']
            self.stdout.write("\n🔐 权限状态:")
            self.stdout.write(f"  菜单权限代码: {perms['menu_permission_codes']}")
            self.stdout.write(f"  权限代码: {perms['permission_codes']}")
            self.stdout.write(f"  孤立权限: {perms['orphan_permissions']}")
            self.stdout.write(f"  孤立菜单: {perms['orphan_menus']}")
            
            if perms['request_page_status']:
                req = perms['request_page_status']
                status = "✅" if req['consistency'] else "❌"
                self.stdout.write(f"  需求提报页面: {status} ({req['child_buttons']}个按钮, {req['related_permissions']}个权限)")
        
        if 'services' in results:
            services = results['services']
            self.stdout.write("\n🚀 服务状态:")
            self.stdout.write(f"  后端服务 (8000): {'✅' if services['backend_service'] else '❌'}")
            self.stdout.write(f"  前端服务 (8080): {'✅' if services['frontend_service'] else '❌'}")
            self.stdout.write(f"  数据库连接: {'✅' if services['database_connection'] else '❌'}")
        
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("✅ 检查完成")
