/**
 * 权限路由管理模块
 * 解决动态路由刷新后丢失的问题
 */

import dynamicRouterManager from '@/router/dynamicRouter'

const state = {
  // 完整的路由列表（静态路由 + 动态路由）
  routes: [],
  // 动态路由是否已添加标记
  hasRoutes: false,
  // 用户菜单数据
  userMenus: [],
  // 路由添加状态
  routeAddStatus: 'idle', // idle, loading, success, error
  // 持久化状态
  persistenceStatus: 'none' // none, restored, saved
}

const mutations = {
  /**
   * 设置完整路由列表
   */
  SET_ROUTES(state, routes) {
    state.routes = routes
  },

  /**
   * 设置动态路由添加状态
   */
  SET_HAS_ROUTES(state, flag) {
    state.hasRoutes = flag
    // 保存到本地存储
    try {
      localStorage.setItem('has_routes', flag.toString())
    } catch (error) {
      // 静默处理存储错误
    }
  },

  /**
   * 设置用户菜单数据
   */
  SET_USER_MENUS(state, menus) {
    state.userMenus = menus
    // 保存到本地存储
    try {
      localStorage.setItem('permission_menus', JSON.stringify(menus))
    } catch (error) {
      // 静默处理存储错误
    }
  },

  /**
   * 设置路由添加状态
   */
  SET_ROUTE_ADD_STATUS(state, status) {
    state.routeAddStatus = status
  },

  /**
   * 设置持久化状态
   */
  SET_PERSISTENCE_STATUS(state, status) {
    state.persistenceStatus = status
  },

  /**
   * 重置权限状态
   */
  RESET_PERMISSION(state) {
    state.routes = []
    state.hasRoutes = false
    state.userMenus = []
    state.routeAddStatus = 'idle'
    state.persistenceStatus = 'none'

    // 清除本地存储
    try {
      localStorage.removeItem('has_routes')
      localStorage.removeItem('permission_menus')
      localStorage.removeItem('user_menus')
    } catch (error) {
      // 静默处理清除错误
    }
  }
}

const actions = {
  /**
   * 从本地存储恢复状态
   */
  restoreFromStorage({ commit }) {
    try {
      // 恢复菜单数据
      const storedMenus = localStorage.getItem('permission_menus')
      if (storedMenus) {
        const menus = JSON.parse(storedMenus)
        if (menus && menus.length > 0) {
          commit('SET_USER_MENUS', menus)
          commit('SET_PERSISTENCE_STATUS', 'restored')

          // 恢复路由状态
          const hasRoutes = localStorage.getItem('has_routes')
          if (hasRoutes === 'true') {
            commit('SET_HAS_ROUTES', true)
          }

          return menus
        }
      }

      commit('SET_PERSISTENCE_STATUS', 'none')
    } catch (error) {
      // 恢复权限数据失败：清除损坏的数据
      localStorage.removeItem('has_routes')
      localStorage.removeItem('permission_menus')
      localStorage.removeItem('user_menus')
      commit('SET_PERSISTENCE_STATUS', 'none')
    }
    return null
  },

  /**
   * 从本地存储恢复路由（改进版）
   */
  async restoreRoutes({ commit }, router) {
    try {
      // 检查是否有缓存的菜单数据
      const storedMenus = localStorage.getItem('permission_menus')
      const hasRoutes = localStorage.getItem('has_routes')

      if (storedMenus && hasRoutes === 'true') {
        const menus = JSON.parse(storedMenus)
        if (menus && menus.length > 0) {
          // 恢复菜单数据到store
          commit('SET_USER_MENUS', menus)

          // 重新添加动态路由
          const success = await dynamicRouterManager.addDynamicRoutes(router, menus)

          if (success) {
            commit('SET_HAS_ROUTES', true)
            commit('SET_ROUTE_ADD_STATUS', 'success')
            commit('SET_PERSISTENCE_STATUS', 'restored')
            // 从本地存储恢复路由成功
            return true
          }
        }
      }

      return false
    } catch (error) {
      console.warn('恢复路由失败:', error)
      // 清除损坏的数据
      localStorage.removeItem('has_routes')
      localStorage.removeItem('permission_menus')
      commit('SET_PERSISTENCE_STATUS', 'none')
      return false
    }
  },

  /**
   * 获取并添加动态路由
   */
  async generateRoutes({ commit, state }, router) {
    try {
      commit('SET_ROUTE_ADD_STATUS', 'loading')

      // 检查用户是否已登录
      const token = localStorage.getItem('token')
      if (!token) {
        commit('SET_ROUTE_ADD_STATUS', 'error')
        return false
      }

      // 如果已经添加过路由，直接返回成功
      if (state.hasRoutes) {
        commit('SET_ROUTE_ADD_STATUS', 'success')
        return true
      }

      // 检查是否有缓存的菜单数据
      if (state.userMenus && state.userMenus.length > 0) {
        const success = await dynamicRouterManager.initDynamicRoutes(router)
        if (success) {
          commit('SET_HAS_ROUTES', true)
          commit('SET_ROUTE_ADD_STATUS', 'success')
          commit('SET_PERSISTENCE_STATUS', 'restored')
          return true
        }
      }

      // 从服务器初始化动态路由
      const success = await dynamicRouterManager.initDynamicRoutes(router)

      if (success) {
        // 获取用户菜单数据
        const userMenus = await dynamicRouterManager.getUserMenus()
        commit('SET_USER_MENUS', userMenus)

        // 标记路由已添加
        commit('SET_HAS_ROUTES', true)
        commit('SET_ROUTE_ADD_STATUS', 'success')
        commit('SET_PERSISTENCE_STATUS', 'saved')

        return true
      } else {
        commit('SET_ROUTE_ADD_STATUS', 'error')
        commit('SET_PERSISTENCE_STATUS', 'none')
        return false
      }
    } catch (error) {
      commit('SET_ROUTE_ADD_STATUS', 'error')
      return false
    }
  },



  /**
   * 清除动态路由
   */
  clearRoutes({ commit }, router) {
    // 清除路由管理器中的路由
    if (router) {
      dynamicRouterManager.clearDynamicRoutes(router)
    }

    // 重置状态
    commit('RESET_PERMISSION')
  },

  /**
   * 重新加载动态路由
   */
  async reloadRoutes({ dispatch }, router) {
    // 先清除现有路由
    await dispatch('clearRoutes', router)

    // 重新生成路由
    return await dispatch('generateRoutes', router)
  },

  /**
   * 检查路由是否需要重新生成
   */
  checkRouteStatus({ state }) {
    const token = localStorage.getItem('token')
    
    // 如果未登录，路由状态应该被清除
    if (!token && state.hasRoutes) {
      return 'need_clear'
    }
    
    // 如果已登录但路由未添加，需要生成路由
    if (token && !state.hasRoutes) {
      return 'need_generate'
    }
    
    // 路由状态正常
    return 'normal'
  }
}

const getters = {
  /**
   * 获取完整路由列表
   */
  routes: state => state.routes,

  /**
   * 获取动态路由添加状态
   */
  hasRoutes: state => state.hasRoutes,

  /**
   * 获取用户菜单
   */
  userMenus: state => state.userMenus,

  /**
   * 获取路由添加状态
   */
  routeAddStatus: state => state.routeAddStatus,

  /**
   * 检查是否正在加载路由
   */
  isLoadingRoutes: state => state.routeAddStatus === 'loading',

  /**
   * 检查路由是否加载成功
   */
  isRoutesLoaded: state => state.routeAddStatus === 'success' && state.hasRoutes,

  /**
   * 获取持久化状态
   */
  persistenceStatus: state => state.persistenceStatus
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
