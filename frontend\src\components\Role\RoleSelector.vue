<template>
  <a-select
    v-model:value="selectedRole"
    placeholder="请选择角色"
    :loading="loading"
    :size="size"
    :style="style"
    :allow-clear="allowClear"
    @change="handleChange"
  >
    <a-select-option
      v-for="role in roles"
      :key="role.key"
      :value="role.key"
    >
      <div class="role-option">
        <span class="role-name">{{ role.name }}</span>
        <span class="role-key">{{ role.key }}</span>
      </div>
    </a-select-option>
  </a-select>
</template>

<!-- eslint-disable vue/no-unused-vars -->
<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import api from '@/api';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'middle'
  },
  style: {
    type: [String, Object],
    default: () => ({})
  },
  allowClear: {
    type: <PERSON>olean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 状态
const loading = ref(false);
const roles = ref([]);

// 双向绑定
const selectedRole = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 获取角色列表
const getRoles = async () => {
  try {
    loading.value = true;
    // 优先使用角色管理系统的简单列表API
    let response;
    try {
      response = await api.roles.getSimpleList();
    } catch (error) {
      // 如果角色管理系统不可用，回退到认证系统的角色API
      console.warn('角色管理系统不可用，使用认证系统角色API:', error);
      response = await api.auth.getRoles();
    }

    if (response.code === 200) {
      const roleData = response.data || [];
      // 统一角色数据格式
      roles.value = roleData.map(role => ({
        key: role.code || role.key,
        name: role.name,
        id: role.id,
        description: role.description
      }));
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
    message.error('获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理选择变化
const handleChange = (value) => {
  const selectedRoleData = roles.value.find(role => role.key === value);
  emit('change', value, selectedRoleData);
};

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== selectedRole.value) {
    selectedRole.value = newValue;
  }
});

onMounted(() => {
  getRoles();
});
</script>

<style scoped>
.role-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.role-name {
  font-weight: 500;
  color: var(--text-primary);
}

.role-key {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}
</style>
