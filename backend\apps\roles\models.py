from django.db import models


class Role(models.Model):
    """角色模型"""
    name = models.CharField('角色名称', max_length=50, unique=True)
    code = models.CharField('角色编码', max_length=50, unique=True)
    description = models.TextField('角色描述', blank=True, null=True)
    # 注意：权限关系通过 RolePermission 中间表管理，不在这里定义 ManyToManyField
    is_active = models.BooleanField('是否启用', default=True)
    is_system = models.BooleanField('是否系统角色', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'sys_role'
        verbose_name = '角色'
        verbose_name_plural = '角色'
        default_permissions = ()  # 禁用Django自动权限创建
        ordering = ['created_at']

    def __str__(self):
        return self.name


# UserRole模型已删除 - 改用User.role字段
