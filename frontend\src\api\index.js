/**
 * API服务定义
 *
 * 功能说明：
 * 1. 统一管理所有后端API接口调用
 * 2. 按页面功能组织API请求
 * 3. 提供缓存机制优化性能
 * 4. 支持文件上传下载等特殊请求类型
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-01-22
 */
import { apiClient, buildUrlWithParams } from './config'

// ==================== 请求缓存配置 ====================
const requestCache = new Map()
const cacheTimeout = 5 * 60 * 1000 // 5分钟缓存



// 生成缓存键
function generateCacheKey(url, params) {
  return `${url}?${JSON.stringify(params || {})}`
}

// 获取缓存
function getCache(key) {
  const cached = requestCache.get(key)
  if (cached && Date.now() - cached.timestamp < cacheTimeout) {
    return cached.data
  }
  requestCache.delete(key)
  return null
}

// 设置缓存
function setCache(key, data) {
  requestCache.set(key, {
    data,
    timestamp: Date.now()
  })
}

// 注意：axios实例和拦截器配置已移至 ./config.js

// 创建通用CRUD服务的工厂函数
const createCrudService = (resourceName) => ({
  getList(params = {}) {
    return apiClient.get(buildUrlWithParams(`/${resourceName}`, params));
  },
  getDetail(id) {
    return apiClient.get(`/${resourceName}/${id}/`);
  },
  create(data) {
    return apiClient.post(`/${resourceName}/`, data);
  },
  update(id, data) {
    return apiClient.put(`/${resourceName}/${id}/`, data);
  },
  delete(id) {
    return apiClient.delete(`/${resourceName}/${id}/`);
  }
});

// ========== API服务定义 ==========

const api = {
  // ========== 公共请求（认证、用户信息、字典数据等） ==========

  // 认证相关API - 登录页面使用
  auth: {
    /**
     * 用户登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise} 登录结果，包含token和用户信息
     */
    login(username, password) {
      return apiClient.post('/auth/login/', {
        username,
        password
      });
    },

    /**
     * 获取当前用户信息
     * @returns {Promise} 用户详细信息
     */
    getCurrentUser() {
      return apiClient.get('/auth/user/');
    },

    /**
     * 用户登出
     * @param {string} refreshToken - 刷新令牌
     * @returns {Promise} 登出结果
     */
    logout(refreshToken) {
      return apiClient.post('/auth/logout/', {
        refresh: refreshToken
      });
    },

    /**
     * 修改密码
     * @param {string} oldPassword - 旧密码
     * @param {string} newPassword - 新密码
     * @returns {Promise} 修改结果
     */
    changePassword(oldPassword, newPassword) {
      return apiClient.post('/auth/change-password/', {
        old_password: oldPassword,
        new_password: newPassword
      });
    },

    /**
     * 获取用户权限列表
     * @returns {Promise} 权限列表
     */
    getUserPermissions() {
      return apiClient.get('/auth/permissions/');
    },

    /**
     * 获取角色列表
     * @returns {Promise} 角色列表
     */
    getRoles() {
      return apiClient.get('/auth/roles/');
    },
    // 检查权限
    checkPermission(permission, pagePath) {
      return apiClient.post('/auth/check-permission/', {
        permission,
        page_path: pagePath
      });
    }
  },

  // ========== 采购需求管理 ==========

  // 采购需求相关API - 需求提报页面、需求列表页面使用
  purchaseRequests: {
    /**
     * 获取采购需求列表
     * @param {Object} params - 查询参数（分页、筛选条件等）
     * @returns {Promise} 需求列表数据
     * @usage 需求提报列表页面 (/purchase/requests)
     */
    getList(params = {}) {
      return apiClient.get(buildUrlWithParams('/requests', params));
    },

    /**
     * 获取采购需求详情
     * @param {number} id - 需求ID
     * @returns {Promise} 需求详细信息
     * @usage 需求详情页面 (/purchase/requests/:id)
     */
    getDetail(id) {
      return apiClient.get(`/requests/${id}/`);
    },

    /**
     * 创建新的采购需求
     * @param {Object} data - 需求数据
     * @returns {Promise} 创建结果
     * @usage 新建需求页面 (/purchase/requests/new)
     */
    create(data) {
      return apiClient.post('/requests/', data);
    },

    /**
     * 更新采购需求
     * @param {number} id - 需求ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     * @usage 编辑需求页面 (/purchase/requests/:id/edit)
     */
    update(id, data) {
      return apiClient.put(`/requests/${id}/`, data);
    },

    /**
     * 删除采购需求
     * @param {number} id - 需求ID
     * @returns {Promise} 删除结果
     * @usage 需求列表页面的删除操作
     */
    delete(id) {
      return apiClient.delete(`/requests/${id}/`);
    },

    /**
     * 提交采购需求
     * @param {number} id - 需求ID
     * @returns {Promise} 提交结果
     * @usage 需求列表页面、详情页面的提交操作
     */
    submit(id) {
      return apiClient.post(`/requests/${id}/submit/`);
    },

    /**
     * 批量提交采购需求
     * @param {Array} requestIds - 需求ID数组
     * @returns {Promise} 批量提交结果
     * @usage 需求列表页面的批量操作
     */
    batchSubmit(requestIds) {
      return apiClient.post('/requests/batch_submit/', {
        request_ids: requestIds
      });
    },

    // --- 需求审核相关API (需求审核页面使用) ---

    /**
     * 审批通过采购需求
     * @param {number} id - 需求ID
     * @param {Object} data - 审批数据（审批意见等）
     * @returns {Promise} 审批结果
     * @usage 需求审核页面 (/purchase/approval)
     */
    approve(id, data) {
      return apiClient.post(`/requests/${id}/approve/`, data);
    },

    /**
     * 拒绝采购需求
     * @param {number} id - 需求ID
     * @param {Object} data - 拒绝原因等
     * @returns {Promise} 拒绝结果
     * @usage 需求审核页面 (/purchase/approval)
     */
    reject(id, data) {
      return apiClient.post(`/requests/${id}/reject/`, data);
    },

    /**
     * 退回采购需求
     * @param {number} id - 需求ID
     * @param {Object} data - 退回原因等
     * @returns {Promise} 退回结果
     * @usage 需求审核页面 (/purchase/approval)
     */
    return(id, data) {
      return apiClient.post(`/requests/${id}/return/`, data);
    },

    // --- 物资采购相关API (物资采购页面使用) ---

    /**
     * 批量申请采购
     * @param {Array} requestIds - 需求ID数组
     * @returns {Promise} 申请结果
     * @usage 物资采购页面 (/purchase/procurement)
     */
    batchApplyPurchase(requestIds) {
      return apiClient.post('/requests/batch_apply_purchase/', {
        request_ids: requestIds
      });
    },

    /**
     * 执行采购操作
     * @param {number} id - 需求ID
     * @param {Object} data - 采购数据
     * @returns {Promise} 采购结果
     * @usage 物资采购页面 (/purchase/procurement)
     */
    purchase(id, data) {
      return apiClient.post(`/requests/${id}/purchase/`, data);
    },

    /**
     * 批量采购
     * @param {Array} requestIds - 需求ID数组
     * @returns {Promise} 批量采购结果
     * @usage 物资采购页面 (/purchase/procurement)
     */
    batchPurchase(requestIds) {
      return apiClient.post('/requests/batch_purchase/', {
        request_ids: requestIds
      });
    },

    // --- 物资验收相关API (物资验收页面使用) ---

    /**
     * 验收采购需求
     * @param {number} id - 需求ID
     * @returns {Promise} 验收结果
     * @usage 物资验收页面 (/purchase/acceptance)
     */
    accept(id) {
      return apiClient.post(`/requests/${id}/accept/`);
    },

    /**
     * 财务退回到验收环节
     * @param {number} id - 需求ID
     * @param {Object} data - 退回原因等
     * @returns {Promise} 退回结果
     * @usage 物资验收页面 (/purchase/acceptance)
     */
    financeReturnToAcceptance(id, data) {
      return apiClient.post(`/requests/${id}/finance_return_to_acceptance/`, data);
    },

    // --- 结算报销相关API (结算报销页面使用) ---

    /**
     * 报销采购需求
     * @param {number} id - 需求ID
     * @returns {Promise} 报销结果
     * @usage 结算报销页面 (/purchase/reimbursement)
     */
    reimburse(id) {
      return apiClient.post(`/requests/${id}/reimburse/`);
    },

    // --- 文档生成和打印相关API ---

    /**
     * 生成采购订单
     * @param {number} departmentId - 部门ID
     * @returns {Promise} 订单生成结果
     * @usage 采购总览页面 (/purchase/overview)
     */
    generateOrder(departmentId) {
      return apiClient.post('/requests/generate_order/', {
        department_id: departmentId
      });
    },

    /**
     * 生成单个采购订单
     * @param {number} id - 需求ID
     * @returns {Promise} 订单生成结果
     * @usage 需求详情页面、采购页面
     */
    generatePurchaseOrder(id) {
      return apiClient.post(`/requests/${id}/generate_purchase_order/`);
    },

    /**
     * 打印验收单
     * @param {number} id - 需求ID
     * @returns {Promise} 打印数据
     * @usage 物资验收页面 (/purchase/acceptance)
     */
    printAcceptance(id) {
      return apiClient.get(`/requests/${id}/print_acceptance/`);
    },

    /**
     * 获取打印上下文数据
     * @param {number} id - 需求ID
     * @returns {Promise} 打印数据
     * @usage 各页面的打印功能
     */
    getPrintContext(id) {
      return apiClient.get(`/requests/${id}/print/`);
    },

    // --- 数据导入导出相关API ---

    /**
     * 导出Excel文件
     * @param {Object} filters - 筛选条件
     * @returns {Promise} Excel文件流
     * @usage 需求列表页面的导出功能
     */
    exportToExcel(filters = {}) {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value);
        }
      });

      const url = params.toString() ?
        `/requests/export/?${params.toString()}` :
        '/requests/export/';

      return apiClient.get(url, { responseType: 'blob' });
    },

    /**
     * 下载导入模板
     * @returns {Promise} 模板文件流
     * @usage 需求列表页面的批量导入功能
     */
    downloadTemplate() {
      return apiClient.get('/requests/template/', { responseType: 'blob' });
    },

    /**
     * 从Excel导入数据
     * @param {File} file - Excel文件
     * @returns {Promise} 导入结果
     * @usage 需求列表页面的批量导入功能
     */
    importFromExcel(file) {
      const formData = new FormData();
      formData.append('file', file);
      return apiClient.post('/requests/import/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },

    // --- 历史数据和统计分析相关API ---

    /**
     * 获取历史采购数据
     * @param {number} deptId - 部门ID
     * @param {string} itemName - 物品名称
     * @param {string} specification - 规格型号
     * @returns {Promise} 历史数据
     * @usage 需求表单页面的历史价格参考功能
     */
    getHistoryData(deptId, itemName, specification = '') {
      const params = new URLSearchParams();
      if (deptId) params.append('dept_id', deptId);
      if (itemName) params.append('item_name', itemName);
      if (specification) params.append('specification', specification);
      return apiClient.get(`/requests/history/?${params.toString()}`);
    },

    /**
     * 获取历史平均价格
     * @param {Object} params - 查询参数
     * @returns {Promise} 平均价格数据
     * @usage 需求表单页面的价格预警功能
     */
    getHistoricalAveragePrice(params) {
      return apiClient.get('/requests/historical_average_price/', { params });
    },

    /**
     * 获取时间预警信息
     * @returns {Promise} 预警数据
     * @usage 采购总览页面 (/purchase/overview)
     */
    getTimeWarnings() {
      return apiClient.get('/requests/time_warnings/');
    },

    // --- 批量操作相关API ---

    /**
     * 发货通知
     * @param {number} id - 需求ID
     * @returns {Promise} 通知结果
     * @usage 物资采购页面 (/purchase/procurement)
     */
    notifyShipping(id) {
      return apiClient.post(`/requests/${id}/notify_shipping/`);
    },

    /**
     * 批量发货通知
     * @param {Array} requestIds - 需求ID数组
     * @returns {Promise} 批量通知结果
     * @usage 物资采购页面的批量操作
     */
    batchNotifyShipping(requestIds) {
      return apiClient.post('/requests/batch_notify_shipping/', {
        request_ids: requestIds
      });
    },

    /**
     * 生成报销单
     * @param {number} id - 需求ID
     * @returns {Promise} 生成结果
     * @usage 结算报销页面 (/purchase/reimbursement)
     */
    generateReimbursement(id) {
      return apiClient.post(`/requests/${id}/generate_reimbursement/`);
    },

    /**
     * 批量生成报销单
     * @param {Array} requestIds - 需求ID数组
     * @returns {Promise} 批量生成结果
     * @usage 结算报销页面的批量操作
     */
    batchGenerateReimbursement(requestIds) {
      return apiClient.post('/requests/batch_generate_reimbursement/', {
        request_ids: requestIds
      });
    },

    /**
     * 批量审批
     * @param {Array} requestIds - 需求ID数组
     * @param {string} comment - 审批意见
     * @returns {Promise} 批量审批结果
     * @usage 需求审核页面的批量操作
     */
    batchApprove(requestIds, comment = '') {
      return apiClient.post('/requests/batch_approve/', {
        request_ids: requestIds,
        comment
      });
    },

    /**
     * 批量生成采购单
     * @param {Array} requestIds - 需求ID数组
     * @returns {Promise} 批量生成结果
     * @usage 物资采购页面的批量操作
     */
    batchGeneratePurchaseOrder(requestIds) {
      return apiClient.post('/requests/batch_generate_purchase_order/', {
        request_ids: requestIds
      });
    },
    // 更新采购信息（不改变状态）
    updatePurchaseInfo(id, data) {
      return apiClient.put(`/requests/${id}/`, data);
    },
    // 完成采购（改变状态为已采购）
    completePurchase(id, data) {
      return apiClient.post(`/requests/${id}/purchase/`, data);
    },
    // 记录变更
    recordChange(data) {
      return apiClient.post('/requests/change_record/', data);
    },
    // 更新验收信息（不改变状态）
    updateAcceptanceInfo(id, data) {
      return apiClient.put(`/requests/${id}/`, data);
    },
    // 完成验收（改变状态为已验收）
    completeAcceptance(id, data) {
      return apiClient.post(`/requests/${id}/accept/`, data);
    },
    // 上传照片
    uploadPhoto(id, formData) {
      return apiClient.post(`/requests/${id}/upload_photo/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    // 更新结算信息（不改变状态）
    updateSettlementInfo(id, data) {
      return apiClient.put(`/requests/${id}/`, data);
    },
    // 完成结算（改变状态为已结算）
    completeSettlement(id, data) {
      return apiClient.post(`/requests/${id}/reimburse/`, data);
    },
    // 需求提报页面优化API
    getRequirementsOptimized(params = {}) {
      return apiClient.get('/purchase-requests/requirements/optimized/', { params });
    },
    // 优化的列表API - 支持不同页面类型
    getOptimizedList(params = {}) {
      return apiClient.get(buildUrlWithParams('/requests/optimized', params));
    },
    // 快速列表API - 高性能数据获取
    getFastList(params = {}) {
      return apiClient.get(buildUrlWithParams('/requests/fast', params));
    },
    // 物资采购页面专用优化API
    getProcurementOptimized(params = {}) {
      return apiClient.get('/procurement/optimized/', { params });
    }
  },

  // 采购需求API (简化版本，兼容现有代码)
  purchase: {
    // 获取列表
    getList(params) {
      return apiClient.get('/requests/', { params });
    },
    // 创建
    create(data) {
      return apiClient.post('/requests/', data);
    },
    // 获取详情
    getDetail(id) {
      return apiClient.get(`/requests/${id}/`);
    },
    // 更新
    update(id, data) {
      return apiClient.put(`/requests/${id}/`, data);
    },
    // 删除
    delete(id) {
      return apiClient.delete(`/requests/${id}/`);
    },
    // 提交
    submit(id) {
      return apiClient.post(`/requests/${id}/submit/`);
    },
    // 审批
    approve(id, data) {
      return apiClient.post(`/requests/${id}/approve/`, data);
    },
    // 导出
    export(params) {
      return apiClient.get('/requests/export/', { params, responseType: 'blob' });
    },
    // 导入
    importData(formData) {
      return apiClient.post('/requests/import/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    // 下载模板
    downloadTemplate() {
      return apiClient.get('/requests/template/', { responseType: 'blob' });
    },
    // 获取历史数据
    getHistory(params) {
      return apiClient.get('/requests/history/', { params });
    },
    // 重置数据
    resetData(count = 1000) {
      return apiClient.post('/reset_data/', { count });
    }
  },

  // ========== 物资验收管理 ==========

  // 物资验收相关API - 物资验收页面使用
  acceptances: {
    /**
     * 获取验收列表
     * @param {Object} params - 查询参数
     * @returns {Promise} 验收列表数据
     * @usage 物资验收列表页面 (/purchase/acceptance)
     */
    getList(params = {}) {
      const acceptanceParams = { ...params };

      // 如果没有指定status或status_in，则默认获取验收相关状态
      if (!acceptanceParams.status && !acceptanceParams.status_in) {
        acceptanceParams.status_in = 'pending_acceptance,accepted';
      }

      return apiClient.get(buildUrlWithParams('/requests', acceptanceParams));
    },

    /**
     * 执行验收操作
     * @param {number} id - 需求ID
     * @param {Object} data - 验收数据（照片、备注等）
     * @returns {Promise} 验收结果
     * @usage 物资验收详情页面 (/purchase/acceptance/:id)
     */
    accept(id, data = {}) {
      return apiClient.post(`/requests/${id}/accept/`, data);
    },

    /**
     * 异常审批
     * @param {number} id - 需求ID
     * @returns {Promise} 审批结果
     * @usage 物资验收页面的异常处理
     */
    approveException(id) {
      return apiClient.post(`/requests/${id}/approve-exception/`);
    },

    /**
     * 导出验收数据到Excel
     * @param {Object} filters - 筛选条件
     * @returns {Promise} Excel文件流
     * @usage 物资验收列表页面的导出功能
     */
    exportToExcel(filters = {}) {
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          // 如果是数组，转换为逗号分隔的字符串
          if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, value);
          }
        }
      });

      // 确保有验收状态过滤，如果没有提供status_in参数
      if (!filters.status_in) {
        params.append('status_in', 'pending_acceptance,accepted');
      }

      const url = `/requests/export/?${params.toString()}`;
      return apiClient.get(url, { responseType: 'blob' });
    }
  },

  // 结算报销相关API（已整合到采购需求中）
  reimbursements: {
    // 获取报销列表（通过采购需求API，筛选报销相关状态）
    getList(params = {}) {
      const reimbursementParams = {
        ...params,
        status_in: 'pending_reimbursement,reimbursed'
      };
      return apiClient.get(buildUrlWithParams('/requests', reimbursementParams));
    },
    // 结算操作
    settle(id, voucherData) {
      return apiClient.post(`/requests/${id}/reimburse/`, voucherData);
    },
    // 获取结算报销统计数据
    getStatistics() {
      return apiClient.get('/reimbursement-statistics/');
    }
  },

  // 数据字典相关API - 全局使用，为下拉选择器提供数据
  dicts: {
    /**
     * 获取指定类型的数据字典
     * @param {string} type - 字典类型（如：item_category, unit, procurement_method等）
     * @param {boolean} enabled - 是否只获取启用的字典项
     * @returns {Promise} 字典数据列表
     * @usage 用于各页面的下拉选择器组件
     */
    getDict(type, enabled = true) {
      const cacheKey = generateCacheKey(`/system/dicts/${type}/`, { enabled })
      const cached = getCache(cacheKey)
      if (cached) {
        return Promise.resolve(cached)
      }

      return apiClient.get(`/system/dicts/${type}/`, {
        params: { enabled }
      }).then(response => {
        setCache(cacheKey, response)
        return response
      })
    },
    getAllDicts(params = {}) {
      const cacheKey = generateCacheKey('/system/dicts/', params)

      // 如果有时间戳参数，说明是强制刷新，跳过缓存
      if (!params._t) {
        const cached = getCache(cacheKey)
        if (cached) {
          return Promise.resolve(cached)
        }
      }

      return apiClient.get('/system/dicts/', { params }).then(response => {
        // 强制刷新时不缓存结果
        if (!params._t) {
          setCache(cacheKey, response)
        }
        return response
      })
    },
    getStatistics() {
      return apiClient.get('/system/dicts/statistics/')
    },
    export(filters = {}) {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value);
        }
      });

      const url = params.toString() ?
        `/system/dicts/export/?${params.toString()}` :
        '/system/dicts/export/';

      return apiClient.get(url, { responseType: 'blob' });
    },
    downloadTemplate() {
      return apiClient.get('/system/dicts/template/', { responseType: 'blob' });
    },
    importFromExcel(formData) {
      return apiClient.post('/system/dicts/import/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    ...createCrudService('system/dicts')
  },

  // ========== 系统设置管理 ==========

  // 部门管理API - 部门管理页面使用
  departments: {
    /**
     * 获取部门列表（分页）
     * @param {Object} params - 查询参数
     * @returns {Promise} 部门列表数据
     * @usage 部门管理页面 (/settings/departments)
     */
    getList(params = {}) {
      const cacheKey = generateCacheKey('/system/departments/', params)
      const cached = getCache(cacheKey)
      if (cached) {
        return Promise.resolve(cached)
      }

      return apiClient.get('/system/departments/', { params }).then(response => {
        setCache(cacheKey, response)
        return response
      })
    },

    /**
     * 获取所有部门数据（不分页）
     * @returns {Promise} 所有部门数据
     * @usage 用于下拉选择器、筛选选项等全局组件
     */
    getAll() {
      const cacheKey = 'departments_all'
      const cached = getCache(cacheKey)
      if (cached) {
        return Promise.resolve(cached)
      }

      return apiClient.get('/system/departments/all/').then(response => {
        setCache(cacheKey, response, 300000) // 缓存5分钟
        return response
      })
    },

    /**
     * 获取部门树形结构
     * @returns {Promise} 部门树形数据
     * @usage 部门选择器组件
     */
    getTree() {
      const cacheKey = 'departments_tree'
      const cached = getCache(cacheKey)
      if (cached) {
        return Promise.resolve(cached)
      }

      return apiClient.get('/system/departments/tree/').then(response => {
        setCache(cacheKey, response)
        return response
      })
    },
    getStatistics() {
      return apiClient.get('/system/departments/statistics/')
    },
    export(filters = {}) {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value);
        }
      });

      const url = params.toString() ?
        `/system/departments/export/?${params.toString()}` :
        '/system/departments/export/';

      return apiClient.get(url, { responseType: 'blob' });
    },
    importFromExcel(formData) {
      return apiClient.post('/system/departments/import/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    downloadTemplate() {
      return apiClient.get('/system/departments/template/', { responseType: 'blob' });
    },
    ...createCrudService('system/departments')
  },



  // 通知管理API
  notifications: {
    getList(params = {}) {
      return apiClient.get('/system/notifications/', { params });
    },
    getDetail(id) {
      return apiClient.get(`/system/notifications/${id}/`);
    },
    create(data) {
      return apiClient.post('/system/notifications/create/', data);
    },
    update(id, data) {
      return apiClient.put(`/system/notifications/${id}/`, data);
    },
    delete(id) {
      return apiClient.delete(`/system/notifications/${id}/`);
    },
    batchDelete(ids) {
      return apiClient.post('/system/notifications/batch-delete/', { ids });
    },
    markAsRead(id) {
      return apiClient.post(`/system/notifications/${id}/read/`);
    },
    markAllAsRead() {
      return apiClient.post('/system/notifications/read-all/');
    },
    getUnreadCount() {
      return apiClient.get('/system/notifications/count/');
    },
    getStatistics() {
      return apiClient.get('/system/notifications/statistics/');
    }
  },

  // 系统管理API（包含通知管理）
  system: {
    // 通知管理
    getNotifications(params = {}) {
      return apiClient.get('/system/notifications/', { params });
    },
    createNotification(data) {
      return apiClient.post('/system/notifications/create/', data);
    },
    updateNotification(id, data) {
      return apiClient.put(`/system/notifications/${id}/`, data);
    },
    deleteNotification(id) {
      return apiClient.delete(`/system/notifications/${id}/`);
    }
  },

  // 系统日志管理API
  systemLogs: {
    getLogs(params = {}) {
      return apiClient.get('/system/logs/', { params });
    },
    getStatistics() {
      return apiClient.get('/system/logs/statistics/');
    },
    exportLogs(params = {}) {
      return apiClient.get('/system/logs/export/', {
        params,
        responseType: 'blob'
      });
    },
    batchExport(logIds) {
      return apiClient.post('/system/logs/batch-export/', {
        log_ids: logIds
      }, {
        responseType: 'blob'
      });
    }
  },

  // 仪表板统计API
  dashboard: {
    getStatistics(days = 30, startDate = null, endDate = null) {
      if (startDate && endDate) {
        return apiClient.get(`/dashboard/statistics/?start_date=${startDate}&end_date=${endDate}`);
      }
      return apiClient.get(`/dashboard/statistics/?days=${days}`);
    },
    getTrend(days = 30, startDate = null, endDate = null, groupBy = 'day') {
      let url = '/dashboard/trend/';
      const params = new URLSearchParams();

      if (startDate && endDate) {
        params.append('start_date', startDate);
        params.append('end_date', endDate);
      } else {
        params.append('days', days);
      }

      params.append('group_by', groupBy);

      return apiClient.get(`${url}?${params.toString()}`);
    },
    getPriceWarnings() {
      return apiClient.get('/dashboard/price-warnings/');
    },
    getRecentActivities() {
      return apiClient.get('/dashboard/activities/');
    },
    // 获取采购方式统计
    getMethodStatistics(days = 30, startDate = null, endDate = null) {
      if (startDate && endDate) {
        return apiClient.get(`/dashboard/method-statistics/?start_date=${startDate}&end_date=${endDate}`);
      }
      return apiClient.get(`/dashboard/method-statistics/?days=${days}`);
    },
    // 获取采购类型统计
    getPurchaseTypeStatistics(days = 30, startDate = null, endDate = null) {
      if (startDate && endDate) {
        return apiClient.get(`/dashboard/purchase-type-statistics/?start_date=${startDate}&end_date=${endDate}`);
      }
      return apiClient.get(`/dashboard/purchase-type-statistics/?days=${days}`);
    },
    // 获取部门统计（一级单位）
    getDepartmentStatistics(days = 30, startDate = null, endDate = null) {
      if (startDate && endDate) {
        return apiClient.get(`/dashboard/department-statistics/?start_date=${startDate}&end_date=${endDate}`);
      }
      return apiClient.get(`/dashboard/department-statistics/?days=${days}`);
    },

    // 优化的统计API - 支持筛选条件
    getOverviewStatistics(params = {}) {
      return apiClient.get('/overview/statistics/', { params });
    },

    // 图表数据API - 专门用于图表渲染
    getOverviewCharts(params = {}) {
      return apiClient.get('/overview/charts/', { params });
    },

    // 优化的列表API - 用于提升列表页面性能
    getOptimizedList(params = {}) {
      return apiClient.get('/optimized/list/', { params });
    },

    // 获取筛选选项 - 优化版本
    getFilterOptions() {
      return apiClient.get('/optimized/filter-options/');
    },

    // 快速列表API - 高性能版本
    getFastList(params = {}) {
      return apiClient.get('/fast/list/', { params });
    },
    // 获取状态分布统计
    getStatusDistribution() {
      return apiClient.get('/dashboard/status-distribution/');
    },
    // 获取部门状态分布统计（带参数）
    getDepartmentStatusDistribution(days = 30, startDate = null, endDate = null) {
      if (startDate && endDate) {
        return apiClient.get(`/dashboard/department-status/?start_date=${startDate}&end_date=${endDate}`);
      }
      return apiClient.get(`/dashboard/department-status/?days=${days}`);
    },
    // 获取经费项目统计
    getBudgetStatistics() {
      return apiClient.get('/dashboard/budget-statistics/');
    }
  },

  // 用户管理API - 用户管理页面使用
  users: {
    // 继承基础CRUD操作
    ...createCrudService('auth/users'),

    /**
     * 重置用户密码
     * @param {number} id - 用户ID
     * @param {string} newPassword - 新密码
     * @returns {Promise} 重置结果
     * @usage 用户管理页面 (/settings/users)
     */
    resetPassword(id, newPassword) {
      return apiClient.post(`/auth/users/${id}/reset-password/`, {
        password: newPassword
      });
    },

    /**
     * 切换用户状态（启用/禁用）
     * @param {number} id - 用户ID
     * @param {boolean} isActive - 是否激活
     * @returns {Promise} 切换结果
     * @usage 用户管理页面 (/settings/users)
     */
    toggleStatus(id, isActive) {
      return apiClient.patch(`/auth/users/${id}/`, {
        is_active: isActive
      });
    },

    // --- 个人资料相关API (用户资料页面使用) ---

    /**
     * 获取当前用户信息
     * @returns {Promise} 用户信息
     * @usage 用户资料页面 (/profile)
     */
    getCurrentUser() {
      return apiClient.get('/auth/users/profile/');
    },

    /**
     * 更新个人资料
     * @param {Object} data - 用户资料数据
     * @returns {Promise} 更新结果
     * @usage 用户资料页面 (/profile)
     */
    updateProfile(data) {
      return apiClient.put('/auth/users/profile/', data);
    },

    /**
     * 修改密码
     * @param {Object} data - 密码数据（旧密码、新密码）
     * @returns {Promise} 修改结果
     * @usage 用户资料页面 (/profile)
     */
    changePassword(data) {
      return apiClient.post('/auth/users/change-password/', data);
    },

    /**
     * 获取用户统计信息
     * @returns {Promise} 统计数据
     * @usage 用户管理页面的统计面板
     */
    getUserStatistics() {
      return apiClient.get('/auth/users/statistics/');
    },

    /**
     * 获取管理员统计信息
     * @returns {Promise} 管理员统计数据
     * @usage 用户管理页面的管理员面板
     */
    getAdminStatistics() {
      return apiClient.get('/auth/users/admin-statistics/');
    },

    /**
     * 获取在线用户数量
     * @returns {Promise} 在线用户数量
     * @usage 布局页面底部状态栏显示
     */
    getOnlineCount() {
      return apiClient.get('/auth/users/online-count/');
    },
    getUserLogs(params = {}) {
      return apiClient.get('/auth/users/logs/', { params });
    },
    // 导入导出功能
    downloadTemplate() {
      return apiClient.get('/auth/users/template/', { responseType: 'blob' });
    },
    import(file) {
      const formData = new FormData();
      formData.append('file', file);
      return apiClient.post('/auth/users/import/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    export(filters = {}) {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value);
        }
      });

      const url = params.toString() ?
        `/auth/users/export/?${params.toString()}` :
        '/auth/users/export/';

      return apiClient.get(url, { responseType: 'blob' });
    }
  },

  // 权限控制API
  permissions: {
    getList() {
      return apiClient.get('/roles/permissions/');
    },
    getAllPermissions() {
      return apiClient.get('/roles/permissions/');
    },
    /**
     * 获取权限矩阵数据 - 统一API接口
     * @returns {Promise} 完整的权限矩阵数据
     * @usage 权限管理页面 (/settings/permissions)
     */
    getMatrixData() {
      return apiClient.get('/roles/permission-matrix/');
    },
    assignToRole(roleId, permissionIds) {
      return apiClient.post(`/roles/${roleId}/permissions/`, {
        permission_ids: permissionIds
      });
    },
    getUserPermissions() {
      return apiClient.get('/permissions/my-permissions/');
    },
    updateUserPermission(userId, permissionKey, enabled) {
      return apiClient.post(`/auth/users/${userId}/permissions/`, {
        permission_key: permissionKey,
        enabled: enabled
      });
    },
    getUserPermissionsList(userId) {
      return apiClient.get(`/auth/users/${userId}/permissions/`);
    },
    /**
     * 获取权限树形结构
     * @returns {Promise} 权限树形数据
     * @usage 权限管理页面的树形显示
     */
    getPermissionTree() {
      return apiClient.get('/roles/permissions/tree/');
    }
  },

  // 角色管理API
  roles: {
    getList(params = {}) {
      return apiClient.get('/roles/', { params });
    },
    create(data) {
      return apiClient.post('/roles/', data);
    },
    getById(id) {
      return apiClient.get(`/roles/${id}/`);
    },
    update(id, data) {
      return apiClient.put(`/roles/${id}/`, data);
    },
    delete(id) {
      return apiClient.delete(`/roles/${id}/`);
    },
    assignPermissions(roleId, permissionIds) {
      return apiClient.post(`/roles/${roleId}/permissions/`, {
        permission_ids: permissionIds
      });
    },
    getPermissions(roleId) {
      return apiClient.get(`/roles/${roleId}/permissions/`);
    },
    updatePermissions(roleId, data) {
      return apiClient.put(`/roles/${roleId}/permissions/`, data);
    },
    getSimpleList() {
      return apiClient.get('/roles/simple/');
    },
    getUsers(roleId) {
      return apiClient.get(`/roles/${roleId}/users/`);
    },
    export(params = {}) {
      return apiClient.get('/roles/export/', {
        params,
        responseType: 'blob'
      });
    },
    downloadTemplate() {
      return apiClient.get('/roles/template/', {
        responseType: 'blob'
      });
    },
    import(file) {
      const formData = new FormData();
      formData.append('file', file);
      return apiClient.post('/roles/import/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    addUsers(roleId, userIds) {
      return apiClient.post(`/roles/${roleId}/users/`, {
        user_ids: userIds
      });
    },
    removeUser(roleId, userId) {
      return apiClient.delete(`/roles/${roleId}/users/${userId}/`);
    }
  },

  // 菜单管理API
  menus: {
    // 获取菜单树
    getTree(adminMode = false, forceRefresh = false) {
      const params = adminMode ? { admin_mode: 'true' } : {};

      // 如果是强制刷新，添加时间戳参数绕过缓存
      if (forceRefresh) {
        params._t = Date.now();
        params._cache_bust = Math.random().toString(36).substr(2, 9);
      }

      // 创建特殊的请求配置，绕过所有缓存
      const config = {
        params,
        headers: {}
      };

      if (forceRefresh) {
        config.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
        config.headers['Pragma'] = 'no-cache';
        config.headers['Expires'] = '0';
      }

      return apiClient.get('/system/menus/tree/', config);
    },

    // 获取用户菜单
    getUserMenus() {
      return apiClient.get('/system/menus/user_menus/');
    },

    // 获取用户权限
    getUserPermissions() {
      return apiClient.get('/system/menus/user_permissions/');
    },

    // 强制刷新用户菜单缓存
    refreshUserMenus() {
      return apiClient.post('/system/menus/refresh_user_menus/');
    },

    // 获取菜单统计数据
    getStatistics(forceRefresh = false) {
      const params = {};

      // 如果是强制刷新，添加时间戳参数绕过缓存
      if (forceRefresh) {
        params._t = Date.now();
        params._cache_bust = Math.random().toString(36).substr(2, 9);
      }

      // 创建特殊的请求配置，绕过所有缓存
      const config = {
        params,
        headers: {}
      };

      if (forceRefresh) {
        config.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
        config.headers['Pragma'] = 'no-cache';
        config.headers['Expires'] = '0';
      }

      return apiClient.get('/system/menus/statistics/', config);
    },

    // 菜单CRUD操作
    getList(params = {}) {
      return apiClient.get('/system/menus/', { params });
    },

    getDetail(id) {
      return apiClient.get(`/system/menus/${id}/`);
    },

    create(data) {
      return apiClient.post('/system/menus/', data);
    },

    update(id, data) {
      return apiClient.put(`/system/menus/${id}/`, data);
    },

    delete(id) {
      return apiClient.delete(`/system/menus/${id}/`);
    }
  },

  // 文件上传API
  upload: {
    uploadFile(file, watermark = '') {
      const formData = new FormData();
      formData.append('file', file);
      if (watermark) {
        formData.append('watermark', watermark);
      }

      return apiClient.post('/upload/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    uploadFiles(files, watermark = '') {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      if (watermark) {
        formData.append('watermark', watermark);
      }

      return apiClient.post('/upload/batch/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    getFileUrl(filePath) {
      return `${process.env.VUE_APP_API_URL}/media/${filePath}`;
    }
  }
};

export default api;