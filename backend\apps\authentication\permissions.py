"""
权限控制模块
定义不同角色的权限和装饰器
"""
from functools import wraps
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from rest_framework.decorators import permission_classes
from rest_framework.permissions import BasePermission


# 注意：硬编码权限定义已移除，现在使用统一的数据库权限系统
# 所有权限检查都通过 apps.system.permissions 模块进行


def has_permission(user, permission):
    """检查用户是否有指定权限 - 已弃用，请使用 apps.system.permissions.has_permission"""
    # 重定向到新的权限系统
    from apps.system.permissions import has_permission as new_has_permission
    return new_has_permission(user, permission)


def require_permission(permission):
    """权限装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not has_permission(request.user, permission):
                return JsonResponse({
                    'code': 403,
                    'message': '权限不足',
                    'data': None
                }, status=403)
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


class RolePermission(BasePermission):
    """DRF权限类"""
    permission_required = None
    
    def has_permission(self, request, view):
        if hasattr(view, 'permission_required'):
            permission = view.permission_required
        else:
            permission = self.permission_required
        
        if not permission:
            return True
        
        return has_permission(request.user, permission)


def get_user_permissions(user):
    """获取用户所有权限 - 已弃用，请使用 apps.system.permissions.get_user_permissions"""
    # 重定向到新的权限系统
    from apps.system.permissions import get_user_permissions as new_get_user_permissions
    return new_get_user_permissions(user)


def get_user_role_info(user):
    """获取用户角色信息 - 已弃用，请使用 apps.system.permissions.get_permission_info"""
    # 重定向到新的权限系统
    from apps.system.permissions import get_permission_info
    return get_permission_info(user)


def check_page_permission(user, page_path):
    """检查页面访问权限 - 已弃用，请使用 apps.system.permissions.check_page_permission"""
    # 重定向到新的权限系统
    from apps.system.permissions import check_page_permission as new_check_page_permission
    return new_check_page_permission(user, page_path)
