from rest_framework import serializers
from django.contrib.auth import authenticate
from .models import User


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""
    username = serializers.CharField(max_length=150)
    password = serializers.CharField(max_length=128, write_only=True)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('用户名或密码错误')
            if not user.is_active:
                raise serializers.ValidationError('用户账号已被禁用')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('用户名和密码不能为空')
        
        return attrs


class UserSerializer(serializers.ModelSerializer):
    """用户信息序列化器"""
    dept_hierarchy_path = serializers.SerializerMethodField()
    dept_name = serializers.SerializerMethod<PERSON>ield()
    role_name = serializers.SerializerMethodField()
    role_permissions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'username', 'real_name', 'phone', 'email',
            'dept_id', 'dept_hierarchy_path', 'dept_name', 'role', 'role_name', 'role_permissions',
            'is_active', 'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login', 'dept_hierarchy_path', 'dept_name', 'role_name', 'role_permissions']

    def get_dept_hierarchy_path(self, obj):
        """获取部门层级路径"""
        if not obj.dept_id:
            return ''

        try:
            from apps.system.models import Department
            dept = Department.objects.filter(id=obj.dept_id).first()
            return dept.hierarchy_path if dept else ''
        except ImportError:
            return ''

    def get_dept_name(self, obj):
        """获取部门名称"""
        if not obj.dept_id:
            return ''

        try:
            from apps.system.models import Department
            dept = Department.objects.filter(id=obj.dept_id).first()
            return dept.dept_name if dept else ''
        except ImportError:
            return ''

    def get_role_name(self, obj):
        """获取角色名称 - 简化版，直接使用user.role字段"""
        if not obj.role:
            return '普通用户'

        try:
            from apps.roles.models import Role
            role = Role.objects.filter(code=obj.role, is_active=True).first()
            if role:
                return role.name
        except ImportError:
            pass

        # 如果没有找到角色，返回角色代码
        return obj.role

    def get_role_permissions(self, obj):
        """获取角色权限 - 简化版，直接使用权限系统"""
        try:
            from apps.system.permissions import get_user_permissions
            permissions = get_user_permissions(obj)
            return permissions
        except Exception as e:
            print(f"获取用户权限失败: {e}")
            return []


class UserCreateSerializer(serializers.ModelSerializer):
    """用户创建序列化器"""
    password = serializers.CharField(write_only=True, min_length=6, max_length=20)
    
    class Meta:
        model = User
        fields = [
            'username', 'password', 'real_name', 'phone', 'email',
            'dept_id', 'role', 'is_active'
        ]
    
    def create(self, validated_data):
        password = validated_data.pop('password')

        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()

        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户更新序列化器"""

    class Meta:
        model = User
        fields = [
            'real_name', 'phone', 'email', 'dept_id', 'role', 'is_active'
        ]

    def update(self, instance, validated_data):
        # 更新用户基本信息
        instance = super().update(instance, validated_data)
        return instance


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, min_length=6, max_length=20)
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('原密码错误')
        return value
    
    def save(self):
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user
