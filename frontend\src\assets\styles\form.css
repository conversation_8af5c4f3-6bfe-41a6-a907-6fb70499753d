/* 统一表单样式 */

/* 表单容器 */
.form-container {
  padding: 0;
  background: transparent;
}

.form-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.form-header {
  padding: var(--space-xl) var(--space-2xl);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  position: relative;
  overflow: hidden;
}

.form-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.form-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  letter-spacing: 0.5px;
}

.form-title .anticon {
  font-size: 24px;
}

.form-subtitle {
  font-size: var(--text-sm);
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* 表单内容区域 */
.form-content {
  padding: var(--space-2xl);
}

/* 表单分组 */
.form-section {
  margin-bottom: var(--space-2xl);
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-sm);
  border-bottom: 2px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.section-title .anticon {
  color: var(--primary-color);
  font-size: var(--text-lg);
}

/* 表单项样式 */
.unified-form .ant-form-item {
  margin-bottom: var(--space-xl);
}

.unified-form .ant-form-item-label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  padding-bottom: var(--space-xs);
}

.unified-form .ant-form-item-label > label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.unified-form .ant-form-item-label > label.ant-form-item-required::before {
  color: var(--error-color);
  font-size: var(--text-sm);
}

/* 输入框样式 */
.unified-form .ant-input,
.unified-form .ant-input-number,
.unified-form .ant-textarea {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  padding: var(--space-md);
}

.unified-form .ant-input:focus,
.unified-form .ant-input-number:focus-within,
.unified-form .ant-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-form .ant-input:hover,
.unified-form .ant-input-number:hover,
.unified-form .ant-textarea:hover {
  border-color: var(--primary-light);
}

/* 选择器样式 */
.unified-form .ant-select-selector {
  border: 2px solid var(--border-light) !important;
  border-radius: var(--radius-sm) !important;
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  padding: var(--space-xs) var(--space-md) !important;
}

.unified-form .ant-select-focused .ant-select-selector {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1) !important;
}

.unified-form .ant-select-selector:hover {
  border-color: var(--primary-light) !important;
}

/* 日期选择器样式 */
.unified-form .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  padding: var(--space-md);
  width: 100%;
}

.unified-form .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-form .ant-picker:hover {
  border-color: var(--primary-light);
}

/* 上传组件样式 */
.unified-form .ant-upload {
  width: 100%;
}

.unified-form .ant-upload-drag {
  border: 2px dashed var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);
  transition: var(--transition-normal);
}

.unified-form .ant-upload-drag:hover {
  border-color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
}

.unified-form .ant-upload-drag.ant-upload-drag-hover {
  border-color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
}

/* 开关样式 */
.unified-form .ant-switch {
  background-color: var(--gray-300);
}

.unified-form .ant-switch-checked {
  background-color: var(--primary-color);
}

/* 单选框和复选框样式 */
.unified-form .ant-radio-wrapper,
.unified-form .ant-checkbox-wrapper {
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.unified-form .ant-radio-checked .ant-radio-inner,
.unified-form .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 表单操作区域 */
.form-actions {
  padding: var(--space-xl) var(--space-2xl);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
}

.form-actions.center {
  justify-content: center;
}

.form-actions.space-between {
  justify-content: space-between;
}

/* 表单按钮样式 */
.form-btn-primary {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--text-sm);
  min-width: 120px;
}

.form-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
  color: var(--text-inverse);
}

.form-btn-secondary {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 120px;
}

.form-btn-secondary:hover {
  border-color: var(--border-medium);
  color: var(--text-primary);
  background: var(--bg-secondary);
}

.form-btn-danger {
  border: 2px solid var(--error-color);
  color: var(--error-color);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-xl);
  background: var(--bg-primary);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
  min-width: 120px;
}

.form-btn-danger:hover {
  border-color: var(--error-dark);
  color: var(--error-dark);
  background: rgba(239, 68, 68, 0.05);
  transform: translateY(-1px);
}

/* 表单验证样式 */
.unified-form .ant-form-item-has-error .ant-input,
.unified-form .ant-form-item-has-error .ant-input-number,
.unified-form .ant-form-item-has-error .ant-select-selector,
.unified-form .ant-form-item-has-error .ant-picker,
.unified-form .ant-form-item-has-error .ant-textarea {
  border-color: var(--error-color) !important;
}

.unified-form .ant-form-item-explain-error {
  color: var(--error-color);
  font-size: var(--text-xs);
  margin-top: var(--space-xs);
}

/* 表单提示样式 */
.form-tip {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: var(--space-md);
  margin-bottom: var(--space-lg);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.form-tip.info {
  background: rgba(6, 182, 212, 0.1);
  border-color: var(--info-color);
  color: var(--info-dark);
}

.form-tip.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: var(--warning-color);
  color: var(--warning-dark);
}

.form-tip.success {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--success-color);
  color: var(--success-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-content {
    padding: var(--space-lg);
  }
  
  .form-actions {
    padding: var(--space-lg);
    flex-direction: column;
  }
  
  .form-btn-primary,
  .form-btn-secondary,
  .form-btn-danger {
    width: 100%;
    margin-bottom: var(--space-sm);
  }
}
