# Generated by Django 5.2.3 on 2025-06-15 18:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0007_purchaserequest_acceptance_date_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaserequest',
            name='reimbursement_generated_date',
            field=models.DateTimeField(blank=True, help_text='验收完成后生成报销单的时间', null=True, verbose_name='报销单生成日期'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='shipping_date',
            field=models.DateTimeField(blank=True, help_text='采购完成后的发货通知时间', null=True, verbose_name='发货日期'),
        ),
    ]
