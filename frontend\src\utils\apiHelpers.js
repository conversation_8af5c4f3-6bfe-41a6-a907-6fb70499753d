/**
 * API调用辅助工具
 * 统一处理常见的API调用模式，减少重复代码
 */
import { message } from 'ant-design-vue'

/**
 * 创建通用的数据获取函数
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 配置选项
 */
export function createDataFetcher(apiCall, options = {}) {
  const {
    loadingRef,
    dataRef,
    errorHandler,
    successMessage,
    transform
  } = options

  return async (params = {}) => {
    try {
      if (loadingRef) loadingRef.value = true
      
      const response = await apiCall(params)
      let data = response.data || response
      
      // 数据转换
      if (transform && typeof transform === 'function') {
        data = transform(data)
      }
      
      if (dataRef) {
        dataRef.value = data
      }
      
      if (successMessage) {
        message.success(successMessage)
      }
      
      return data
    } catch (error) {
      if (errorHandler && typeof errorHandler === 'function') {
        errorHandler(error)
      } else {
        const errorMessage = error.response?.data?.message || error.message || '操作失败'
        message.error(errorMessage)
      }
      throw error
    } finally {
      if (loadingRef) loadingRef.value = false
    }
  }
}

/**
 * 创建批量操作函数
 * @param {Function} apiCall - 批量API调用函数
 * @param {Object} options - 配置选项
 */
export function createBatchOperation(apiCall, options = {}) {
  const {
    confirmTitle = '批量操作确认',
    confirmContent,
    successMessage,
    errorMessage = '批量操作失败',
    onSuccess,
    onError
  } = options

  return async (selectedItems, extraData = {}) => {
    return new Promise((resolve, reject) => {
      const { Modal } = require('ant-design-vue')
      
      Modal.confirm({
        title: confirmTitle,
        content: confirmContent || `确定要对选中的 ${selectedItems.length} 个项目执行此操作吗？`,
        onOk: async () => {
          try {
            const response = await apiCall(selectedItems, extraData)
            
            if (successMessage) {
              message.success(successMessage)
            }
            
            if (onSuccess && typeof onSuccess === 'function') {
              onSuccess(response)
            }
            
            resolve(response)
          } catch (error) {
            message.error(errorMessage)
            
            if (onError && typeof onError === 'function') {
              onError(error)
            }
            
            reject(error)
          }
        },
        onCancel: () => {
          reject(new Error('用户取消操作'))
        }
      })
    })
  }
}

/**
 * 创建表单提交函数
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 配置选项
 */
export function createFormSubmitter(apiCall, options = {}) {
  const {
    loadingRef,
    successMessage = '操作成功',
    errorMessage = '操作失败',
    onSuccess,
    onError,
    validateForm,
    transformData
  } = options

  return async (formData) => {
    try {
      // 表单验证
      if (validateForm && typeof validateForm === 'function') {
        const isValid = await validateForm(formData)
        if (!isValid) return false
      }

      if (loadingRef) loadingRef.value = true

      // 数据转换
      let submitData = formData
      if (transformData && typeof transformData === 'function') {
        submitData = transformData(formData)
      }

      const response = await apiCall(submitData)
      
      message.success(successMessage)
      
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess(response)
      }
      
      return response
    } catch (error) {
      const msg = error.response?.data?.message || error.message || errorMessage
      message.error(msg)
      
      if (onError && typeof onError === 'function') {
        onError(error)
      }
      
      throw error
    } finally {
      if (loadingRef) loadingRef.value = false
    }
  }
}

/**
 * 创建分页数据获取函数
 * @param {Function} apiCall - API调用函数
 * @param {Object} paginationRef - 分页配置引用
 * @param {Object} dataRef - 数据引用
 */
export function createPaginatedFetcher(apiCall, paginationRef, dataRef, options = {}) {
  const { loadingRef, transform } = options

  return async (filters = {}) => {
    try {
      if (loadingRef) loadingRef.value = true

      const params = {
        page: paginationRef.value.current,
        page_size: paginationRef.value.pageSize,
        ...filters
      }

      const response = await apiCall(params)
      let data = response.data || response

      if (transform && typeof transform === 'function') {
        data = transform(data)
      }

      // 更新数据
      dataRef.value = data.results || data.data || data

      // 更新分页信息
      paginationRef.value = {
        ...paginationRef.value,
        total: data.total || data.count || 0,
        current: data.current_page || paginationRef.value.current
      }

      return data
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || '获取数据失败'
      message.error(errorMessage)
      throw error
    } finally {
      if (loadingRef) loadingRef.value = false
    }
  }
}

/**
 * 创建导出功能
 * @param {Function} apiCall - 导出API调用函数
 * @param {string} filename - 文件名
 */
export function createExporter(apiCall, filename = 'export.xlsx') {
  return async (filters = {}) => {
    try {
      message.loading('正在导出数据...', 0)
      
      const response = await apiCall(filters)
      
      // 创建下载链接
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.destroy()
      message.success('导出成功')
    } catch (error) {
      message.destroy()
      message.error('导出失败')
      throw error
    }
  }
}

/**
 * 创建状态更新函数
 * @param {Function} apiCall - 状态更新API调用函数
 * @param {Object} options - 配置选项
 */
export function createStatusUpdater(apiCall, options = {}) {
  const {
    successMessage = '状态更新成功',
    errorMessage = '状态更新失败',
    onSuccess,
    refreshData
  } = options

  return async (id, newStatus, reason = '') => {
    try {
      const response = await apiCall(id, { status: newStatus, reason })
      
      message.success(successMessage)
      
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess(response)
      }
      
      if (refreshData && typeof refreshData === 'function') {
        refreshData()
      }
      
      return response
    } catch (error) {
      const msg = error.response?.data?.message || error.message || errorMessage
      message.error(msg)
      throw error
    }
  }
}

/**
 * 通用的错误处理函数
 */
export function handleApiError(error, customMessage = '') {
  const errorMessage = error.response?.data?.message || 
                      error.response?.data?.detail || 
                      error.message || 
                      customMessage || 
                      '操作失败'
  
  message.error(errorMessage)
}

export default {
  createDataFetcher,
  createBatchOperation,
  createFormSubmitter,
  createPaginatedFetcher,
  createExporter,
  createStatusUpdater,
  handleApiError
}
