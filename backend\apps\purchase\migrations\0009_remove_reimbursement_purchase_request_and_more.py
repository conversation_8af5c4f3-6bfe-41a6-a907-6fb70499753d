# Generated by Django 5.2.3 on 2025-06-15 19:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0008_purchaserequest_reimbursement_generated_date_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='reimbursement',
            name='purchase_request',
        ),
        migrations.RemoveField(
            model_name='reimbursement',
            name='reimburser',
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='actual_quantity',
            field=models.IntegerField(default=0, verbose_name='实际验收数量'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='approval_attachment',
            field=models.FileField(blank=True, null=True, upload_to='approvals/', verbose_name='审批附件'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='courier_company',
            field=models.CharField(blank=True, max_length=100, verbose_name='快递公司'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='courier_receipt_photo',
            field=models.ImageField(blank=True, null=True, upload_to='receipts/', verbose_name='快递单照片'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='difference_rate',
            field=models.DecimalField(decimal_places=2, default=0, help_text='差异率百分比', max_digits=5, verbose_name='差异率'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='exception_approval_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='异常审批日期'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='exception_approved',
            field=models.BooleanField(default=False, verbose_name='异常已审批'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='exception_approver_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='异常审批人ID'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='exception_reason',
            field=models.TextField(blank=True, verbose_name='异常原因'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='financial_serial_no',
            field=models.CharField(blank=True, max_length=50, verbose_name='财务流水号'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='has_exception',
            field=models.BooleanField(default=False, verbose_name='存在异常'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='internal_approval',
            field=models.BooleanField(default=False, verbose_name='内部审批状态'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='item_photo',
            field=models.ImageField(blank=True, null=True, upload_to='items/', verbose_name='物品照片'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='quantity_difference',
            field=models.IntegerField(default=0, help_text='实际数量 - 采购数量', verbose_name='数量差异'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='reimbursement_remarks',
            field=models.TextField(blank=True, verbose_name='报销备注'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='reimbursement_voucher_no',
            field=models.CharField(blank=True, max_length=50, verbose_name='报销凭证号'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='tracking_number',
            field=models.CharField(blank=True, max_length=100, verbose_name='快递单号'),
        ),
        migrations.DeleteModel(
            name='Acceptance',
        ),
        migrations.DeleteModel(
            name='Reimbursement',
        ),
    ]
