// 全局组件注册
import DataTable from './Common/DataTable.vue'
import StatusTag from './Common/StatusTag.vue'
import FilterForm from './Common/FilterForm.vue'
import LazyImage from './Common/LazyImage.vue'
import LoadingWrapper from './Common/LoadingWrapper.vue'

// 业务组件
import DepartmentTreeSelect from './Department/DepartmentTreeSelect.vue'
import DictSelector from './Dict/Selector.vue'
import BatchImport from './Import/BatchImport.vue'
import PermissionCheck from './Permission/PermissionCheck.vue'
import RoleSelector from './Role/RoleSelector.vue'
import AdvancedSearch from './Search/AdvancedSearch.vue'

// 图表组件（暂时注释掉，需要时再添加）
// import LineChart from './Charts/Line.vue'
// import PriceTrendChart from './Charts/PriceTrendChart.vue'

// 全局组件列表
const components = {
  // 通用组件
  DataTable,
  StatusTag,
  FilterForm,
  LazyImage,
  LoadingWrapper,
  
  // 业务组件
  DepartmentTreeSelect,
  DictSelector,
  BatchImport,
  PermissionCheck,
  RoleSelector,
  AdvancedSearch

  // 图表组件（暂时注释掉）
  // LineChart,
  // PriceTrendChart
}

// 安装插件
export default {
  install(app) {
    // 注册全局组件
    Object.keys(components).forEach(key => {
      app.component(key, components[key])
    })
  }
}

// 导出单个组件
export {
  DataTable,
  StatusTag,
  FilterForm,
  LazyImage,
  LoadingWrapper,
  DepartmentTreeSelect,
  DictSelector,
  BatchImport,
  PermissionCheck,
  RoleSelector,
  AdvancedSearch
}
