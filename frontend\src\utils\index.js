// 增加数据字典类型定义
export const DICT_TYPES = {
  ITEM_CATEGORY: '物品分类',
  PROCUREMENT_METHOD: '采购方式',
  FUND_SOURCE: '经费来源',
  UNIT: '物品单位',
  USER_ROLES: '用户-角色'
};

// 增加字段校验工具函数
export function validateQuantity(value) {
  if (!value && value !== 0) return false;
  return Number(value) >= 1;
}

export function validateUnitPrice(value) {
  if (!value && value !== 0) return false;
  return Number(value) >= 0;
}

export function validateAmount(value) {
  if (!value && value !== 0) return false;
  return Number(value) >= 0;
}

// 增加表单验证规则
export const FORM_RULES = {
  quantity: [
    { required: true, message: '请输入数量' },
    {
      validator: (_, value) => {
        if (validateQuantity(value)) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('数量必须大于等于1'));
      }
    }
  ],
  unitPrice: [
    { required: true, message: '请输入单价' },
    {
      validator: (_, value) => {
        if (validateUnitPrice(value)) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('单价不能为负数'));
      }
    }
  ],
  total_amount: [
    { required: true, message: '金额不能为空' },
    {
      validator: (_, value) => {
        if (validateAmount(value)) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('金额不能为负数'));
      }
    }
  ]
};