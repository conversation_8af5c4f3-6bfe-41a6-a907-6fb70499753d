<template>
  <div class="lazy-image" :style="containerStyle">
    <img
      v-if="loaded"
      :src="src"
      :alt="alt"
      :class="imageClass"
      @load="onLoad"
      @error="onError"
    />
    <div v-else-if="loading" class="loading-placeholder">
      <a-spin size="small" />
    </div>
    <div v-else-if="error" class="error-placeholder">
      <ExclamationCircleOutlined />
      <span>加载失败</span>
    </div>
    <div v-else class="placeholder" :style="placeholderStyle">
      <FileImageOutlined />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ExclamationCircleOutlined, FileImageOutlined } from '@ant-design/icons-vue'

export default {
  name: 'LazyImage',
  components: {
    ExclamationCircleOutlined,
    FileImageOutlined
  },
  props: {
    src: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    },
    width: {
      type: [String, Number],
      default: 'auto'
    },
    height: {
      type: [String, Number],
      default: 'auto'
    },
    placeholder: {
      type: String,
      default: ''
    },
    lazy: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    const loading = ref(false)
    const loaded = ref(false)
    const error = ref(false)
    const imageRef = ref(null)
    let observer = null

    // 容器样式
    const containerStyle = computed(() => ({
      width: typeof props.width === 'number' ? `${props.width}px` : props.width,
      height: typeof props.height === 'number' ? `${props.height}px` : props.height,
      display: 'inline-block',
      position: 'relative'
    }))

    // 占位符样式
    const placeholderStyle = computed(() => ({
      backgroundColor: '#f5f5f5',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: '100%',
      color: '#ccc',
      fontSize: '24px'
    }))

    // 图片样式类
    const imageClass = computed(() => ({
      'lazy-image-loaded': loaded.value,
      'lazy-image-error': error.value
    }))

    // 加载图片
    const loadImage = () => {
      if (loading.value || loaded.value) return

      loading.value = true
      error.value = false

      const img = new Image()
      img.onload = () => {
        loaded.value = true
        loading.value = false
      }
      img.onerror = () => {
        error.value = true
        loading.value = false
      }
      img.src = props.src
    }

    // 图片加载成功
    const onLoad = () => {
      loaded.value = true
      loading.value = false
    }

    // 图片加载失败
    const onError = () => {
      error.value = true
      loading.value = false
    }

    // 设置 Intersection Observer
    const setupObserver = () => {
      if (!props.lazy) {
        loadImage()
        return
      }

      if ('IntersectionObserver' in window) {
        observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                loadImage()
                observer.unobserve(entry.target)
              }
            })
          },
          {
            rootMargin: '50px'
          }
        )

        if (imageRef.value) {
          observer.observe(imageRef.value)
        }
      } else {
        // 不支持 IntersectionObserver 时直接加载
        loadImage()
      }
    }

    onMounted(() => {
      setupObserver()
    })

    onUnmounted(() => {
      if (observer) {
        observer.disconnect()
      }
    })

    return {
      loading,
      loaded,
      error,
      imageRef,
      containerStyle,
      placeholderStyle,
      imageClass,
      onLoad,
      onError
    }
  }
}
</script>

<style scoped>
.lazy-image {
  overflow: hidden;
  border-radius: 4px;
}

.lazy-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.lazy-image-loaded {
  opacity: 1;
}

.loading-placeholder,
.error-placeholder,
.placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

.error-placeholder {
  color: #ff4d4f;
}

.error-placeholder span {
  font-size: 12px;
}

.placeholder {
  color: #bfbfbf;
}
</style>
