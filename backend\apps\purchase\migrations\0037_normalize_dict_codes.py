# Generated manually to normalize dictionary codes to lowercase

from django.db import migrations


def normalize_dict_codes(apps, schema_editor):
    """将所有字典编码统一为小写格式"""
    Dictionary = apps.get_model('purchase', 'Dictionary')
    PurchaseRequest = apps.get_model('purchase', 'PurchaseRequest')
    
    # 字典编码映射（大写 -> 小写）
    code_mappings = {
        # 状态字典
        'DRAFT': 'draft',
        'PENDING_APPROVAL': 'pending_approval',
        'APPROVED': 'approved',
        'REJECTED': 'rejected',
        'PENDING_PURCHASE': 'pending_purchase',
        'PURCHASED': 'purchased',
        'RETURNED': 'returned',
        'PENDING_ACCEPTANCE': 'pending_acceptance',
        'ACCEPTED': 'accepted',
        'PENDING_REIMBURSEMENT': 'pending_reimbursement',
        'SETTLED': 'settled',
        
        # 采购类型字典
        'UNIFIED': 'unified',
        'SELF': 'self',
        
        # 其他可能的大写编码
        'PUBLIC_BIDDING': 'public_bidding',
        'INVITED_BIDDING': 'invited_bidding',
        'COMPETITIVE_NEGOTIATION': 'competitive_negotiation',
        'SINGLE_SOURCE': 'single_source',
        'INQUIRY': 'inquiry',
        'DIRECT_PURCHASE': 'direct_purchase',
    }
    
    # 1. 更新字典表中的编码
    print("正在更新字典表中的编码...")
    for old_code, new_code in code_mappings.items():
        updated_count = Dictionary.objects.filter(code=old_code).update(code=new_code)
        if updated_count > 0:
            print(f"  更新字典编码: {old_code} -> {new_code} ({updated_count}条)")
    
    # 2. 更新采购需求表中的字典字段
    print("正在更新采购需求表中的字典字段...")
    
    # 更新状态字段
    for old_code, new_code in code_mappings.items():
        updated_count = PurchaseRequest.objects.filter(status=old_code).update(status=new_code)
        if updated_count > 0:
            print(f"  更新状态字段: {old_code} -> {new_code} ({updated_count}条)")
    
    # 更新采购类型字段
    for old_code, new_code in code_mappings.items():
        updated_count = PurchaseRequest.objects.filter(purchase_type=old_code).update(purchase_type=new_code)
        if updated_count > 0:
            print(f"  更新采购类型字段: {old_code} -> {new_code} ({updated_count}条)")
    
    # 更新其他字典字段
    dict_fields = [
        'item_category', 'unit', 'procurement_method', 
        'fund_project', 'requirement_source'
    ]
    
    for field in dict_fields:
        for old_code, new_code in code_mappings.items():
            filter_kwargs = {field: old_code}
            update_kwargs = {field: new_code}
            updated_count = PurchaseRequest.objects.filter(**filter_kwargs).update(**update_kwargs)
            if updated_count > 0:
                print(f"  更新{field}字段: {old_code} -> {new_code} ({updated_count}条)")
    
    print("字典编码标准化完成！")


def reverse_normalize_dict_codes(apps, schema_editor):
    """回滚操作：将小写编码转换回大写（如果需要）"""
    # 这里可以实现回滚逻辑，但通常不需要
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0036_remove_old_type_fields'),
    ]

    operations = [
        migrations.RunPython(
            normalize_dict_codes,
            reverse_normalize_dict_codes,
        ),
    ]
