// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import routes from './routes'
import store from '@/store'

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 优化的路由守卫 - 减少延迟和提高性能
let isAddingRoutes = false
let routeInitPromise = null

// 白名单路由（不需要动态路由的页面）
const whiteList = ['/login', '/dashboard', '/404', '/403']

router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('token')

  // 登录页面处理
  if (to.path === '/login') {
    if (token) {
      next('/dashboard')
    } else {
      next()
    }
    return
  }

  // 需要认证的页面
  if (!token) {
    next('/login')
    return
  }

  // 白名单页面直接放行
  if (whiteList.includes(to.path)) {
    next()
    return
  }

  // 已登录，检查动态路由状态
  const hasRoutes = store.getters['permission/hasRoutes']

  if (hasRoutes) {
    // 路由已加载，直接放行
    next()
    return
  }

  // 如果正在初始化路由，等待完成
  if (isAddingRoutes && routeInitPromise) {
    try {
      await routeInitPromise
      next({ ...to, replace: true })
    } catch (error) {
      next('/dashboard')
    }
    return
  }

  // 开始初始化路由
  isAddingRoutes = true
  // 开始初始化动态路由

  // 创建路由初始化Promise，避免重复初始化
  routeInitPromise = (async () => {
    try {
      // 1. 尝试快速恢复路由（优先级最高）
      const restored = await store.dispatch('permission/restoreRoutes', router)
      if (restored) {
        // 快速恢复路由成功
        return true
      }

      // 2. 从服务器获取并生成路由
      const success = await store.dispatch('permission/generateRoutes', router)

      if (success) {
        // 动态路由生成成功，同时获取用户权限
        try {
          await store.dispatch('getUserPermissions')
          console.log('✅ 路由初始化时权限获取成功')
        } catch (error) {
          console.warn('⚠️ 路由初始化时权限获取失败:', error)
          // 权限获取失败不影响路由初始化
        }
        return true
      } else {
        // 动态路由生成失败
        return false
      }
    } catch (error) {
      // 路由初始化异常：清除无效token
      localStorage.removeItem('token')
      store.commit('permission/RESET_PERMISSION')
      throw error
    }
  })()

  try {
    const success = await routeInitPromise
    if (success) {
      next({ ...to, replace: true })
    } else {
      next('/dashboard')
    }
  } catch (error) {
    next('/login')
  } finally {
    isAddingRoutes = false
    routeInitPromise = null
  }
})

// 路由后置守卫
router.afterEach((to) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 采购管理系统`
  } else {
    document.title = '采购管理系统'
  }
})

export default router