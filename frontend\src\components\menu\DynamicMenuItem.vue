<template>
  <!-- 如果有子菜单，渲染为子菜单 -->
  <a-sub-menu 
    v-if="hasChildren" 
    :key="menuKey"
    :title="menu.menu_name"
    :class="menuClass"
  >
    <template #icon>
      <component :is="iconComponent" v-if="iconComponent" />
    </template>
    
    <template #title>
      <span class="menu-title">{{ menu.menu_name }}</span>
      <a-badge 
        v-if="badgeCount > 0" 
        :count="badgeCount" 
        :offset="[10, 0]"
        class="menu-badge"
      />
    </template>
    
    <!-- 递归渲染子菜单 -->
    <DynamicMenuItem
      v-for="child in visibleChildren"
      :key="child.menu_code || child.route_path"
      :menu="child"
      :level="level + 1"
      @menu-click="handleChildMenuClick"
    />
  </a-sub-menu>
  
  <!-- 如果没有子菜单，渲染为菜单项 -->
  <a-menu-item 
    v-else 
    :key="menuKey"
    :class="menuClass"
    @click="handleMenuClick"
  >
    <template #icon>
      <component :is="iconComponent" v-if="iconComponent" />
    </template>
    
    <span class="menu-title">{{ menu.menu_name }}</span>
    <a-badge 
      v-if="badgeCount > 0" 
      :count="badgeCount" 
      :offset="[10, 0]"
      class="menu-badge"
    />
  </a-menu-item>
</template>

<script>
import { computed, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'

export default {
  name: 'DynamicMenuItem',
  props: {
    menu: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    }
  },
  emits: ['menu-click'],
  setup(props, { emit }) {
    const store = useStore()

    // 计算属性
    const menuKey = computed(() => {
      return props.menu.menu_code || props.menu.route_path || `menu-${Date.now()}`
    })

    const hasChildren = computed(() => {
      return props.menu.children && props.menu.children.length > 0
    })

    const visibleChildren = computed(() => {
      if (!hasChildren.value) return []
      
      // 过滤隐藏的子菜单
      return props.menu.children.filter(child => !child.hidden)
    })

    const menuClass = computed(() => {
      const classes = [`menu-level-${props.level}`]
      
      if (props.menu.hidden) {
        classes.push('menu-hidden')
      }
      
      if (props.level === 0) {
        classes.push('menu-root')
      }
      
      return classes.join(' ')
    })

    // 动态加载图标组件
    const iconComponent = computed(() => {
      if (!props.menu.icon) return null
      
      try {
        // 支持Ant Design Vue图标
        return defineAsyncComponent(() => 
          import('@ant-design/icons-vue').then(icons => {
            const iconName = props.menu.icon
            const IconComponent = icons[iconName]
            if (IconComponent) {
              return IconComponent
            } else {
              console.warn(`图标组件未找到: ${iconName}`)
              return icons.MenuOutlined // 默认图标
            }
          })
        )
      } catch (error) {
        console.warn('图标加载失败:', props.menu.icon, error)
        return null
      }
    })

    // 获取菜单徽章数量（待处理任务数）
    const badgeCount = computed(() => {
      // 这里可以根据菜单的business_status获取对应的待处理数量
      // 暂时返回0，后续可以集成实际的统计数据
      if (props.menu.business_status) {
        // 示例：根据业务状态获取待处理数量
        const pendingCounts = store.getters.pendingCounts || {}
        return pendingCounts[props.menu.business_status] || 0
      }
      return 0
    })

    // 方法
    const handleMenuClick = () => {
      console.log('菜单项点击:', props.menu.menu_name, props.menu.route_path)
      
      // 发射事件给父组件
      emit('menu-click', props.menu)
    }

    const handleChildMenuClick = (childMenu) => {
      // 传递子菜单点击事件
      emit('menu-click', childMenu)
    }

    return {
      menuKey,
      hasChildren,
      visibleChildren,
      menuClass,
      iconComponent,
      badgeCount,
      handleMenuClick,
      handleChildMenuClick
    }
  }
}
</script>

<style scoped>
.menu-title {
  display: inline-block;
  vertical-align: middle;
}

.menu-badge {
  margin-left: 8px;
}

/* 不同层级的菜单样式 */
.menu-level-0 {
  font-weight: 500;
}

.menu-level-1 {
  padding-left: 24px !important;
}

.menu-level-2 {
  padding-left: 48px !important;
}

/* 隐藏菜单的样式 */
.menu-hidden {
  display: none;
}

/* 根菜单样式 */
.menu-root :deep(.ant-menu-submenu-title) {
  font-weight: 500;
  color: #333;
}

.menu-root :deep(.ant-menu-submenu-title):hover {
  color: #1890ff;
}

/* 菜单项悬停效果 */
:deep(.ant-menu-item):hover,
:deep(.ant-menu-submenu-title):hover {
  color: #1890ff;
  background-color: #f0f9ff;
}

/* 选中状态样式 */
:deep(.ant-menu-item-selected) {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
}

:deep(.ant-menu-item-selected::after) {
  border-right: 3px solid #1890ff;
}

/* 图标样式 */
:deep(.anticon) {
  font-size: 16px;
  margin-right: 8px;
  vertical-align: middle;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .menu-level-1 {
    padding-left: 16px !important;
  }
  
  .menu-level-2 {
    padding-left: 32px !important;
  }
}

/* 折叠状态下的样式 */
.ant-menu-inline-collapsed .menu-title {
  display: none;
}

.ant-menu-inline-collapsed .menu-badge {
  display: none;
}
</style>
