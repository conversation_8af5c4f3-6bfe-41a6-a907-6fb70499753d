<template>
  <div class="notification-center">
    <!-- 通知铃铛图标 -->
    <a-badge :count="unreadCount" :offset="[10, 0]">
      <a-button type="text" shape="circle" size="large" @click.stop="toggleNotificationPanel"
        :class="{ 'notification-btn-active': panelVisible }">
        <BellOutlined />
      </a-button>
    </a-badge>

    <!-- 通知面板 -->
    <div v-if="panelVisible" class="notification-panel" v-click-outside="closeNotificationPanel">
      <div class="notification-header">

        <span class="notification-title">通知中心</span>
        <a-space>
          <a-button type="link" size="small" @click="markAllAsRead" :disabled="unreadCount === 0">
            全部已读
          </a-button>
          <a-button type="link" size="small" @click="closeNotificationPanel">
            <CloseOutlined />
          </a-button>
        </a-space>
      </div>

      <div class="notification-content">
        <a-spin :spinning="loading">
          <div v-if="notifications.length === 0" class="notification-empty">
            <a-empty description="暂无通知" />
          </div>
          <div v-else class="notification-list">
            <div v-for="notification in notifications" :key="notification.id" class="notification-item"
              :class="{ 'notification-unread': !notification.is_read }" @click="handleNotificationClick(notification)">+
              <div class="notification-item-header">
                <span class="notification-item-title">{{ notification.title }}</span>
                <span class="notification-item-time">{{ notification.time_ago }}</span>
              </div>
              <div class="notification-item-content">{{ notification.content }}</div>
              <div class="notification-item-footer">
                <a-tag :color="getNotificationTypeColor(notification.notification_type)" size="small">
                  {{ notification.notification_type_display }}
                </a-tag>
                <a-tag v-if="notification.priority !== 'normal'" :color="getPriorityColor(notification.priority)"
                  size="small">
                  {{ notification.priority_display }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-spin>
      </div>

      <div class="notification-footer">
        <a-button type="link" size="small" @click.stop="goToNotificationManagement">
          查看全部通知
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * 通知中心组件
 *
 * 功能说明：
 * 1. 实时显示系统通知和消息
 * 2. 集成WebSocket实现消息推送
 * 3. 提供通知列表和详情查看
 * 4. 支持通知标记和批量操作
 *
 * 技术特点：
 * - 基于WebSocket的实时通信
 * - 响应式的通知状态管理
 * - 优雅的UI交互和动画效果
 * - 完善的错误处理和重连机制
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-08
 */
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { BellOutlined, CloseOutlined } from '@ant-design/icons-vue'
import api from '@/api'
import wsManager from '@/utils/websocket'

const router = useRouter()

// 响应式数据
const panelVisible = ref(false)
const loading = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const reconnectTimer = ref(null)

// 通知类型颜色映射
const getNotificationTypeColor = (type) => {
  const colors = {
    'approval': 'blue',
    'purchase': 'green',
    'acceptance': 'orange',
    'reimbursement': 'purple',
    'rejection': 'red',
    'return': 'volcano',
    'system': 'default'
  }
  return colors[type] || 'default'
}

// 优先级颜色映射
const getPriorityColor = (priority) => {
  const colors = {
    'low': 'default',
    'normal': 'blue',
    'high': 'orange',
    'urgent': 'red'
  }
  return colors[priority] || 'default'
}

// 切换通知面板
const toggleNotificationPanel = () => {
  panelVisible.value = !panelVisible.value
  if (panelVisible.value) {
    getNotifications()
  }
}

// 关闭通知面板
const closeNotificationPanel = () => {
  panelVisible.value = false
}

// 获取通知列表
const getNotifications = async () => {
  loading.value = true
  try {
    const response = await api.notifications.getList({ page_size: 10 })
    if (response.code === 200) {
      notifications.value = response.data.results || []
    }
  } catch (error) {
    console.error('获取通知失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取未读通知数量
const getUnreadCount = async () => {
  try {
    const response = await api.notifications.getUnreadCount()
    if (response.code === 200) {
      unreadCount.value = response.data.unread_count || 0
    }
  } catch (error) {
    console.error('获取未读通知数量失败:', error)
  }
}

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    const response = await api.notifications.markAllAsRead()
    if (response.code === 200) {
      message.success(response.message)
      notifications.value.forEach(n => {
        n.is_read = true
      })
      unreadCount.value = 0
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    message.error('标记已读失败')
  }
}

// 处理通知点击
const handleNotificationClick = async (notification) => {
  // 标记为已读
  if (!notification.is_read) {
    try {
      await api.notifications.markAsRead(notification.id)
      notification.is_read = true
      unreadCount.value = Math.max(0, unreadCount.value - 1)
    } catch (error) {
      console.error('标记已读失败:', error)
    }
  }

  // 跳转到通知管理页面并打开详情
  closeNotificationPanel()
  router.push({
    path: '/userInfo/notifications',
    query: {
      detail: notification.id,
      from: 'notification-center'
    }
  })
}

// 跳转到通知管理页面
const goToNotificationManagement = () => {
  closeNotificationPanel()
  router.push('/userInfo/notifications')
}

// 定时刷新未读通知数量
let refreshTimer = null

const startRefreshTimer = () => {
  refreshTimer = setInterval(() => {
    getUnreadCount()
  }, 30000) // 每30秒刷新一次
}

const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// WebSocket事件处理
const handleNewNotification = (notification) => {
  // 添加新通知到列表顶部
  notifications.value.unshift(notification)
  // 更新未读数量
  if (!notification.is_read) {
    unreadCount.value++
    // 播放通知提示音
    const audio = new Audio('/notification-sound.mp3')
    audio.play().catch(() => {
      // 静默处理音频播放失败
    })
    // 桌面通知
    if (Notification.permission === 'granted') {
      new Notification(notification.title, { body: notification.content })
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification(notification.title, { body: notification.content })
        }
      })
    }
  }
  // 显示通知提示
  message.success({
    content: `新通知：${notification.title}`,
    duration: 3,
    key: 'new-notification'
  })
}

const handleWebSocketConnected = () => {
  getUnreadCount()
  // 清除重连计时器
  if (reconnectTimer.value) {
    clearInterval(reconnectTimer.value)
    reconnectTimer.value = null
  }
}

const handleWebSocketDisconnected = () => {
  message.warning('通知连接已断开，正在尝试重连...')
  // 实现自动重连逻辑
  if (!reconnectTimer.value) {
    reconnectTimer.value = setInterval(() => {
      if (!wsManager.isConnected()) {
        wsManager.connect()
      }
    }, 5000)
  }
}

// 生命周期
onMounted(() => {
  getUnreadCount()
  startRefreshTimer()

  // 连接WebSocket
  wsManager.connect()
  wsManager.on('notification', handleNewNotification)
  wsManager.on('connected', handleWebSocketConnected)
  wsManager.on('disconnected', handleWebSocketDisconnected)
})

onUnmounted(() => {
  stopRefreshTimer()

  // 清理WebSocket事件监听
  wsManager.off('notification', handleNewNotification)
  wsManager.off('connected', handleWebSocketConnected)
  wsManager.off('disconnected', handleWebSocketDisconnected)
})

// 点击外部关闭面板的指令
const vClickOutside = {
  mounted(el, binding) {
    el.clickOutsideEvent = function (event) {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value()
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}
</script>

<style scoped>
.notification-center {
  position: relative;
}

.notification-btn-active {
  background-color: #f0f0f0;
}

.notification-panel {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 420px;
  max-height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  z-index: 1001;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.notification-title {
  font-weight: 600;
  font-size: 16px;
}

.notification-content {
  max-height: 400px;
  overflow-y: auto;
}

.notification-empty {
  padding: 40px 20px;
  text-align: center;
}

.notification-list {
  padding: 8px 0;
}

.notification-item {
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.notification-item:hover {
  background-color: #f8f9fa;
  transform: translateX(2px);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-unread {
  background: linear-gradient(90deg, #f6ffed 0%, #ffffff 100%);
  border-left: 4px solid #52c41a;
}

.notification-unread::before {
  content: '';
  position: absolute;
  top: 16px;
  right: 16px;
  width: 8px;
  height: 8px;
  background: #52c41a;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.notification-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
}

.notification-item-title {
  font-weight: 500;
  font-size: 14px;
  color: #262626;
  flex: 1;
  margin-right: 8px;
}

.notification-item-time {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
}

.notification-item-content {
  font-size: 13px;
  color: #595959;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  /* 添加标准属性 */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-item-footer {
  display: flex;
  gap: 4px;
}

.notification-footer {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

:deep(.ant-badge-count) {
  font-size: 10px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
}
</style>