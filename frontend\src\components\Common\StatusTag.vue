<template>
  <a-tag
    :color="tagColor"
    :class="tagClass"
    :title="showTooltip ? currentConfig.description : undefined"
  >
    <component :is="iconComponent" v-if="showIcon" />
    {{ displayText }}
    <span v-if="showDescription" class="status-description">
      ({{ currentConfig.description }})
    </span>
  </a-tag>
</template>

<script>
import { computed } from 'vue'
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  FileTextOutlined,
  ShoppingCartOutlined,
  DollarCircleOutlined,
  RollbackOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue'
import { getStatusConfig } from '@/utils/status'

export default {
  name: 'StatusTag',
  components: {
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ExclamationCircleOutlined,
    SyncOutlined,
    FileTextOutlined,
    ShoppingCartOutlined,
    DollarCircleOutlined,
    RollbackOutlined,
    QuestionCircleOutlined
  },
  props: {
    status: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'purchase' // purchase, acceptance, reimbursement
    },
    showIcon: {
      type: Boolean,
      default: true
    },
    showDescription: {
      type: Boolean,
      default: false
    },
    showTooltip: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    // 获取当前状态配置 - 使用统一的状态配置
    const currentConfig = computed(() => {
      // 如果指定了type且不是purchase，则使用旧的配置逻辑保持兼容性
      if (props.type && props.type !== 'purchase') {
        const legacyConfig = {
          acceptance: {
            normal: { color: 'success', text: '正常', icon: 'CheckCircleOutlined' },
            exception: { color: 'error', text: '异常', icon: 'ExclamationCircleOutlined' },
            pending: { color: 'warning', text: '待处理', icon: 'ClockCircleOutlined' }
          },
          reimbursement: {
            pending: { color: 'orange', text: '待审批', icon: 'ClockCircleOutlined' },
            approved: { color: 'blue', text: '已审批', icon: 'CheckCircleOutlined' },
            rejected: { color: 'red', text: '已驳回', icon: 'CloseCircleOutlined' },
            paid: { color: 'success', text: '已支付', icon: 'CheckCircleOutlined' }
          }
        }
        const typeConfig = legacyConfig[props.type]
        return typeConfig?.[props.status] || { color: 'default', text: props.status, icon: 'QuestionCircleOutlined' }
      }

      // 使用统一的状态配置
      return getStatusConfig(props.status)
    })

    // 标签颜色
    const tagColor = computed(() => currentConfig.value.color)

    // 显示文本
    const displayText = computed(() => currentConfig.value.text)

    // 图标组件
    const iconComponent = computed(() => {
      if (!props.showIcon) return null
      return currentConfig.value.icon
    })

    // 标签样式类
    const tagClass = computed(() => {
      return `status-tag status-tag-${props.status}`
    })

    return {
      currentConfig,
      tagColor,
      displayText,
      iconComponent,
      tagClass
    }
  }
}
</script>

<style scoped>
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  border-radius: 6px;
  padding: 2px 8px;
  font-size: 12px;
}

.status-tag :deep(.anticon) {
  font-size: 12px;
}

.status-description {
  font-size: 11px;
  opacity: 0.8;
  margin-left: 4px;
}

/* 特定状态的样式 */
.status-tag-draft {
  background: #f5f5f5;
  color: #666;
  border-color: #d9d9d9;
}

.status-tag-pending,
.status-tag-pending_approval {
  background: #fff7e6;
  color: #d46b08;
  border-color: #ffd591;
}

.status-tag-approved {
  background: #e6f7ff;
  color: #1890ff;
  border-color: #91d5ff;
}

.status-tag-rejected {
  background: #fff2f0;
  color: #f5222d;
  border-color: #ffccc7;
}

.status-tag-purchased {
  background: #e6fffb;
  color: #13c2c2;
  border-color: #87e8de;
}

.status-tag-pending_acceptance {
  background: #f9f0ff;
  color: #722ed1;
  border-color: #d3adf7;
}

.status-tag-accepted {
  background: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}

.status-tag-pending_reimbursement {
  background: #fffbe6;
  color: #faad14;
  border-color: #ffe58f;
}

.status-tag-reimbursed {
  background: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}
</style>
