/* 商务风格配置面板样式 */

/* 主容器样式 */
.business-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.business-panel .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.business-panel .ant-modal-header {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.business-panel .ant-modal-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.business-panel .ant-modal-close {
  color: #ffffff;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.business-panel .ant-modal-close:hover {
  opacity: 1;
}

.business-panel .ant-modal-body {
  padding: 0;
  background: #ffffff;
}

/* 标签页样式 */
.business-tabs {
  background: #ffffff;
}

.business-tabs .ant-tabs-nav {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  margin: 0;
  padding: 0 24px;
  border-bottom: 2px solid #e2e8f0;
}

.business-tabs .ant-tabs-tab {
  background: transparent;
  border: none;
  margin: 0 8px 0 0;
  padding: 16px 24px;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.business-tabs .ant-tabs-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.business-tabs .ant-tabs-tab:hover::before {
  opacity: 1;
}

.business-tabs .ant-tabs-tab-active {
  background: #ffffff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.business-tabs .ant-tabs-tab-active::before {
  opacity: 0;
}

.business-tabs .ant-tabs-tab-btn {
  color: #64748b;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.business-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1e40af;
  font-weight: 600;
}

.business-tabs .ant-tabs-content-holder {
  padding: 24px;
  min-height: 400px;
}

/* 字段分类样式 */
.field-category-section {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.field-category-section:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.1);
}

.field-category-section:last-child {
  margin-bottom: 0;
}

.category-title {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #1e40af;
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
}

.category-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

.category-fields {
  padding: 16px;
}

.category-fields .ant-checkbox-wrapper {
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.category-fields .ant-checkbox-wrapper:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
}

.category-fields .ant-checkbox-wrapper-checked {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #3b82f6;
}

.category-fields .ant-checkbox {
  margin-right: 8px;
}

.category-fields .ant-checkbox-checked .ant-checkbox-inner {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border-color: #3b82f6;
}

/* 全选控件样式 */
.select-all-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.select-all-section .ant-checkbox-wrapper {
  font-weight: 600;
  color: #1e40af;
}

/* 筛选条件样式 */
.filter-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
}

.filter-section h4 {
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.filter-section .ant-form-item-label > label {
  color: #374151;
  font-weight: 500;
}

.filter-section .ant-select,
.filter-section .ant-input,
.filter-section .ant-picker {
  border-radius: 6px;
  border-color: #d1d5db;
  transition: all 0.3s ease;
}

.filter-section .ant-select:hover,
.filter-section .ant-input:hover,
.filter-section .ant-picker:hover {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.filter-section .ant-select-focused,
.filter-section .ant-input:focus,
.filter-section .ant-picker-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 预览区域样式 */
.preview-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.preview-header h4 {
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.preview-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.preview-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #d1d5db;
}

.preview-table .ant-table-tbody > tr:hover > td {
  background: #f8fafc;
}

.preview-summary {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

/* 操作按钮样式 */
.action-buttons {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  text-align: right;
}

.action-buttons .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  height: 40px;
  padding: 0 24px;
  margin-left: 12px;
  transition: all 0.3s ease;
}

.action-buttons .ant-btn-default {
  border-color: #d1d5db;
  color: #6b7280;
}

.action-buttons .ant-btn-default:hover {
  border-color: #9ca3af;
  color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons .ant-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.action-buttons .ant-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 字段筛选面板样式 */
.filter-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 20px;
}

.filter-panel .ant-card-head {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  border-bottom: none;
  padding: 0;
}

.filter-panel .ant-card-head-title {
  color: #ffffff;
  font-weight: 600;
  padding: 16px 20px;
}

.filter-panel .ant-card-body {
  background: #ffffff;
  padding: 20px;
}

.filter-panel .filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.filter-panel .filter-item {
  min-width: 200px;
  flex: 1;
}

.filter-panel .filter-item .ant-form-item-label > label {
  color: #374151;
  font-weight: 500;
  margin-bottom: 4px;
}

.filter-panel .filter-actions {
  display: flex;
  gap: 8px;
  align-items: flex-end;
  margin-top: 16px;
}

.filter-panel .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  height: 36px;
  padding: 0 16px;
  transition: all 0.3s ease;
}

.filter-panel .ant-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.filter-panel .ant-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.filter-panel .ant-btn-default {
  border-color: #d1d5db;
  color: #6b7280;
}

.filter-panel .ant-btn-default:hover {
  border-color: #9ca3af;
  color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 字段筛选控件样式 */
.column-filter-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.column-filter-panel .ant-card-head {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  border-bottom: none;
  padding: 0;
}

.column-filter-panel .ant-card-head-title {
  color: #ffffff;
  font-weight: 600;
  padding: 16px 20px;
}

.column-filter-panel .ant-card-body {
  background: #ffffff;
  padding: 20px;
}

.column-filter-panel .preset-section {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex ;
  justify-content: space-between;
  align-items: center;
}
.column-filter-panel .filter-tip{
  padding: 20px 0 0 20px;
}

.column-filter-panel .preset-section .ant-dropdown-trigger {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 16px;
  color: #374151;
  font-weight: 500;
  transition: all 0.3s ease;
}

.column-filter-panel .preset-section .ant-dropdown-trigger:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

/* 移除响应式设计，保持固定布局 */

/* 筛选控件标签样式 */
.filter-item-with-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  font-weight: 500;
}
