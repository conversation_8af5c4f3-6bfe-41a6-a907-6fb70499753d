# Generated by Django 5.2.3 on 2025-07-01 10:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0023_add_shipping_origin_field'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AcceptancePhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('photo', models.ImageField(help_text='验收照片', upload_to='acceptance_photos/%Y/%m/', verbose_name='照片')),
                ('photo_type', models.CharField(choices=[('courier', '快递单照片'), ('item', '物品照片'), ('package', '包装照片')], default='item', max_length=20, verbose_name='照片类型')),
                ('description', models.CharField(blank=True, help_text='照片的描述信息', max_length=200, verbose_name='照片描述')),
                ('upload_time', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('purchase_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='acceptance_photos_new', to='purchase.purchaserequest', verbose_name='采购需求')),
                ('uploader', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='上传者')),
            ],
            options={
                'verbose_name': '验收照片',
                'verbose_name_plural': '验收照片',
                'db_table': 'purchase_acceptance_photo',
                'ordering': ['upload_time'],
                'indexes': [models.Index(fields=['purchase_request'], name='idx_photo_request'), models.Index(fields=['photo_type'], name='idx_photo_type'), models.Index(fields=['upload_time'], name='idx_photo_upload_time')],
            },
        ),
    ]
