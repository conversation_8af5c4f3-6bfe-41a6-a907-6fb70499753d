<template>
  <div class="price-trend-chart">
    <a-card title="历史价格趋势" size="small">
      <template #extra>
        <a-space>
          <a-button
            type="link"
            size="small"
            @click="refreshData"
            :loading="loading"
          >
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </template>

      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>

      <div v-else-if="!hasData" class="no-data-container">
        <a-empty description="暂无历史采购数据" />
      </div>

      <div v-else>
        <!-- 统计信息 -->
        <div class="statistics-row">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="平均单价"
                :value="statistics.avg_price"
                :precision="2"
                prefix="¥"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="最高单价"
                :value="statistics.max_price"
                :precision="2"
                prefix="¥"
                :value-style="{ color: '#f5222d' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="最低单价"
                :value="statistics.min_price"
                :precision="2"
                prefix="¥"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="采购次数"
                :value="statistics.purchase_count"
                suffix="次"
                :value-style="{ color: '#722ed1' }"
              />
            </a-col>
          </a-row>
        </div>

        <!-- 价格趋势图 -->
        <div class="chart-container" ref="chartContainer">
          <canvas ref="chartCanvas"></canvas>
        </div>

        <!-- 价格预警 -->
        <div v-if="currentPrice && isPriceWarning" class="price-warning">
          <a-alert
            type="warning"
            show-icon
            :message="`价格预警：当前单价 ¥${currentPrice} 高于历史平均价格20%以上`"
            :description="`历史平均价格：¥${statistics.avg_price.toFixed(2)}，建议重新评估采购价格的合理性。`"
          />
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import Chart from 'chart.js/auto';
import api from '@/api';

const props = defineProps({
  itemName: {
    type: String,
    required: true
  },
  deptId: {
    type: [String, Number],
    required: true
  },
  currentPrice: {
    type: Number,
    default: null
  }
});

// 状态
const loading = ref(false);
const historyData = ref([]);
const statistics = ref({
  avg_price: 0,
  max_price: 0,
  min_price: 0,
  total_quantity: 0,
  purchase_count: 0
});

// 图表相关
const chartContainer = ref(null);
const chartCanvas = ref(null);
let chartInstance = null;

// 计算属性
const hasData = computed(() => historyData.value.length > 0);

const isPriceWarning = computed(() => {
  if (!props.currentPrice || !statistics.value.avg_price) return false;
  const warningThreshold = statistics.value.avg_price * 1.2;
  return props.currentPrice > warningThreshold;
});

// 获取历史数据
const fetchHistoryData = async () => {
  if (!props.itemName || !props.deptId) return;
  
  try {
    loading.value = true;
    const response = await api.purchase.getHistory({
      item_name: props.itemName,
      dept_id: props.deptId
    });
    
    if (response.code === 200) {
      historyData.value = response.data.history || [];
      statistics.value = response.data.statistics || {};
      
      // 更新图表
      await nextTick();
      updateChart();
    }
  } catch (error) {
    console.error('获取历史数据失败:', error);
    message.error('获取历史数据失败');
  } finally {
    loading.value = false;
  }
};

// 更新图表
const updateChart = () => {
  if (!chartCanvas.value || !hasData.value) return;
  
  // 销毁现有图表
  if (chartInstance) {
    chartInstance.destroy();
  }
  
  const ctx = chartCanvas.value.getContext('2d');
  
  // 准备图表数据
  const labels = historyData.value.map(item => item.date).reverse();
  const prices = historyData.value.map(item => item.unit_price).reverse();
  const quantities = historyData.value.map(item => item.quantity).reverse();
  
  chartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [
        {
          label: '单价 (¥)',
          data: prices,
          borderColor: '#1890ff',
          backgroundColor: 'rgba(24, 144, 255, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          yAxisID: 'y'
        },
        {
          label: '数量',
          data: quantities,
          borderColor: '#52c41a',
          backgroundColor: 'rgba(82, 196, 26, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false,
      },
      plugins: {
        title: {
          display: true,
          text: `${props.itemName} - 近12个月采购趋势`,
          font: {
            size: 16,
            weight: 'bold'
          }
        },
        legend: {
          position: 'top',
        },
        tooltip: {
          callbacks: {
            title: function(context) {
              return `采购时间: ${context[0].label}`;
            },
            label: function(context) {
              if (context.datasetIndex === 0) {
                return `单价: ¥${context.parsed.y.toFixed(2)}`;
              } else {
                return `数量: ${context.parsed.y}`;
              }
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: '采购时间'
          }
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: '单价 (¥)'
          },
          grid: {
            drawOnChartArea: false,
          },
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          title: {
            display: true,
            text: '数量'
          },
          grid: {
            drawOnChartArea: false,
          },
        },
      }
    }
  });
  
  // 添加平均价格线
  if (statistics.value.avg_price > 0) {
    chartInstance.data.datasets.push({
      label: '平均价格',
      data: new Array(labels.length).fill(statistics.value.avg_price),
      borderColor: '#faad14',
      borderWidth: 2,
      borderDash: [5, 5],
      fill: false,
      pointRadius: 0,
      yAxisID: 'y'
    });
    
    chartInstance.update();
  }
};

// 刷新数据
const refreshData = () => {
  fetchHistoryData();
};

// 监听属性变化
watch([() => props.itemName, () => props.deptId], () => {
  fetchHistoryData();
}, { immediate: true });

// 组件挂载
onMounted(() => {
  fetchHistoryData();
});

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy();
  }
});
</script>

<style scoped>
.price-trend-chart {
  margin-bottom: var(--space-lg);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.statistics-row {
  margin-bottom: var(--space-lg);
  padding: var(--space-md);
  background: var(--bg-secondary);
  border-radius: 8px;
}

.chart-container {
  height: 400px;
  margin-bottom: var(--space-md);
}

.price-warning {
  margin-top: var(--space-md);
}

:deep(.ant-statistic-title) {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: 4px;
}

:deep(.ant-statistic-content) {
  font-size: var(--text-lg);
  font-weight: 600;
}

:deep(.ant-card-head) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-hover) 100%);
  color: white;
}

:deep(.ant-card-head-title) {
  color: white;
  font-weight: 600;
}

:deep(.ant-card-extra a) {
  color: white;
}
</style>
