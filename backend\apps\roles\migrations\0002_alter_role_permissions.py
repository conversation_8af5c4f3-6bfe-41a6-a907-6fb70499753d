# Generated by Django 5.2.4 on 2025-07-23 16:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        ("roles", "0001_initial"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="role",
            name="permissions",
            field=models.ManyToManyField(
                blank=True,
                db_table="sys_role_permission",
                related_name="roles",
                to="auth.permission",
                verbose_name="权限",
            ),
        ),
    ]
