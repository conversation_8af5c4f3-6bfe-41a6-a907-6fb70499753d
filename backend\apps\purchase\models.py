"""
采购管理系统 - 数据模型定义

功能说明：
1. 定义采购需求的完整数据结构和业务流程
2. 实现基于状态的业务流程控制和数据验证
3. 提供丰富的业务方法和属性计算
4. 支持完整的采购生命周期管理

技术特点：
- 基于Django ORM的模型设计
- 分阶段的数据验证和业务规则
- 自动计算金额和历史数据更新
- 完善的权限控制和状态管理

<AUTHOR>
@version 1.0.0
@since 2025-01-08
"""
import os
import re
from datetime import datetime
from django.db import models
from django.conf import settings
from django.utils import timezone
from decimal import Decimal
from django.core.exceptions import ValidationError


def chinese_to_pinyin(text):
    """
    简单的中文转拼音函数
    将常见的中文部门名称转换为拼音
    """
    # 常见部门名称的拼音映射
    pinyin_map = {
        '北京': 'beijing',
        '上海': 'shanghai',
        '广州': 'guangzhou',
        '深圳': 'shenzhen',
        '杭州': 'hangzhou',
        '南京': 'nanjing',
        '武汉': 'wuhan',
        '成都': 'chengdu',
        '西安': 'xian',
        '重庆': 'chongqing',
        '天津': 'tianjin',
        '苏州': 'suzhou',
        '青岛': 'qingdao',
        '长沙': 'changsha',
        '大连': 'dalian',
        '厦门': 'xiamen',
        '福州': 'fuzhou',
        '济南': 'jinan',
        '郑州': 'zhengzhou',
        '沈阳': 'shenyang',
        '哈尔滨': 'haerbin',
        '长春': 'changchun',
        '石家庄': 'shijiazhuang',
        '太原': 'taiyuan',
        '呼和浩特': 'huhehaote',
        '兰州': 'lanzhou',
        '银川': 'yinchuan',
        '西宁': 'xining',
        '乌鲁木齐': 'wulumuqi',
        '拉萨': 'lasa',
        '昆明': 'kunming',
        '贵阳': 'guiyang',
        '南宁': 'nanning',
        '海口': 'haikou',
        '三亚': 'sanya',
        '总部': 'zongbu',
        '分公司': 'fengongsi',
        '办事处': 'banshichu',
        '营业部': 'yingyebu',
        '销售部': 'xiaoshoubu',
        '技术部': 'jishubu',
        '财务部': 'caiwubu',
        '人事部': 'renshibu',
        '行政部': 'xingzhengbu',
        '市场部': 'shichangbu',
        '研发部': 'yanfabu'
    }

    if not text:
        return 'unknown'

    # 移除特殊字符，只保留中文和字母数字
    clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)

    # 尝试完全匹配
    if clean_text in pinyin_map:
        return pinyin_map[clean_text]

    # 尝试部分匹配
    for chinese, pinyin in pinyin_map.items():
        if chinese in clean_text:
            return pinyin

    # 如果没有匹配，返回简化的英文名称
    return re.sub(r'[^\w]', '', text.lower())[:10] or 'dept'


def get_acceptance_photo_path(instance, filename):
    """
    生成验收照片的存储路径和文件名
    命名规则：{部门拼音}_{需求ID}_{年月日时分秒}_{类型}.{扩展名}
    例如：beijing_123456_20250719143025_front.png
    """
    # 获取文件扩展名
    ext = filename.split('.')[-1].lower() if '.' in filename else 'jpg'

    # 获取当前时间戳
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')

    # 获取需求ID
    request_id = instance.purchase_request.id if instance.purchase_request else 'unknown'

    # 获取部门拼音
    dept_pinyin = 'unknown'
    if instance.purchase_request and instance.purchase_request.hierarchy_path:
        # 从层级路径中提取第一个部门名称
        dept_name = instance.purchase_request.hierarchy_path.split('-')[0].strip()
        dept_pinyin = chinese_to_pinyin(dept_name)

    # 获取照片类型
    photo_type = instance.photo_type or 'item'

    # 生成文件名
    filename = f"{dept_pinyin}_{request_id}_{timestamp}_{photo_type}.{ext}"

    # 返回完整路径
    return f"acceptance_photos/{datetime.now().year}/{datetime.now().month:02d}/{filename}"


class PurchaseRequest(models.Model):
    """
    采购需求表 - 重构后的规范化表结构
    对应数据库表：purchase_purchase_request

    业务流程：
    1. 需求提报：draft → pending_approval
    2. 需求审核：pending_approval → approved/rejected
    3. 物资采购：approved → pending_purchase → purchased
    4. 物资验收：purchased → pending_acceptance → accepted
    5. 结算报销：accepted → pending_reimbursement → settled

    表结构特点：
    - 字段按业务逻辑分组排列
    - 统一的命名规范
    - 完整的外键约束
    - 优化的索引配置
    """

    # 统一的状态定义 - 与前端status.js保持一致
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('pending_approval', '待审批'),
        ('approved', '已审批'),
        ('rejected', '已驳回'),
        ('pending_purchase', '待采购'),
        ('purchased', '已采购'),
        ('returned', '已退回'),
        ('pending_acceptance', '待验收'),
        ('accepted', '已验收'),
        ('pending_reimbursement', '待结算'),
        ('settled', '已结算')
    ]

    # 采购类型选择
    PURCHASE_TYPE_CHOICES = [
        ('unified', '统一采购'),
        ('self', '自行采购'),
    ]

    # ==================== A. 基础需求信息 (Basic Request Info) ====================
    # 物品信息
    item_category = models.CharField('物品种类', max_length=50, help_text='物品分类')
    item_name = models.CharField('物品名称', max_length=100, help_text='具体物品名称')
    specification = models.CharField('规格型号', max_length=200, blank=True, default='', help_text='物品的详细规格')
    unit = models.CharField('计量单位', max_length=20, help_text='如：个、台、套、箱等')

    # 1. 预算阶段：预算单价，需求数量，预算金额（预算单价与需求数量的乘积）
    budget_quantity = models.PositiveIntegerField('需求数量', help_text='申请采购的数量')
    budget_unit_price = models.DecimalField('预算单价', max_digits=12, decimal_places=2, help_text='预估的单价（元）')
    budget_total_amount = models.DecimalField('预算金额', max_digits=15, decimal_places=2, null=True, blank=True, help_text='预算单价 × 需求数量')

    # 需求来源和采购信息
    requirement_source = models.CharField('需求来源', max_length=100, help_text='需求产生的原因或背景')
    procurement_method = models.CharField('采购方式', max_length=50, help_text='如：公开招标、询价采购、直接采购等')
    fund_project = models.CharField('经费项目', max_length=100, help_text='资金来源项目名称')
    purchase_type = models.CharField('采购类型', max_length=20, choices=PURCHASE_TYPE_CHOICES, default='unified', help_text='统一采购或自行采购')
    remarks = models.TextField('需求备注', blank=True, help_text='补充说明信息')

    # ==================== B. 组织信息 (Organization Info) ====================
    dept_id = models.PositiveIntegerField('申请部门ID', help_text='申请部门的ID')
    hierarchy_path = models.CharField('部门层级路径', max_length=200, help_text='例：分公司-办事处-科室')
    requester = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.RESTRICT,
        related_name='purchase_requests',
        verbose_name='申请人',
        help_text='需求提报人'
    )

    # ==================== C. 流程状态 (Process Status) ====================
    status = models.CharField('当前状态', max_length=30, choices=STATUS_CHOICES, default='draft', help_text='当前业务状态')
    order_number = models.CharField('采购订单号', max_length=50, blank=True, unique=True, help_text='审批通过后自动生成')
    submission_date = models.DateTimeField('提交时间', null=True, blank=True, help_text='提交审批的时间')

    # ==================== D. 审批信息 (Approval Info) ====================
    approver = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='approved_requests',
        verbose_name='审批人',
        null=True,
        blank=True,
        help_text='最终审批人'
    )
    approved_at = models.DateTimeField('审批时间', null=True, blank=True, help_text='审批完成时间')
    approval_comment = models.TextField('审批意见', blank=True, help_text='审批时的意见或建议')
    rejection_reason = models.TextField('驳回原因', blank=True, help_text='审批驳回时的详细原因')

    # ==================== E. 采购执行信息 (Purchase Execution) ====================
    purchaser = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='purchased_requests',
        verbose_name='采购人',
        null=True,
        blank=True,
        help_text='负责采购的人员'
    )
    purchase_date = models.DateTimeField('采购完成时间', null=True, blank=True, help_text='采购完成的时间')
    # 2. 采购阶段：采购单价，采购数量，采购金额（采购单价与采购数量的乘积）
    purchase_quantity = models.PositiveIntegerField('采购数量', default=0, help_text='实际采购的数量')
    purchase_unit_price = models.DecimalField('采购单价', max_digits=12, decimal_places=2, null=True, blank=True, help_text='实际采购单价')
    purchase_total_amount = models.DecimalField('采购金额', max_digits=15, decimal_places=2, null=True, blank=True, help_text='采购单价 × 采购数量')
    supplier_name = models.CharField('供应商名称', max_length=200, blank=True, help_text='实际供应商')
    purchase_remarks = models.TextField('采购备注', blank=True, help_text='采购过程中的备注信息')
    shipping_date = models.DateTimeField('发货时间', null=True, blank=True, help_text='供应商发货时间')



    # ==================== F. 验收信息 (Acceptance Info) ====================
    acceptor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='accepted_requests',
        verbose_name='验收人',
        null=True,
        blank=True,
        help_text='负责验收的人员'
    )
    acceptance_date = models.DateTimeField('验收完成时间', null=True, blank=True, help_text='验收完成的时间')
    # 3. 验收阶段：验收数量
    acceptance_quantity = models.PositiveIntegerField('验收数量', default=0, help_text='实际验收确认的数量')
    quantity_variance = models.IntegerField('数量差异', default=0, help_text='验收数量 - 采购数量')
    variance_rate = models.DecimalField('差异率百分比', max_digits=5, decimal_places=2, default=0, help_text='数量差异的百分比')

    # 物流信息
    courier_company = models.CharField('快递公司', max_length=100, blank=True, help_text='物流配送公司')
    tracking_number = models.CharField('快递单号', max_length=100, blank=True, help_text='物流跟踪号码')
    shipping_origin = models.CharField('商品发货地', max_length=200, blank=True, help_text='商品发货的地址或城市')

    # 异常处理
    has_exception = models.BooleanField('存在异常', default=False, help_text='验收过程中是否发现异常')
    exception_reason = models.TextField('异常原因', blank=True, help_text='异常的具体描述')
    exception_approved = models.BooleanField('异常已审批', default=False, help_text='异常是否已经审批')
    exception_approver = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='exception_approved_requests',
        verbose_name='异常审批人',
        null=True,
        blank=True,
        help_text='处理异常的审批人'
    )
    exception_approved_at = models.DateTimeField('异常审批时间', null=True, blank=True, help_text='异常审批完成时间')
    acceptance_remarks = models.TextField('验收备注', blank=True, help_text='验收过程中的备注信息')

    # ==================== G. 结算信息 (Settlement Info) ====================
    reimburser = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='reimbursed_requests',
        verbose_name='结算人',
        null=True,
        blank=True,
        help_text='负责结算的财务人员'
    )
    reimbursement_date = models.DateTimeField('结算完成时间', null=True, blank=True, help_text='结算完成的时间')
    # 4. 结算阶段：结算金额（采购单价与验收数量的乘积）
    settlement_amount = models.DecimalField('结算金额', max_digits=15, decimal_places=2, null=True, blank=True, help_text='采购单价 × 验收数量')
    voucher_number = models.CharField('报销凭证号', max_length=50, blank=True, help_text='财务系统凭证号')
    financial_serial = models.CharField('财务流水号', max_length=50, blank=True, help_text='财务系统流水号')
    transaction_number = models.CharField('交易流水号', max_length=50, blank=True, help_text='银行或支付系统的交易流水号')
    payee_name = models.CharField('收款人姓名', max_length=100, blank=True, help_text='收款人户名')
    payee_account = models.CharField('收款账号', max_length=200, blank=True, help_text='收款人银行账号')
    settlement_remarks = models.TextField('结算备注', blank=True, help_text='结算相关的补充说明')

    # ==================== H. 退回信息 (Return Info) ====================
    returner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='returned_requests',
        verbose_name='退回人',
        null=True,
        blank=True,
        help_text='执行退回操作的人员'
    )
    return_date = models.DateTimeField('退回时间', null=True, blank=True, help_text='退回操作的时间')
    return_reason = models.TextField('退回理由', blank=True, help_text='退回到申请人的原因')

    # 财务退回重新验收相关字段
    re_acceptance_required = models.BooleanField('需要重新验收', default=False, help_text='财务退回要求重新验收的标记')
    finance_return_reason = models.TextField('财务退回原因', blank=True, help_text='财务人员退回重新验收的原因')
    finance_returner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='finance_returned_requests',
        verbose_name='财务退回人',
        help_text='执行财务退回操作的用户'
    )
    finance_return_date = models.DateTimeField('财务退回时间', null=True, blank=True, help_text='财务退回的时间')
    re_acceptance_count = models.PositiveIntegerField('重新验收次数', default=0, help_text='记录重新验收的次数')

    # ==================== I. 附件信息 (Attachments) ====================
    requirement_attachment = models.FileField(
        '需求说明文档',
        upload_to='purchase_attachments/%Y/%m/',
        blank=True,
        null=True,
        help_text='支持Excel/PDF格式的需求说明文档'
    )
    approval_attachment = models.FileField(
        '审批附件',
        upload_to='approvals/%Y/%m/',
        blank=True,
        null=True,
        help_text='审批相关的附件文档'
    )
    courier_receipt_photo = models.ImageField(
        '快递单照片',
        upload_to='receipts/%Y/%m/',
        blank=True,
        null=True,
        help_text='快递单据照片'
    )
    item_photo = models.ImageField(
        '物品照片',
        upload_to='items/%Y/%m/',
        blank=True,
        null=True,
        help_text='验收物品照片'
    )

    # ==================== J. 统计分析 (Analytics) ====================
    # 历史价格最小、最大值及平均价都采用采购单价计算
    history_purchase_count = models.PositiveIntegerField('历史采购次数', default=0, help_text='同部门同物品的历史采购次数')
    history_avg_price = models.DecimalField('历史平均采购单价', max_digits=12, decimal_places=2, default=0, help_text='同部门同物品的历史平均采购单价')
    history_max_price = models.DecimalField('历史最高采购单价', max_digits=12, decimal_places=2, default=0, help_text='历史最高采购单价')
    history_min_price = models.DecimalField('历史最低采购单价', max_digits=12, decimal_places=2, default=0, help_text='历史最低采购单价')

    # ==================== K. 兼容性字段 (Compatibility Fields) ====================
    # 为了保持向后兼容，保留旧字段名作为属性
    @property
    def quantity(self):
        """兼容性属性：需求数量"""
        return self.budget_quantity

    @property
    def unit_price(self):
        """兼容性属性：预算单价"""
        return self.budget_unit_price

    @property
    def total_amount(self):
        """兼容性属性：预算金额"""
        return self.budget_total_amount

    @property
    def actual_quantity(self):
        """兼容性属性：采购数量"""
        return self.purchase_quantity

    @property
    def actual_unit_price(self):
        """兼容性属性：采购单价"""
        return self.purchase_unit_price

    @property
    def actual_total_amount(self):
        """兼容性属性：采购金额"""
        return self.purchase_total_amount

    @property
    def accepted_quantity(self):
        """兼容性属性：验收数量"""
        return self.acceptance_quantity

    # ==================== L. 系统字段 (System Fields) ====================
    created_at = models.DateTimeField('创建时间', auto_now_add=True, help_text='记录创建时间')
    updated_at = models.DateTimeField('最后更新时间', auto_now=True, help_text='记录最后更新时间')

    class Meta:
        db_table = 'purchase_purchase_request'
        verbose_name = '采购需求'
        verbose_name_plural = '采购需求管理'
        ordering = ['-created_at']
        default_permissions = ()  # 禁用Django自动权限创建

        indexes = [
            # 核心业务索引
            models.Index(fields=['status'], name='idx_status'),
            models.Index(fields=['dept_id'], name='idx_dept_id'),
            models.Index(fields=['requester'], name='idx_requester_id'),
            models.Index(fields=['created_at'], name='idx_created_at'),
            models.Index(fields=['submission_date'], name='idx_submission_date'),
            models.Index(fields=['item_name'], name='idx_item_name'),

            # 复合索引
            models.Index(fields=['dept_id', 'status'], name='idx_dept_status'),
            models.Index(fields=['requester', 'status'], name='idx_requester_status'),
        ]

        constraints = [
            # 唯一性约束
            models.UniqueConstraint(
                fields=['order_number'],
                condition=models.Q(order_number__isnull=False) & ~models.Q(order_number=''),
                name='unique_order_number'
            ),
        ]

    def __str__(self):
        return f"{self.item_name} - {self.get_status_display()}"

    def clean(self):
        """基于业务流程阶段的数据验证"""
        from django.core.exceptions import ValidationError
        errors = {}

        # 根据当前状态进行分阶段验证
        if self.status in ['draft', 'pending_approval']:
            # 需求提报阶段：只验证需求相关字段
            self._validate_requirement_fields(errors)

        elif self.status in ['approved', 'pending_purchase']:
            # 审批通过阶段：验证需求字段 + 审批字段
            self._validate_requirement_fields(errors)
            self._validate_approval_fields(errors)

        elif self.status in ['purchased', 'returned']:
            # 采购阶段：验证需求字段 + 审批字段 + 采购字段
            self._validate_requirement_fields(errors)
            self._validate_approval_fields(errors)
            self._validate_purchase_fields(errors)

        elif self.status in ['pending_acceptance', 'accepted']:
            # 验收阶段：验证需求字段 + 审批字段 + 采购字段 + 验收字段
            self._validate_requirement_fields(errors)
            self._validate_approval_fields(errors)
            self._validate_purchase_fields(errors)
            self._validate_acceptance_fields(errors)

        elif self.status in ['pending_reimbursement', 'settled']:
            # 结算阶段：验证所有字段
            self._validate_requirement_fields(errors)
            self._validate_approval_fields(errors)
            self._validate_purchase_fields(errors)
            self._validate_acceptance_fields(errors)
            self._validate_settlement_fields(errors)

        elif self.status == 'rejected':
            # 驳回状态：只验证需求字段（可能需要修改）
            self._validate_requirement_fields(errors)

        if errors:
            raise ValidationError(errors)

    def _validate_requirement_fields(self, errors):
        """验证需求提报阶段的字段"""
        # 基础需求信息验证
        if not self.item_name or not self.item_name.strip():
            errors['item_name'] = '物品名称不能为空'

        if not self.specification or not self.specification.strip():
            errors['specification'] = '规格型号不能为空'

        if not self.unit or not self.unit.strip():
            errors['unit'] = '计量单位不能为空'

        if self.budget_quantity is None or self.budget_quantity <= 0:
            errors['budget_quantity'] = '预算数量必须大于0'

        if self.budget_unit_price is None or self.budget_unit_price <= 0:
            errors['budget_unit_price'] = '预算单价必须大于0'

        if not self.requester_id:
            errors['requester'] = '申请人不能为空'

        if not self.dept_id:
            errors['dept'] = '申请部门不能为空'

    def _validate_approval_fields(self, errors):
        """验证审批阶段的字段"""
        if self.status == 'pending_approval' and not self.submission_date:
            errors['submission_date'] = '待审批状态必须有提交时间'

        if self.status == 'approved':
            if not self.approver_id:
                errors['approver'] = '已审批状态必须有审批人'
            if not self.approved_at:
                errors['approved_at'] = '已审批状态必须有审批时间'

    def _validate_purchase_fields(self, errors):
        """验证采购阶段的字段"""
        if self.status == 'purchased':
            if not self.purchaser_id:
                errors['purchaser'] = '已采购状态必须有采购人'
            if not self.purchase_date:
                errors['purchase_date'] = '已采购状态必须有采购时间'
            if self.purchase_quantity is None or self.purchase_quantity <= 0:
                errors['purchase_quantity'] = '采购数量必须大于0'
            if self.purchase_unit_price is None or self.purchase_unit_price <= 0:
                errors['purchase_unit_price'] = '采购单价必须大于0'
            if not self.supplier_name or not self.supplier_name.strip():
                errors['supplier_name'] = '供应商名称不能为空'

    def _validate_acceptance_fields(self, errors):
        """验证验收阶段的字段"""
        if self.status == 'accepted':
            if not self.acceptor_id:
                errors['acceptor'] = '已验收状态必须有验收人'
            if not self.acceptance_date:
                errors['acceptance_date'] = '已验收状态必须有验收时间'
            if self.acceptance_quantity is None or self.acceptance_quantity < 0:
                errors['acceptance_quantity'] = '验收数量不能为负数'

    def _validate_settlement_fields(self, errors):
        """验证结算阶段的字段"""
        if self.status == 'settled':
            if not self.reimburser_id:
                errors['reimburser'] = '已结算状态必须有结算人'
            if not self.reimbursement_date:
                errors['reimbursement_date'] = '已结算状态必须有结算时间'
            if self.settlement_amount is None or self.settlement_amount <= 0:
                errors['settlement_amount'] = '结算金额必须大于0'

    def save(self, *args, **kwargs):
        """保存时自动计算金额和更新历史数据"""
        # 如果是新建且没有订单号，生成订单号
        if not self.pk and not self.order_number:
            self._generate_unique_order_number()

        # 基于业务流程阶段的自动计算
        self._auto_calculate_amounts()

        # 执行基于业务流程的数据验证
        self.full_clean()

        # 更新历史价格数据
        self._update_historical_data()

        super().save(*args, **kwargs)

    def _auto_calculate_amounts(self):
        """基于业务流程阶段自动计算金额"""
        # 1. 预算金额计算（需求提报阶段就需要）
        if self.budget_unit_price is not None and self.budget_quantity is not None:
            self.budget_total_amount = Decimal(str(self.budget_quantity)) * Decimal(str(self.budget_unit_price))
        else:
            # 如果没有单价或数量，设置为0
            self.budget_total_amount = Decimal('0.00')

        # 2. 采购金额计算（只在采购阶段及之后）
        if (self.status in ['purchased', 'pending_acceptance', 'accepted', 'pending_reimbursement', 'settled'] and
            self.purchase_unit_price and self.purchase_quantity):
            self.purchase_total_amount = Decimal(str(self.purchase_quantity)) * self.purchase_unit_price

        # 3. 结算金额计算（只在验收阶段及之后）
        if (self.status in ['accepted', 'pending_reimbursement', 'settled'] and
            self.purchase_unit_price and self.acceptance_quantity and
            self.purchase_unit_price > 0 and self.acceptance_quantity > 0):
            self.settlement_amount = Decimal(str(self.acceptance_quantity)) * self.purchase_unit_price

    def _update_historical_data(self):
        """更新历史价格数据"""
        # 只有在采购完成后才更新历史统计数据
        if (self.status == 'purchased' and self.purchase_unit_price and
            not self.pk):  # 新记录才更新统计
            self.update_history_statistics()

    def update_history_statistics(self):
        """更新历史采购统计数据（基于采购单价）"""
        # 查询同部门同物品的历史采购记录（已采购状态，有采购单价）
        history_requests = PurchaseRequest.objects.filter(
            dept_id=self.dept_id,
            item_name=self.item_name,
            status__in=['purchased', 'pending_acceptance', 'accepted', 'pending_reimbursement', 'settled'],
            purchase_unit_price__isnull=False,
            purchase_unit_price__gt=0
        ).exclude(pk=self.pk if self.pk else None)

        if history_requests.exists():
            # 历史价格最小、最大值及平均价都采用采购单价计算
            prices = [req.purchase_unit_price for req in history_requests]
            quantities = [req.purchase_quantity for req in history_requests]

            self.history_purchase_count = sum(quantities)
            self.history_avg_price = sum(prices) / len(prices)
            self.history_max_price = max(prices)
            self.history_min_price = min(prices)

    @property
    def is_price_warning(self):
        """判断是否触发价格预警（单价高于历史均价20%）"""
        if self.history_avg_price > 0:
            warning_threshold = self.history_avg_price * Decimal('1.2')
            # 使用预算单价进行价格预警判断
            return self.budget_unit_price > warning_threshold
        return False

    def _generate_unique_order_number(self):
        """生成唯一的订单号（内部使用）"""
        from datetime import datetime
        import uuid

        date_str = datetime.now().strftime('%Y%m%d')

        # 使用UUID确保唯一性
        unique_suffix = uuid.uuid4().hex[:8].upper()
        self.order_number = f'PO{date_str}{unique_suffix}'

        # 双重检查确保唯一性
        while PurchaseRequest.objects.filter(order_number=self.order_number).exists():
            unique_suffix = uuid.uuid4().hex[:8].upper()
            self.order_number = f'PO{date_str}{unique_suffix}'

    def generate_order_number(self):
        """生成采购订单号"""
        if not self.order_number:
            from datetime import datetime
            import uuid
            date_str = datetime.now().strftime('%Y%m%d')

            # 使用事务和重试机制避免并发冲突
            from django.db import transaction
            max_retries = 10

            for attempt in range(max_retries):
                try:
                    with transaction.atomic():
                        # 获取当天的订单数量
                        today_count = PurchaseRequest.objects.filter(
                            order_number__startswith=f'PO{date_str}'
                        ).count()

                        # 生成订单号
                        order_number = f'PO{date_str}{today_count + 1:04d}'

                        # 检查是否已存在
                        if not PurchaseRequest.objects.filter(order_number=order_number).exists():
                            self.order_number = order_number
                            self.save(update_fields=['order_number'])
                            break
                        else:
                            # 如果存在，添加随机后缀
                            random_suffix = uuid.uuid4().hex[:4].upper()
                            self.order_number = f'PO{date_str}{today_count + 1:04d}{random_suffix}'
                            self.save(update_fields=['order_number'])
                            break

                except Exception as e:
                    if attempt == max_retries - 1:
                        # 最后一次尝试失败，使用UUID
                        self.order_number = f'PO{date_str}{uuid.uuid4().hex[:8].upper()}'
                        self.save(update_fields=['order_number'])
                        break
                    continue

        return self.order_number

    def submit(self):
        """
        提交审批 - 状态转换：draft → pending_approval
        """
        if self.status == 'draft':
            self.status = 'pending_approval'
            self.submission_date = timezone.now()
            self.save()
            # 发送通知给审批人员
            self._send_notification_to_approvers()
        else:
            raise ValueError(f'当前状态 {self.status} 不能提交审批')

    def approve(self, approver_id, comment=''):
        """
        审批通过 - 状态转换：pending_approval → approved
        """
        if self.status != 'pending_approval':
            raise ValueError(f'当前状态 {self.status} 不能执行审批操作')

        self.status = 'approved'
        self.approver_id = approver_id
        self.approved_at = timezone.now()
        self.approval_comment = comment
        self.generate_order_number()
        self.save()

        # 记录审批日志
        try:
            from apps.system.logging_service import LoggingService
            from apps.authentication.models import User
            approver = User.objects.get(id=approver_id)

            # 获取请求上下文信息
            ip_address = None
            user_agent = None
            try:
                from apps.system.request_context import RequestContext
                ip_address = RequestContext.get_client_ip()
                user_agent = RequestContext.get_user_agent()
            except Exception:
                pass

            LoggingService.log_user_action(
                user=approver,
                log_type='approve',
                action=f'审批通过采购需求: {self.item_name}',
                target_model='PurchaseRequest',
                target_id=self.id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_data={'approval_comment': comment}
            )

            # 发送审批通过通知
            from apps.system.notification_service import notify_approval_passed
            notify_approval_passed(self, approver)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"审批通知发送失败: {e}")

        # 移除自动转换，改为手动生成采购单时转换

    def reject(self, approver_id, reason=''):
        """
        审批驳回 - 状态转换：pending_approval → rejected
        """
        if self.status != 'pending_approval':
            raise ValueError(f'当前状态 {self.status} 不能执行驳回操作')

        self.status = 'rejected'
        self.approver_id = approver_id
        self.approved_at = timezone.now()
        self.rejection_reason = reason
        self.save()

        # 记录驳回日志
        try:
            from apps.system.logging_service import LoggingService
            from apps.authentication.models import User
            approver = User.objects.get(id=approver_id)

            # 获取请求上下文信息
            ip_address = None
            user_agent = None
            try:
                from apps.system.request_context import RequestContext
                ip_address = RequestContext.get_client_ip()
                user_agent = RequestContext.get_user_agent()
            except Exception:
                pass

            LoggingService.log_user_action(
                user=approver,
                log_type='reject',
                action=f'驳回采购需求: {self.item_name}',
                target_model='PurchaseRequest',
                target_id=self.id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_data={'rejection_reason': reason}
            )

            # 发送驳回通知
            from apps.system.notification_service import notify_approval_rejected
            notify_approval_rejected(self, approver, reason)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"驳回通知发送失败: {e}")

    def to_pending_purchase(self):
        """转为待采购状态"""
        if self.status == 'approved':
            self.status = 'pending_purchase'
            self.save()

    def purchase(self, purchaser_id, **kwargs):
        """
        采购完成 - 状态转换：pending_purchase → purchased
        """
        if self.status != 'pending_purchase':
            raise ValueError(f'当前状态 {self.status} 不能执行采购操作')

        # 先更新采购信息，再改变状态
        if 'actual_quantity' in kwargs:
            self.purchase_quantity = kwargs['actual_quantity']
        if 'actual_unit_price' in kwargs:
            self.purchase_unit_price = Decimal(str(kwargs['actual_unit_price']))
        if 'supplier_name' in kwargs:
            self.supplier_name = kwargs['supplier_name']
        if 'purchase_remarks' in kwargs:
            self.purchase_remarks = kwargs['purchase_remarks']

        # 计算采购金额
        if self.purchase_unit_price and self.purchase_quantity:
            self.purchase_total_amount = Decimal(str(self.purchase_quantity)) * Decimal(str(self.purchase_unit_price))

        # 最后设置状态和相关信息
        self.status = 'purchased'
        self.purchaser_id = purchaser_id
        self.purchase_date = timezone.now()

        self.save()

        # 发送采购完成通知
        try:
            from apps.authentication.models import User
            from apps.system.notification_service import notify_purchase_completed
            purchaser = User.objects.get(id=purchaser_id)
            notify_purchase_completed(self, purchaser)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"采购完成通知发送失败: {e}")

    def to_pending_acceptance(self):
        """转为待验收状态"""
        if self.status == 'purchased':
            self.status = 'pending_acceptance'
            self.save()

    def accept(self, acceptor_id, **kwargs):
        """
        验收完成 - 状态转换：pending_acceptance → accepted
        """
        if self.status != 'pending_acceptance':
            raise ValueError(f'当前状态 {self.status} 不能执行验收操作')

        self.status = 'accepted'
        self.acceptor_id = acceptor_id
        self.acceptance_date = timezone.now()

        # 设置验收数量和物流信息
        if 'accepted_quantity' in kwargs:
            self.acceptance_quantity = kwargs['accepted_quantity']
            self.calculate_quantity_variance()
        else:
            self.acceptance_quantity = self.actual_quantity or self.quantity

        if 'courier_company' in kwargs:
            self.courier_company = kwargs['courier_company']
        if 'tracking_number' in kwargs:
            self.tracking_number = kwargs['tracking_number']
        if 'shipping_origin' in kwargs:
            self.shipping_origin = kwargs['shipping_origin']
        if 'acceptance_remarks' in kwargs:
            self.acceptance_remarks = kwargs['acceptance_remarks']
        if 'exception_reason' in kwargs:
            self.exception_reason = kwargs['exception_reason']

        self.save()

        # 发送验收完成通知
        try:
            from apps.authentication.models import User
            from apps.system.notification_service import notify_acceptance_completed
            acceptor = User.objects.get(id=acceptor_id)
            notify_acceptance_completed(self, acceptor)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"验收完成通知发送失败: {e}")

    def to_pending_reimbursement(self):
        """转为待报销审批状态"""
        if self.status == 'accepted':
            self.status = 'pending_reimbursement'
            self.save()

    def settle(self, reimburser_id, **kwargs):
        """
        结算完成 - 状态转换：pending_reimbursement → settled
        """
        if self.status != 'pending_reimbursement':
            raise ValueError(f'当前状态 {self.status} 不能执行结算操作')

        self.status = 'settled'
        self.reimburser_id = reimburser_id
        self.reimbursement_date = timezone.now()

        # 更新结算信息
        if 'settlement_amount' in kwargs:
            self.settlement_amount = kwargs['settlement_amount']
        if 'transaction_number' in kwargs:
            self.transaction_number = kwargs['transaction_number']
        if 'payee_name' in kwargs:
            self.payee_name = kwargs['payee_name']
        if 'settlement_remarks' in kwargs:
            self.settlement_remarks = kwargs['settlement_remarks']

        self.save()

        # 发送结算完成通知
        try:
            from apps.authentication.models import User
            from apps.system.notification_service import notify_reimbursement_completed
            reimburser = User.objects.get(id=reimburser_id)
            notify_reimbursement_completed(self, reimburser)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"结算完成通知发送失败: {e}")

    def reject_reimbursement(self, reason=''):
        """报销驳回"""
        if self.status == 'pending_reimbursement':
            self.status = 'reimbursement_rejected'
            self.rejection_reason = reason
            self.save()

    # ==================== 状态转换验证方法 ====================
    @classmethod
    def get_valid_transitions(cls):
        """获取有效的状态转换映射"""
        return {
            'draft': ['pending_approval'],
            'pending_approval': ['approved', 'rejected'],
            'approved': ['pending_purchase'],
            'rejected': ['draft'],
            'pending_purchase': ['purchased', 'returned'],
            'purchased': ['pending_acceptance'],
            'returned': ['draft'],
            'pending_acceptance': ['accepted'],
            'accepted': ['pending_reimbursement'],
            'pending_reimbursement': ['settled', 'pending_acceptance'],  # 允许财务退回到待验收
            'settled': []
        }

    def can_transition_to(self, target_status):
        """检查是否可以转换到目标状态"""
        valid_transitions = self.get_valid_transitions()
        return target_status in valid_transitions.get(self.status, [])

    def validate_status_transition(self, target_status):
        """验证状态转换是否合法"""
        if not self.can_transition_to(target_status):
            raise ValueError(f'不能从状态 {self.status} 转换到 {target_status}')

    # ==================== 业务计算方法 ====================
    def calculate_quantity_variance(self):
        """计算验收数量差异"""
        if self.acceptance_quantity > 0:
            expected_quantity = self.purchase_quantity or self.budget_quantity
            self.quantity_variance = self.acceptance_quantity - expected_quantity

            # 计算差异率
            if expected_quantity > 0:
                self.variance_rate = round(abs(self.quantity_variance) / expected_quantity * 100, 2)
            else:
                self.variance_rate = 0

            # 判断是否需要异常处理
            variance_rate_float = float(self.variance_rate)
            if variance_rate_float >= 5:  # 差异率≥5%触发异常
                self.has_exception = True
                if not self.exception_reason:
                    self.exception_reason = f'验收数量差异过大：预期{expected_quantity}，实际{self.acceptance_quantity}，差异率{variance_rate_float:.2f}%'
            else:
                self.has_exception = False
                if '验收数量差异过大' in (self.exception_reason or ''):
                    self.exception_reason = ''

    def approve_exception(self, approver_id):
        """异常审批通过"""
        if self.has_exception:
            self.exception_approved = True
            self.exception_approver_id = approver_id
            self.exception_approved_at = timezone.now()
            self.save()

    @property
    def can_auto_approve_acceptance(self):
        """是否可以自动通过验收"""
        try:
            # 确保variance_rate是数字类型
            variance_rate = float(self.variance_rate) if self.variance_rate is not None else 0
            return variance_rate < 5
        except (ValueError, TypeError):
            # 如果转换失败，默认返回False（不能自动通过）
            return False

    def generate_reimbursement(self, voucher_no='', financial_serial_no='', remarks=''):
        """生成报销单"""
        if self.status == 'accepted':
            self.status = 'pending_reimbursement'
            self.reimbursement_date = timezone.now()
            self.voucher_number = voucher_no or f'RB{timezone.now().strftime("%Y%m%d")}{self.id:04d}'
            self.financial_serial = financial_serial_no
            self.settlement_remarks = remarks
            self.save()

    def return_to_draft(self, returner_id, reason):
        """退回到草稿状态"""
        if self.status in ['approved', 'pending_purchase']:
            self.status = 'returned'
            self.returner_id = returner_id  # 使用_id后缀直接设置外键ID
            self.return_reason = reason
            self.return_date = timezone.now()
            self.save()

    def finance_return_to_acceptance(self, finance_user, reason):
        """财务退回到待验收状态"""
        if self.status != 'pending_reimbursement':
            raise ValueError(f'当前状态 {self.status} 不能执行财务退回操作')

        self.status = 'pending_acceptance'
        self.re_acceptance_required = True
        self.finance_return_reason = reason
        self.finance_returner = finance_user
        self.finance_return_date = timezone.now()
        self.re_acceptance_count += 1
        self.save()

        # 记录操作日志
        try:
            from apps.system.views import log_user_action
            log_user_action(
                user=finance_user,
                log_type='finance_return',
                action=f'财务退回重新验收: {self.item_name}',
                target_model='PurchaseRequest',
                target_id=self.id,
                request_data={'finance_return_reason': reason}
            )

            # 发送通知给验收人员
            from apps.system.notification_service import notify_finance_return_to_acceptance
            notify_finance_return_to_acceptance(self, finance_user)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"财务退回通知发送失败: {e}")

    def _send_notification_to_approvers(self):
        """发送通知给审批人员"""
        try:
            from apps.system.models import Notification
            from apps.system.consumers import send_notification_to_user_sync
            from apps.roles.models import Role
            from django.contrib.auth import get_user_model

            User = get_user_model()

            # 获取审批角色的用户
            try:
                approver_role = Role.objects.get(code='approver')
                approvers = User.objects.filter(roles=approver_role, is_active=True)

                for approver in approvers:
                    # 创建通知
                    notification = Notification.create_notification(
                        title=f'新的采购需求待审批',
                        content=f'采购需求"{self.item_name}"已提交，请及时审批。',
                        notification_type='approval',
                        recipient=approver,
                        sender=self.requester,
                        target_model='PurchaseRequest',
                        target_id=str(self.id),
                        target_url=f'/purchase/approval?id={self.id}',
                        priority='normal'
                    )

                    # 发送实时通知
                    from apps.system.serializers import NotificationSerializer
                    notification_data = NotificationSerializer(notification).data
                    send_notification_to_user_sync(approver.id, notification_data)

            except Role.DoesNotExist:
                print("审批角色不存在")

        except Exception as e:
            print(f"发送通知失败: {str(e)}")

    def _send_notification_to_purchaser(self):
        """发送通知给采购人员"""
        try:
            from apps.system.models import Notification
            from apps.system.consumers import send_notification_to_user_sync
            from apps.roles.models import Role
            from django.contrib.auth import get_user_model

            User = get_user_model()

            # 获取采购角色的用户
            try:
                purchaser_role = Role.objects.get(code='purchaser')
                purchasers = User.objects.filter(roles=purchaser_role, is_active=True)

                for purchaser in purchasers:
                    # 创建通知
                    notification = Notification.create_notification(
                        title=f'新的采购任务',
                        content=f'采购需求"{self.item_name}"已审批通过，请及时采购。',
                        notification_type='purchase',
                        recipient=purchaser,
                        sender=None,
                        target_model='PurchaseRequest',
                        target_id=str(self.id),
                        target_url=f'/purchase/procurement?id={self.id}',
                        priority='normal'
                    )

                    # 发送实时通知
                    from apps.system.serializers import NotificationSerializer
                    notification_data = NotificationSerializer(notification).data
                    send_notification_to_user_sync(purchaser.id, notification_data)

            except Role.DoesNotExist:
                print("采购角色不存在")

        except Exception as e:
            print(f"发送通知失败: {str(e)}")




class Dictionary(models.Model):
    """
    数据字典表
    对应数据库表：purchase_dictionary
    """
    TYPE_CHOICES = [
        ('status', '状态'),
        ('purchase_type', '采购类型'),
        ('procurement_method', '采购方式'),
        ('fund_project', '经费项目'),
        ('unit', '计量单位'),
        ('item_category', '物品种类'),
        ('requirement_source', '需求来源'),
        ('urgency_level', '紧急程度'),
        ('其他', '其他')
    ]

    # 可编辑状态选择
    EDITABLE_CHOICES = [
        (1, '可编辑'),
        (2, '不可编辑'),
    ]

    # 基本信息
    type_code = models.CharField('字典类型编码', max_length=50, choices=TYPE_CHOICES, help_text='英文类型编码，用于代码匹配')
    type_name = models.CharField('字典类型名称', max_length=50, help_text='中文类型名称，用于界面显示')
    code = models.CharField('编码', max_length=50, help_text='系统内唯一标识')
    name = models.CharField('名称', max_length=100, help_text='字典项的显示名称')
    description = models.TextField('描述', blank=True)
    order = models.IntegerField('排序', default=1)
    status = models.BooleanField('状态', default=True)
    editable = models.IntegerField('可编辑', choices=EDITABLE_CHOICES, default=1, help_text='1=可编辑，2=不可编辑')

    # 系统信息
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '数据字典'
        verbose_name_plural = '数据字典管理'
        ordering = ['type_code', 'order']
        unique_together = [('type_code', 'code')]
        default_permissions = ()  # 禁用Django自动权限创建
        indexes = [
            models.Index(fields=['type_code'], name='idx_dict_type_code'),
            models.Index(fields=['status'], name='idx_dict_status'),
            models.Index(fields=['type_code', 'status'], name='idx_dict_type_code_status'),
            models.Index(fields=['type_code', 'order'], name='idx_dict_type_code_order'),
            models.Index(fields=['editable'], name='idx_dict_editable'),
        ]

    def __str__(self):
        return f"{self.type_code} - {self.name}"


class AcceptancePhoto(models.Model):
    """
    验收照片表 - 支持多张照片上传
    """
    purchase_request = models.ForeignKey(
        PurchaseRequest,
        on_delete=models.CASCADE,
        related_name='acceptance_photos_new',
        verbose_name='采购需求'
    )
    photo = models.ImageField(
        '照片',
        upload_to=get_acceptance_photo_path,
        help_text='验收照片'
    )
    photo_type = models.CharField(
        '照片类型',
        max_length=20,
        choices=[
            ('courier', '快递单照片'),
            ('item', '物品照片'),
            ('package', '包装照片'),
            ('front', '正面照片'),
            ('side', '侧面照片'),
            ('overall', '整体照片'),
        ],
        default='item'
    )
    description = models.CharField(
        '照片描述',
        max_length=200,
        blank=True,
        help_text='照片的描述信息'
    )
    upload_time = models.DateTimeField(
        '上传时间',
        auto_now_add=True
    )
    uploader = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='上传者'
    )

    class Meta:
        db_table = 'purchase_acceptance_photo'
        verbose_name = '验收照片'
        verbose_name_plural = '验收照片'
        ordering = ['upload_time']
        default_permissions = ()  # 禁用Django自动权限创建
        indexes = [
            models.Index(fields=['purchase_request'], name='idx_photo_request'),
            models.Index(fields=['photo_type'], name='idx_photo_type'),
            models.Index(fields=['upload_time'], name='idx_photo_upload_time'),
        ]

    def __str__(self):
        return f"{self.purchase_request.item_name} - {self.get_photo_type_display()}"
