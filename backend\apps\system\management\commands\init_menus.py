"""
初始化系统菜单数据
"""
from django.core.management.base import BaseCommand
from apps.system.models import Menu, RoleMenu
from apps.roles.models import Role


class Command(BaseCommand):
    help = '初始化系统菜单数据'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化菜单数据...')
        
        # 创建菜单数据
        menus_data = [
            # 一级菜单
            {
                'menu_name': '仪表盘',
                'menu_code': 'dashboard',
                'menu_type': 'menu',
                'route_path': '/dashboard',
                'component_path': 'Dashboard',
                'icon': 'dashboard',
                'sort_order': 1,
                'parent': None,
                'is_active': True,
                'is_visible': True
            },
            {
                'menu_name': '采购管理',
                'menu_code': 'purchase',
                'menu_type': 'menu',
                'route_path': '/purchase',
                'component_path': None,
                'icon': 'shopping-cart',
                'sort_order': 2,
                'parent': None,
                'is_active': True,
                'is_visible': True
            },
            {
                'menu_name': '系统设置',
                'menu_code': 'settings',
                'menu_type': 'menu',
                'route_path': '/settings',
                'component_path': None,
                'icon': 'setting',
                'sort_order': 3,
                'parent': None,
                'is_active': True,
                'is_visible': True
            },
            {
                'menu_name': '个人中心',
                'menu_code': 'personal_center',
                'menu_type': 'menu',
                'route_path': '/profile',
                'component_path': 'Profile',
                'icon': 'user',
                'sort_order': 4,
                'parent': None,
                'is_active': True,
                'is_visible': True
            }
        ]
        
        # 创建一级菜单
        created_menus = {}
        for menu_data in menus_data:
            menu, created = Menu.objects.get_or_create(
                menu_code=menu_data['menu_code'],
                defaults=menu_data
            )
            created_menus[menu_data['menu_code']] = menu
            if created:
                self.stdout.write(f'✅ 创建菜单: {menu.menu_name}')
            else:
                self.stdout.write(f'📋 菜单已存在: {menu.menu_name}')
        
        # 创建二级菜单
        sub_menus_data = [
            # 采购管理子菜单
            {
                'menu_name': '采购概览',
                'menu_code': 'purchase_overview',
                'menu_type': 'menu',
                'route_path': '/purchase/overview',
                'component_path': 'PurchaseOverview',
                'icon': 'eye',
                'sort_order': 1,
                'parent_code': 'purchase'
            },
            {
                'menu_name': '采购申请',
                'menu_code': 'purchase_requests',
                'menu_type': 'menu',
                'route_path': '/purchase-requests',
                'component_path': 'PurchaseRequestList',
                'icon': 'file-text',
                'sort_order': 2,
                'parent_code': 'purchase'
            },
            {
                'menu_name': '物品验收',
                'menu_code': 'acceptances',
                'menu_type': 'menu',
                'route_path': '/acceptances',
                'component_path': 'AcceptanceList',
                'icon': 'check-circle',
                'sort_order': 3,
                'parent_code': 'purchase'
            },
            {
                'menu_name': '费用报销',
                'menu_code': 'reimbursements',
                'menu_type': 'menu',
                'route_path': '/reimbursements',
                'component_path': 'ReimbursementList',
                'icon': 'dollar',
                'sort_order': 4,
                'parent_code': 'purchase'
            },
            # 系统设置子菜单
            {
                'menu_name': '用户管理',
                'menu_code': 'settings_users',
                'menu_type': 'menu',
                'route_path': '/settings/users',
                'component_path': 'UserManagement',
                'icon': 'team',
                'sort_order': 1,
                'parent_code': 'settings'
            },
            {
                'menu_name': '角色管理',
                'menu_code': 'settings_roles',
                'menu_type': 'menu',
                'route_path': '/settings/roles',
                'component_path': 'RoleManagement',
                'icon': 'safety-certificate',
                'sort_order': 2,
                'parent_code': 'settings'
            },
            {
                'menu_name': '权限管理',
                'menu_code': 'settings_permissions',
                'menu_type': 'menu',
                'route_path': '/settings/permissions',
                'component_path': 'PermissionsMatrix',
                'icon': 'lock',
                'sort_order': 3,
                'parent_code': 'settings'
            },
            {
                'menu_name': '部门管理',
                'menu_code': 'settings_departments',
                'menu_type': 'menu',
                'route_path': '/settings/departments',
                'component_path': 'DepartmentManagement',
                'icon': 'apartment',
                'sort_order': 4,
                'parent_code': 'settings'
            }
        ]
        
        # 创建二级菜单
        for sub_menu_data in sub_menus_data:
            parent_code = sub_menu_data.pop('parent_code')
            parent_menu = created_menus.get(parent_code)
            if parent_menu:
                sub_menu_data['parent'] = parent_menu
                sub_menu_data['is_active'] = True
                sub_menu_data['is_visible'] = True

                menu, created = Menu.objects.get_or_create(
                    menu_code=sub_menu_data['menu_code'],
                    defaults=sub_menu_data
                )
                if created:
                    self.stdout.write(f'✅ 创建子菜单: {menu.menu_name}')
                else:
                    self.stdout.write(f'📋 子菜单已存在: {menu.menu_name}')
        
        # 创建默认角色
        admin_role, created = Role.objects.get_or_create(
            code='admin',
            defaults={
                'name': '系统管理员',
                'description': '系统管理员，拥有所有权限',
                'is_active': True,
                'is_system': True
            }
        )
        if created:
            self.stdout.write('✅ 创建管理员角色')
        else:
            self.stdout.write('📋 管理员角色已存在')
        
        # 注意：角色菜单关联现在通过权限系统管理
        # 不再使用RoleMenu表

        self.stdout.write(self.style.SUCCESS('菜单数据初始化完成！'))
        self.stdout.write(f'总计菜单数量: {Menu.objects.count()}')
        self.stdout.write('💡 角色菜单关联请通过权限管理系统配置')
