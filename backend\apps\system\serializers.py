from rest_framework import serializers
from ..common.base_serializers import BaseModelSerializer, UniqueFieldMixin
from .models import Department, Notification, Menu


class DepartmentSerializer(BaseModelSerializer, UniqueFieldMixin):
    """部门序列化器"""
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Department
        fields = [
            'id', 'dept_name', 'parent_id', 'dept_code',
            'hierarchy_path', 'manager_name', 'contact_phone', 'description',
            'approval_limit', 'can_approve', 'can_accept', 'can_finance',
            'status', 'user_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'hierarchy_path', 'user_count', 'created_at', 'updated_at']

    def get_user_count(self, obj):
        """获取部门用户数量"""
        from apps.authentication.models import User
        return User.objects.filter(dept_id=obj.id, is_active=True).count()

    def validate_dept_code(self, value):
        """验证部门编码唯一性"""
        return self.validate_unique_field('dept_code', value, '部门编码已存在')


class DepartmentTreeSerializer(serializers.ModelSerializer):
    """部门树形结构序列化器"""
    children = serializers.SerializerMethodField()

    class Meta:
        model = Department
        fields = [
            'id', 'dept_name', 'parent_id', 'dept_code',
            'hierarchy_path', 'manager_name', 'contact_phone',
            'approval_limit', 'can_approve', 'can_accept', 'can_finance',
            'status', 'children'
        ]

    def get_children(self, obj):
        """获取子部门"""
        children = Department.objects.filter(parent_id=obj.id, status=True)
        return DepartmentTreeSerializer(children, many=True).data





class DictTypeSerializer(serializers.Serializer):
    """数据字典类型序列化器"""
    type = serializers.CharField()
    items = serializers.ListField()



class NotificationSerializer(serializers.ModelSerializer):
    """
    通知序列化器
    """
    notification_type_display = serializers.CharField(source='get_notification_type_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    sender_name = serializers.SerializerMethodField()
    recipient_name = serializers.SerializerMethodField()
    time_ago = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'content', 'notification_type', 'notification_type_display',
            'priority', 'priority_display', 'sender', 'sender_name', 'recipient',
            'recipient_name', 'target_model', 'target_id', 'target_url',
            'is_read', 'read_at', 'is_deleted', 'is_archived', 'action_buttons',
            'delivery_methods', 'delivery_status', 'is_sent', 'sent_at', 'retry_count',
            'created_at', 'updated_at', 'time_ago'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'read_at', 'sent_at']

    def get_sender_name(self, obj):
        """获取发送者姓名"""
        if obj.sender:
            return obj.sender.first_name or obj.sender.username
        return '系统'

    def get_recipient_name(self, obj):
        """获取接收者姓名"""
        return obj.recipient.first_name or obj.recipient.username

    def get_time_ago(self, obj):
        """获取相对时间"""
        from django.utils import timezone
        from datetime import timedelta

        now = timezone.now()
        diff = now - obj.created_at

        if diff < timedelta(minutes=1):
            return '刚刚'
        elif diff < timedelta(hours=1):
            return f'{diff.seconds // 60}分钟前'
        elif diff < timedelta(days=1):
            return f'{diff.seconds // 3600}小时前'
        elif diff < timedelta(days=7):
            return f'{diff.days}天前'
        else:
            return obj.created_at.strftime('%Y-%m-%d')


# ==================== 菜单管理序列化器 ====================

class MenuSerializer(BaseModelSerializer):
    """菜单序列化器"""
    children = serializers.SerializerMethodField()
    parent_name = serializers.SerializerMethodField()

    class Meta:
        model = Menu
        fields = [
            'id', 'parent', 'parent_name', 'menu_name', 'menu_code',
            'menu_type', 'route_path', 'component_path', 'icon',
            'sort_order', 'is_visible', 'is_active', 'permission_code',
            'business_status', 'created_at', 'updated_at', 'children'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'children', 'parent_name']

    def get_children(self, obj):
        """获取子菜单"""
        if hasattr(obj, 'children'):
            # 检查是否是管理模式（从context中获取）
            admin_mode = self.context.get('admin_mode', False)

            if admin_mode:
                # 管理模式：获取所有子菜单（包括禁用的）
                children = obj.children.all().order_by('sort_order')
            else:
                # 普通模式：只获取启用的子菜单
                children = obj.children.filter(is_active=True).order_by('sort_order')

            return MenuSerializer(children, many=True, context=self.context).data
        return []

    def get_parent_name(self, obj):
        """获取父菜单名称"""
        return obj.parent.menu_name if obj.parent else None