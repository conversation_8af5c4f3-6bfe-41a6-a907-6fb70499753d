/**
 * 防抖函数工具
 * 用于替代lodash-es的debounce函数，避免额外依赖
 */

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {Object} options - 选项
 * @param {boolean} options.leading - 是否在延迟开始前调用
 * @param {boolean} options.trailing - 是否在延迟结束后调用
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300, options = {}) {
  let timeout
  let result
  const { leading = false, trailing = true } = options

  const debounced = function (...args) {
    const context = this
    const later = () => {
      timeout = null
      if (trailing && !leading) {
        result = func.apply(context, args)
      }
    }

    const callNow = leading && !timeout

    clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) {
      result = func.apply(context, args)
    }

    return result
  }

  // 取消防抖
  debounced.cancel = () => {
    clearTimeout(timeout)
    timeout = null
  }

  // 立即执行
  debounced.flush = function (...args) {
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
      return func.apply(this, args)
    }
  }

  return debounced
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {Object} options - 选项
 * @returns {Function} 节流后的函数
 */
export function throttle(func, wait = 300, options = {}) {
  let timeout
  let previous = 0
  const { leading = true, trailing = true } = options

  const throttled = function (...args) {
    const context = this
    const now = Date.now()

    if (!previous && !leading) {
      previous = now
    }

    const remaining = wait - (now - previous)

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      return func.apply(context, args)
    } else if (!timeout && trailing) {
      timeout = setTimeout(() => {
        previous = leading ? Date.now() : 0
        timeout = null
        func.apply(context, args)
      }, remaining)
    }
  }

  // 取消节流
  throttled.cancel = () => {
    clearTimeout(timeout)
    timeout = null
    previous = 0
  }

  return throttled
}

export default {
  debounce,
  throttle
}
