# Generated by Django 5.2.1 on 2025-06-14 01:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='department',
            name='approval_limit',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='该部门可审批的最大金额，为空表示无限制', max_digits=12, null=True, verbose_name='采购审批额度限制'),
        ),
        migrations.AddField(
            model_name='department',
            name='can_accept',
            field=models.BooleanField(default=False, help_text='是否具有验收权限', verbose_name='可以验收'),
        ),
        migrations.AddField(
            model_name='department',
            name='can_approve',
            field=models.BooleanField(default=False, help_text='是否具有审批权限', verbose_name='可以审批'),
        ),
        migrations.AddField(
            model_name='department',
            name='can_finance',
            field=models.BooleanField(default=False, help_text='是否具有财务权限', verbose_name='可以财务结算'),
        ),
    ]
