from django.urls import path
from . import views

urlpatterns = [
    # 认证相关
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('user/', views.user_info_view, name='user-info'),
    path('change-password/', views.change_password_view, name='change-password'),

    # 权限相关
    path('permissions/', views.user_permissions_view, name='user-permissions'),
    path('roles/', views.roles_list_view, name='roles-list'),
    path('check-permission/', views.check_permission_view, name='check-permission'),

    # 用户管理
    path('users/', views.UserListCreateView.as_view(), name='user-list-create'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user-detail'),
    path('users/<int:pk>/permissions/', views.user_permissions_management_view, name='user-permissions-management'),
    path('users/template/', views.download_user_template, name='user-template'),
    path('users/import/', views.import_users, name='user-import'),
    path('users/export/', views.export_users, name='user-export'),

    # 个人资料
    path('users/profile/', views.user_profile_view, name='user-profile'),
    path('users/statistics/', views.user_statistics_view, name='user-statistics'),
    path('users/admin-statistics/', views.admin_user_statistics_view, name='admin-user-statistics'),
    path('users/online-count/', views.online_users_count_view, name='online-users-count'),
    path('users/logs/', views.user_logs_view, name='user-logs'),
]
