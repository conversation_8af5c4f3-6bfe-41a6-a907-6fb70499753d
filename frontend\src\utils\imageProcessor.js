/**
 * 图片处理工具
 * 用于照片上传时的自动处理：缩放、压缩等
 */

/**
 * 将文件转换为Canvas
 * @param {File} file - 图片文件
 * @returns {Promise<HTMLCanvasElement>} Canvas元素
 */
function fileToCanvas(file) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)
      resolve(canvas)
    }
    
    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 将Canvas转换为Blob
 * @param {HTMLCanvasElement} canvas - Canvas元素
 * @param {string} mimeType - 输出格式
 * @param {number} quality - 质量(0-1)
 * @returns {Promise<Blob>} 图片Blob
 */
function canvasToBlob(canvas, mimeType = 'image/jpeg', quality = 0.8) {
  return new Promise((resolve) => {
    canvas.toBlob(resolve, mimeType, quality)
  })
}

/**
 * 计算缩放尺寸，保持宽高比
 * @param {number} originalWidth - 原始宽度
 * @param {number} originalHeight - 原始高度
 * @param {number} targetWidth - 目标宽度(像素)
 * @param {number} targetHeight - 目标高度(像素)
 * @returns {Object} 缩放后的尺寸
 */
function calculateScaleSize(originalWidth, originalHeight, targetWidth, targetHeight) {
  // 计算宽高比
  const aspectRatio = originalWidth / originalHeight
  const targetAspectRatio = targetWidth / targetHeight
  
  let newWidth, newHeight
  
  if (aspectRatio > targetAspectRatio) {
    // 原图更宽，以宽度为准
    newWidth = targetWidth
    newHeight = targetWidth / aspectRatio
  } else {
    // 原图更高，以高度为准
    newHeight = targetHeight
    newWidth = targetHeight * aspectRatio
  }
  
  return {
    width: Math.round(newWidth),
    height: Math.round(newHeight)
  }
}

/**
 * 处理验收照片
 * 统一缩放到指定尺寸，保持宽高比，优化文件大小
 * @param {File} file - 原始图片文件
 * @param {Object} options - 处理选项
 * @returns {Promise<File>} 处理后的图片文件
 */
export async function processAcceptancePhoto(file, options = {}) {
  const {
    // 目标尺寸：3cm x 3.5cm，按96DPI计算约为113x133像素
    targetWidth = 113,   // 3cm * 96DPI / 2.54
    targetHeight = 133,  // 3.5cm * 96DPI / 2.54
    quality = 0.8,       // 压缩质量
    maxFileSize = 500 * 1024, // 最大文件大小500KB
    outputFormat = 'image/jpeg'
  } = options
  
  try {
    console.log('🔄 开始处理照片:', {
      originalName: file.name,
      originalSize: file.size,
      targetSize: `${targetWidth}x${targetHeight}`
    })
    
    // 转换为Canvas
    const canvas = await fileToCanvas(file)
    
    // 计算缩放尺寸
    const scaleSize = calculateScaleSize(
      canvas.width, 
      canvas.height, 
      targetWidth, 
      targetHeight
    )
    
    console.log('📐 尺寸计算:', {
      original: `${canvas.width}x${canvas.height}`,
      scaled: `${scaleSize.width}x${scaleSize.height}`
    })
    
    // 创建新的Canvas用于缩放
    const scaledCanvas = document.createElement('canvas')
    const scaledCtx = scaledCanvas.getContext('2d')
    
    // 设置目标尺寸（居中显示）
    scaledCanvas.width = targetWidth
    scaledCanvas.height = targetHeight
    
    // 填充白色背景
    scaledCtx.fillStyle = '#FFFFFF'
    scaledCtx.fillRect(0, 0, targetWidth, targetHeight)
    
    // 计算居中位置
    const offsetX = (targetWidth - scaleSize.width) / 2
    const offsetY = (targetHeight - scaleSize.height) / 2
    
    // 绘制缩放后的图片
    scaledCtx.drawImage(
      canvas,
      0, 0, canvas.width, canvas.height,
      offsetX, offsetY, scaleSize.width, scaleSize.height
    )
    
    // 转换为Blob，尝试不同质量直到满足文件大小要求
    let currentQuality = quality
    let blob
    let attempts = 0
    const maxAttempts = 5
    
    do {
      blob = await canvasToBlob(scaledCanvas, outputFormat, currentQuality)
      console.log(`📊 压缩尝试 ${attempts + 1}: 质量=${currentQuality.toFixed(2)}, 大小=${blob.size}字节`)
      
      if (blob.size <= maxFileSize || attempts >= maxAttempts) {
        break
      }
      
      currentQuality *= 0.8 // 降低质量
      attempts++
    } while (currentQuality > 0.1)
    
    // 创建新的File对象
    const processedFile = new File(
      [blob],
      file.name.replace(/\.[^/.]+$/, '.jpg'), // 统一为jpg格式
      {
        type: outputFormat,
        lastModified: Date.now()
      }
    )
    
    console.log('✅ 照片处理完成:', {
      finalSize: processedFile.size,
      compression: `${((1 - processedFile.size / file.size) * 100).toFixed(1)}%`,
      finalQuality: currentQuality.toFixed(2)
    })
    
    return processedFile
    
  } catch (error) {
    console.error('❌ 照片处理失败:', error)
    // 如果处理失败，返回原文件
    return file
  }
}

/**
 * 验证图片文件
 * @param {File} file - 图片文件
 * @returns {Object} 验证结果
 */
export function validateImageFile(file) {
  const errors = []
  
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
  if (!allowedTypes.includes(file.type)) {
    errors.push('只支持 JPG、PNG 格式的图片')
  }
  
  // 检查文件大小（10MB）
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    errors.push('图片大小不能超过 10MB')
  }
  
  // 检查文件名
  if (!file.name || file.name.length > 100) {
    errors.push('文件名不能为空且不能超过100个字符')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 获取图片信息
 * @param {File} file - 图片文件
 * @returns {Promise<Object>} 图片信息
 */
export function getImageInfo(file) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height,
        aspectRatio: img.width / img.height,
        size: file.size,
        type: file.type,
        name: file.name
      })
    }
    
    img.onerror = () => {
      reject(new Error('无法读取图片信息'))
    }
    
    img.src = URL.createObjectURL(file)
  })
}

export default {
  processAcceptancePhoto,
  validateImageFile,
  getImageInfo
}
