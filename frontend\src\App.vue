<template>
  <div id="app">
    <a-config-provider :locale="locale">
      <router-view />
    </a-config-provider>
  </div>
</template>

<script>
import zhCN from 'ant-design-vue/es/locale/zh_CN'

export default {
  name: 'App',
  data() {
    return {
      locale: zhCN
    }
  }
}
</script>

<style>
/* 深蓝色系商务配色方案 */
:root {
  /* 主色调 - 深蓝色系 */
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);

  /* 辅助色 */
  --secondary-color: #64748b;
  --secondary-light: #94a3b8;
  --secondary-dark: #475569;

  /* 功能色 */
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --info-color: #0284c7;

  /* 中性色 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-inverse: #ffffff;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-overlay: rgba(30, 58, 138, 0.05);

  /* 边框色 */
  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --border-dark: #94a3b8;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(30, 58, 138, 0.08);
  --shadow-md: 0 4px 16px rgba(30, 58, 138, 0.12);
  --shadow-lg: 0 8px 32px rgba(30, 58, 138, 0.16);

  /* 圆角 */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;

  /* 字体大小 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 28px;

  /* 间距 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
}

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  min-height: 100vh;
  font-size: var(--text-base);
  line-height: 1.6;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: var(--radius-sm);
  transition: all 0.3s;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

/* 全局动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 通用卡片样式 */
.business-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.business-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* 通用按钮样式 */
.primary-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  font-weight: 600;
  font-size: var(--text-sm);
  letter-spacing: 0.5px;
  padding: var(--space-md) var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.secondary-btn {
  border: 2px solid var(--border-medium);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: var(--text-sm);
  background: var(--bg-primary);
  padding: calc(var(--space-md) - 2px) calc(var(--space-lg) - 2px);
  transition: all 0.3s;
  cursor: pointer;
}

.secondary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-overlay);
}

/* 全局 Ant Design 组件样式覆盖 */
.ant-btn-primary {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --text-base: 14px;
    --text-lg: 16px;
    --text-xl: 18px;
    --text-2xl: 20px;
    --text-3xl: 24px;
  }

  .business-card {
    border-radius: var(--radius-md);
    margin: 0 var(--space-sm);
  }
}
</style>
