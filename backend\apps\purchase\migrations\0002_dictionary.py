# Generated by Django 5.2.3 on 2025-06-13 11:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Dictionary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('采购方式', '采购方式'), ('经费来源', '经费来源'), ('物品分类', '物品分类'), ('单位', '单位'), ('其他', '其他')], max_length=50, verbose_name='字典类型')),
                ('code', models.CharField(help_text='系统内唯一标识', max_length=50, verbose_name='编码')),
                ('name', models.CharField(max_length=100, verbose_name='名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('order', models.IntegerField(default=1, verbose_name='排序')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '数据字典',
                'verbose_name_plural': '数据字典管理',
                'ordering': ['type', 'order'],
                'unique_together': {('type', 'code')},
            },
        ),
    ]
