from rest_framework import generics, status, permissions
from rest_framework.permissions import AllowAny
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from apps.system.models_permission import Permission
from django.db import models
from django.http import HttpResponse
from django.utils import timezone
import pandas as pd
import io
import logging

logger = logging.getLogger(__name__)
from .models import Role
from .serializers import (
    RoleSerializer, PermissionSerializer,
    RoleSimpleSerializer
)
from ..common.base_views import StandardPagination, BaseListCreateView, BaseDetailView


def get_module_label(module_name):
    """获取模块显示名称"""
    module_labels = {
        'dashboard': '仪表盘',
        'purchase': '采购管理',
        'system': '系统管理',
        'acceptance': '验收管理',
        'reimbursement': '报销管理',
        'user': '用户管理',
        'role': '角色管理',
        'permission': '权限管理',
        'menu': '菜单管理',
        'dept': '部门管理',
        'dict': '字典管理',
        'log': '日志管理',
        'report': '报表管理',
        'profile': '个人中心',
        'other': '其他'
    }
    return module_labels.get(module_name, module_name)


def convert_permission_key(perm_key):
    """
    转换前端权限key为后端权限codename - 已简化，直接返回原始代码
    """
    # 现在直接使用数据库权限系统，不需要复杂的转换
    return [perm_key]


class RoleListCreateView(BaseListCreateView):
    """角色列表和创建视图"""
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [permissions.AllowAny]  # 临时允许所有用户访问
    filterset_fields = ['is_active', 'is_system']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()
        # 可以添加额外的过滤逻辑
        return queryset




class RoleDetailView(BaseDetailView):
    """角色详情视图"""
    queryset = Role.objects.all()
    serializer_class = RoleSerializer

    def destroy(self, request, *args, **kwargs):
        """重写删除方法以添加业务验证"""
        role = self.get_object()

        if role.is_system:
            return self.error_response('系统角色不能删除')

        # 检查是否有用户关联
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user_count = User.objects.filter(role=role.code).count()
        if user_count > 0:
            return self.error_response(f'该角色还有{user_count}个用户关联，不能删除')

        return super().destroy(request, *args, **kwargs)


# UserRole视图已删除 - 改用User.role字段


@api_view(['GET'])
def permissions_list_view(request):
    """
    获取所有权限列表（无分页）
    GET /api/roles/permissions/
    """
    try:
        # 使用自定义权限系统
        permissions = Permission.objects.filter(is_active=True).order_by('module', 'sort_order', 'code')

        # 按模块分组权限
        grouped_permissions = {}
        permission_list = []

        for perm in permissions:
            # 如果权限没有中文模块名称，则自动设置
            if not perm.module_name:
                perm.module_name = get_module_label(perm.module)
                perm.save()

            permission_data = {
                'id': perm.id,
                'name': perm.name,
                'codename': perm.code,
                'category': perm.category,
                'description': perm.description or '',
                'module': perm.module,
                'module_name': perm.module_name or get_module_label(perm.module)
            }

            permission_list.append(permission_data)

            # 按模块分组
            module = perm.module

            if module not in grouped_permissions:
                grouped_permissions[module] = {
                    'module_name': module,
                    'module_label': perm.module_name or get_module_label(module),
                    'permissions': []
                }
            grouped_permissions[module]['permissions'].append(permission_data)

        return Response({
            'code': 200,
            'message': '获取成功',
            'data': {
                'results': permission_list,
                'grouped_permissions': list(grouped_permissions.values()),
                'count': len(permission_list),
                'total': len(permission_list)
            }
        })
    except Exception as e:
        logger.error(f'获取权限列表失败: {str(e)}', exc_info=True)
        return Response({
            'code': 500,
            'message': f'获取权限列表失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)








@api_view(['GET'])
def roles_simple_list_view(request):
    """
    获取角色简单列表（用于下拉选择）
    GET /api/roles/simple/
    """
    try:
        roles = Role.objects.filter(is_active=True).order_by('name')
        serializer = RoleSimpleSerializer(roles, many=True)

        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取角色列表失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def permission_matrix_view(request):
    """
    获取权限矩阵数据 - 基于菜单树结构的统一API接口
    GET /api/roles/permission-matrix/

    返回完整的权限矩阵数据，包括：
    - 基于菜单树的权限层级结构
    - 所有角色列表
    - 角色-权限关系映射
    - 权限的详细信息
    """
    try:
        # 获取所有角色
        from .models import Role
        roles = Role.objects.filter(is_active=True).order_by('created_at')

        # 获取所有权限
        from apps.system.models_permission import Permission, RolePermission
        permissions = Permission.objects.filter(is_active=True).order_by('module', 'sort_order', 'code')

        # 获取菜单树结构
        from apps.system.models import Menu
        menus = Menu.objects.filter(is_active=True).order_by('sort_order', 'id')

        # 构建权限映射表（按权限代码索引）
        permission_map = {}
        permissions_flat = []

        for perm in permissions:
            # 如果权限没有中文模块名称，则自动设置
            if not perm.module_name:
                perm.module_name = get_module_label(perm.module)
                perm.save()

            # 构建权限基础数据
            permission_data = {
                'id': perm.id,
                'name': perm.name,
                'code': perm.code,
                'category': perm.category,
                'module': perm.module,
                'module_name': perm.module_name,
                'description': perm.description or '',
                'sort_order': perm.sort_order,
                'parent_id': perm.parent.id if perm.parent else None,
                'parent_code': perm.parent.code if perm.parent else None
            }

            permissions_flat.append(permission_data)
            permission_map[perm.code] = permission_data

        # 构建菜单树结构
        def build_menu_tree(menu_list):
            """构建菜单树"""
            menu_dict = {}
            tree = []

            # 将菜单转换为字典
            for menu in menu_list:
                menu_dict[menu.id] = {
                    'id': menu.id,
                    'menu_name': menu.menu_name,
                    'menu_code': menu.menu_code,
                    'menu_type': menu.menu_type,
                    'permission_code': menu.permission_code,
                    'sort_order': menu.sort_order,
                    'parent_id': menu.parent_id,
                    'children': []
                }

            # 构建树结构
            for menu in menu_list:
                menu_data = menu_dict[menu.id]
                if menu.parent_id is None:
                    tree.append(menu_data)
                else:
                    parent = menu_dict.get(menu.parent_id)
                    if parent:
                        parent['children'].append(menu_data)

            return tree

        menu_tree = build_menu_tree(menus)

        # 构建角色权限矩阵
        role_permissions_map = {}
        for role in roles:
            role_perms = RolePermission.objects.filter(role=role).select_related('permission')
            role_permissions_map[role.id] = [rp.permission.id for rp in role_perms]

        # 构建角色信息
        roles_data = []
        for role in roles:
            role_data = {
                'id': role.id,
                'name': role.name,
                'code': role.code,
                'description': role.description,
                'is_active': role.is_active,
                'permission_ids': role_permissions_map.get(role.id, [])
            }
            roles_data.append(role_data)

        # 构建基于菜单树的权限矩阵数据
        def build_permission_tree_node(menu_node):
            """基于菜单节点构建权限树节点"""
            node = {
                'key': f'menu_{menu_node["id"]}',
                'type': menu_node['menu_type'],
                'name': menu_node['menu_name'],
                'menu_id': menu_node['id'],
                'menu_code': menu_node['menu_code'],
                'children': []
            }

            # 如果菜单有权限代码，添加权限信息
            if menu_node.get('permission_code'):
                permission = permission_map.get(menu_node['permission_code'])
                if permission:
                    node.update({
                        'id': permission['id'],
                        'code': permission['code'],
                        'description': permission['description'],
                        'permission_name': permission['name']
                    })

            # 递归处理子菜单
            for child_menu in menu_node.get('children', []):
                child_node = build_permission_tree_node(child_menu)
                node['children'].append(child_node)

                # 移除自动添加按钮权限的逻辑，完全依赖菜单树结构
                # 这样可以避免重复显示按钮权限

            return node

        tree_data = []
        for menu_root in menu_tree:
            tree_node = build_permission_tree_node(menu_root)
            tree_data.append(tree_node)

        # 计算详细统计数据
        directory_count = menus.filter(menu_type='directory').count()
        page_count = menus.filter(menu_type='page').count()
        button_count = menus.filter(menu_type='button').count()

        # 统计有权限代码的页面和按钮数量
        permission_page_count = permissions.filter(category='page').count()
        permission_button_count = permissions.filter(category='button').count()

        return Response({
            'code': 200,
            'message': '获取权限矩阵成功',
            'data': {
                'roles': roles_data,
                'tree_data': tree_data,
                'role_permissions_map': role_permissions_map,
                'statistics': {
                    'total_permissions': len(permissions_flat),
                    'total_roles': len(roles_data),
                    'total_menus': len(menus),
                    'directory_count': directory_count,
                    'page_count': page_count,
                    'button_count': button_count,
                    'permission_page_count': permission_page_count,
                    'permission_button_count': permission_button_count
                }
            }
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return Response({
            'code': 500,
            'message': f'获取权限矩阵失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST', 'PUT'])
def role_permissions_view(request, pk):
    """
    角色权限配置
    GET /api/roles/{id}/permissions/ - 获取角色权限
    POST /api/roles/{id}/permissions/ - 添加角色权限
    PUT /api/roles/{id}/permissions/ - 更新角色权限
    """
    try:
        role = Role.objects.get(pk=pk)
    except Role.DoesNotExist:
        return Response({
            'code': 404,
            'message': '角色不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)

    if request.method == 'GET':
        # 获取角色权限 - 使用自定义权限系统
        from apps.system.models_permission import RolePermission

        role_permissions = RolePermission.objects.filter(role=role).select_related('permission')
        permissions = [rp.permission for rp in role_permissions]

        # 按模块分组权限
        grouped_permissions = {}
        permission_ids = []

        for perm in permissions:
            module = perm.module

            if module not in grouped_permissions:
                grouped_permissions[module] = {
                    'module_name': module,
                    'module_label': get_module_label(module),
                    'permissions': []
                }

            # 构建权限数据
            perm_data = {
                'id': perm.id,
                'name': perm.name,
                'codename': perm.code,
                'category': perm.category,
                'description': perm.description or ''
            }

            grouped_permissions[module]['permissions'].append(perm_data)
            permission_ids.append(perm.id)

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'permissions': [
                    {
                        'id': p.id,
                        'name': p.name,
                        'codename': p.code,
                        'category': p.category,
                        'module': p.module,
                        'description': p.description or ''
                    } for p in permissions
                ],
                'grouped_permissions': list(grouped_permissions.values()),
                'permission_ids': permission_ids
            }
        })

    elif request.method in ['POST', 'PUT']:
        # 更新角色权限 - 使用自定义权限系统
        from apps.system.models_permission import Permission, RolePermission

        permission_ids = request.data.get('permission_ids', [])

        try:
            # 处理权限ID，支持数字ID和权限code两种格式
            processed_permission_ids = []

            for perm_id in permission_ids:
                if isinstance(perm_id, int):
                    # 如果是数字，直接使用
                    processed_permission_ids.append(perm_id)
                elif isinstance(perm_id, str):
                    # 如果是字符串，通过code查找对应的Permission
                    try:
                        permission = Permission.objects.get(code=perm_id)
                        processed_permission_ids.append(permission.id)
                    except Permission.DoesNotExist:
                        print(f"Warning: Permission with code '{perm_id}' not found")
                        continue

            # 清除现有权限关联
            RolePermission.objects.filter(role=role).delete()

            # 后端不做任何级联处理，完全按前端请求的权限列表执行
            final_permission_ids = set(processed_permission_ids)

            # 创建新的权限关联
            final_permissions = Permission.objects.filter(id__in=final_permission_ids)
            role_permissions = []
            for permission in final_permissions:
                role_permissions.append(
                    RolePermission(role=role, permission=permission)
                )

            RolePermission.objects.bulk_create(role_permissions)

            return Response({
                'code': 200,
                'message': '权限配置成功',
                'data': {
                    'role_id': role.id,
                    'requested_permission_count': len(processed_permission_ids),
                    'final_permission_count': len(final_permission_ids),
                    'auto_added_count': len(final_permission_ids) - len(processed_permission_ids),
                    'processed_permissions': list(processed_permission_ids),
                    'final_permissions': list(final_permission_ids)
                }
            })
        except Exception as e:
            return Response({
                'code': 400,
                'message': f'权限配置失败: {str(e)}',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'POST'])
def role_users_view(request, pk):
    """
    角色用户管理
    GET /api/roles/{id}/users/ - 获取角色关联的用户
    POST /api/roles/{id}/users/ - 添加用户到角色
    """
    try:
        role = Role.objects.get(pk=pk)
    except Role.DoesNotExist:
        return Response({
            'code': 404,
            'message': '角色不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)

    if request.method == 'GET':
        # 获取角色关联的用户 - 简化版
        from django.contrib.auth import get_user_model
        User = get_user_model()
        users_with_role = User.objects.filter(role=role.code)
        users = []
        for user in users_with_role:
            users.append({
                'id': user.id,
                'username': user.username,
                'real_name': user.real_name,
                'phone': user.phone,
                'email': user.email,
                'assigned_at': None  # 简化版不记录分配时间
            })

        return Response({
            'code': 200,
            'message': 'success',
            'data': users
        })

    elif request.method == 'POST':
        # 添加用户到角色
        user_ids = request.data.get('user_ids', [])

        if not user_ids:
            return Response({
                'code': 400,
                'message': '请选择要添加的用户',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()

            added_count = 0
            for user_id in user_ids:
                try:
                    user = User.objects.get(id=user_id)
                    # 检查是否已经关联 - 简化版
                    if user.role != role.code:
                        user.role = role.code
                        user.save(update_fields=['role'])
                        added_count += 1
                except User.DoesNotExist:
                    continue

            return Response({
                'code': 200,
                'message': f'成功添加 {added_count} 个用户到角色',
                'data': {'added_count': added_count}
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'添加用户失败: {str(e)}',
                'data': {}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def download_role_template(request):
    """
    下载角色导入模板
    GET /api/roles/template/
    """
    try:
        import io
        from django.http import HttpResponse
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = '角色导入模板'

        # 设置表头
        headers = ['角色名称', '角色编码', '角色描述', '是否启用', '是否系统角色']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 添加示例数据
        sample_data = [
            ['采购员', 'purchaser', '负责采购需求提交和管理', '是', '否'],
            ['审批员', 'approver', '负责采购需求审批', '是', '否'],
            ['验收员', 'acceptor', '负责采购物品验收', '是', '否'],
            ['财务员', 'finance', '负责财务结算和报销', '是', '否']
        ]

        for row_idx, row_data in enumerate(sample_data, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 设置列宽
        column_widths = [15, 15, 30, 12, 15]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + i)].width = width

        # 添加说明工作表
        ws_info = wb.create_sheet('填写说明')
        instructions = [
            ['字段名称', '说明', '示例'],
            ['角色名称', '必填，角色的显示名称', '采购员'],
            ['角色编码', '必填，角色的唯一标识', 'purchaser'],
            ['角色描述', '选填，角色的详细说明', '负责采购需求提交和管理'],
            ['是否启用', '选填，角色状态（是/否）', '是'],
            ['是否系统角色', '选填，是否为系统内置角色（是/否）', '否']
        ]

        for row_idx, row_data in enumerate(instructions, 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws_info.cell(row=row_idx, column=col_idx, value=value)
                if row_idx == 1:  # 表头
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
                    cell.alignment = Alignment(horizontal='center')

        # 设置说明工作表列宽
        ws_info.column_dimensions['A'].width = 15
        ws_info.column_dimensions['B'].width = 30
        ws_info.column_dimensions['C'].width = 25

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 设置响应头并返回文件
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="角色导入模板.xlsx"'

        return response

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'模板下载失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def import_roles(request):
    """
    导入角色数据
    POST /api/roles/import/
    """
    try:
        from openpyxl import load_workbook

        if 'file' not in request.FILES:
            return Response({
                'code': 400,
                'message': '请选择要导入的文件',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # 读取Excel文件
        try:
            wb = load_workbook(file)
            ws = wb['角色导入模板']

            # 获取表头
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(cell.value)

            # 读取数据行
            data_rows = []
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):  # 跳过空行
                    data_rows.append(row)

        except Exception as e:
            return Response({
                'code': 400,
                'message': f'文件格式错误: {str(e)}',
                'data': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # 处理数据
        success_count = 0
        error_count = 0
        errors = []

        for row_idx, row in enumerate(data_rows, 2):
            try:
                # 解析数据
                name = row[0] if len(row) > 0 and row[0] else None
                code = row[1] if len(row) > 1 and row[1] else None
                description = row[2] if len(row) > 2 and row[2] else ''
                is_active_str = row[3] if len(row) > 3 and row[3] else '是'
                is_system_str = row[4] if len(row) > 4 and row[4] else '否'

                # 验证必填字段
                if not name or not code:
                    errors.append(f'第{row_idx}行：角色名称和角色编码为必填项')
                    error_count += 1
                    continue

                # 转换布尔值
                is_active = is_active_str in ['是', 'True', 'true', '1', 1]
                is_system = is_system_str in ['是', 'True', 'true', '1', 1]

                # 检查角色是否已存在
                if Role.objects.filter(code=code).exists():
                    errors.append(f'第{row_idx}行：角色编码 {code} 已存在')
                    error_count += 1
                    continue

                # 创建角色
                role = Role.objects.create(
                    name=name,
                    code=code,
                    description=description,
                    is_active=is_active,
                    is_system=is_system
                )

                success_count += 1

            except Exception as e:
                errors.append(f'第{row_idx}行：{str(e)}')
                error_count += 1

        return Response({
            'code': 200,
            'message': f'导入完成，成功{success_count}条，失败{error_count}条',
            'data': {
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors
            }
        })

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导入失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_roles(request):
    """
    导出角色数据
    GET /api/roles/export/
    """
    try:
        import io
        from django.http import HttpResponse
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        # 获取查询参数
        search = request.GET.get('search', '')
        type_filter = request.GET.get('type', '')
        status_filter = request.GET.get('status', '')

        # 构建查询条件
        queryset = Role.objects.all()

        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(code__icontains=search) |
                models.Q(description__icontains=search)
            )

        if type_filter == 'system':
            queryset = queryset.filter(is_system=True)
        elif type_filter == 'custom':
            queryset = queryset.filter(is_system=False)

        if status_filter == 'active':
            queryset = queryset.filter(is_active=True)
        elif status_filter == 'inactive':
            queryset = queryset.filter(is_active=False)

        roles = queryset.order_by('id')

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = '角色数据'

        # 设置表头
        headers = ['角色ID', '角色名称', '角色编码', '角色描述', '角色类型', '用户数量', '状态', '创建时间', '更新时间']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')

        # 设置列宽
        column_widths = [10, 20, 15, 30, 15, 12, 10, 20, 20]
        columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I']
        for i, width in enumerate(column_widths):
            ws.column_dimensions[columns[i]].width = width

        # 填充数据
        for row_idx, role in enumerate(roles, 2):
            # 获取用户数量 - 简化版
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user_count = User.objects.filter(role=role.code).count()

            ws.cell(row=row_idx, column=1, value=role.id)
            ws.cell(row=row_idx, column=2, value=role.name)
            ws.cell(row=row_idx, column=3, value=role.code)
            ws.cell(row=row_idx, column=4, value=role.description or '')
            ws.cell(row=row_idx, column=5, value='系统角色' if role.is_system else '自定义角色')
            ws.cell(row=row_idx, column=6, value=user_count)
            ws.cell(row=row_idx, column=7, value='启用' if role.is_active else '停用')
            ws.cell(row=row_idx, column=8, value=role.created_at.strftime('%Y-%m-%d %H:%M:%S') if role.created_at else '')
            ws.cell(row=row_idx, column=9, value=role.updated_at.strftime('%Y-%m-%d %H:%M:%S') if role.updated_at else '')

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 创建HTTP响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="角色数据_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response

    except Exception as e:
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
def role_user_remove_view(request, pk, user_id):
    """
    从角色中移除用户
    DELETE /api/roles/{id}/users/{user_id}/ - 从角色中移除指定用户
    """
    try:
        role = Role.objects.get(pk=pk)
    except Role.DoesNotExist:
        return Response({
            'code': 404,
            'message': '角色不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({
            'code': 404,
            'message': '用户不存在',
            'data': {}
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        # 简化版：直接清空用户角色
        if user.role == role.code:
            user.role = ''
            user.save(update_fields=['role'])

            return Response({
                'code': 200,
                'message': f'成功将用户 {user.real_name} 从角色中移除',
                'data': {}
            })
        else:
            return Response({
                'code': 404,
                'message': '用户角色关联不存在',
                'data': {}
            }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'移除用户失败: {str(e)}',
            'data': {}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
