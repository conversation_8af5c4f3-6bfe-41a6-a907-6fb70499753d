<template>
  <a-modal :visible="visible" title="打印需求提报单配置" width="80%" :footer="null" @cancel="$emit('cancel')">
    <div class="print-config-container">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <h4>筛选条件</h4>
        <a-form layout="inline" :model="filters">
          <a-row :gutter="12" align="bottom">
            <a-col :span="4">
              <a-form-item label="需求单位" :wrapper-col="{ span: 24 }">
                <a-select v-model:value="filters.department" placeholder="选择需求单位" allowClear show-search
                  :filter-option="filterOption" @change="searchRecords">
                  <a-select-option v-for="dept in departmentOptions" :key="dept.value" :value="dept.value"
                    :label="dept.label">
                    {{ dept.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item label="申请人" :wrapper-col="{ span: 24 }">
                <a-select v-model:value="filters.applicant" placeholder="选择申请人" allowClear show-search
                  :filter-option="filterOption" @change="searchRecords">
                  <a-select-option v-for="applicant in applicantOptions" :key="applicant.value" :value="applicant.value"
                    :label="applicant.label">
                    {{ applicant.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item label="采购类型" :wrapper-col="{ span: 24 }">
                <a-select v-model:value="filters.purchase_type" placeholder="选择采购类型" allowClear @change="searchRecords">
                  <a-select-option value="unified">统一采购</a-select-option>
                  <a-select-option value="self">自行采购</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item label="提交时间" :wrapper-col="{ span: 24 }">
                <a-range-picker v-model:value="filters.date_range" @change="searchRecords" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="5">
              <a-form-item :wrapper-col="{ span: 24 }">
                <a-space>
                  <a-button @click="searchRecords" type="primary" size="small">查询记录</a-button>
                  <a-button @click="resetFilters" size="small">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 打印字段配置 -->
      <div class="fields-section">
        <a-collapse v-model:activeKey="fieldsCollapseKey" size="small">
          <a-collapse-panel key="1" header="打印字段配置">
            <template #extra>
              <a-space>
                <a-button @click.stop="selectAllFields" size="small">全选</a-button>
                <a-button @click.stop="resetFields" size="small">重置</a-button>
                <a-button @click.stop="selectRequiredFields" size="small">仅必选</a-button>
              </a-space>
            </template>
            <a-checkbox-group v-model:value="selectedFields" @change="onFieldsChange">
              <div class="export-fields-container">
                <div v-for="category in fieldCategories" :key="category.title" class="export-field-category">
                  <div class="export-category-header">
                    <h5 class="export-category-title">{{ category.title }}</h5>
                  </div>
                  <div class="export-category-fields">
                    <div v-for="field in category.fields" :key="field.key" class="export-field-option">
                      <a-checkbox :value="field.key" :disabled="field.required">
                        <span class="field-title">{{ field.title }}</span>
                        <a-tag v-if="field.required" size="small" color="blue">必选</a-tag>
                      </a-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </a-checkbox-group>
          </a-collapse-panel>
        </a-collapse>
      </div>

      <!-- 选择打印记录 -->
      <div class="records-section">
        <h4>选择打印记录 ({{ selectedRecords.length }}/{{ records.length }})
          <a-space style="margin-left: 16px;">
            <span style="font-size: 12px; font-weight: normal;">每页显示：</span>
            <a-select v-model:value="recordPageSize" size="small" style="width: 80px;" @change="onRecordPageSizeChange">
              <a-select-option :value="10">10条</a-select-option>
              <a-select-option :value="20">20条</a-select-option>
              <a-select-option :value="50">50条</a-select-option>
              <a-select-option :value="100">100条</a-select-option>
            </a-select>
          </a-space>
        </h4>
        <a-table :columns="recordColumns" :data-source="records" :row-selection="recordRowSelection"
          :pagination="{ pageSize: recordPageSize, showSizeChanger: false, showQuickJumper: true }" size="small"
          :scroll="{ x: 'max-content', y: 400 }" row-key="id">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'purchase_type'">
              {{ record.purchase_type === 'unified' ? '统一采购' : '自行采购' }}
            </template>
            <template v-else-if="column.dataIndex === 'total_amount'">
              ¥{{ parseFloat(record.total_amount || 0).toFixed(2) }}
            </template>
            <template v-else-if="column.dataIndex === 'unit_price'">
              ¥{{ parseFloat(record.unit_price || 0).toFixed(2) }}
            </template>
            <template v-else-if="column.dataIndex === 'created_at' || column.dataIndex === 'submission_date'">
              {{ record[column.dataIndex] ? new Date(record[column.dataIndex]).toLocaleDateString() : '-' }}
            </template>
          </template>
        </a-table>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <a-space>
          <a-button @click="previewPrint" :disabled="selectedRecords.length === 0">
            <EyeOutlined />
            打印预览 ({{ selectedRecords.length }}条)
          </a-button>
          <a-button type="primary" @click="executePrint" :disabled="selectedRecords.length === 0" :loading="printing">
            <PrinterOutlined />
            直接打印 ({{ selectedRecords.length }}条)
          </a-button>
          <a-button @click="$emit('cancel')">取消</a-button>
        </a-space>
      </div>
    </div>

    <!-- 打印预览模态框 -->
    <a-modal :open="previewVisible" title="需求提报单打印预览" width="90%" :footer="null" class="print-preview-modal"
      @cancel="previewVisible = false">
      <div class="print-preview-content" ref="printContent">
        <div v-for="(pageRecords, pageIndex) in paginatedRecords" :key="`table-${pageIndex}`" class="print-page">
          <div class="page-header">
            <h2>{{ getPrintTitle() }}</h2>
            <div class="page-info">
              第 {{ pageIndex + 1 }} 页，共 {{ paginatedRecords.length }} 页 |
              本页 {{ pageRecords.length }} 条，总计 {{ selectedRecordData.length }} 条记录
            </div>
          </div>

          <table class="print-table">
            <thead>
              <tr>
                <th v-for="field in getDisplayFields()" :key="field.key" :style="getColumnStyle(field.key)">
                  {{ field.title }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="record in pageRecords" :key="record.id">
                <td v-for="field in getDisplayFields()" :key="field.key" :style="getColumnStyle(field.key)">
                  {{ getFieldValue(record, field.key) }}
                </td>
              </tr>
            </tbody>
          </table>

          <div class="page-footer">
            <div class="footer-info">
              <span>打印时间：{{ new Date().toLocaleString() }}</span>
            </div>
            <div class="signature-area">
              <div class="signature-item">
                <span class="signature-label">申请人签字：</span>
                <span class="signature-line">_________________</span>
                <span class="signature-date">日期：_________</span>
              </div>
              <div class="signature-item">
                <span class="signature-label">批准人签字：</span>
                <span class="signature-line">_________________</span>
                <span class="signature-date">日期：_________</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <a-space>
          <a-button @click="printDocument" type="primary" :loading="printing">
            <PrinterOutlined />
            确认打印
          </a-button>
          <a-button @click="previewVisible = false">关闭预览</a-button>
        </a-space>
      </template>
    </a-modal>
  </a-modal>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PrinterOutlined, EyeOutlined } from '@ant-design/icons-vue'
import api from '@/api'
import { getStatusConfig } from '@/utils/status'
import { getPageFieldCategories, getPageColumnOptions } from '@/utils/validation'

export default {
  name: 'RequestPrintConfig',
  components: {
    PrinterOutlined,
    EyeOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['cancel', 'print-complete'],
  setup(props, { emit }) {
    const records = ref([])
    const selectedRecords = ref([])
    const selectedRecordData = ref([])
    const departmentOptions = ref([])
    const applicantOptions = ref([])
    const printing = ref(false)
    const previewVisible = ref(false)
    const printContent = ref(null)

    // 字段配置折叠状态
    const fieldsCollapseKey = ref([])

    // 记录分页大小
    const recordPageSize = ref(20)

    const filters = ref({
      department: undefined,
      applicant: undefined,
      purchase_type: undefined,
      date_range: undefined
    })

    const printConfig = ref({
      mode: 'table',
      recordsPerPage: 20
    })

    // 分页记录
    const paginatedRecords = computed(() => {
      const pageSize = printConfig.value.recordsPerPage
      const pages = []
      for (let i = 0; i < selectedRecordData.value.length; i += pageSize) {
        pages.push(selectedRecordData.value.slice(i, i + pageSize))
      }
      return pages
    })

    // 基于业务流程的字段配置（需求提报阶段）
    const columnOptions = getPageColumnOptions('requirement')
    const rawFieldCategories = getPageFieldCategories('requirement')

    // 构建字段分类数据结构
    const fieldCategories = computed(() => {
      return rawFieldCategories.map(category => ({
        title: category.title,
        fields: columnOptions
          .filter(opt => opt.category === category.key && opt.key !== 'action')
          .map(opt => ({
            key: opt.key,
            title: opt.title,
            required: opt.required || false
          }))
      })).filter(category => category.fields.length > 0)
    })

    // 字段配置 - 使用与API数据匹配的字段名
    const selectedFields = ref([
      'id', 'item_name', 'specification', 'unit', 'quantity',
      'unit_price', 'total_amount', 'requester_name',
      'hierarchy_path', 'purchase_type', 'created_at', 'submission_date'
    ])

    // 记录表格列配置 - 根据选中字段动态生成
    const recordColumns = computed(() => {
      const allColumns = [
        { title: 'ID', dataIndex: 'id', key: 'id', width: 80, align: 'center' },
        { title: '物品名称', dataIndex: 'item_name', key: 'item_name', width: 150, align: 'center', ellipsis: true },
        { title: '规格型号', dataIndex: 'specification', key: 'specification', width: 120, align: 'center', ellipsis: true },
        { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, align: 'center' },
        { title: '单价', dataIndex: 'unit_price', key: 'unit_price', width: 100, align: 'center' },
        { title: '总金额', dataIndex: 'total_amount', key: 'total_amount', width: 100, align: 'center' },
        { title: '申请人', dataIndex: 'requester_name', key: 'requester_name', width: 100, align: 'center' },
        { title: '需求单位', dataIndex: 'hierarchy_path', key: 'hierarchy_path', width: 120, align: 'center', ellipsis: true },
        { title: '采购类型', dataIndex: 'purchase_type', key: 'purchase_type', width: 100, align: 'center' },
        { title: '状态', dataIndex: 'status', key: 'status', width: 100, align: 'center' },
        { title: '提交时间', dataIndex: 'submission_date', key: 'submission_date', width: 120, align: 'center' }
      ]

      // 根据选中字段过滤列
      return allColumns.filter(col => selectedFields.value.includes(col.key))
    })

    // 行选择配置
    const recordRowSelection = {
      selectedRowKeys: selectedRecords,
      onChange: (keys, rows) => {
        selectedRecords.value = keys
        selectedRecordData.value = rows
      },
      getCheckboxProps: (record) => ({
        disabled: record.status !== 'approved', // 只允许选择已审批的记录
        name: record.item_name,
      }),
    }

    // 初始化部门选项 - 用于下拉选择，使用专门的无分页接口
    const initDepartmentOptions = async () => {
      try {
        const response = await api.departments.getAll()
        if (response.code === 200) {
          const departments = response.data || []
          departmentOptions.value = departments.map(dept => ({
            value: dept.id,
            label: dept.dept_name,
            hierarchy_path: dept.hierarchy_path,
            parent_id: dept.parent_id
          }))
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('获取部门列表失败:', error)
        }
      }
    }

    // 搜索记录
    const searchRecords = async () => {
      try {
        const params = {
          status_in: 'approved,purchased,accepted,settled', // 获取可打印的记录
          page_size: 50 // 优化页面大小
        }

        if (filters.value.department) {
          // 根据选择的部门获取部门信息
          const selectedDept = departmentOptions.value.find(d => d.value == filters.value.department)
          if (selectedDept) {
            // 如果是一级部门（没有parent_id），筛选该部门及其所有子部门
            if (!selectedDept.parent_id) {
              // 一级部门：使用部门名称匹配层级路径，确保匹配该部门及其所有子部门
              params.hierarchy_path__icontains = selectedDept.label
            } else {
              // 二级部门：使用层级路径精确匹配
              params.hierarchy_path__icontains = selectedDept.hierarchy_path
            }
          }
        }
        if (filters.value.applicant) {
          params.requester__username__icontains = filters.value.applicant
        }
        if (filters.value.purchase_type) {
          params.purchase_type = filters.value.purchase_type
        }
        if (filters.value.date_range && filters.value.date_range.length === 2) {
          params.submission_date_start = filters.value.date_range[0].format('YYYY-MM-DD')
          params.submission_date_end = filters.value.date_range[1].format('YYYY-MM-DD')
        }

        const response = await api.purchaseRequests.getList(params)
        if (response.code === 200) {
          records.value = response.data.results || []

          // 提取申请人选项
          const applicants = [...new Set(records.value.map(r => r.requester_name || r.applicant_name).filter(Boolean))]
          applicantOptions.value = applicants.map(name => ({
            value: name,
            label: name
          }))
        }
      } catch (error) {
        message.error('获取记录失败')
      }
    }

    // 重置筛选条件
    const resetFilters = () => {
      filters.value = {
        department: undefined,
        applicant: undefined,
        purchase_type: undefined,
        date_range: undefined
      }
      selectedRecords.value = []
      selectedRecordData.value = []
    }

    // 供应商筛选函数
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 记录分页大小变化处理
    const onRecordPageSizeChange = (value) => {
      recordPageSize.value = value
    }

    // 获取显示字段
    const getDisplayFields = () => {
      return fieldCategories.value.flatMap(category => category.fields)
        .filter(field => selectedFields.value.includes(field.key))
    }

    // 获取列样式
    const getColumnStyle = (fieldKey) => {
      const widthMap = {
        'id': '60px',
        'item_name': '120px',
        'specification': '100px',
        'unit': '60px',
        'quantity': '80px',
        'unit_price': '90px',
        'total_amount': '90px',
        'requester_name': '80px',
        'hierarchy_path': '100px',
        'purchase_type': '80px',
        'created_at': '100px',
        'submission_date': '100px'
      }
      return { width: widthMap[fieldKey] || '80px' }
    }

    // 字段选择相关方法
    const selectAllFields = () => {
      const allFields = fieldCategories.value.flatMap(category =>
        category.fields.map(field => field.key)
      )
      selectedFields.value = allFields
    }

    const selectRequiredFields = () => {
      const requiredFields = fieldCategories.value.flatMap(category =>
        category.fields.filter(field => field.required).map(field => field.key)
      )
      selectedFields.value = requiredFields
    }

    const resetFields = () => {
      // 获取必需字段作为默认选择
      const requiredFields = fieldCategories.value.flatMap(category =>
        category.fields.filter(field => field.required).map(field => field.key)
      )
      selectedFields.value = requiredFields.length > 0 ? requiredFields : [
        'id', 'item_name', 'specification', 'unit', 'quantity',
        'unit_price', 'total_amount', 'requester_name',
        'hierarchy_path', 'purchase_type', 'created_at', 'submission_date'
      ]
    }

    const onFieldsChange = () => {
      // 字段变化处理 - 表格列会自动根据selectedFields更新
    }

    // 获取打印标题
    const getPrintTitle = () => {
      if (filters.value.department) {
        const selectedDept = departmentOptions.value.find(d => d.value == filters.value.department)
        if (selectedDept) {
          return `${selectedDept.label}需求提报单汇总表`
        }
      }
      return '需求提报单汇总表'
    }

    // 获取字段值
    const getFieldValue = (record, fieldKey) => {
      const value = record[fieldKey]
      switch (fieldKey) {
        case 'purchase_type':
          return value === 'unified' ? '统一采购' : '自行采购'
        case 'created_at':
        case 'submission_date':
          return value ? new Date(value).toLocaleDateString() : '-'
        case 'budget_unit_price':
        case 'budget_total_amount':
          return value ? `¥${parseFloat(value).toFixed(2)}` : '-'
        default:
          return value || '-'
      }
    }

    // 状态相关方法
    const getStatusColor = (status) => {
      const config = getStatusConfig(status)
      return config.color
    }

    const getStatusText = (status) => {
      const config = getStatusConfig(status)
      return config.text
    }

    // 打印预览
    const previewPrint = () => {
      if (selectedRecords.value.length === 0) {
        message.warning('请选择要打印的记录')
        return
      }
      previewVisible.value = true
    }

    // 直接打印
    const executePrint = async () => {
      if (selectedRecords.value.length === 0) {
        message.warning('请先选择要打印的记录')
        return
      }

      printing.value = true
      try {
        await printDocument()
        message.success('打印任务已发送到系统打印队列')
        emit('print-complete', selectedRecordData.value)
        emit('cancel')
      } catch (error) {
        message.error(`打印失败: ${error.message}`)
      } finally {
        printing.value = false
      }
    }

    // 打印文档
    const printDocument = async () => {
      if (selectedRecords.value.length === 0) {
        message.error('请先选择要打印的记录')
        return
      }

      printing.value = true
      try {
        await new Promise(resolve => setTimeout(resolve, 100))

        // 创建打印窗口
        const printWindow = window.open('', '_blank', 'width=800,height=600')

        if (!printWindow) {
          throw new Error('无法打开打印窗口，请检查浏览器弹窗设置')
        }

        const printHTML = `<!DOCTYPE html><html><head><title>需求提报单打印</title><style>
* { margin: 0; padding: 0; box-sizing: border-box; }
body { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; line-height: 1.4; }
.print-page { page-break-after: always; padding: 20px; min-height: 100vh; }
.print-page:last-child { page-break-after: avoid; }
.page-header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px; background: none !important; background-color: transparent !important; background-image: none !important; }
.page-header h2 { font-size: 24px; margin-bottom: 8px; background: none !important; background-color: transparent !important; background-image: none !important; color: #333 !important; }
.page-info { font-size: 12px; color: #666; }
.print-table { width: 100%; border-collapse: collapse; font-size: 11px; }
.print-table th, .print-table td { border: 1px solid #d9d9d9; padding: 6px 4px; text-align: left; vertical-align: middle; }
.print-table th { background-color: #fafafa; font-weight: 600; text-align: center; }
.page-footer { margin-top: 30px; border-top: 1px solid #d9d9d9; padding-top: 15px; }
.footer-info { font-size: 10px; color: #666; margin-bottom: 20px; }
.signature-area { display: flex; justify-content: space-between; margin-top: 20px; }
.signature-item { display: flex; align-items: center; gap: 10px; }
.signature-label { font-size: 12px; color: #333; white-space: nowrap; }
.signature-line { font-size: 12px; color: #333; border-bottom: 1px solid #333; padding-bottom: 2px; min-width: 120px; text-align: center; }
.signature-date { font-size: 12px; color: #333; white-space: nowrap; }
@media print {
  @page {
    size: A4;
    margin: 15mm;
  }
  body {
    margin: 0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    background: white !important;
  }
  .print-page {
    margin: 0;
    padding: 0;
    box-shadow: none;
    border-radius: 0;
    background: white !important;
    min-height: auto;
    page-break-inside: avoid;
  }
  .page-header {
    background: white !important;
    border-bottom: 2px solid #000;
    margin-bottom: 10mm;
    padding-bottom: 5mm;
  }
  .page-header h2 {
    background: white !important;
    color: #000 !important;
    font-size: 16pt;
    font-weight: bold;
  }
  .print-table {
    font-size: 9pt;
    margin-bottom: 10mm;
  }
  .print-table th, .print-table td {
    padding: 2mm;
    border: 1px solid #000;
  }
  .print-table th {
    background: #f0f0f0 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  .page-footer {
    margin-top: 15mm;
    page-break-inside: avoid;
  }
}
</style></head><body>${printContent.value.innerHTML}</body></html>`

        printWindow.document.write(printHTML)
        printWindow.document.close()

        // 等待内容加载完成
        await new Promise(resolve => {
          if (printWindow.document.readyState === 'complete') {
            resolve()
          } else {
            printWindow.onload = resolve
            setTimeout(resolve, 2000) // 最多等待2秒
          }
        })

        // 聚焦到打印窗口并执行打印
        printWindow.focus()

        // 延迟一下确保窗口完全加载
        await new Promise(resolve => setTimeout(resolve, 500))

        // 执行打印
        printWindow.print()

        // 监听打印完成或取消事件
        const checkPrintStatus = () => {
          // 延迟关闭窗口，给用户时间完成打印操作
          setTimeout(() => {
            if (printWindow && !printWindow.closed) {
              printWindow.close()
            }
          }, 1000)
        }

        // 监听打印事件
        if (printWindow.matchMedia) {
          const mediaQueryList = printWindow.matchMedia('print')
          mediaQueryList.addListener(checkPrintStatus)
        } else {
          // 备用方案：延迟关闭
          checkPrintStatus()
        }

        message.success('打印任务已发送到系统打印队列')
        emit('print-complete', selectedRecordData.value)
        previewVisible.value = false
      } catch (error) {
        message.error(`打印失败: ${error.message}`)
      } finally {
        printing.value = false
      }
    }

    // 监听visible变化
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        initDepartmentOptions()
        searchRecords()
      }
    })

    return {
      records,
      selectedRecords,
      selectedRecordData,
      departmentOptions,
      applicantOptions,
      printing,
      previewVisible,
      printContent,
      filters,
      printConfig,
      fieldsCollapseKey,
      recordPageSize,

      paginatedRecords,
      selectedFields,
      fieldCategories,
      recordColumns,
      recordRowSelection,
      initDepartmentOptions,
      searchRecords,
      resetFilters,
      filterOption,
      onRecordPageSizeChange,

      selectAllFields,
      selectRequiredFields,
      resetFields,
      onFieldsChange,
      getDisplayFields,
      getPrintTitle,
      getFieldValue,
      getColumnStyle,
      previewPrint,
      executePrint,
      printDocument,
      getStatusColor,
      getStatusText
    }
  }
}
</script>

<style scoped>
.print-config-container {
  max-height: 80vh;
  overflow-y: auto;
}

.filter-section,
.records-section,
.fields-section,
.actions-section {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.filter-section h4,
.records-section h4,
.fields-section h4 {
  margin: 0 0 12px 0;
  color: #1890ff;
  font-weight: 600;
  font-size: 14px;
}

.fields-section .ant-collapse {
  margin-bottom: 0;
  background: transparent;
  border: none;
}

.fields-section .ant-collapse-content-box {
  padding: 8px 0;
}

.fields-section .ant-collapse-header {
  padding: 8px 0 !important;
  background: transparent !important;
}

/* 打印字段配置样式 - 与验收页面保持一致 */
.export-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
}

.export-field-category {
  flex: 1;
  min-width: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  overflow: hidden;
}

.export-category-header {
  background-color: #e6f7ff;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 12px;
}

.export-category-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.export-category-fields {
  padding: 12px;
}

.export-field-option {
  margin-bottom: 8px;
}

.export-field-option:last-child {
  margin-bottom: 0;
}

.field-title {
  margin-right: 8px;
}

.actions-section {
  text-align: center;
  border: none;
  background: #fafafa;
}

/* 打印预览样式 */
.print-preview-modal .ant-modal-body {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

/* 强制移除所有可能的蓝色背景 */
.print-preview-content .page-header,
.print-preview-content .page-header *,
.print-page .page-header,
.print-page .page-header *,
div.page-header,
div.page-header h2,
.ant-modal .page-header,
.ant-modal .page-header h2 {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  background-attachment: initial !important;
  background-blend-mode: initial !important;
  background-clip: initial !important;
  background-origin: initial !important;
  background-position: initial !important;
  background-repeat: initial !important;
  background-size: initial !important;
}

.print-preview-content {
  background: #f5f5f5;
  padding: 20px;
}

.print-page {
  background: white;
  margin-bottom: 20px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.print-page:last-child {
  margin-bottom: 0;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #333;
  padding-bottom: 10px;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
}

.page-header h2 {
  font-size: 20px;
  margin-bottom: 6px;
  color: #333;
  font-weight: 600;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
}

.page-info {
  font-size: 12px;
  color: #666;
}

.print-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.print-table th,
.print-table td {
  border: 1px solid #d9d9d9;
  padding: 8px 4px;
  text-align: left;
  vertical-align: middle;
}

.print-table th {
  background-color: #fafafa;
  font-weight: 600;
  text-align: center;
}

.print-table td {
  font-size: 11px;
}

.page-footer {
  margin-top: 30px;
  border-top: 1px solid #d9d9d9;
  padding-top: 15px;
}

.footer-info {
  font-size: 10px;
  color: #666;
  margin-bottom: 20px;
}

.signature-area {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.signature-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.signature-label {
  font-size: 12px;
  color: #333;
  white-space: nowrap;
}

.signature-line {
  font-size: 12px;
  color: #333;
  border-bottom: 1px solid #333;
  padding-bottom: 2px;
  min-width: 120px;
  text-align: center;
}

.signature-date {
  font-size: 12px;
  color: #333;
  white-space: nowrap;
}
</style>
