def main():
    """
    运行 Django 管理任务。

    此函数设置 Django 设置模块环境变量，尝试导入 Django 命令行执行函数，
    若导入失败则抛出包含调试信息的 ImportError 异常，最后执行命令行参数指定的管理任务。
    """
    # 设置默认的 Django 设置模块环境变量，指定项目使用的配置文件
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'purchase_system.settings')
    try:
        # 尝试从 Django 核心管理模块导入命令行执行函数
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        # 若导入失败，抛出新的 ImportError 异常，提示用户检查 Django 是否安装、
        # 是否在 PYTHONPATH 环境变量中，以及是否激活了虚拟环境
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    # 执行命令行参数指定的 Django 管理任务
    execute_from_command_line(sys.argv)
#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'purchase_system.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
