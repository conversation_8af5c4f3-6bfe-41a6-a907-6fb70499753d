# Generated by Django 5.2.3 on 2025-06-15 20:27

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0009_remove_reimbursement_purchase_request_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='purchaserequest',
            new_name='idx_purchase_status',
            old_name='purchase_pu_status_16eb7e_idx',
        ),
        migrations.RenameIndex(
            model_name='purchaserequest',
            new_name='idx_purchase_submitted',
            old_name='purchase_pu_submiss_2de80e_idx',
        ),
        migrations.RenameIndex(
            model_name='purchaserequest',
            new_name='idx_purchase_item_dept',
            old_name='purchase_pu_item_na_e9a31b_idx',
        ),
        migrations.AddIndex(
            model_name='dictionary',
            index=models.Index(fields=['type'], name='idx_dict_type'),
        ),
        migrations.AddIndex(
            model_name='dictionary',
            index=models.Index(fields=['status'], name='idx_dict_status'),
        ),
        migrations.AddIndex(
            model_name='dictionary',
            index=models.Index(fields=['type', 'status'], name='idx_dict_type_status'),
        ),
        migrations.AddIndex(
            model_name='dictionary',
            index=models.Index(fields=['type', 'order'], name='idx_dict_type_order'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['dept_id'], name='idx_purchase_dept'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['requester'], name='idx_purchase_requester'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['created_at'], name='idx_purchase_created'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['final_approval_date'], name='idx_purchase_approved'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['purchase_date'], name='idx_purchase_purchased'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['acceptance_date'], name='idx_purchase_accepted'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['reimbursement_date'], name='idx_purchase_reimbursed'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['status', 'dept_id'], name='idx_purchase_status_dept'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['status', 'created_at'], name='idx_purchase_status_time'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['hierarchy_path', 'status'], name='idx_purchase_path_status'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['unit_price'], name='idx_purchase_price'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['history_avg_price'], name='idx_purchase_avg_price'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['order_number'], name='idx_purchase_order_no'),
        ),
    ]
