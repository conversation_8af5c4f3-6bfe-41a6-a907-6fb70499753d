from django.contrib import admin
from django.contrib.auth.forms import ReadOnlyPasswordHashField
from django import forms
from .models import User


class UserCreationForm(forms.ModelForm):
    """用户创建表单"""
    password1 = forms.CharField(label='密码', widget=forms.PasswordInput)
    password2 = forms.CharField(label='确认密码', widget=forms.PasswordInput)

    class Meta:
        model = User
        fields = ('username', 'email', 'real_name', 'phone', 'dept_id', 'role')

    def clean_password2(self):
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("密码不匹配")
        return password2

    def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password1"])
        if commit:
            user.save()
        return user


class UserChangeForm(forms.ModelForm):
    """用户修改表单"""
    password = ReadOnlyPasswordHashField(
        label="密码",
        help_text="原始密码不会存储，因此无法查看此用户的密码，但您可以使用 <a href=\"../password/\">此表单</a> 更改密码。"
    )

    class Meta:
        model = User
        fields = ('username', 'password', 'email', 'first_name', 'last_name',
                 'real_name', 'phone', 'dept_id', 'role', 'is_active', 'is_staff', 'is_superuser')

    def clean_password(self):
        return self.initial["password"]


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    """用户管理后台"""

    form = UserChangeForm
    add_form = UserCreationForm

    list_display = ['username', 'real_name', 'phone', 'dept_id', 'role', 'is_active', 'is_staff', 'date_joined']
    list_filter = ['role', 'is_active', 'is_staff', 'is_superuser', 'date_joined']
    search_fields = ['username', 'real_name', 'phone', 'email']
    ordering = ['-date_joined']

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('个人信息', {'fields': ('first_name', 'last_name', 'real_name', 'email', 'phone')}),
        ('工作信息', {'fields': ('dept_id', 'role')}),
        ('权限', {'fields': ('is_active', 'is_staff', 'is_superuser')}),
        ('重要日期', {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'password1', 'password2'),
        }),
        ('个人信息', {'fields': ('real_name', 'email', 'phone')}),
        ('工作信息', {'fields': ('dept_id', 'role')}),
        ('权限', {'fields': ('is_active', 'is_staff', 'is_superuser')}),
    )

    readonly_fields = ('last_login', 'date_joined')

    def get_form(self, request, obj=None, **kwargs):
        """根据是否为新增用户返回不同的表单"""
        defaults = {}
        if obj is None:
            defaults['form'] = self.add_form
        defaults.update(kwargs)
        return super().get_form(request, obj, **defaults)
