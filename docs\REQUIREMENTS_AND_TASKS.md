# 采购管理系统需求与任务记录

## 目录
- [第一轮需求](#第一轮需求)
- [第二轮需求](#第二轮需求)
- [第三轮需求](#第三轮需求)

---

## 第一轮需求

### 需求描述
数据看板页面布局和性能优化

### 具体问题
1. 前端整体界面分四部分即头部、底部、侧边菜单栏以及主体页面，现在出现整体界面出现滚轮滚动使得整个界面都移动并且有些页面底部在可视界面下方而看不见，我想让头部、底部和左侧菜单栏应该固定出现在界面上，不随页面滚动而改变位置，右侧主体content页面大小应在整个可视界面内部，当内容溢出时应在页面出现滚轮实现上下或者左右滑动查看内容；

2. 左侧菜单栏数据看板项出现了选中时其背景宽度过长使得右侧溢出左侧菜单栏的问题，我需要左侧菜单栏所有选项长度均相同且美观合理；

3. 点击数据看板页面，页面反应延迟太高，图标需要过一会再能出现，且依次点击本月"、"本季度"、"本年"按钮时延迟会依次递增，可能是数据处理较慢，要求优化后端数据处理过程和前端页面渲染过程，尽量使用用户使用时达到无感刷新，同时本页面采购趋势分析图表的X轴时间显示不完全，只能显示一般时间，可以增高X轴到图表底部的距离使得X轴数据显示完全；

### 任务分解
1. **修复布局滚动问题**
   - 修改文件：`frontend/src/views/Layout.vue`
   - 修改内容：
     - 设置`.layout`为`height: 100vh; overflow: hidden`
     - 设置`.layout-header`为`position: fixed`
     - 调整`.layout-content`的`margin-top`和`height`
     - 优化`.layout-main`和`.main-content`的滚动属性

2. **修复左侧菜单栏样式问题**
   - 修改文件：`frontend/src/views/Layout.vue`
   - 修改内容：
     - 调整菜单项的`margin`和`width`
     - 添加`overflow: hidden`防止溢出
     - 统一菜单项宽度为`calc(100% - 16px)`

3. **优化数据看板性能**
   - 修改文件：
     - `backend/apps/purchase/views.py` - 添加缓存和查询优化
     - `frontend/src/views/Dashboard.vue` - 添加防抖和加载状态
   - 修改内容：
     - 后端添加Redis缓存，优化数据库查询
     - 前端添加防抖处理，并行加载数据
     - 增加图表X轴底部距离

### 完成状态
✅ 已完成

---

## 第二轮需求

### 需求描述
采购总览界面和数据看板页面进一步优化

### 具体问题
1. 经过刚才的修改，内容溢出改善了，但是主界面还有一个大的滚动条，我只需要在主体内容页面有滚动条，最外侧滚动条不需要，可能是距离头部和底部距离较大并且有margin，我希望主体内容部分和头部、左侧栏以及底部的距离缩小一点点。

2. 数据看板页面内容显示还是延迟太高，用户体验很不好，还需要再次优化一下后台数据库查询和数据处理过程使得从进入页面到数据渲染完成在0.5ms时间内，同时点击"本年"按钮后，"采购趋势分析"图标要求以月份为单位，否则以天为单位横坐标太过密集；"时间预警"部分尺寸大小与其他几个此部分不一致，请修改一致大小。

3. 采购总览页面用户体验及其差，反映在页面3个图表数据展示和采购记录列表数据查询时间特别长，可能是数据库数据较多，因此请优化后端关于此页面请求的数据库查询及数据处理时间，并且优化前端渲染过程尽量使页面从打开到渲染完成花费时间在0.3ms以内以达到无感刷新。

### 任务分解
1. **优化主界面布局间距**
   - 修改文件：`frontend/src/views/Layout.vue`
   - 修改内容：缩小padding和margin值

2. **优化数据看板性能和显示**
   - 修改文件：
     - `backend/apps/purchase/views.py`
     - `frontend/src/views/Dashboard.vue`
   - 修改内容：进一步优化查询和渲染

3. **优化采购总览页面性能**
   - 修改文件：
     - `backend/apps/purchase/views.py`
     - `frontend/src/views/purchase/Overview.vue`
   - 修改内容：并行加载数据，优化查询

### 完成状态
✅ 已完成

---

## 第三轮需求

### 需求描述
系统整体优化和功能修复

### 具体问题
1. 在本系统目录下建立一个文件夹unused，将所有与本系统无关或者未使用到的文件和文件夹移到该文件夹内，同时优化整个目录使整个目录结构条理清晰，方便开发者管理和二次调整；

2. 建立一个markdown格式文件，将前面每次我提的需求和后面将要提的需求写进去，再把每次根据需求生成的任务，修改步骤和涉及到的文件以及如何调整写进去，要求按顺序书写，条理清晰，结构紧凑，能使人一目了然，不同的需求间隔清晰；

3. 把系统各个文件定义的类、变量、方法函数等内容添加注释，解释该内容起什么作用，要求注释内容详实规范，清晰易懂，根据不同的内容选择行注释或者块注释，同时根据阿里巴巴编程规范格式化各个编程文件，使得开发者便于阅读，解决代码文件中遇到的警告。

### 任务分解
1. **整理项目目录结构**
   - 创建unused文件夹
   - 移动无关文件到unused目录
   - 优化目录结构

2. **创建需求文档**
   - 建立markdown格式文档
   - 记录所有需求和任务
   - 按时间顺序整理

3. **添加代码注释和格式化**
   - 为所有文件添加详实注释
   - 按阿里巴巴规范格式化代码
   - 解决代码警告

### 完成状态
🔄 进行中
