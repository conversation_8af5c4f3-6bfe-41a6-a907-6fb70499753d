# Generated by Django 5.2.3 on 2025-06-15 09:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0005_merge_20250614_1254'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaserequest',
            name='purchase_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='采购日期'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='purchaser',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchased_requests', to=settings.AUTH_USER_MODEL, verbose_name='采购人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='fund_project_name',
            field=models.CharField(help_text='关联数据字典-经费来源', max_length=100, verbose_name='经费项目名称'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='requirement_source',
            field=models.CharField(help_text='用户手动输入', max_length=100, verbose_name='需求来源'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='status',
            field=models.CharField(choices=[('draft', '草稿'), ('pending', '待审批'), ('approved', '已审批'), ('rejected', '已驳回'), ('pending_purchase', '待采购'), ('purchased', '已采购'), ('pending_acceptance', '待验收'), ('accepted', '已验收'), ('pending_reimbursement', '待报销审批'), ('reimbursed', '已结算'), ('reimbursement_rejected', '报销驳回')], default='draft', max_length=30, verbose_name='状态'),
        ),
    ]
