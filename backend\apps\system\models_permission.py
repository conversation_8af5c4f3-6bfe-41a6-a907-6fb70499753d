"""
统一权限管理模型
"""
from django.db import models


class Permission(models.Model):
    """统一权限模型"""
    CATEGORY_CHOICES = [
        ('page', '页面权限'),
        ('button', '按钮权限'),
        ('api', 'API权限'),
        ('data', '数据权限'),
    ]

    name = models.CharField('权限名称', max_length=100)
    code = models.CharField('权限代码', max_length=100, unique=True)
    category = models.CharField('权限类型', max_length=20, choices=CATEGORY_CHOICES, default='page')
    module = models.CharField('所属模块', max_length=50)
    module_name = models.CharField('模块中文名称', max_length=100, blank=True, null=True)
    description = models.TextField('权限描述', blank=True, null=True)

    # 父权限（用于权限层级）
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父权限'
    )

    sort_order = models.IntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    is_system = models.BooleanField('是否系统权限', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'sys_permission'
        verbose_name = '系统权限'
        verbose_name_plural = '系统权限'
        ordering = ['module', 'sort_order', 'code']
        default_permissions = ()  # 禁用Django自动权限创建
        
    def __str__(self):
        return f"{self.name} ({self.code})"


class RolePermission(models.Model):
    """角色权限关联"""
    role = models.ForeignKey(
        'roles.Role',
        on_delete=models.CASCADE,
        verbose_name='角色'
    )
    permission = models.ForeignKey(
        Permission,
        on_delete=models.CASCADE,
        verbose_name='权限'
    )
    granted_at = models.DateTimeField('授权时间', auto_now_add=True)
    granted_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='授权人'
    )
    
    class Meta:
        db_table = 'sys_role_permission'
        verbose_name = '角色权限'
        verbose_name_plural = '角色权限'
        unique_together = ['role', 'permission']
        default_permissions = ()  # 禁用Django自动权限创建
        
    def __str__(self):
        return f"{self.role.name} - {self.permission.name}"


class UserPermission(models.Model):
    """用户权限关联（用于特殊权限分配）"""
    user = models.ForeignKey(
        'authentication.User',
        on_delete=models.CASCADE,
        verbose_name='用户'
    )
    permission = models.ForeignKey(
        Permission,
        on_delete=models.CASCADE,
        verbose_name='权限'
    )
    is_granted = models.BooleanField('是否授权', default=True)
    granted_at = models.DateTimeField('授权时间', auto_now_add=True)
    granted_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_permissions',
        verbose_name='授权人'
    )
    
    class Meta:
        db_table = 'sys_user_permission'
        verbose_name = '用户权限'
        verbose_name_plural = '用户权限'
        unique_together = ['user', 'permission']
        default_permissions = ()  # 禁用Django自动权限创建
        
    def __str__(self):
        status = '授权' if self.is_granted else '拒绝'
        return f"{self.user.username} - {self.permission.name} ({status})"
