<template>
  <div class="purchase-request-form">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <EditOutlined />
          {{ isEditMode ? "编辑采购需求" : "新建采购需求" }}
        </h1>
        <p class="page-subtitle">请填写详细的采购需求信息，确保信息准确完整</p>
      </div>
      <div class="header-actions">
        <a-button @click="goBack" class="secondary-action-btn">
          <ArrowLeftOutlined />
          返回
        </a-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content business-card">
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical" class="purchase-form">
        <!-- 基本信息区域 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="物品种类" name="item_category">
                <dict-selector v-model:model-value="formData.item_category" dict-type="item_category" placeholder="请选择物品种类" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="物品名称" name="item_name">
                <a-input v-model:value="formData.item_name" placeholder="请输入物品名称" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="规格型号" name="specification">
                <a-input v-model:value="formData.specification" placeholder="请输入规格型号" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="单位" name="unit">
                <dict-selector v-model:model-value="formData.unit" dict-type="unit" placeholder="请选择单位" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="需求数量" name="budget_quantity">
                <a-input-number v-model:value="formData.budget_quantity" placeholder="请输入需求数量" style="width: 100%"
                  @change="calculateAmount" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="预算单价" name="budget_unit_price">
                <a-input-number v-model:value="formData.budget_unit_price" placeholder="请输入预算单价" style="width: 100%"
                  @change="calculateAmount" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 需求信息区域 -->
        <div class="form-section">
          <h3 class="section-title">需求信息</h3>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="需求单位" name="hierarchy_path">
                <department-tree-select v-model:model-value="formData.hierarchy_path" return-path leaf-only
                  placeholder="请选择需求单位" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="采购方式" name="procurement_method">
                <dict-selector v-model:model-value="formData.procurement_method" dict-type="procurement_method"
                  placeholder="请选择采购方式" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="需求来源" name="requirement_source">
                <a-input v-model:value="formData.requirement_source" placeholder="请输入需求来源" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="经费项目名称" name="fund_project_name">
                <dict-selector v-model:model-value="formData.fund_project_name" dict-type="fund_project"
                  placeholder="请选择经费项目名称" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="采购类型" name="purchase_type">
                <dict-selector v-model:model-value="formData.purchase_type" dict-type="purchase_type" placeholder="请选择采购类型" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 金额和备注区域 -->
        <div class="form-section">
          <h3 class="section-title">金额和备注</h3>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="总金额">
                <a-input :value="`¥${(
                  (formData.budget_quantity || 0) *
                  (formData.budget_unit_price || 0)
                ).toFixed(2)}`" disabled style="font-weight: bold; color: #1890ff" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="备注" name="remarks">
                <a-textarea v-model:value="formData.remarks" placeholder="请输入备注信息" :rows="3" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 驳回/退回信息区域 -->
        <div v-if="formData.rejection_reason || formData.return_reason" class="form-section">
          <h3 class="section-title">处理意见</h3>

          <!-- 审核驳回信息 -->
          <div v-if="formData.rejection_reason" class="rejection-info">
            <a-alert message="审核驳回" :description="formData.rejection_reason" type="error" show-icon
              style="margin-bottom: 16px" />
          </div>

          <!-- 采购退回信息 -->
          <div v-if="formData.return_reason" class="return-info">
            <a-alert message="采购退回" :description="formData.return_reason" type="warning" show-icon
              style="margin-bottom: 16px" />
          </div>
        </div>

        <!-- 附件上传区域 -->
        <div class="form-section">
          <h3 class="section-title">附件上传</h3>
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="需求说明文档" name="attachment">
                <a-upload v-model:file-list="fileList" name="file" :before-upload="beforeUpload"
                  @change="handleFileChange" accept=".xlsx,.xls,.pdf" :max-count="1">
                  <a-button>
                    <UploadOutlined />
                    上传文档
                  </a-button>
                  <template #tip>
                    <div class="ant-upload__tip">
                      支持 Excel/PDF 格式，文件大小不超过 10MB
                    </div>
                  </template>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 历史价格趋势图表（暂时注释掉） -->
        <!-- <a-row :gutter="16" v-if="formData.item_name && formData.hierarchy_path">
          <a-col :span="24">
            <PriceTrendChart
              :item-name="formData.item_name"
              :dept-id="getDeptIdFromPath(formData.hierarchy_path)"
              :current-price="formData.unit_price"
            />
          </a-col>
        </a-row> -->
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="form-actions business-card">
      <a-space size="large">
        <a-button @click="goBack" class="secondary-action-btn" size="large">
          <CloseOutlined />
          取消
        </a-button>
        <a-button @click="saveAsDraft" :loading="loading" class="secondary-action-btn" size="large">
          <SaveOutlined />
          保存草稿
        </a-button>
        <a-button type="primary" @click="submitForm" :loading="loading" class="primary-action-btn" size="large">
          <CheckOutlined />
          {{ isEditMode ? "更新" : "提交" }}
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { message } from "ant-design-vue";
import {
  UploadOutlined,
  EditOutlined,
  ArrowLeftOutlined,
  CloseOutlined,
  SaveOutlined,
  CheckOutlined,
} from "@ant-design/icons-vue";
import DepartmentTreeSelect from "@/components/Department/DepartmentTreeSelect.vue";
// import PriceTrendChart from '@/components/Charts/PriceTrendChart.vue'
import DictSelector from "@/components/Dict/Selector.vue";
import api from "@/api";
import { getFormConfig } from "@/utils/validation";
import dictConverter from "@/utils/dictConverter";

export default {
  name: "PurchaseRequestForm",
  components: {
    DepartmentTreeSelect,
    // PriceTrendChart,
    DictSelector,
    UploadOutlined,
    EditOutlined,
    ArrowLeftOutlined,
    CloseOutlined,
    SaveOutlined,
    CheckOutlined,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const loading = ref(false);
    const formRef = ref();
    const fileList = ref([]);
    const uploadedFile = ref(null);
    const userInfo = ref({});
    const departmentList = ref([]);

    // 是否为编辑模式
    const isEditMode = computed(() => {
      const editMode = route.path.includes("/edit") || route.params.id;
      console.log('🔍 路由调试信息:');
      console.log('  - route.path:', route.path);
      console.log('  - route.params:', route.params);
      console.log('  - route.params.id:', route.params.id);
      console.log('  - isEditMode:', editMode);
      return editMode;
    });

    // 表单数据
    const formData = reactive({
      item_category: "",
      item_name: "",
      specification: "",
      unit: "",
      budget_quantity: 1,
      budget_unit_price: 0,
      hierarchy_path: "",
      procurement_method: "",
      requirement_source: "",
      fund_project_name: "",
      purchase_type: "unified", // 默认为统一采购
      remarks: "",
      attachments: [],
      // 驳回/退回信息
      rejection_reason: "",
      return_reason: "",
    });

    // 价格预警
    const priceWarning = computed(() => {
      return (
        formData.budget_unit_price > 0 &&
        historyData.avgPrice > 0 &&
        formData.budget_unit_price > historyData.avgPrice * 1.2
      );
    });

    // 历史数据
    const historyData = reactive({
      avgPrice: 0,
      maxPrice: 0,
      minPrice: 0,
      totalCount: 0,
    });

    // 基于业务阶段的动态验证规则
    const rules = computed(() => {
      const currentStatus = formData.status || "draft";
      const formConfig = getFormConfig(currentStatus);
      const dynamicRules = {};

      // 基础验证规则定义
      const baseRules = {
        item_category: [
          { required: true, message: "请选择物品种类", trigger: "change" },
        ],
        specification: [
          { required: true, message: "请输入规格型号", trigger: "blur" },
        ],
        item_name: [
          { required: true, message: "请输入物品名称", trigger: "blur" },
        ],
        unit: [{ required: true, message: "请选择单位", trigger: "change" }],
        budget_quantity: [
          { required: true, message: "请输入需求数量", trigger: "change" },
          {
            validator: (_, value) => {
              if (value === null || value === undefined || value === "") {
                return Promise.reject(new Error("请输入需求数量"));
              }
              if (isNaN(value) || value < 1) {
                return Promise.reject(new Error("数量必须大于等于1"));
              }
              return Promise.resolve();
            },
            trigger: "change",
          },
        ],
        budget_unit_price: [
          { required: true, message: "请输入预算单价", trigger: "change" },
          {
            validator: (_, value) => {
              if (value === null || value === undefined || value === "") {
                return Promise.reject(new Error("请输入预算单价"));
              }
              if (isNaN(value) || value <= 0) {
                return Promise.reject(new Error("单价必须大于0"));
              }
              return Promise.resolve();
            },
            trigger: "change",
          },
        ],
        dept_id: [
          { required: true, message: "请选择申请部门", trigger: "change" },
        ],
        procurement_method: [
          { required: true, message: "请选择采购方式", trigger: "change" },
        ],
        requirement_source: [
          { required: true, message: "请输入需求来源", trigger: "blur" },
        ],
        fund_project_name: [
          { required: true, message: "请选择经费项目名称", trigger: "change" },
        ],
        purchase_type: [
          { required: true, message: "请选择采购类型", trigger: "change" },
        ],
        hierarchy_path: [
          { required: true, message: "请选择需求单位", trigger: "change" },
        ],

      };

      // 根据当前阶段的必填字段生成验证规则
      formConfig.requiredFields.forEach((fieldName) => {
        if (baseRules[fieldName]) {
          dynamicRules[fieldName] = baseRules[fieldName];
        }
      });
      console.log("dynamicRules", dynamicRules);

      return dynamicRules;
    });

    // 计算金额
    const calculateAmount = () => {
      if (formData.budget_quantity >= 1 && formData.budget_unit_price >= 0) {
        const amount = formData.budget_quantity * formData.budget_unit_price;
        if (priceWarning.value) {
          message.warning("单价高于历史均价20%，已触发价格预警");
        }
        return amount;
      }
      return 0;
    };

    // 获取部门列表 - 用于下拉选择，使用专门的无分页接口
    const getDepartmentList = async () => {
      try {
        const response = await api.departments.getAll();
        if (response.code === 200) {
          departmentList.value = response.data || [];
        }
      } catch (error) {
        console.error("获取部门列表失败:", error);
      }
    };

    // 获取用户信息
    const getUserInfo = async () => {
      try {
        const response = await api.auth.getCurrentUser();
        if (response.code === 200) {
          userInfo.value = response.data;
          // 如果是新建模式且用户有部门路径信息，设置默认部门路径信息
          if (!isEditMode.value && userInfo.value.dept_hierarchy_path) {
            formData.hierarchy_path = userInfo.value.dept_hierarchy_path;
          }
        }
      } catch (error) {
        console.error("获取用户信息失败:", error);
      }
    };

    // 获取历史数据
    const getHistoryData = async () => {
      if (!formData.item_name || !formData.hierarchy_path) return;

      try {
        const deptId = getDeptIdFromPath(formData.hierarchy_path);
        if (!deptId) return;

        const response = await api.purchase.getHistory({
          item_name: formData.item_name,
          dept_id: deptId,
        });

        if (response.code === 200 && response.data) {
          const stats = response.data.statistics;
          if (stats) {
            historyData.avgPrice = stats.avg_price || 0;
            historyData.maxPrice = stats.max_price || 0;
            historyData.minPrice = stats.min_price || 0;
            historyData.totalCount = stats.purchase_count || 0;
          } else {
            // 没有历史数据时重置为0
            historyData.avgPrice = 0;
            historyData.maxPrice = 0;
            historyData.minPrice = 0;
            historyData.totalCount = 0;
          }
        } else {
          // API返回失败时重置历史数据
          historyData.avgPrice = 0;
          historyData.maxPrice = 0;
          historyData.minPrice = 0;
          historyData.totalCount = 0;
        }
      } catch (error) {
        // 重置历史数据，避免显示错误的数据
        historyData.avgPrice = 0;
        historyData.maxPrice = 0;
        historyData.minPrice = 0;
        historyData.totalCount = 0;

        if (process.env.NODE_ENV === "development") {
          console.error("获取历史数据失败:", error);
        }

        let errorMessage = "获取历史数据失败";

        if (error.response && error.response.data) {
          const apiError = error.response.data;
          if (apiError.message) {
            errorMessage = `获取历史数据失败: ${apiError.message}`;
          } else if (apiError.detail) {
            errorMessage = `获取历史数据失败: ${apiError.detail}`;
          }
        } else if (error.message) {
          errorMessage = `获取历史数据失败: ${error.message}`;
        }

        // 显示错误消息，停留3秒
        message.error(errorMessage, 3);
      }
    };

    // 从部门路径获取部门ID
    const getDeptIdFromPath = (path) => {
      if (!path || !departmentList.value.length) {
        return null;
      }

      // 在部门列表中查找匹配的hierarchy_path
      const department = departmentList.value.find(dept => dept.hierarchy_path === path);
      return department ? department.id : null;
    };

    // 文件上传前验证
    const beforeUpload = (file) => {
      const isValidType =
        file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel" ||
        file.type === "application/pdf";

      if (!isValidType) {
        message.error("只能上传 Excel 或 PDF 文件!");
        return false;
      }

      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error("文件大小不能超过 10MB!");
        return false;
      }

      return false; // 阻止自动上传
    };

    // 文件选择处理
    const handleFileChange = (info) => {
      if (info.file.status !== "removed") {
        uploadedFile.value = info.file;
        formData.attachments = [info.file];
        message.success("文件选择成功");
      } else {
        uploadedFile.value = null;
        formData.attachments = [];
      }
    };

    // 返回上一页
    const goBack = () => {
      router.go(-1);
    };

    // 保存为草稿
    const saveAsDraft = async () => {
      try {
        loading.value = true;

        const submitData = {
          ...formData,
          dept_id: getDeptIdFromPath(formData.hierarchy_path),
          fund_project: formData.fund_project_name,
        };

        // 新建模式或编辑被驳回/退回的记录时，设置状态为draft
        if (!isEditMode.value || ["rejected", "returned"].includes(formData.status)) {
          submitData.status = "draft";
        }

        // 转换字典名称为编码
        const convertedData = await dictConverter.convertRequestForSubmit(submitData);

        let response;
        if (isEditMode.value) {
          response = await api.purchaseRequests.update(
            route.params.id,
            convertedData
          );
        } else {
          response = await api.purchaseRequests.create(submitData);
        }

        if (response.code === 200) {
          message.success("保存草稿成功");
          router.push("/purchase/requests");
        }
      } catch (error) {
        if (process.env.NODE_ENV === "development") {
          console.error("保存草稿失败:", error);
        }

        // 构建详细的错误信息
        let errorMessage = "保存失败";

        if (error.response && error.response.data) {
          const apiError = error.response.data;

          // 处理验证错误
          if (apiError.errors && typeof apiError.errors === 'object') {
            const errorDetails = [];
            for (const [field, messages] of Object.entries(apiError.errors)) {
              if (Array.isArray(messages)) {
                errorDetails.push(`${field}: ${messages.join(', ')}`);
              } else {
                errorDetails.push(`${field}: ${messages}`);
              }
            }
            errorMessage = `保存失败: ${errorDetails.join('; ')}`;
          } else if (apiError.message) {
            errorMessage = `保存失败: ${apiError.message}`;
          } else if (apiError.detail) {
            errorMessage = `保存失败: ${apiError.detail}`;
          }
        } else if (error.message) {
          errorMessage = `保存失败: ${error.message}`;
        }

        // 显示错误消息，停留3秒
        message.error(errorMessage, 3);
      } finally {
        loading.value = false;
      }
    };

    // 提交表单
    const submitForm = async () => {
      try {
        await formRef.value.validate();
        loading.value = true;

        const submitData = {
          ...formData,
          dept_id: getDeptIdFromPath(formData.hierarchy_path),
          fund_project: formData.fund_project_name,
        };

        // 转换字典名称为编码
        const convertedData = await dictConverter.convertRequestForSubmit(submitData);

        let response;
        if (isEditMode.value) {
          response = await api.purchaseRequests.update(
            route.params.id,
            convertedData
          );
        } else {
          response = await api.purchaseRequests.create(convertedData);
        }

        if (response.code === 200) {
          message.success(isEditMode.value ? "更新成功" : "创建成功");
          router.push("/purchase/requests");
        }
      } catch (error) {
        if (process.env.NODE_ENV === "development") {
          console.error("表单提交失败:", error);
        }

        let errorMessage = "提交失败";

        // 如果是表单验证错误，显示具体的错误信息
        if (error.errorFields && error.errorFields.length > 0) {
          const errorDetails = error.errorFields.map(field => {
            const fieldName = field.name[0];
            const fieldError = field.errors[0];
            return `${fieldName}: ${fieldError}`;
          });
          errorMessage = `验证失败 - ${errorDetails.join('; ')}`;

          if (process.env.NODE_ENV === "development") {
            console.warn("验证失败的字段详情:", error.errorFields);
          }
        } else if (error.response && error.response.data) {
          // API 错误
          const apiError = error.response.data;

          // 处理后端验证错误
          if (apiError.errors && typeof apiError.errors === 'object') {
            const errorDetails = [];
            for (const [field, messages] of Object.entries(apiError.errors)) {
              if (Array.isArray(messages)) {
                errorDetails.push(`${field}: ${messages.join(', ')}`);
              } else {
                errorDetails.push(`${field}: ${messages}`);
              }
            }
            errorMessage = `提交失败: ${errorDetails.join('; ')}`;
          } else if (apiError.message) {
            errorMessage = `提交失败: ${apiError.message}`;
          } else if (apiError.detail) {
            errorMessage = `提交失败: ${apiError.detail}`;
          } else {
            errorMessage = "提交失败: 服务器错误";
          }

          if (process.env.NODE_ENV === "development") {
            console.error("API错误:", apiError);
          }
        } else if (error.message) {
          errorMessage = `提交失败: ${error.message}`;
        }

        // 显示错误消息，停留3秒
        message.error(errorMessage, 3);
      } finally {
        loading.value = false;
      }
    };

    // 获取采购需求详情（编辑模式）
    const getPurchaseRequestDetail = async () => {
      if (!isEditMode.value || !route.params.id) return;

      try {
        loading.value = true;
        const response = await api.purchaseRequests.getDetail(route.params.id);

        if (response.code === 200) {
          // 只提取需求阶段相关的字段，避免包含验收、采购、结算等后续阶段的字段
          const data = response.data;

          // 转换字典编码为显示名称
          const convertedData = await dictConverter.convertRequestForDisplay(data);

          Object.assign(formData, {
            item_category: convertedData.item_category,
            item_name: convertedData.item_name,
            unit: convertedData.unit,
            budget_quantity: convertedData.budget_quantity || convertedData.quantity || 1,
            budget_unit_price: convertedData.budget_unit_price || convertedData.unit_price || 0,
            hierarchy_path: convertedData.hierarchy_path,
            procurement_method: convertedData.procurement_method,
            requirement_source: convertedData.requirement_source,
            fund_project_name: convertedData.fund_project_name || convertedData.fund_project,
            purchase_type: convertedData.purchase_type,
            remarks: convertedData.remarks,
            specification: convertedData.specification,
            attachments: convertedData.attachments || [],
            // 驳回/退回信息
            rejection_reason: convertedData.rejection_reason,
            return_reason: convertedData.return_reason,
            // 保留一些必要的元数据字段
            id: convertedData.id,
            status: convertedData.status,
            dept_id: convertedData.dept_id,
            // 新增字段
            budget_total_amount: convertedData.budget_total_amount,
            fund_project: convertedData.fund_project,
          });
          // 获取历史数据
          await getHistoryData();
        }
      } catch (error) {
        if (process.env.NODE_ENV === "development") {
          console.error("获取采购需求详情失败:", error);
        }

        let errorMessage = "获取详情失败";

        if (error.response && error.response.data) {
          const apiError = error.response.data;
          if (apiError.message) {
            errorMessage = `获取详情失败: ${apiError.message}`;
          } else if (apiError.detail) {
            errorMessage = `获取详情失败: ${apiError.detail}`;
          }
        } else if (error.message) {
          errorMessage = `获取详情失败: ${error.message}`;
        }

        // 显示错误消息，停留3秒
        message.error(errorMessage, 3);
      } finally {
        loading.value = false;
      }
    };

    // 监听物品名称和部门变化，获取历史数据
    watch(
      [() => formData.item_name, () => formData.hierarchy_path],
      ([newItemName, newDeptPath]) => {
        if (newItemName && newDeptPath) {
          getHistoryData();
        }
      }
    );

    // 页面加载时初始化
    onMounted(async () => {
      // 获取部门列表（用于路径到ID的转换）
      await getDepartmentList();

      // 获取用户信息并设置默认部门路径信息（仅在新建模式下）
      await getUserInfo();

      if (isEditMode.value) {
        await getPurchaseRequestDetail();
      }
    });

    return {
      loading,
      formRef,
      fileList,
      uploadedFile,
      userInfo,
      isEditMode,
      formData,
      historyData,
      rules,
      priceWarning,
      calculateAmount,
      getHistoryData,
      getDeptIdFromPath,
      beforeUpload,
      handleFileChange,
      goBack,
      saveAsDraft,
      submitForm,
    };
  },
};
</script>

<style scoped>
.purchase-request-form {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  margin-bottom: 24px;
  background: var(--primary-gradient);
  color: #ffffff;
  border-radius: var(--radius-lg);
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
  color: #ffffff;
}

.header-content p {
  margin: 8px 0 0 0;
  color: rgba(255, 255, 255, 0.9);
}

.form-content {
  padding: 32px;
  margin-bottom: 24px;
}

.purchase-form {
  max-width: none;
}

.form-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.form-actions {
  padding: 24px;
  text-align: center;
  border-top: 1px solid #e5e7eb;
}

.primary-action-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: 6px;
  font-weight: 600;
  height: 48px;
  padding: 0 24px;
  color: #ffffff;
  transition: all 0.2s ease;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
  color: #ffffff;
}

.secondary-action-btn {
  border: 2px solid #d1d5db;
  color: #6b7280;
  border-radius: 6px;
  font-weight: 500;
  height: 48px;
  padding: 0 24px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.secondary-action-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #eff6ff;
}

:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #374151;
}

:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  border-radius: 6px;
  border: 2px solid #e5e7eb;
  transition: all 0.2s ease;
}

:deep(.ant-input:focus),
:deep(.ant-input-number-focused),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-picker:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.ant-input:hover),
:deep(.ant-input-number:hover),
:deep(.ant-select:hover .ant-select-selector),
:deep(.ant-picker:hover) {
  border-color: #93c5fd;
}

:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-upload) {
  width: 100%;
}

:deep(.ant-upload-list) {
  margin-top: 8px;
}

/* 移除响应式设计，保持固定布局 */
</style>
