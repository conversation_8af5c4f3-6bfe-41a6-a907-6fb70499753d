<template>
  <div class="dynamic-menu">
    <a-spin :spinning="loading" tip="加载菜单中...">
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="light"
        :inline-collapsed="collapsed"
        class="menu-container"
        @select="handleMenuSelect"
        @openChange="handleOpenChange"
      >
        <template v-for="menu in filteredMenuTree" :key="menu.menu_code || menu.route_path">
          <DynamicMenuItem 
            :menu="menu" 
            :level="0"
            @menu-click="handleMenuClick"
          />
        </template>
      </a-menu>
    </a-spin>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
import DynamicMenuItem from './DynamicMenuItem.vue'
import { getUserMenus } from '@/router/dynamicRouter'
import dynamicRouterManager from '@/router/dynamicRouter'

export default {
  name: 'DynamicMenu',
  components: {
    DynamicMenuItem
  },
  props: {
    theme: {
      type: String,
      default: 'light'
    },
    collapsed: {
      type: Boolean,
      default: false
    }
  },
  emits: ['menu-click'],
  setup(props, { emit }) {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()

    // 响应式数据
    const loading = ref(false)
    const menuTree = ref([])
    const selectedKeys = ref([])
    const openKeys = ref([])

    // 注入布局组件的方法（如果有）
    const toggleSidebar = inject('toggleSidebar', null)

    // 计算属性
    const currentUser = computed(() => store.getters.currentUser)
    
    // 过滤隐藏的菜单和按钮类型的菜单
    const filteredMenuTree = computed(() => {
      const filterHiddenMenus = (menus) => {
        return menus.filter(menu => {
          // 过滤隐藏的菜单
          if (menu.hidden) {
            return false
          }

          // 过滤按钮类型的菜单（按钮不在左侧菜单栏显示）
          if (menu.menu_type === 'button') {
            return false
          }

          // 递归过滤子菜单
          if (menu.children && menu.children.length > 0) {
            menu.children = filterHiddenMenus(menu.children)
          }

          return true
        })
      }

      const filtered = filterHiddenMenus(menuTree.value)
      if (filtered.length > 0) {
        console.log('✅ 菜单过滤完成，可显示菜单数量:', filtered.length)
      }
      return filtered
    })

    // 方法
    const loadUserMenus = async (forceRefresh = false) => {
      console.log('🔄 开始加载用户菜单...', forceRefresh ? '(强制刷新)' : '')
      console.log('👤 当前用户状态:', currentUser.value)

      if (!currentUser.value) {
        console.warn('⚠️ 用户未登录，无法加载菜单')
        return
      }

      // 如果是强制刷新，清除所有缓存
      if (forceRefresh) {
        console.log('🗑️ 强制刷新：清除所有菜单缓存')
        dynamicRouterManager.forceRefreshCache()
        store.dispatch('clearUserMenus')

        // 清除本地存储
        try {
          localStorage.removeItem('user_menus')
          localStorage.removeItem('dynamic_routes_cache')
          sessionStorage.removeItem('user_menus')
        } catch (error) {
          console.warn('清除本地缓存失败:', error)
        }
      }

      loading.value = true
      try {
        console.log('📡 调用getUserMenus API...')
        // 使用新的动态路由管理器获取菜单
        const menus = await getUserMenus()
        console.log('📋 API返回的菜单数据:', menus)

        menuTree.value = menus || []
        console.log('✅ 用户菜单加载成功:', menuTree.value.length, '个菜单')
        console.log('🌳 菜单树结构:', menuTree.value)

        // 设置默认选中和展开的菜单
        updateMenuState()
      } catch (error) {
        console.error('❌ 菜单加载异常:', error)
        message.error('菜单加载失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }

    const updateMenuState = () => {
      const currentPath = route.path

      // 设置选中的菜单
      const findSelectedMenu = (menus, path) => {
        for (const menu of menus) {
          if (menu.route_path === path) {
            return menu.menu_code || menu.route_path
          }
          if (menu.children && menu.children.length > 0) {
            const found = findSelectedMenu(menu.children, path)
            if (found) return found
          }
        }
        return null
      }

      const selectedKey = findSelectedMenu(menuTree.value, currentPath)
      if (selectedKey) {
        selectedKeys.value = [selectedKey]
      }

      // 设置展开的菜单（父菜单）- 确保当前路径的父菜单始终展开
      const findOpenKeys = (menus, path, parentKeys = []) => {
        for (const menu of menus) {
          const currentKeys = [...parentKeys, menu.menu_code || menu.route_path]

          if (menu.route_path === path) {
            return parentKeys
          }

          if (menu.children && menu.children.length > 0) {
            const found = findOpenKeys(menu.children, path, currentKeys)
            if (found) return found
          }
        }
        return []
      }

      const newOpenKeys = findOpenKeys(menuTree.value, currentPath)

      // 合并当前展开的菜单和新需要展开的菜单，确保不会意外关闭
      const mergedOpenKeys = [...new Set([...openKeys.value, ...newOpenKeys])]
      openKeys.value = mergedOpenKeys

      console.log('🔄 菜单状态更新:', {
        currentPath,
        selectedKey,
        newOpenKeys,
        mergedOpenKeys
      })
    }

    const handleMenuSelect = ({ key, selectedKeys: keys }) => {
      selectedKeys.value = keys
      console.log('菜单选中:', key)
    }

    const handleOpenChange = (keys) => {
      // 获取当前路径需要展开的父菜单
      const currentPath = route.path
      const findRequiredOpenKeys = (menus, path, parentKeys = []) => {
        for (const menu of menus) {
          const currentKeys = [...parentKeys, menu.menu_code || menu.route_path]

          if (menu.route_path === path) {
            return parentKeys
          }

          if (menu.children && menu.children.length > 0) {
            const found = findRequiredOpenKeys(menu.children, path, currentKeys)
            if (found) return found
          }
        }
        return []
      }

      const requiredOpenKeys = findRequiredOpenKeys(menuTree.value, currentPath)

      // 确保当前路径的父菜单始终保持展开状态
      const finalKeys = [...new Set([...keys, ...requiredOpenKeys])]
      openKeys.value = finalKeys

      console.log('菜单展开状态变化:', {
        userKeys: keys,
        requiredKeys: requiredOpenKeys,
        finalKeys
      })
    }

    const handleMenuClick = (menu) => {
      console.log('菜单点击:', menu)
      
      // 发射事件给父组件
      emit('menu-click', menu)
      
      // 如果有路由路径，进行导航
      if (menu.route_path) {
        router.push(menu.route_path)
        
        // 在移动端，点击菜单后收起侧边栏
        if (toggleSidebar && window.innerWidth <= 768) {
          toggleSidebar()
        }
      }
    }

    // 监听路由变化
    watch(() => route.path, () => {
      updateMenuState()
    })

    // 监听用户变化
    watch(currentUser, (newUser) => {
      if (newUser) {
        loadUserMenus()
      } else {
        menuTree.value = []
        selectedKeys.value = []
        openKeys.value = []
      }
    })

    // 监听折叠状态变化
    watch(() => props.collapsed, (collapsed) => {
      if (collapsed) {
        // 折叠时只保留一级菜单展开
        const firstLevelKeys = menuTree.value
          .filter(menu => menu.children && menu.children.length > 0)
          .map(menu => menu.menu_code || menu.route_path)
        openKeys.value = openKeys.value.filter(key => firstLevelKeys.includes(key))
      }
    })

    // 生命周期
    onMounted(() => {
      // DynamicMenu组件已挂载，开始初始化菜单

      // 强制清除所有缓存确保菜单更新
      dynamicRouterManager.forceRefreshCache()
      store.dispatch('clearUserMenus')

      // 清除本地存储的菜单缓存
      try {
        localStorage.removeItem('user_menus')
        localStorage.removeItem('dynamic_routes_cache')
        localStorage.removeItem('permission_menus')
        localStorage.removeItem('has_routes')
        sessionStorage.removeItem('user_menus')
        sessionStorage.removeItem('permission_menus')
        // 已清除所有菜单缓存
      } catch (error) {
        // 清除本地缓存失败，静默处理
      }

      if (currentUser.value) {
        // 用户已登录，开始加载菜单
        loadUserMenus()
      } else {
        // 用户未登录，尝试获取用户信息
        const token = localStorage.getItem('token')
        if (token) {
          // 发现token，尝试获取用户信息
          store.dispatch('getCurrentUser').then(() => {
            // 用户信息获取成功，重新加载菜单
            loadUserMenus()
          })
        }
      }
    })

    // 暴露方法给父组件
    const refreshMenu = (forceRefresh = false) => {
      return loadUserMenus(forceRefresh)
    }

    // 监听权限变更事件
    const handlePermissionChange = () => {
      // 检测到权限变更，强制刷新菜单
      refreshMenu(true)
    }

    // 监听全局权限变更事件
    window.addEventListener('permission-changed', handlePermissionChange)

    return {
      // 响应式数据
      loading,
      menuTree,
      selectedKeys,
      openKeys,
      filteredMenuTree,
      
      // 方法
      handleMenuSelect,
      handleOpenChange,
      handleMenuClick,
      refreshMenu
    }
  }
}
</script>

<style scoped>
.dynamic-menu {
  height: 100%;
  overflow-y: auto;
}

.menu-container {
  border-right: none;
  height: 100%;
}

.menu-container :deep(.ant-menu-item),
.menu-container :deep(.ant-menu-submenu-title) {
  margin: 0;
  border-radius: 0;
}

.menu-container :deep(.ant-menu-item-selected) {
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.menu-container :deep(.ant-menu-submenu-selected > .ant-menu-submenu-title) {
  color: #1890ff;
}

/* 折叠状态下的样式 */
.menu-container.ant-menu-inline-collapsed {
  width: 80px;
}

.menu-container.ant-menu-inline-collapsed :deep(.ant-menu-submenu-title) {
  padding: 0 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dynamic-menu {
    width: 100%;
  }
}
</style>
