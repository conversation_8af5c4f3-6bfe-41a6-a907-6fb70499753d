<template>
  <!-- 搜索区域 -->
  <div class="search-section">
    <a-select
      v-model:value="searchField"
      placeholder="搜索字段"
      size="large"
      style="width: 120px"
    >
      <a-select-option
        v-for="field in searchFields"
        :key="field.value"
        :value="field.value"
      >
        {{ field.label }}
      </a-select-option>
    </a-select>
    <a-select
      v-model:value="searchCondition"
      placeholder="条件"
      size="large"
      style="width: 90px"
    >
      <a-select-option value="contains">包含</a-select-option>
      <a-select-option value="equals">等于</a-select-option>
      <a-select-option value="starts_with">开头是</a-select-option>
      <a-select-option value="ends_with">结尾是</a-select-option>
    </a-select>
    <a-input
      v-model:value="searchValue"
      placeholder="输入搜索内容"
      size="large"
      style="width: 280px"
      @press-enter="handleSearch"
    >
      <template #prefix>
        <SearchOutlined />
      </template>
    </a-input>
    <a-button
      type="primary"
      size="large"
      @click="handleSearch"
      :loading="loading"
      class="primary-btn"
    >
      <SearchOutlined />
      搜索
    </a-button>
    <a-button
      size="large"
      @click="handleReset"
      class="reset-btn"
    >
      <ReloadOutlined />
      重置
    </a-button>
  </div>

  <!-- 分隔线 -->
  <a-divider class="section-divider" v-if="showFilters" />

  <!-- 筛选区域 -->
  <div class="filter-section" v-if="showFilters">
    <div class="filter-item" v-if="filters.status">
      <span class="filter-label">状态</span>
      <a-select
        v-model:value="filterValues.status"
        placeholder="选择状态"
        mode="multiple"
        allow-clear
        size="large"
        style="width: 140px"
      >
        <a-select-option
          v-for="status in statusOptions"
          :key="status.value"
          :value="status.value"
        >
          {{ status.label }}
        </a-select-option>
      </a-select>
    </div>
    <div class="filter-item" v-if="filters.amount">
      <span class="filter-label">金额区间</span>
      <div class="amount-range">
        <a-input-number
          v-model:value="filterValues.amountMin"
          placeholder="最小金额"
          style="width: 120px"
          size="large"
          :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
          :parser="value => value.replace(/¥\s?|(,*)/g, '')"
        />
        <span class="range-separator">-</span>
        <a-input-number
          v-model:value="filterValues.amountMax"
          placeholder="最大金额"
          style="width: 120px"
          size="large"
          :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
          :parser="value => value.replace(/¥\s?|(,*)/g, '')"
        />
      </div>
    </div>
    <div class="filter-item" v-if="filters.department">
      <span class="filter-label">部门</span>
      <DepartmentTreeSelect
        v-model="filterValues.department"
        placeholder="选择部门"
        allow-clear
        size="large"
        style="width: 160px"
      />
    </div>
    <div class="filter-item" v-if="filters.dateRange">
      <span class="filter-label">时间范围</span>
      <a-range-picker
        v-model:value="filterValues.dateRange"
        style="width: 240px"
        size="large"
        format="YYYY-MM-DD"
      />
    </div>
    <div class="filter-actions">
      <a-button
        type="primary"
        size="large"
        @click="applyFilters"
        class="primary-btn"
      >
        <SearchOutlined />
        筛选
      </a-button>
      <a-button
        size="large"
        @click="clearFilters"
        class="reset-btn"
      >
        <ReloadOutlined />
        清空
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import DepartmentTreeSelect from '@/components/Department/DepartmentTreeSelect.vue';

defineProps({
  // 搜索字段配置
  searchFields: {
    type: Array,
    default: () => [
      { label: '物品名称', value: 'item_name' },
      { label: '需求单位', value: 'hierarchy_path' },
      { label: '申请人', value: 'requester_name' }
    ]
  },
  // 筛选器配置
  filters: {
    type: Object,
    default: () => ({
      status: true,
      amount: true,
      department: true,
      dateRange: true
    })
  },
  // 状态选项
  statusOptions: {
    type: Array,
    default: () => [
      { label: '草稿', value: 'draft' },
      { label: '待审批', value: 'pending' },
      { label: '已审批', value: 'approved' },
      { label: '已驳回', value: 'rejected' },
      { label: '已采购', value: 'purchased' }
    ]
  },
  // 是否显示筛选区
  showFilters: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['search', 'filter', 'reset']);

// 搜索状态
const searchField = ref('item_name');
const searchCondition = ref('contains');
const searchValue = ref('');

// 筛选状态
const filterValues = reactive({
  status: [],
  amountMin: null,
  amountMax: null,
  department: null,
  dateRange: null
});



// 搜索处理
const handleSearch = () => {
  const searchParams = {
    field: searchField.value,
    condition: searchCondition.value,
    value: searchValue.value
  };
  emit('search', searchParams);
};

// 重置搜索
const handleReset = () => {
  searchField.value = 'item_name';
  searchCondition.value = 'contains';
  searchValue.value = '';
  clearFilters();
  emit('reset');
};

// 应用筛选
const applyFilters = () => {
  const filters = { ...filterValues };
  
  // 处理日期范围
  if (filters.dateRange && filters.dateRange.length === 2) {
    filters.dateStart = filters.dateRange[0].format('YYYY-MM-DD');
    filters.dateEnd = filters.dateRange[1].format('YYYY-MM-DD');
    delete filters.dateRange;
  }
  
  emit('filter', filters);
};

// 清空筛选
const clearFilters = () => {
  Object.keys(filterValues).forEach(key => {
    if (Array.isArray(filterValues[key])) {
      filterValues[key] = [];
    } else {
      filterValues[key] = null;
    }
  });
};


</script>

<style scoped>
/* 搜索区域 */
.search-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  background: white;
  padding: 16px 20px;
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 筛选区域 */
.filter-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  background: white;
  padding: 16px 20px;
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 筛选项 */
.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
  white-space: nowrap;
}

/* 金额区间 */
.amount-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #6b7280;
  font-weight: 500;
}

/* 筛选操作按钮 */
.filter-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

/* 分隔线样式 */
.section-divider {
  margin: 0 0 16px 0;
  border-color: #e5e7eb;
}

/* 按钮样式 */
.primary-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;
  height: 40px;
  padding: 0 16px;
  transition: all 0.3s;
  color: white;
  font-size: 14px;
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.reset-btn {
  border: 1px solid #d1d5db;
  color: #6b7280;
  border-radius: 6px;
  font-weight: 500;
  height: 40px;
  padding: 0 16px;
  background: white;
  transition: all 0.3s;
  font-size: 14px;
}

.reset-btn:hover {
  border-color: #9ca3af;
  color: #374151;
  background: #f9fafb;
}

/* 输入控件样式 */
:deep(.search-section .ant-input),
:deep(.filter-section .ant-input) {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s;
  height: 40px;
}

:deep(.search-section .ant-input:focus),
:deep(.filter-section .ant-input:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

:deep(.search-section .ant-input:hover),
:deep(.filter-section .ant-input:hover) {
  border-color: #9ca3af;
}

:deep(.search-section .ant-select-selector),
:deep(.filter-section .ant-select-selector) {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  transition: all 0.3s;
  height: 40px;
}

:deep(.search-section .ant-select-focused .ant-select-selector),
:deep(.filter-section .ant-select-focused .ant-select-selector) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

:deep(.search-section .ant-select-selector:hover),
:deep(.filter-section .ant-select-selector:hover) {
  border-color: #9ca3af;
}

:deep(.filter-section .ant-picker) {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  transition: all 0.3s;
  height: 40px;
}

:deep(.filter-section .ant-picker:hover) {
  border-color: #9ca3af;
}

:deep(.filter-section .ant-picker.ant-picker-focused) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

:deep(.filter-section .ant-input-number) {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  transition: all 0.3s;
  height: 40px;
}

:deep(.filter-section .ant-input-number:hover) {
  border-color: #9ca3af;
}

:deep(.filter-section .ant-input-number:focus-within) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 多选标签样式 */
:deep(.filter-section .ant-select-multiple .ant-select-selection-item) {
  background: #eff6ff;
  border: 1px solid #3b82f6;
  color: #1e40af;
  border-radius: 4px;
  font-size: 12px;
  margin: 2px;
}

/* 移除响应式优化，保持固定布局 */
</style>
