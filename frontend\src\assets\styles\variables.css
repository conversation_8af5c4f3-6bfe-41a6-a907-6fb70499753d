/* 统一的颜色和样式变量 */
:root {
  /* 主色调 */
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  
  /* 成功色 */
  --success-color: #10b981;
  --success-light: #34d399;
  --success-dark: #059669;
  
  /* 警告色 */
  --warning-color: #f59e0b;
  --warning-light: #fbbf24;
  --warning-dark: #d97706;
  
  /* 错误色 */
  --error-color: #ef4444;
  --error-light: #f87171;
  --error-dark: #dc2626;
  
  /* 信息色 */
  --info-color: #06b6d4;
  --info-light: #22d3ee;
  --info-dark: #0891b2;
  
  /* 中性色 */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* 文本颜色 */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;
  --text-muted: #94a3b8;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-hover: #fafbfc;
  --bg-hover-light: rgba(248, 250, 252, 0.6);
  
  /* 边框色 */
  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --border-dark: #94a3b8;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  
  /* 间距 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 24px;
  --space-2xl: 32px;
  --space-3xl: 48px;
  
  /* 字体大小 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* 业务卡片样式 */
.business-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--space-lg);
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
}

.business-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--border-medium);
}

/* 确保页面标题区域显示 */
.page-header.business-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  position: relative;
  overflow: hidden;
}

.page-header.business-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

/* 筛选区域样式 */
.filter-section.business-card {
  padding: var(--space-xl) var(--space-2xl);
}

/* 操作栏样式 */
.action-bar.business-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-2xl);
}

/* 表格区域样式 */
.table-section.business-card {
  padding: 0;
  overflow: hidden;
}

/* 主要按钮样式 */
.primary-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 44px;
  padding: 0 var(--space-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
  color: var(--text-inverse);
}

/* 重置按钮样式 */
.reset-btn {
  border: 2px solid var(--border-light);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  height: 44px;
  padding: 0 var(--space-lg);
  background: var(--bg-primary);
  transition: var(--transition-normal);
}

.reset-btn:hover {
  border-color: var(--border-medium);
  color: var(--text-primary);
  background: var(--bg-secondary);
}

/* 业务表格样式 */
.business-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* 状态标签样式 */
.status-tag {
  border-radius: 20px;
  font-weight: 500;
  font-size: var(--text-xs);
  padding: 4px 12px;
  border: none;
  letter-spacing: 0.3px;
}

.status-draft {
  background: var(--gray-100);
  color: var(--gray-600);
}

.status-pending {
  background: #fef3c7;
  color: #d97706;
}

.status-approved {
  background: #d1fae5;
  color: #059669;
}

.status-rejected {
  background: #fee2e2;
  color: #dc2626;
}

.status-purchased {
  background: #e0e7ff;
  color: #6366f1;
}

/* 筛选表单样式 */
.filter-form {
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.filter-form .ant-form-item {
  margin-bottom: 0;
}

.filter-form .ant-form-item-label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* 输入框统一样式 */
.unified-input .ant-input,
.unified-input .ant-select-selector,
.unified-input .ant-input-number,
.unified-input .ant-picker {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.unified-input .ant-input:focus,
.unified-input .ant-select-focused .ant-select-selector,
.unified-input .ant-input-number:focus-within,
.unified-input .ant-picker:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.unified-input .ant-input:hover,
.unified-input .ant-select-selector:hover,
.unified-input .ant-input-number:hover,
.unified-input .ant-picker:hover {
  border-color: var(--primary-light);
}

/* 导入其他样式文件 */
@import './table.css';
@import './form.css';

/* 移除响应式设计，保持固定布局 */
