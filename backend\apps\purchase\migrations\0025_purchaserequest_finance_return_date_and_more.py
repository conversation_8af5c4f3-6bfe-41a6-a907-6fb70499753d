# Generated by Django 5.2.3 on 2025-07-01 10:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0024_acceptancephoto'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaserequest',
            name='finance_return_date',
            field=models.DateTimeField(blank=True, help_text='财务退回的时间', null=True, verbose_name='财务退回时间'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='finance_return_reason',
            field=models.TextField(blank=True, help_text='财务人员退回重新验收的原因', verbose_name='财务退回原因'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='finance_returner',
            field=models.ForeignKey(blank=True, help_text='执行财务退回操作的用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='finance_returned_requests', to=settings.AUTH_USER_MODEL, verbose_name='财务退回人'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='re_acceptance_count',
            field=models.PositiveIntegerField(default=0, help_text='记录重新验收的次数', verbose_name='重新验收次数'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='re_acceptance_required',
            field=models.BooleanField(default=False, help_text='财务退回要求重新验收的标记', verbose_name='需要重新验收'),
        ),
    ]
