<template>
  <a-button
    v-if="hasPermission"
    v-bind="$attrs"
    @click="handleClick"
    :loading="loading"
  >
    <slot />
  </a-button>
</template>

<script>
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'

export default {
  name: 'PermissionButton',
  inheritAttrs: false,
  props: {
    permission: {
      type: [String, Array],
      required: true
    },
    role: {
      type: [String, Array],
      default: null
    },
    // 是否在权限不足时显示提示
    showMessage: {
      type: Boolean,
      default: false
    },
    // 权限不足时的提示信息
    noPermissionMessage: {
      type: String,
      default: '权限不足，无法执行此操作'
    },
    // 是否检查在线权限（实时验证）
    checkOnline: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const store = useStore()
    const loading = ref(false)

    // 权限检查
    const hasPermission = computed(() => {
      // 如果指定了角色，先检查用户角色
      if (props.role) {
        if (!store.getters.hasRole(props.role)) {
          return false
        }
      }

      // 检查权限
      return store.getters.hasPermission(props.permission)
    })

    // 处理点击事件
    const handleClick = async (event) => {
      // 如果需要在线检查权限
      if (props.checkOnline) {
        loading.value = true
        try {
          // 实时验证权限
          await store.dispatch('getUserPermissions')
          if (!store.getters.hasPermission(props.permission)) {
            if (props.showMessage) {
              message.error(props.noPermissionMessage)
            }
            return
          }
        } catch (error) {
          console.error('权限验证失败:', error)
          if (props.showMessage) {
            message.error('权限验证失败，请重新登录')
          }
          return
        } finally {
          loading.value = false
        }
      }

      // 权限验证通过，触发点击事件
      emit('click', event)
    }

    return {
      hasPermission,
      loading,
      handleClick
    }
  }
}
</script>
