# Generated by Django 5.2.1 on 2025-06-14 05:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0002_department_approval_limit_department_can_accept_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='department',
            name='contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话'),
        ),
        migrations.AddField(
            model_name='department',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='部门描述'),
        ),
        migrations.AddField(
            model_name='department',
            name='manager_name',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='部门负责人'),
        ),
        migrations.AddField(
            model_name='dictdata',
            name='value',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='字典值'),
        ),
    ]
