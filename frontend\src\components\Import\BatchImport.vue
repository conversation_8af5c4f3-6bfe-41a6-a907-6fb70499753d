<template>
  <div class="batch-import">
    <a-card title="批量导入" size="small">
      <template #extra>
        <a-space>
          <a-button type="link" @click="downloadTemplate" :loading="downloadLoading">
            <DownloadOutlined />
            下载模板
          </a-button>
        </a-space>
      </template>

      <div class="import-steps">
        <a-steps :current="currentStep" size="small">
          <a-step title="下载模板" description="下载标准导入模板" />
          <a-step title="填写数据" description="按模板格式填写数据" />
          <a-step title="上传文件" description="上传填写好的文件" />
          <a-step title="验证数据" description="系统验证数据格式" />
          <a-step title="导入完成" description="数据导入成功" />
        </a-steps>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <a-upload-dragger v-model:fileList="fileList" name="file" :multiple="false" :before-upload="beforeUpload"
          @change="handleFileChange" accept=".xlsx,.xls" :show-upload-list="false">
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持 .xlsx 格式文件，请先下载模板并按格式填写数据
          </p>
        </a-upload-dragger>

        <!-- 文件信息 -->
        <div v-if="selectedFile" class="file-info">
          <a-alert type="info" show-icon :message="`已选择文件: ${selectedFile.name}`"
            :description="`文件大小: ${formatFileSize(selectedFile.size)}`" />
        </div>
      </div>

      <!-- 导入按钮 -->
      <div class="import-actions">
        <a-space>
          <a-button type="primary" size="large" @click="handleImport" :loading="importing" :disabled="!selectedFile">
            <UploadOutlined />
            开始导入
          </a-button>
          <a-button size="large" @click="resetImport" :disabled="importing">
            重置
          </a-button>
        </a-space>
      </div>

      <!-- 导入进度 -->
      <div v-if="importing" class="import-progress">
        <a-progress :percent="importProgress" :status="importStatus" :stroke-color="{
          '0%': '#108ee9',
          '100%': '#87d068',
        }" />
        <p class="progress-text">{{ progressText }}</p>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <a-result :status="getResultStatus()" :title="importResult.title" :sub-title="importResult.message">
          <template #extra>
            <a-space>
              <a-button v-if="importResult.successCount > 0" type="primary" @click="$emit('importSuccess')">
                查看数据
              </a-button>
              <a-button @click="resetImport">
                重新导入
              </a-button>
            </a-space>
          </template>
        </a-result>

        <!-- 错误详情 -->
        <div v-if="importResult.errors && importResult.errors.length > 0" class="error-details">
          <a-collapse>
            <a-collapse-panel key="errors" header="查看错误详情">
              <a-table :columns="errorColumns" :data-source="importResult.errors" size="small"
                :pagination="{ pageSize: 10 }">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'errors'">
                    <a-tag v-for="error in record.errors" :key="error" color="red">
                      {{ error }}
                    </a-tag>
                  </template>
                </template>
              </a-table>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { DownloadOutlined, InboxOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import api from '@/api';

const props = defineProps({
  // 导入类型
  importType: {
    type: String,
    default: 'purchase_requests'
  },
  // 模板下载URL
  templateUrl: {
    type: String,
    default: '/api/requests/template/'
  },
  // 导入URL
  importUrl: {
    type: String,
    default: '/api/requests/import/'
  }
});

defineEmits(['importSuccess']);

// 状态
const currentStep = ref(0);
const fileList = ref([]);
const selectedFile = ref(null);

const downloadLoading = ref(false);
const importing = ref(false);
const importProgress = ref(0);
const importStatus = ref('active');
const importResult = ref(null);

// 错误表格列
const errorColumns = [
  {
    title: '行号',
    dataIndex: 'row',
    key: 'row',
    width: 80
  },
  {
    title: '错误信息',
    dataIndex: 'errors',
    key: 'errors'
  }
];

// 计算属性
const progressText = computed(() => {
  if (importProgress.value < 30) {
    return '正在验证文件格式...';
  } else if (importProgress.value < 60) {
    return '正在解析数据...';
  } else if (importProgress.value < 90) {
    return '正在导入数据...';
  } else {
    return '导入完成';
  }
});

// 获取结果状态
const getResultStatus = () => {
  if (!importResult.value) return 'info';

  const { successCount = 0, errorCount = 0 } = importResult.value;

  if (errorCount === 0) {
    return 'success';  // 全部成功
  } else if (successCount > 0) {
    return 'warning';  // 部分成功
  } else {
    return 'error';    // 全部失败
  }
};

// 下载模板
const downloadTemplate = async () => {
  try {
    downloadLoading.value = true;

    const response = await fetch(props.templateUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('下载失败');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = '采购需求导入模板.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    message.success('模板下载成功');
    currentStep.value = Math.max(currentStep.value, 1);

  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  } finally {
    downloadLoading.value = false;
  }
};

// 文件上传前验证
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel';

  if (!isExcel) {
    message.error('只能上传 Excel 文件!');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!');
    return false;
  }

  return false; // 阻止自动上传
};

// 文件选择处理
const handleFileChange = (info) => {
  if (info.file.status !== 'removed') {
    selectedFile.value = info.file;
    currentStep.value = Math.max(currentStep.value, 2);
    message.success('文件选择成功');
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 执行导入
const handleImport = async () => {
  if (!selectedFile.value) {
    message.error('请先选择要导入的文件');
    return;
  }

  try {
    importing.value = true;
    importProgress.value = 0;
    importStatus.value = 'active';
    currentStep.value = 3;

    // 模拟进度更新 - 确保百分比为整数
    const progressInterval = setInterval(() => {
      if (importProgress.value < 90) {
        importProgress.value = Math.min(90, Math.floor(importProgress.value + Math.random() * 20));
      }
    }, 500);

    // 创建FormData
    const formData = new FormData();
    formData.append('file', selectedFile.value);

    // 发送导入请求
    const response = await api.purchase.importData(formData);

    clearInterval(progressInterval);
    importProgress.value = 100;

    if (response.code === 200) {
      const successCount = response.data.success_count || 0;
      const errorCount = response.data.error_count || 0;
      const hasErrors = errorCount > 0;

      importStatus.value = hasErrors ? 'warning' : 'success';
      currentStep.value = 4;

      importResult.value = {
        success: !hasErrors,
        title: hasErrors ? '导入完成（部分失败）' : '导入成功',
        message: response.message,
        successCount: successCount,
        errorCount: errorCount,
        errors: response.data.errors || []
      };

      if (hasErrors) {
        message.warning(`导入完成，成功 ${successCount} 条，失败 ${errorCount} 条`);
      } else {
        message.success(`导入成功，共导入 ${successCount} 条记录`);
      }
    } else {
      throw new Error(response.message || '导入失败');
    }

  } catch (error) {
    console.error('导入失败:', error);
    importStatus.value = 'exception';

    importResult.value = {
      success: false,
      title: '导入失败',
      message: error.message || '导入过程中发生错误',
      errors: []
    };

    message.error('导入失败');
  } finally {
    importing.value = false;
  }
};

// 重置导入
const resetImport = () => {
  currentStep.value = 0;
  fileList.value = [];
  selectedFile.value = null;
  importing.value = false;
  importProgress.value = 0;
  importStatus.value = 'active';
  importResult.value = null;
};
</script>

<style scoped>
.batch-import {
  margin-bottom: var(--space-lg);
}

.import-steps {
  margin-bottom: var(--space-xl);
}

.upload-section {
  margin: var(--space-lg) 0;
}

.file-info {
  margin-top: var(--space-md);
}

.import-actions {
  text-align: center;
  margin: var(--space-lg) 0;
}

.import-progress {
  margin: var(--space-lg) 0;
  text-align: center;
}

.progress-text {
  margin-top: var(--space-sm);
  color: var(--text-secondary);
}

.import-result {
  margin-top: var(--space-lg);
}

.error-details {
  margin-top: var(--space-md);
}

:deep(.ant-upload-drag) {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  background: var(--bg-secondary);
  transition: all 0.3s ease;
}

:deep(.ant-upload-drag:hover) {
  border-color: var(--primary-color);
}

:deep(.ant-upload-drag-icon) {
  font-size: 48px;
  color: var(--primary-color);
}

:deep(.ant-card-head) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-hover) 100%);
  color: white;
}

:deep(.ant-card-head-title) {
  color: white;
  font-weight: 600;
}

:deep(.ant-card-extra a) {
  color: white;
}
</style>
