# Generated by Django 5.2.1 on 2025-06-12 14:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_category', models.Char<PERSON>ield(max_length=50, verbose_name='物品种类')),
                ('item_name', models.CharField(max_length=100, verbose_name='物品名称')),
                ('spec', models.CharField(max_length=200, verbose_name='规格型号')),
                ('unit', models.CharField(max_length=20, verbose_name='单位')),
                ('quantity', models.IntegerField(verbose_name='采购数量')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='单价')),
                ('amount', models.DecimalField(decimal_places=2, editable=False, max_digits=10, verbose_name='金额')),
                ('dept_id', models.IntegerField(verbose_name='关联部门ID')),
                ('hierarchy_path', models.CharField(help_text='例：分公司-办事处', max_length=200, verbose_name='部门层级路径')),
                ('procurement_method', models.CharField(help_text='关联数据字典-采购方式', max_length=50, verbose_name='采购方式')),
                ('requirement_source', models.CharField(help_text='关联数据字典-经费来源', max_length=100, verbose_name='需求来源')),
                ('fund_project_name', models.CharField(help_text='关联数据字典-经费项目', max_length=100, verbose_name='经费项目名称')),
                ('remarks', models.TextField(blank=True, verbose_name='备注')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('pending', '待审批'), ('approved', '已审批'), ('rejected', '已驳回'), ('purchased', '已采购')], default='draft', max_length=20, verbose_name='状态')),
                ('submission_date', models.DateTimeField(blank=True, null=True, verbose_name='提交日期')),
                ('final_approver_id', models.IntegerField(blank=True, null=True, verbose_name='审批人用户ID')),
                ('final_approval_date', models.DateTimeField(blank=True, null=True, verbose_name='审批日期')),
                ('final_approval_comment', models.TextField(blank=True, verbose_name='审批意见')),
                ('history_purchase_count', models.IntegerField(default=0, verbose_name='本单位历次采购数量统计')),
                ('history_avg_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='本单位历次采购平均单价')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_requests', to=settings.AUTH_USER_MODEL, verbose_name='申请人')),
            ],
            options={
                'verbose_name': '采购需求',
                'verbose_name_plural': '采购需求管理',
                'db_table': 'purchase_purchase_request',
            },
        ),
        migrations.CreateModel(
            name='Acceptance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('courier_company', models.CharField(max_length=100, verbose_name='快递公司')),
                ('tracking_number', models.CharField(max_length=100, verbose_name='快递单号')),
                ('courier_receipt_photo', models.ImageField(upload_to='receipts/', verbose_name='快递单照片')),
                ('item_photo', models.ImageField(upload_to='items/', verbose_name='物品照片')),
                ('acceptance_date', models.DateTimeField(verbose_name='验收日期')),
                ('status', models.CharField(choices=[('pending', '待验收'), ('accepted', '已验收')], default='pending', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('acceptor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='acceptances', to=settings.AUTH_USER_MODEL, verbose_name='验收人')),
                ('purchase_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='acceptance', to='purchase.purchaserequest', verbose_name='关联的采购需求')),
            ],
            options={
                'verbose_name': '物品验收',
                'verbose_name_plural': '物品验收管理',
                'db_table': 'purchase_acceptance',
            },
        ),
        migrations.CreateModel(
            name='Reimbursement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reimbursement_voucher_no', models.CharField(max_length=50, verbose_name='报销凭证号')),
                ('financial_serial_no', models.CharField(blank=True, max_length=50, verbose_name='财务流水号')),
                ('reimbursement_date', models.DateTimeField(verbose_name='报销日期')),
                ('remarks', models.TextField(blank=True, verbose_name='备注')),
                ('status', models.CharField(choices=[('pending', '待结算'), ('approved', '已审批'), ('settled', '已结算')], default='pending', max_length=20, verbose_name='状态')),
                ('internal_approval', models.BooleanField(default=False, verbose_name='内部审批状态')),
                ('approval_attachment', models.FileField(blank=True, upload_to='approvals/', verbose_name='审批附件')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('purchase_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='reimbursement', to='purchase.purchaserequest', verbose_name='关联的采购需求')),
                ('reimburser', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reimbursements', to=settings.AUTH_USER_MODEL, verbose_name='报销人')),
            ],
            options={
                'verbose_name': '结算报销',
                'verbose_name_plural': '结算报销管理',
                'db_table': 'purchase_reimbursement',
            },
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['item_name', 'dept_id'], name='purchase_pu_item_na_e9a31b_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['status'], name='purchase_pu_status_16eb7e_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['submission_date'], name='purchase_pu_submiss_2de80e_idx'),
        ),
    ]
