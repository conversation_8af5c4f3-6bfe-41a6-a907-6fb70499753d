---
type: "always_apply"
description: "Example description"
---
1.自动检测所有端口的服务，若是前后端服务正常启动，那就不用重启，此次所有修复直接测试API调用；若是没有重启，那就自动启动：前端启动命令：npm run serve,端口必须是8080，后端服务启动命令：daphne -b 0.0.0.0 -p 8000 purchase_system.asgi:application，端口是8000，若端口不符合，则清楚该占用端口并重新启动；
2.修复问题结合问题代码和上下文，分析前端请求和后端接口，包括路径、参数、方法等，但是注意保证前端页面不强制改动且不随意改变接口和请求路径，避免出现请求路径出错出现404问题。
3.注意修复过程中不得随意更改数据库内容，测试数据可手动点确认，避免数据出错。
4.修复完成后迅速完成修复，此次所有修复直接测试API调用，若测试结果仍有问题则继续修复，修复完成不需要创建修复报告。
5.所有涉及到终端的代码使用windows平台PowerShell格式命令，防止出现命令不识别的问题

一、代码风格与一致性规则
确保自动生成的代码符合项目统一的编码规范，减少人工调整成本。

语言特定格式规则
Python（Django）：遵循 PEP8 标准（如缩进 4 空格、函数间空两行、变量名小写下划线式）。


JavaScript/TypeScript（Vue）：遵循 ESLint 规则（如单引号、句尾无分号、箭头函数简写）。


跨文件命名一致性
自动对齐文件名与内部组件 / 类名

注释规范
自动为函数 / 类添加标准化注释

二、功能完整性规则
确保自动生成的代码 “可运行”，避免遗漏关键逻辑（如配置、依赖、错误处理）。


错误处理强制
自动为可能抛出异常的逻辑添加处理

依赖自动关联
检测到使用未导入的模块 / 组件时，自动添加导入语句


三、安全性规则
防止自动生成的代码引入安全漏洞（尤其前后端交互场景）。

输入验证强制
Django 接收用户输入（如request.POST）时，自动添加验证逻辑（使用forms.ModelForm或serializers.Serializer）；Vue 前端提交数据前，自动添加基础校验（如非空、格式匹配）。
示例规则："security.input_validate: {django: form_serializer, vue: prop_rules_vuelidate}"


四、项目结构适配规则 ** 让自动生成的代码 “融入” 现有项目目录结构，避免路径混乱。

五、修复优化规则（针对代码修复场景）
自动修复常见问题时，遵循 “最小修改” 原则，避免破坏现有逻辑。

冗余代码清理，前提要保证系统界面功能正常运转，不修改随意修改现有页面布局和功能


