# Generated by Django 5.2.4 on 2025-07-17 11:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0027_alter_purchaserequest_budget_total_amount"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="dictionary",
            name="value",
        ),
        migrations.AddField(
            model_name="dictionary",
            name="type_display",
            field=models.CharField(
                default="未分类",
                help_text="中文类型名称，用于界面显示",
                max_length=50,
                verbose_name="类型显示名称",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="dictionary",
            name="name",
            field=models.CharField(
                help_text="字典项的显示名称", max_length=100, verbose_name="名称"
            ),
        ),
        migrations.AlterField(
            model_name="dictionary",
            name="type",
            field=models.CharField(
                choices=[
                    ("status", "状态"),
                    ("purchase_type", "采购类型"),
                    ("procurement_method", "采购方式"),
                    ("fund_project", "经费项目"),
                    ("unit", "计量单位"),
                    ("item_category", "物品种类"),
                    ("requirement_source", "需求来源"),
                    ("urgency_level", "紧急程度"),
                    ("其他", "其他"),
                ],
                help_text="英文类型名称，用于代码匹配",
                max_length=50,
                verbose_name="字典类型",
            ),
        ),
    ]
