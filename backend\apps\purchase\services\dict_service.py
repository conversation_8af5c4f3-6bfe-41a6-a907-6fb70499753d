"""
字典服务
提供统一的字典数据访问和转换功能
"""

from django.core.cache import cache
from django.conf import settings
from apps.purchase.models import Dictionary
import logging

logger = logging.getLogger(__name__)


class DictService:
    """字典服务类"""
    
    # 缓存前缀
    CACHE_PREFIX = 'dict_service'
    # 缓存过期时间（秒）
    CACHE_TIMEOUT = getattr(settings, 'DICT_CACHE_TIMEOUT', 300)  # 5分钟
    
    @classmethod
    def get_cache_key(cls, dict_type, enabled_only=True):
        """生成缓存键"""
        return f"{cls.CACHE_PREFIX}:{dict_type}:{'enabled' if enabled_only else 'all'}"
    
    @classmethod
    def get_dict_items(cls, dict_type, enabled_only=True, use_cache=True):
        """
        获取字典项列表
        
        Args:
            dict_type (str): 字典类型
            enabled_only (bool): 是否只获取启用的项
            use_cache (bool): 是否使用缓存
            
        Returns:
            list: 字典项列表
        """
        cache_key = cls.get_cache_key(dict_type, enabled_only)
        
        # 尝试从缓存获取
        if use_cache:
            cached_data = cache.get(cache_key)
            if cached_data is not None:
                return cached_data
        
        try:
            # 从数据库查询
            queryset = Dictionary.objects.filter(type_code=dict_type)
            if enabled_only:
                queryset = queryset.filter(status=True)

            queryset = queryset.order_by('order', 'code')
            
            # 转换为字典列表
            items = []
            for item in queryset:
                items.append({
                    'id': item.id,
                    'code': item.code,
                    'name': item.name,
                    'description': item.description,
                    'order': item.order,
                    'status': item.status
                })
            
            # 缓存结果
            if use_cache:
                cache.set(cache_key, items, cls.CACHE_TIMEOUT)
            
            return items
            
        except Exception as e:
            logger.error(f"获取字典数据失败: {dict_type}, 错误: {str(e)}")
            return []
    
    @classmethod
    def get_dict_map(cls, dict_type, enabled_only=True, use_cache=True):
        """
        获取字典映射
        
        Args:
            dict_type (str): 字典类型
            enabled_only (bool): 是否只获取启用的项
            use_cache (bool): 是否使用缓存
            
        Returns:
            dict: 字典映射 {code: name}
        """
        items = cls.get_dict_items(dict_type, enabled_only, use_cache)
        return {item['code']: item['name'] for item in items}
    
    @classmethod
    def get_text(cls, dict_type, code, default=None):
        """
        获取字典文本
        
        Args:
            dict_type (str): 字典类型
            code (str): 字典编码
            default (str): 默认值
            
        Returns:
            str: 字典文本
        """
        if not code:
            return default or '-'
        
        try:
            dict_map = cls.get_dict_map(dict_type)
            return dict_map.get(code, code if default is None else default)
        except Exception as e:
            logger.error(f"字典转换失败: {dict_type}.{code}, 错误: {str(e)}")
            return code if default is None else default
    
    @classmethod
    def clear_cache(cls, dict_type=None):
        """
        清除缓存
        
        Args:
            dict_type (str): 字典类型，为None时清除所有字典缓存
        """
        if dict_type:
            # 清除指定类型的缓存
            for enabled_only in [True, False]:
                cache_key = cls.get_cache_key(dict_type, enabled_only)
                cache.delete(cache_key)
        else:
            # 清除所有字典缓存
            # 这里可以根据实际情况实现更精确的缓存清除逻辑
            pass
    
    @classmethod
    def refresh_dict(cls, dict_type):
        """
        刷新字典数据
        
        Args:
            dict_type (str): 字典类型
            
        Returns:
            list: 刷新后的字典项列表
        """
        cls.clear_cache(dict_type)
        return cls.get_dict_items(dict_type, use_cache=False)
    
    # 预定义的字典转换方法 - 从编码转换为中文名称
    @classmethod
    def get_status_text(cls, status):
        """获取状态文本"""
        return cls.get_text('status', status, status or '-')

    @classmethod
    def get_purchase_type_text(cls, purchase_type):
        """获取采购类型文本"""
        return cls.get_text('purchase_type', purchase_type, purchase_type or '-')

    @classmethod
    def get_procurement_method_text(cls, method):
        """获取采购方式文本"""
        return cls.get_text('procurement_method', method, method or '-')

    @classmethod
    def get_fund_project_text(cls, project):
        """获取经费项目文本"""
        return cls.get_text('fund_project', project, project or '-')

    @classmethod
    def get_unit_text(cls, unit):
        """获取单位文本"""
        return cls.get_text('unit', unit, unit or '-')

    @classmethod
    def get_item_category_text(cls, category):
        """获取物品种类文本"""
        return cls.get_text('item_category', category, category or '-')



    @classmethod
    def get_urgency_level_text(cls, level):
        """获取紧急程度文本"""
        return cls.get_text('urgency_level', level, level or '-')


# 创建全局实例
dict_service = DictService()

# 导出常用方法
get_dict_items = dict_service.get_dict_items
get_dict_map = dict_service.get_dict_map
get_text = dict_service.get_text
clear_cache = dict_service.clear_cache
refresh_dict = dict_service.refresh_dict

# 导出预定义转换方法
get_status_text = dict_service.get_status_text
get_purchase_type_text = dict_service.get_purchase_type_text
get_procurement_method_text = dict_service.get_procurement_method_text
get_fund_project_text = dict_service.get_fund_project_text
get_unit_text = dict_service.get_unit_text
get_item_category_text = dict_service.get_item_category_text

get_urgency_level_text = dict_service.get_urgency_level_text
