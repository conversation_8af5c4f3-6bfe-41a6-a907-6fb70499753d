/**
 * 字典编码转换工具
 * 用于将数据库中的字典编码转换为显示名称
 */

import dictService from '@/services/dictService'

class DictConverter {
  constructor() {
    // 缓存字典映射
    this.codeMaps = new Map()
    this.nameMaps = new Map()
  }

  /**
   * 获取字典编码到名称的映射
   * @param {string} dictType - 字典类型
   * @returns {Promise<Object>} 编码到名称的映射对象
   */
  async getCodeToNameMap(dictType) {
    if (this.codeMaps.has(dictType)) {
      return this.codeMaps.get(dictType)
    }

    try {
      const items = await dictService.getDict(dictType)
      const codeMap = {}
      const nameMap = {}
      
      items.forEach(item => {
        codeMap[item.code] = item.name
        nameMap[item.name] = item.code
      })
      
      this.codeMaps.set(dictType, codeMap)
      this.nameMaps.set(dictType, nameMap)
      
      return codeMap
    } catch (error) {
      console.error(`获取字典映射失败: ${dictType}`, error)
      return {}
    }
  }

  /**
   * 获取字典名称到编码的映射
   * @param {string} dictType - 字典类型
   * @returns {Promise<Object>} 名称到编码的映射对象
   */
  async getNameToCodeMap(dictType) {
    if (this.nameMaps.has(dictType)) {
      return this.nameMaps.get(dictType)
    }

    // 如果没有缓存，先获取编码映射（会同时生成名称映射）
    await this.getCodeToNameMap(dictType)
    return this.nameMaps.get(dictType) || {}
  }

  /**
   * 将字典编码转换为显示名称
   * @param {string} dictType - 字典类型
   * @param {string} code - 字典编码
   * @param {string} defaultValue - 默认值
   * @returns {Promise<string>} 显示名称
   */
  async codeToName(dictType, code, defaultValue = '-') {
    if (!code) return defaultValue

    try {
      const codeMap = await this.getCodeToNameMap(dictType)
      return codeMap[code] || code || defaultValue
    } catch (error) {
      console.error(`编码转换失败: ${dictType}.${code}`, error)
      return code || defaultValue
    }
  }

  /**
   * 将字典名称转换为编码
   * @param {string} dictType - 字典类型
   * @param {string} name - 字典名称
   * @param {string} defaultValue - 默认值
   * @returns {Promise<string>} 字典编码
   */
  async nameToCode(dictType, name, defaultValue = '') {
    if (!name) return defaultValue

    try {
      const nameMap = await this.getNameToCodeMap(dictType)
      return nameMap[name] || name || defaultValue
    } catch (error) {
      console.error(`名称转换失败: ${dictType}.${name}`, error)
      return name || defaultValue
    }
  }

  /**
   * 批量转换编码为名称
   * @param {string} dictType - 字典类型
   * @param {Array} codes - 编码数组
   * @returns {Promise<Array>} 名称数组
   */
  async batchCodeToName(dictType, codes) {
    if (!Array.isArray(codes)) return []

    try {
      const codeMap = await this.getCodeToNameMap(dictType)
      return codes.map(code => codeMap[code] || code || '-')
    } catch (error) {
      console.error(`批量编码转换失败: ${dictType}`, error)
      return codes
    }
  }

  /**
   * 批量转换名称为编码
   * @param {string} dictType - 字典类型
   * @param {Array} names - 名称数组
   * @returns {Promise<Array>} 编码数组
   */
  async batchNameToCode(dictType, names) {
    if (!Array.isArray(names)) return []

    try {
      const nameMap = await this.getNameToCodeMap(dictType)
      return names.map(name => nameMap[name] || name || '')
    } catch (error) {
      console.error(`批量名称转换失败: ${dictType}`, error)
      return names
    }
  }

  /**
   * 转换采购请求对象中的字典字段（编码转名称，用于显示）
   * @param {Object} request - 采购请求对象
   * @returns {Promise<Object>} 转换后的对象
   */
  async convertRequestForDisplay(request) {
    if (!request) return request

    const converted = { ...request }
    
    // 定义需要转换的字段及其字典类型
    const fieldMappings = {
      status: 'status',
      item_category: 'item_category',
      unit: 'unit',
      procurement_method: 'procurement_method',
      fund_project: 'fund_project',
      purchase_type: 'purchase_type'
    }

    // 并行转换所有字段
    const conversions = Object.entries(fieldMappings).map(async ([field, dictType]) => {
      if (converted[field]) {
        converted[field] = await this.codeToName(dictType, converted[field])
      }
    })

    await Promise.all(conversions)
    return converted
  }

  /**
   * 转换采购请求对象中的字典字段（名称转编码，用于提交）
   * @param {Object} request - 采购请求对象
   * @returns {Promise<Object>} 转换后的对象
   */
  async convertRequestForSubmit(request) {
    if (!request) return request

    const converted = { ...request }
    
    // 定义需要转换的字段及其字典类型
    const fieldMappings = {
      status: 'status',
      item_category: 'item_category',
      unit: 'unit',
      procurement_method: 'procurement_method',
      fund_project: 'fund_project',
      purchase_type: 'purchase_type'
    }

    // 并行转换所有字段
    const conversions = Object.entries(fieldMappings).map(async ([field, dictType]) => {
      if (converted[field]) {
        converted[field] = await this.nameToCode(dictType, converted[field])
      }
    })

    await Promise.all(conversions)
    return converted
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.codeMaps.clear()
    this.nameMaps.clear()
  }

  /**
   * 刷新指定类型的字典缓存
   * @param {string} dictType - 字典类型
   */
  refreshDict(dictType) {
    this.codeMaps.delete(dictType)
    this.nameMaps.delete(dictType)
  }
}

// 创建单例实例
const dictConverter = new DictConverter()

export default dictConverter

// 导出常用方法
export const {
  codeToName,
  nameToCode,
  batchCodeToName,
  batchNameToCode,
  convertRequestForDisplay,
  convertRequestForSubmit,
  clearCache,
  refreshDict
} = dictConverter
