-- 采购管理系统数据库字段注释脚本（修正版）
-- 基于实际数据库结构为所有表的字段添加详细注释
-- 执行前请确保已连接到正确的数据库

-- ==================== 1. 用户表 (sys_user) ====================
-- 基于Django AbstractUser扩展的用户表，管理系统用户信息
ALTER TABLE `sys_user` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户唯一标识ID，主键',
MODIFY COLUMN `password` varchar(128) NOT NULL COMMENT '用户密码，经过Django加密存储',
MODIFY COLUMN `last_login` datetime(6) NULL COMMENT '最后登录时间，记录用户最近一次登录的时间戳',
MODIFY COLUMN `is_superuser` tinyint(1) NOT NULL COMMENT '是否为超级管理员，1=是 0=否，超级管理员拥有所有权限',
MODIFY COLUMN `username` varchar(150) NOT NULL COMMENT '登录用户名，系统登录的唯一标识，不可重复',
MODIFY COLUMN `first_name` varchar(150) NOT NULL COMMENT '名字，Django内置字段，通常不使用',
MODIFY COLUMN `last_name` varchar(150) NOT NULL COMMENT '姓氏，Django内置字段，通常不使用',
MODIFY COLUMN `email` varchar(254) NOT NULL COMMENT '电子邮箱地址，用于系统通知和密码重置',
MODIFY COLUMN `is_staff` tinyint(1) NOT NULL COMMENT '是否为员工，1=是 0=否，决定是否可以访问Django管理后台',
MODIFY COLUMN `is_active` tinyint(1) NOT NULL COMMENT '账户是否激活，1=激活 0=禁用，禁用用户无法登录系统',
MODIFY COLUMN `date_joined` datetime(6) NOT NULL COMMENT '账户创建时间，记录用户注册或被创建的时间',
MODIFY COLUMN `real_name` varchar(50) NOT NULL COMMENT '真实姓名，用户的实际姓名，用于显示和业务流程中的身份标识',
MODIFY COLUMN `phone` varchar(20) NOT NULL COMMENT '联系电话，用户的手机号码，用于紧急联系和短信通知',
MODIFY COLUMN `dept_id` int NULL COMMENT '所属部门ID，关联sys_department表的id字段，标识用户所属的部门',
MODIFY COLUMN `role` varchar(20) NOT NULL COMMENT '用户角色，关联数据字典角色类型，如admin/approver/acceptor/finance等';

-- ==================== 2. 部门表 (sys_department) ====================
-- 支持两级部门结构的部门管理表
ALTER TABLE `sys_department` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门唯一标识ID，主键',
MODIFY COLUMN `dept_name` varchar(50) NOT NULL COMMENT '部门名称，如"技术部"、"财务部"等',
MODIFY COLUMN `parent_id` int NULL COMMENT '上级部门ID，关联本表id字段，null表示顶级部门，支持两级部门结构',
MODIFY COLUMN `dept_code` varchar(20) NOT NULL COMMENT '部门编码，部门的唯一标识码，用于系统内部识别，不可重复',
MODIFY COLUMN `hierarchy_path` varchar(200) NOT NULL COMMENT '部门层级路径，如"分公司-办事处"，自动生成的完整层级显示',
MODIFY COLUMN `status` tinyint(1) NOT NULL COMMENT '部门状态，1=启用 0=停用，停用的部门不参与业务流程',
MODIFY COLUMN `created_at` datetime(6) NOT NULL COMMENT '创建时间，部门信息创建的时间戳',
MODIFY COLUMN `updated_at` datetime(6) NOT NULL COMMENT '更新时间，部门信息最后修改的时间戳',
MODIFY COLUMN `approval_limit` decimal(12,2) NULL COMMENT '采购审批额度限制，该部门可审批的最大金额（元），null表示无限制',
MODIFY COLUMN `can_accept` tinyint(1) NOT NULL COMMENT '是否具有验收权限，1=可以进行物品验收 0=不可以',
MODIFY COLUMN `can_approve` tinyint(1) NOT NULL COMMENT '是否具有审批权限，1=可以审批采购需求 0=不可以',
MODIFY COLUMN `can_finance` tinyint(1) NOT NULL COMMENT '是否具有财务权限，1=可以进行财务结算 0=不可以',
MODIFY COLUMN `contact_phone` varchar(20) NULL COMMENT '部门联系电话，部门的对外联系电话号码',
MODIFY COLUMN `description` longtext NULL COMMENT '部门描述，部门的详细说明、职能介绍等信息',
MODIFY COLUMN `manager_name` varchar(50) NULL COMMENT '部门负责人姓名，该部门的主要负责人或经理';

-- ==================== 3. 角色表 (sys_role) ====================
-- 系统角色管理表，定义用户权限组
ALTER TABLE `sys_role` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色唯一标识ID，主键',
MODIFY COLUMN `name` varchar(50) NOT NULL COMMENT '角色名称，如"管理员"、"采购员"等，用于显示',
MODIFY COLUMN `code` varchar(50) NOT NULL COMMENT '角色编码，如"admin"、"purchaser"等，系统内部使用的唯一标识',
MODIFY COLUMN `description` longtext NULL COMMENT '角色描述，角色的详细说明、职责范围等信息',
MODIFY COLUMN `is_active` tinyint(1) NOT NULL COMMENT '是否启用，1=启用 0=禁用，禁用的角色无法分配给用户',
MODIFY COLUMN `is_system` tinyint(1) NOT NULL COMMENT '是否为系统角色，1=系统内置角色 0=自定义角色，系统角色不可删除',
MODIFY COLUMN `created_at` datetime(6) NOT NULL COMMENT '创建时间，角色创建的时间戳',
MODIFY COLUMN `updated_at` datetime(6) NOT NULL COMMENT '更新时间，角色信息最后修改的时间戳';

-- ==================== 4. 用户角色关联表 (sys_user_role) ====================
-- 用户与角色的多对多关联表
ALTER TABLE `sys_user_role` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一标识ID，主键',
MODIFY COLUMN `user_id` bigint NOT NULL COMMENT '用户ID，关联sys_user表的id字段',
MODIFY COLUMN `role_id` bigint NOT NULL COMMENT '角色ID，关联sys_role表的id字段',
MODIFY COLUMN `assigned_at` datetime(6) NOT NULL COMMENT '分配时间，角色分配给用户的时间戳',
MODIFY COLUMN `assigned_by_id` bigint NULL COMMENT '分配人ID，执行角色分配操作的用户ID，关联sys_user表';

-- ==================== 5. 系统日志表 (system_log) ====================
-- 记录系统操作日志，用于审计和问题追踪
ALTER TABLE `system_log` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志记录唯一标识ID，主键',
MODIFY COLUMN `log_type` varchar(20) NOT NULL COMMENT '日志类型，如login/logout/create/update/delete/approve等操作类型',
MODIFY COLUMN `username` varchar(150) NULL COMMENT '操作用户名，执行操作的用户登录名，可能为空（系统操作）',
MODIFY COLUMN `action` varchar(200) NULL COMMENT '操作描述，具体的操作内容描述，如"创建采购需求"',
MODIFY COLUMN `ip_address` varchar(45) NULL COMMENT '客户端IP地址，执行操作时的客户端IP，支持IPv4和IPv6',
MODIFY COLUMN `user_agent` longtext NULL COMMENT '用户代理字符串，客户端浏览器和操作系统信息',
MODIFY COLUMN `target_model` varchar(100) NULL COMMENT '目标模型，操作涉及的数据模型名称，如PurchaseRequest',
MODIFY COLUMN `target_id` varchar(50) NULL COMMENT '目标对象ID，操作涉及的具体记录ID',
MODIFY COLUMN `request_data` json NULL COMMENT '请求数据，操作时提交的请求参数，JSON格式存储',
MODIFY COLUMN `response_data` json NULL COMMENT '响应数据，操作的返回结果，JSON格式存储',
MODIFY COLUMN `created_at` datetime(6) NOT NULL COMMENT '日志创建时间，操作发生的时间戳',
MODIFY COLUMN `user_id` bigint NULL COMMENT '操作用户ID，关联sys_user表的id字段';

-- ==================== 6. 通知表 (system_notification) ====================
-- 系统通知消息表，管理用户通知
ALTER TABLE `system_notification` 
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知唯一标识ID，主键',
MODIFY COLUMN `title` varchar(200) NOT NULL COMMENT '通知标题，通知消息的标题，简要说明通知内容',
MODIFY COLUMN `content` longtext NOT NULL COMMENT '通知内容，通知的详细内容，支持富文本格式',
MODIFY COLUMN `notification_type` varchar(50) NOT NULL COMMENT '通知类型，如system/approval/reminder等，用于分类管理',
MODIFY COLUMN `priority` varchar(20) NOT NULL COMMENT '优先级，如low/normal/high/urgent，影响通知的显示顺序',
MODIFY COLUMN `sender_id` bigint NULL COMMENT '发送者ID，发送通知的用户ID，关联sys_user表，null表示系统通知',
MODIFY COLUMN `recipient_id` bigint NULL COMMENT '接收者ID，接收通知的用户ID，关联sys_user表，null表示全员通知',
MODIFY COLUMN `target_model` varchar(100) NULL COMMENT '关联业务对象模型，通知相关的业务对象类型',
MODIFY COLUMN `target_id` varchar(50) NULL COMMENT '关联业务对象ID，通知相关的具体业务记录ID',
MODIFY COLUMN `target_url` varchar(500) NULL COMMENT '跳转链接，点击通知后跳转的页面URL',
MODIFY COLUMN `is_read` tinyint(1) NOT NULL COMMENT '是否已读，1=已读 0=未读，用于标记通知状态',
MODIFY COLUMN `read_at` datetime(6) NULL COMMENT '阅读时间，用户查看通知的时间戳',
MODIFY COLUMN `is_deleted` tinyint(1) NOT NULL COMMENT '是否删除，1=已删除 0=正常，软删除标记',
MODIFY COLUMN `is_archived` tinyint(1) NOT NULL COMMENT '是否归档，1=已归档 0=正常，用于通知管理',
MODIFY COLUMN `action_buttons` json NOT NULL COMMENT '操作按钮配置，JSON格式存储通知的可操作按钮',
MODIFY COLUMN `created_at` datetime(6) NOT NULL COMMENT '创建时间，通知创建的时间戳',
MODIFY COLUMN `updated_at` datetime(6) NOT NULL COMMENT '更新时间，通知信息最后修改的时间戳',
MODIFY COLUMN `expires_at` datetime(6) NULL COMMENT '过期时间，通知的有效期截止时间，过期后自动隐藏',
MODIFY COLUMN `action_taken` varchar(255) NULL COMMENT '已执行的操作，记录用户对通知执行的操作',
MODIFY COLUMN `clicked_at` datetime(6) NULL COMMENT '点击时间，用户点击通知的时间戳',
MODIFY COLUMN `delivery_methods` json NOT NULL COMMENT '投递方式，通知的投递渠道配置，JSON格式',
MODIFY COLUMN `delivery_status` json NOT NULL COMMENT '投递状态，各投递渠道的状态信息，JSON格式',
MODIFY COLUMN `is_sent` tinyint(1) NOT NULL COMMENT '是否已发送，1=已发送 0=未发送',
MODIFY COLUMN `response_time` double NULL COMMENT '响应时间，通知处理的响应时间（秒）',
MODIFY COLUMN `retry_count` int NOT NULL COMMENT '重试次数，通知发送失败后的重试次数',
MODIFY COLUMN `scheduled_time` datetime(6) NULL COMMENT '计划发送时间，定时通知的预定发送时间',
MODIFY COLUMN `sent_at` datetime(6) NULL COMMENT '实际发送时间，通知实际发送的时间戳',
MODIFY COLUMN `summary` varchar(500) NULL COMMENT '通知摘要，通知内容的简要概述';

-- ==================== 7. 采购需求表 (purchase_purchase_request) ====================
-- 核心业务表，管理完整的采购流程从需求提报到结算报销
ALTER TABLE `purchase_purchase_request`
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '采购需求唯一标识ID，主键',
MODIFY COLUMN `status` varchar(30) NOT NULL COMMENT '流程状态，如draft/pending_approval/approved/rejected/purchased/accepted/settled等',

-- A. 基础需求信息
MODIFY COLUMN `item_category` varchar(50) NOT NULL COMMENT '物品种类，物品的分类标识，关联数据字典物品分类',
MODIFY COLUMN `item_name` varchar(100) NOT NULL COMMENT '物品名称，具体的物品名称，如"联想ThinkPad笔记本"',
MODIFY COLUMN `specification` varchar(200) NULL COMMENT '规格型号，物品的详细规格参数，如"i7-12700H/16G/512G SSD"',
MODIFY COLUMN `unit` varchar(20) NOT NULL COMMENT '计量单位，如个、台、套、箱等，关联数据字典单位类型',
MODIFY COLUMN `budget_quantity` int NOT NULL COMMENT '需求数量，申请采购的数量，必须为正整数',
MODIFY COLUMN `budget_unit_price` decimal(12,2) NOT NULL COMMENT '预算单价，预估的单价（元），用于预算控制',
MODIFY COLUMN `budget_total_amount` decimal(15,2) NOT NULL COMMENT '预算金额，预算单价×需求数量，自动计算',

-- B. 需求提报信息
MODIFY COLUMN `requester_id` bigint NOT NULL COMMENT '申请人ID，提交采购需求的用户ID，关联sys_user表',
MODIFY COLUMN `dept_id` int NOT NULL COMMENT '申请部门ID，申请人所属部门ID，关联sys_department表',
MODIFY COLUMN `hierarchy_path` varchar(200) NOT NULL COMMENT '部门层级路径，申请时的部门完整路径',
MODIFY COLUMN `procurement_method` varchar(50) NOT NULL COMMENT '采购方式，如询价采购/招标采购等，关联数据字典采购方式',
MODIFY COLUMN `requirement_source` varchar(50) NOT NULL COMMENT '需求来源，如日常需求/项目需求等，关联数据字典需求来源',
MODIFY COLUMN `fund_project` varchar(100) NULL COMMENT '资金项目，经费来源项目名称或编号',
MODIFY COLUMN `remarks` longtext NOT NULL COMMENT '申请备注，申请人的补充说明和特殊要求',
MODIFY COLUMN `submission_date` datetime(6) NULL COMMENT '提交时间，需求正式提交的时间戳',
MODIFY COLUMN `requirement_attachment` varchar(255) NULL COMMENT '需求附件，申请时上传的相关文件路径',
MODIFY COLUMN `created_at` datetime(6) NOT NULL COMMENT '创建时间，记录创建的时间戳',
MODIFY COLUMN `updated_at` datetime(6) NOT NULL COMMENT '更新时间，记录最后修改的时间戳',

-- C. 审批信息
MODIFY COLUMN `approver_id` int NULL COMMENT '审批人ID，执行审批操作的用户ID，关联sys_user表',
MODIFY COLUMN `approved_at` datetime(6) NULL COMMENT '审批时间，审批操作完成的时间戳',
MODIFY COLUMN `approval_comment` longtext NOT NULL COMMENT '审批意见，审批人的审批意见和建议',
MODIFY COLUMN `approval_attachment` varchar(255) NULL COMMENT '审批附件，审批时上传的相关文件路径',
MODIFY COLUMN `rejection_reason` longtext NOT NULL COMMENT '拒绝原因，审批被拒绝时的详细原因说明',

-- D. 异常审批信息
MODIFY COLUMN `has_exception` tinyint(1) NOT NULL COMMENT '是否有异常，1=存在异常情况 0=正常流程',
MODIFY COLUMN `exception_approved` tinyint(1) NOT NULL COMMENT '异常是否已审批，1=异常已审批通过 0=异常未审批',
MODIFY COLUMN `exception_approver_id` int NULL COMMENT '异常审批人ID，处理异常情况的审批人',
MODIFY COLUMN `exception_approved_at` varchar(255) NULL COMMENT '异常审批时间，异常审批完成的时间',
MODIFY COLUMN `exception_reason` longtext NOT NULL COMMENT '异常原因，异常情况的详细说明',

-- E. 历史价格信息
MODIFY COLUMN `history_purchase_count` int NOT NULL COMMENT '历史采购次数，该物品的历史采购记录数量',
MODIFY COLUMN `history_avg_price` decimal(12,2) NOT NULL COMMENT '历史平均价格，该物品的历史平均采购价格（元）',
MODIFY COLUMN `history_max_price` decimal(12,2) NOT NULL COMMENT '历史最高价格，该物品的历史最高采购价格（元）',
MODIFY COLUMN `history_min_price` decimal(12,2) NOT NULL COMMENT '历史最低价格，该物品的历史最低采购价格（元）',

-- F. 采购信息
MODIFY COLUMN `order_number` varchar(100) NOT NULL COMMENT '订单号，采购订单的编号或标识',
MODIFY COLUMN `purchaser_id` bigint NULL COMMENT '采购人ID，执行采购操作的用户ID，关联sys_user表',
MODIFY COLUMN `purchase_date` datetime(6) NULL COMMENT '采购完成时间，采购操作完成的时间戳',
MODIFY COLUMN `purchase_quantity` int NOT NULL COMMENT '采购数量，实际采购的数量，可能与需求数量不同',
MODIFY COLUMN `purchase_unit_price` decimal(12,2) NULL COMMENT '采购单价，实际采购的单价（元）',
MODIFY COLUMN `purchase_total_amount` decimal(15,2) NULL COMMENT '采购金额，采购单价×采购数量，自动计算',
MODIFY COLUMN `supplier_name` varchar(200) NOT NULL COMMENT '供应商名称，实际供应商的公司名称',
MODIFY COLUMN `purchase_remarks` longtext NOT NULL COMMENT '采购备注，采购过程中的备注信息和说明',
MODIFY COLUMN `purchase_type` varchar(50) NOT NULL COMMENT '采购类型，如正常采购/紧急采购等',
MODIFY COLUMN `transaction_number` varchar(100) NOT NULL COMMENT '交易号，采购交易的流水号或凭证号',

-- G. 物流信息
MODIFY COLUMN `shipping_date` datetime(6) NULL COMMENT '发货时间，供应商发货的时间戳',
MODIFY COLUMN `shipping_origin` varchar(200) NOT NULL COMMENT '发货地点，供应商发货的地址或仓库',
MODIFY COLUMN `tracking_number` varchar(100) NOT NULL COMMENT '快递单号，物流跟踪号码',
MODIFY COLUMN `courier_company` varchar(100) NOT NULL COMMENT '快递公司，承运的物流公司名称',
MODIFY COLUMN `courier_receipt_photo` varchar(255) NULL COMMENT '快递单照片，快递单据的照片文件路径',

-- H. 验收信息
MODIFY COLUMN `acceptor_id` bigint NULL COMMENT '验收人ID，执行验收操作的用户ID，关联sys_user表',
MODIFY COLUMN `acceptance_date` datetime(6) NULL COMMENT '验收时间，验收操作完成的时间戳',
MODIFY COLUMN `acceptance_quantity` int NOT NULL COMMENT '验收数量，实际验收通过的数量',
MODIFY COLUMN `acceptance_remarks` longtext NOT NULL COMMENT '验收意见，验收人的验收意见和问题说明',
MODIFY COLUMN `item_photo` varchar(255) NULL COMMENT '物品照片，验收时拍摄的物品照片文件路径',
MODIFY COLUMN `quantity_variance` int NOT NULL COMMENT '数量差异，验收数量与采购数量的差异',
MODIFY COLUMN `variance_rate` varchar(50) NULL COMMENT '差异率，数量差异的百分比',
MODIFY COLUMN `re_acceptance_required` tinyint(1) NOT NULL COMMENT '是否需要重新验收，1=需要 0=不需要',
MODIFY COLUMN `re_acceptance_count` int NOT NULL COMMENT '重新验收次数，已进行的重新验收次数',

-- I. 退货信息
MODIFY COLUMN `return_date` datetime(6) NULL COMMENT '退货时间，物品退货的时间戳',
MODIFY COLUMN `return_reason` longtext NOT NULL COMMENT '退货原因，退货的详细原因说明',
MODIFY COLUMN `returner_id` bigint NULL COMMENT '退货人ID，执行退货操作的用户ID，关联sys_user表',

-- J. 结算报销信息
MODIFY COLUMN `reimburser_id` bigint NULL COMMENT '报销人ID，执行报销操作的用户ID，关联sys_user表',
MODIFY COLUMN `reimbursement_date` datetime(6) NULL COMMENT '报销时间，报销操作完成的时间戳',
MODIFY COLUMN `settlement_amount` decimal(15,2) NULL COMMENT '结算金额，实际结算的金额（元）',
MODIFY COLUMN `settlement_remarks` varchar(500) NULL COMMENT '结算备注，财务结算时的备注信息',
MODIFY COLUMN `voucher_number` varchar(100) NOT NULL COMMENT '凭证号，财务凭证的编号',
MODIFY COLUMN `financial_serial` varchar(100) NOT NULL COMMENT '财务流水号，财务系统的流水编号',
MODIFY COLUMN `payee_name` varchar(100) NOT NULL COMMENT '收款人姓名，实际收款人的姓名',
MODIFY COLUMN `payee_account` varchar(100) NOT NULL COMMENT '收款账户，收款人的银行账户信息',

-- K. 财务退回信息
MODIFY COLUMN `finance_return_date` datetime(6) NULL COMMENT '财务退回时间，财务退回操作的时间戳',
MODIFY COLUMN `finance_return_reason` longtext NOT NULL COMMENT '财务退回原因，财务退回的详细原因说明',
MODIFY COLUMN `finance_returner_id` bigint NULL COMMENT '财务退回人ID，执行财务退回操作的用户ID';

-- ==================== 8. 数据字典表 (purchase_dictionary) ====================
-- 系统配置数据字典，管理各种枚举值和配置项
ALTER TABLE `purchase_dictionary`
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典项唯一标识ID，主键',
MODIFY COLUMN `type` varchar(50) NOT NULL COMMENT '字典类型，如采购方式/经费来源/物品分类/单位/其他等分类标识',
MODIFY COLUMN `code` varchar(50) NOT NULL COMMENT '字典编码，系统内唯一标识，用于程序中引用',
MODIFY COLUMN `name` varchar(100) NOT NULL COMMENT '字典名称，显示给用户的名称',
MODIFY COLUMN `value` varchar(100) NOT NULL COMMENT '字典值，字典项的实际值，可能与name相同或不同',
MODIFY COLUMN `description` longtext NOT NULL COMMENT '字典描述，字典项的详细说明和使用场景',
MODIFY COLUMN `order` int NOT NULL COMMENT '排序值，控制字典项的显示顺序，数值越小越靠前',
MODIFY COLUMN `status` tinyint(1) NOT NULL COMMENT '状态，1=启用 0=停用，停用的字典项不在前端显示',
MODIFY COLUMN `created_at` datetime(6) NOT NULL COMMENT '创建时间，字典项创建的时间戳',
MODIFY COLUMN `updated_at` datetime(6) NOT NULL COMMENT '更新时间，字典项最后修改的时间戳';

-- ==================== 9. 验收照片表 (purchase_acceptance_photo) ====================
-- 验收照片管理表，存储验收过程中的照片信息
ALTER TABLE `purchase_acceptance_photo`
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '照片记录唯一标识ID，主键',
MODIFY COLUMN `photo` varchar(255) NOT NULL COMMENT '照片文件路径，存储在服务器上的照片文件路径',
MODIFY COLUMN `photo_type` varchar(50) NOT NULL COMMENT '照片类型，如物品照片/包装照片/问题照片等',
MODIFY COLUMN `description` varchar(500) NOT NULL COMMENT '照片描述，照片的详细说明和备注信息',
MODIFY COLUMN `upload_time` datetime(6) NOT NULL COMMENT '上传时间，照片上传的时间戳',
MODIFY COLUMN `purchase_request_id` bigint NOT NULL COMMENT '采购需求ID，关联purchase_purchase_request表的id字段',
MODIFY COLUMN `uploader_id` bigint NULL COMMENT '上传人ID，上传照片的用户ID，关联sys_user表';

-- ==================== 10. Django权限表 (auth_permission) ====================
-- Django内置权限表，管理系统权限定义
ALTER TABLE `auth_permission`
MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT COMMENT '权限唯一标识ID，主键',
MODIFY COLUMN `name` varchar(255) NOT NULL COMMENT '权限名称，权限的显示名称，如"Can add user"',
MODIFY COLUMN `content_type_id` int NOT NULL COMMENT '内容类型ID，关联django_content_type表，标识权限所属的模型',
MODIFY COLUMN `codename` varchar(100) NOT NULL COMMENT '权限代码名，权限的代码标识，如"add_user"';

-- ==================== 11. Django内容类型表 (django_content_type) ====================
-- Django内置内容类型表，管理模型元数据
ALTER TABLE `django_content_type`
MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT COMMENT '内容类型唯一标识ID，主键',
MODIFY COLUMN `app_label` varchar(100) NOT NULL COMMENT '应用标签，Django应用的名称，如authentication/system/purchase',
MODIFY COLUMN `model` varchar(100) NOT NULL COMMENT '模型名称，Django模型的名称，如user/department/purchaserequest';

-- ==================== 12. 角色权限关联表 (sys_role_permissions) ====================
-- 角色与权限的多对多关联表
ALTER TABLE `sys_role_permissions`
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一标识ID，主键',
MODIFY COLUMN `role_id` bigint NOT NULL COMMENT '角色ID，关联sys_role表的id字段',
MODIFY COLUMN `permission_id` int NOT NULL COMMENT '权限ID，关联auth_permission表的id字段';

-- ==================== 13. Django会话表 (django_session) ====================
-- Django内置会话表，管理用户会话信息
ALTER TABLE `django_session`
MODIFY COLUMN `session_key` varchar(40) NOT NULL COMMENT '会话键，会话的唯一标识，主键',
MODIFY COLUMN `session_data` longtext NOT NULL COMMENT '会话数据，序列化的会话信息，包含用户状态等',
MODIFY COLUMN `expire_date` datetime(6) NOT NULL COMMENT '过期时间，会话的过期时间戳，过期后会话失效';

-- ==================== 14. Django管理日志表 (django_admin_log) ====================
-- Django管理后台操作日志表
ALTER TABLE `django_admin_log`
MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT COMMENT '日志记录唯一标识ID，主键',
MODIFY COLUMN `action_time` datetime(6) NOT NULL COMMENT '操作时间，管理后台操作的时间戳',
MODIFY COLUMN `object_id` longtext NULL COMMENT '对象ID，被操作对象的ID，可能为空',
MODIFY COLUMN `object_repr` varchar(200) NOT NULL COMMENT '对象表示，被操作对象的字符串表示',
MODIFY COLUMN `action_flag` smallint NOT NULL COMMENT '操作标志，1=新增 2=修改 3=删除',
MODIFY COLUMN `change_message` longtext NOT NULL COMMENT '变更消息，操作的详细描述信息',
MODIFY COLUMN `content_type_id` int NULL COMMENT '内容类型ID，关联django_content_type表',
MODIFY COLUMN `user_id` bigint NOT NULL COMMENT '操作用户ID，执行操作的管理员用户ID，关联sys_user表';

-- ==================== 15. Django迁移记录表 (django_migrations) ====================
-- Django数据库迁移记录表
ALTER TABLE `django_migrations`
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '迁移记录唯一标识ID，主键',
MODIFY COLUMN `app` varchar(255) NOT NULL COMMENT '应用名称，执行迁移的Django应用名',
MODIFY COLUMN `name` varchar(255) NOT NULL COMMENT '迁移文件名，迁移文件的名称',
MODIFY COLUMN `applied` datetime(6) NOT NULL COMMENT '应用时间，迁移执行的时间戳';

-- ==================== 16. Django用户组表 (auth_group) ====================
-- Django内置用户组表
ALTER TABLE `auth_group`
MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT COMMENT '用户组唯一标识ID，主键',
MODIFY COLUMN `name` varchar(150) NOT NULL COMMENT '用户组名称，用户组的显示名称';

-- ==================== 17. Django用户组权限关联表 (auth_group_permissions) ====================
-- Django用户组与权限的多对多关联表
ALTER TABLE `auth_group_permissions`
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一标识ID，主键',
MODIFY COLUMN `group_id` int NOT NULL COMMENT '用户组ID，关联auth_group表的id字段',
MODIFY COLUMN `permission_id` int NOT NULL COMMENT '权限ID，关联auth_permission表的id字段';

-- ==================== 18. 用户组关联表 (sys_user_groups) ====================
-- 用户与Django用户组的多对多关联表
ALTER TABLE `sys_user_groups`
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一标识ID，主键',
MODIFY COLUMN `user_id` bigint NOT NULL COMMENT '用户ID，关联sys_user表的id字段',
MODIFY COLUMN `group_id` int NOT NULL COMMENT '用户组ID，关联auth_group表的id字段';

-- ==================== 19. 用户权限关联表 (sys_user_user_permissions) ====================
-- 用户与权限的多对多关联表
ALTER TABLE `sys_user_user_permissions`
MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一标识ID，主键',
MODIFY COLUMN `user_id` bigint NOT NULL COMMENT '用户ID，关联sys_user表的id字段',
MODIFY COLUMN `permission_id` int NOT NULL COMMENT '权限ID，关联auth_permission表的id字段';

-- ==================== 表级别注释 ====================
-- 为每个表添加表级别的注释说明

ALTER TABLE `sys_user` COMMENT = '用户表：管理系统用户信息，基于Django AbstractUser扩展，支持部门关联和角色分配';

ALTER TABLE `sys_department` COMMENT = '部门表：管理组织架构，支持两级部门结构（分公司-办事处），包含权限配置';

ALTER TABLE `sys_role` COMMENT = '角色表：定义系统角色，用于权限管理和用户分组';

ALTER TABLE `sys_user_role` COMMENT = '用户角色关联表：用户与角色的多对多关系，支持一个用户拥有多个角色';

ALTER TABLE `system_log` COMMENT = '系统日志表：记录所有系统操作日志，用于审计追踪和问题排查';

ALTER TABLE `system_notification` COMMENT = '系统通知表：管理用户通知消息，支持不同类型和优先级的通知';

ALTER TABLE `purchase_purchase_request` COMMENT = '采购需求表：核心业务表，管理完整采购流程，从需求提报到结算报销的全生命周期';

ALTER TABLE `purchase_dictionary` COMMENT = '数据字典表：系统配置表，管理各种枚举值和下拉选项，如采购方式、经费来源等';

ALTER TABLE `purchase_acceptance_photo` COMMENT = '验收照片表：存储验收过程中的照片信息，支持多种照片类型';

ALTER TABLE `auth_permission` COMMENT = 'Django权限表：定义系统中所有可用权限，与角色表关联实现RBAC权限控制';

ALTER TABLE `django_content_type` COMMENT = 'Django内容类型表：Django框架内置表，管理所有模型的元数据信息';

ALTER TABLE `sys_role_permissions` COMMENT = '角色权限关联表：角色与权限的多对多关系，定义每个角色拥有的具体权限';

ALTER TABLE `django_session` COMMENT = 'Django会话表：管理用户登录会话，存储会话状态和过期信息';

ALTER TABLE `django_admin_log` COMMENT = 'Django管理日志表：记录Django管理后台的所有操作日志';

ALTER TABLE `django_migrations` COMMENT = 'Django迁移记录表：记录数据库结构变更历史，确保数据库版本一致性';

ALTER TABLE `auth_group` COMMENT = 'Django用户组表：Django内置用户组管理，用于权限分组';

ALTER TABLE `auth_group_permissions` COMMENT = 'Django用户组权限关联表：用户组与权限的多对多关系';

ALTER TABLE `sys_user_groups` COMMENT = '用户组关联表：用户与Django用户组的多对多关系';

ALTER TABLE `sys_user_user_permissions` COMMENT = '用户权限关联表：用户与权限的多对多关系，用于直接给用户分配权限';
