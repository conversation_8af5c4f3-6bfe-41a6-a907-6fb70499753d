# Generated by Django 5.2.3 on 2025-06-15 14:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0006_purchaserequest_purchase_date_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaserequest',
            name='acceptance_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='验收日期'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='acceptor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='accepted_requests', to=settings.AUTH_USER_MODEL, verbose_name='验收人'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='reimbursement_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='报销日期'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='reimburser',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reimbursed_requests', to=settings.AUTH_USER_MODEL, verbose_name='报销人'),
        ),
    ]
