<template>
  <div class="purchase-request-detail">
    <!-- 页面标题区域 -->
    <div class="page-header business-card">
      <div class="header-content">
        <h1 class="page-title">
          <FileTextOutlined />
          {{ isNewMode ? '新建采购需求' : '采购需求详情' }}
        </h1>
        <p class="page-subtitle">{{ isNewMode ? '创建新的采购需求' : '查看采购需求的详细信息' }}</p>
      </div>
      <div class="header-actions">
        <!-- 财务退回按钮 -->
        <a-button v-if="canFinanceReturn" type="primary" danger @click="showFinanceReturnModal" class="action-btn"
          style="margin-right: 8px;">
          <ExclamationCircleOutlined />
          财务退回
        </a-button>
        <a-button @click="goBack" class="secondary-action-btn">
          <ArrowLeftOutlined />
          返回
        </a-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <!-- 详情内容 -->
    <div v-else-if="requestDetail || isNewMode" class="detail-content">
      <!-- 基本信息 -->
      <div class="info-section business-card">
        <h3 class="section-title">基本信息</h3>
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <label>需求编号：</label>
              <span>{{ requestDetail.id }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>物品名称：</label>
              <span>{{ requestDetail.item_name }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>物品种类：</label>
              <span>{{ requestDetail.item_category_display || requestDetail.item_category || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>规格型号：</label>
              <span>{{ requestDetail.specification || '' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>数量：</label>
              <span>{{ requestDetail.budget_quantity || requestDetail.quantity }} {{ requestDetail.unit_display || requestDetail.unit || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>单价：</label>
              <span>{{ requestDetail.budget_unit_price || requestDetail.unit_price }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>总金额：</label>
              <span class="amount">{{ requestDetail.budget_total_amount || requestDetail.amount }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item status-item">
              <label>状态：</label>
              <div class="status-container">
                <a-tag :color="getStatusColor(requestDetail.status)">
                  {{ requestDetail.status_display || requestDetail.status || '-' }}
                </a-tag>
                <!-- 重新验收标记 -->
                <a-tag v-if="requestDetail.re_acceptance_required" color="red" class="re-acceptance-tag">
                  <ExclamationCircleOutlined />
                  需要重新验收
                </a-tag>
                <!-- 重新验收次数显示 -->
                <a-tag v-if="requestDetail.re_acceptance_count > 0" color="orange" class="re-acceptance-count">
                  重新验收 {{ requestDetail.re_acceptance_count }} 次
                </a-tag>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>需求单位：</label>
              <span>{{ requestDetail.hierarchy_path || '未知单位' }}</span>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <label>采购方式：</label>
              <span>{{ requestDetail.procurement_method_display || requestDetail.procurement_method || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>需求来源：</label>
              <span>{{ requestDetail.requirement_source || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>经费项目：</label>
              <span>{{ requestDetail.fund_project_display || requestDetail.fund_project || '-' }}</span>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <label>采购类型：</label>
              <span>{{ requestDetail.purchase_type_display || requestDetail.purchase_type || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="24">
            <div class="info-item">
              <label>备注：</label>
              <div class="description">{{ requestDetail.remarks || '无' }}</div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 申请信息 -->
      <div class="info-section business-card">
        <h3 class="section-title">申请信息</h3>
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <label>申请人：</label>
              <span>{{ requestDetail.requester_name || '未知' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(requestDetail.created_at) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>提交时间：</label>
              <span>{{ formatDate(requestDetail.submission_date) }}</span>
            </div>
          </a-col>
          <a-col :span="24">
            <div class="info-item">
              <label>需求说明：</label>
              <div class="description">{{ requestDetail.remarks || requestDetail.description || '' }}</div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 审批信息 -->
      <div v-if="requestDetail.status !== 'draft'" class="info-section business-card">
        <h3 class="section-title">审批信息</h3>
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <label>审批人：</label>
              <span>{{ requestDetail.approver_name || '未审批' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>审批时间：</label>
              <span>{{ formatDate(requestDetail.approved_at) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>审批结果：</label>
              <a-tag :color="getApprovalColor(requestDetail.status)">
                {{ getApprovalText(requestDetail.status) }}
              </a-tag>
            </div>
          </a-col>
          <a-col :span="24">
            <div class="info-item">
              <label>审批意见：</label>
              <div class="description">{{ requestDetail.approval_comment || '' }}</div>
            </div>
          </a-col>
          <!-- 驳回原因 -->
          <a-col v-if="requestDetail.rejection_reason" :span="24">
            <div class="info-item">
              <label>驳回原因：</label>
              <a-alert :message="requestDetail.rejection_reason" type="error" show-icon style="margin-top: 8px;" />
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 采购信息 -->
      <div
        v-if="['pending_purchase', 'purchased', 'returned', 'pending_acceptance', 'accepted', 'pending_reimbursement', 'reimbursed'].includes(requestDetail.status)"
        class="info-section business-card">
        <h3 class="section-title">采购信息</h3>
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <label>采购人：</label>
              <span>{{ requestDetail.purchaser_name || '未分配' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>采购时间：</label>
              <span>{{ formatDate(requestDetail.purchase_date) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>发货时间：</label>
              <span>{{ formatDate(requestDetail.shipping_date) }}</span>
            </div>
          </a-col>
          <!-- 退回原因 -->
          <a-col v-if="requestDetail.return_reason" :span="24">
            <div class="info-item">
              <label>退回原因：</label>
              <a-alert :message="requestDetail.return_reason" type="warning" show-icon style="margin-top: 8px;" />
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 验收信息 -->
      <div v-if="['accepted', 'pending_reimbursement', 'reimbursed'].includes(requestDetail.status)"
        class="info-section business-card">
        <h3 class="section-title">验收信息</h3>
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <label>验收人：</label>
              <span>{{ requestDetail.acceptor_name || '未验收' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>验收时间：</label>
              <span>{{ formatDate(requestDetail.acceptance_date) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>实际数量：</label>
              <span>{{ requestDetail.actual_quantity || requestDetail.quantity }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>快递公司：</label>
              <span>{{ requestDetail.courier_company || '无' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>快递单号：</label>
              <span>{{ requestDetail.tracking_number || '无' }}</span>
            </div>
          </a-col>
        </a-row>

        <!-- 财务退回信息 -->
        <div v-if="requestDetail.finance_return_reason" class="finance-return-info">
          <a-alert type="warning" show-icon :message="`财务退回原因：${requestDetail.finance_return_reason}`"
            :description="`退回人：${requestDetail.finance_returner_name || '无'} | 退回时间：${formatDate(requestDetail.finance_return_date)}`"
            style="margin-top: 16px;" />
        </div>
      </div>

      <!-- 验收照片 - 固定显示三张类型照片 -->
      <div
        v-if="['accepted', 'pending_reimbursement', 'settled'].includes(requestDetail.status)"
        class="info-section business-card">
        <h3 class="section-title">验收照片</h3>
        <div class="photos-grid-fixed">
          <!-- 正面照片 -->
          <div class="photo-item-fixed">
            <div class="photo-type-header">
              <span class="photo-type-label">正面照片</span>
              <span class="photo-type-desc">物品正面视角</span>
            </div>
            <div class="photo-container">
              <a-image
                v-if="getPhotoByType(requestDetail.acceptance_photos, 'front')"
                :src="getPhotoUrl(getPhotoByType(requestDetail.acceptance_photos, 'front').url)"
                :width="200"
                :height="150"
                :preview="true"
                class="photo-image"
              />
              <div v-else class="photo-placeholder">
                <FileImageOutlined class="placeholder-icon" />
                <span class="placeholder-text">暂无正面照片</span>
              </div>
            </div>
          </div>

          <!-- 侧面照片 -->
          <div class="photo-item-fixed">
            <div class="photo-type-header">
              <span class="photo-type-label">侧面照片</span>
              <span class="photo-type-desc">物品侧面视角</span>
            </div>
            <div class="photo-container">
              <a-image
                v-if="getPhotoByType(requestDetail.acceptance_photos, 'side')"
                :src="getPhotoUrl(getPhotoByType(requestDetail.acceptance_photos, 'side').url)"
                :width="200"
                :height="150"
                :preview="true"
                class="photo-image"
              />
              <div v-else class="photo-placeholder">
                <FileImageOutlined class="placeholder-icon" />
                <span class="placeholder-text">暂无侧面照片</span>
              </div>
            </div>
          </div>

          <!-- 整体照片 -->
          <div class="photo-item-fixed">
            <div class="photo-type-header">
              <span class="photo-type-label">整体照片</span>
              <span class="photo-type-desc">物品整体外观</span>
            </div>
            <div class="photo-container">
              <a-image
                v-if="getPhotoByType(requestDetail.acceptance_photos, 'overall')"
                :src="getPhotoUrl(getPhotoByType(requestDetail.acceptance_photos, 'overall').url)"
                :width="200"
                :height="150"
                :preview="true"
                class="photo-image"
              />
              <div v-else class="photo-placeholder">
                <FileImageOutlined class="placeholder-icon" />
                <span class="placeholder-text">暂无整体照片</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 照片统计信息 -->
        <div class="photo-summary">
          <span class="summary-text">
            已上传 {{ getUploadedPhotoCount(requestDetail.acceptance_photos) }}/3 张验收照片
          </span>
          <span v-if="getUploadedPhotoCount(requestDetail.acceptance_photos) < 3" class="summary-warning">
            （照片不完整）
          </span>
        </div>
      </div>

      <!-- 报销信息 -->
      <div v-if="['reimbursed'].includes(requestDetail.status)" class="info-section business-card">
        <h3 class="section-title">报销信息</h3>
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="info-item">
              <label>报销人：</label>
              <span>{{ requestDetail.reimburser_name || '未报销' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>报销时间：</label>
              <span>{{ formatDate(requestDetail.reimbursement_date) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <label>报销金额：</label>
              <span class="amount">¥{{ requestDetail.amount }}</span>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <a-result status="404" title="404" sub-title="抱歉，您访问的采购需求不存在。">
        <template #extra>
          <a-button type="primary" @click="goBack">返回</a-button>
        </template>
      </a-result>
    </div>

    <!-- 财务退回模态框 -->
    <a-modal v-model:visible="financeReturnModalVisible" title="财务退回重新验收" @ok="handleFinanceReturn"
      @cancel="financeReturnModalVisible = false" :confirm-loading="financeReturnLoading">
      <a-form :model="financeReturnForm" layout="vertical">
        <a-form-item label="退回原因" required>
          <a-textarea v-model:value="financeReturnForm.reason" placeholder="请详细说明验收照片不合格的原因，以便验收人员重新验收" :rows="4"
            :maxlength="500" show-count />
        </a-form-item>
        <a-alert type="warning" show-icon message="注意事项" description="财务退回后，该记录将重新进入待验收状态，验收人员需要重新上传合格的验收照片。"
          style="margin-top: 16px;" />
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  FileTextOutlined,
  FileImageOutlined,
  ArrowLeftOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import api from '@/api'
import { getPhotoUrl } from '@/utils/photo'
import { useDictMixin } from '@/mixins/dictMixin'

export default {
  name: 'PurchaseRequestDetail',
  components: {
    FileTextOutlined,
    FileImageOutlined,
    ArrowLeftOutlined,
    ExclamationCircleOutlined
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)
    const requestDetail = ref(null)

    // 使用字典混入获取格式化函数
    const { formatDateTimeDetail } = useDictMixin([])

    // 财务退回相关状态
    const financeReturnModalVisible = ref(false)
    const financeReturnLoading = ref(false)
    const financeReturnForm = ref({
      reason: ''
    })

    // 检查是否为新建模式
    const isNewMode = computed(() => route.params.id === 'new')

    // 获取详情数据
    const getRequestDetail = async () => {
      // 如果是新建模式，不需要获取详情数据
      if (isNewMode.value) {
        console.log('🆕 新建模式，跳过获取详情数据')
        loading.value = false
        return
      }

      loading.value = true
      try {
        const id = route.params.id
        console.log('📋 获取采购需求详情，ID:', id)
        const response = await api.purchaseRequests.getDetail(id)

        if (response.code === 200) {
          requestDetail.value = response.data
          console.log('✅ 获取详情成功:', response.data)
        } else {
          console.error('❌ 获取详情失败:', response)
          message.error('获取详情失败')
        }
      } catch (error) {
        console.error('❌ 获取详情异常:', error)
        message.error('获取详情失败')
      } finally {
        loading.value = false
      }
    }

    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }

    // 检查是否可以财务退回
    const canFinanceReturn = computed(() => {
      if (!requestDetail.value) return false
      // 只有待结算状态的记录才能财务退回
      return requestDetail.value.status === 'pending_reimbursement'
    })

    // 显示财务退回模态框
    const showFinanceReturnModal = () => {
      financeReturnForm.value.reason = ''
      financeReturnModalVisible.value = true
    }

    // 处理财务退回
    const handleFinanceReturn = async () => {
      if (!financeReturnForm.value.reason.trim()) {
        message.error('请填写退回原因')
        return
      }

      financeReturnLoading.value = true
      try {
        const response = await api.purchaseRequests.financeReturnToAcceptance(
          requestDetail.value.id,
          { reason: financeReturnForm.value.reason }
        )

        if (response.code === 200) {
          message.success('财务退回成功')
          financeReturnModalVisible.value = false
          // 重新获取详情数据
          await getRequestDetail()
        } else {
          message.error(response.message || '财务退回失败')
        }
      } catch (error) {
        console.error('财务退回失败:', error)
        message.error('财务退回失败')
      } finally {
        financeReturnLoading.value = false
      }
    }



    // 状态颜色映射
    const getStatusColor = (status) => {
      const colorMap = {
        'draft': 'default',
        'pending_approval': 'processing',
        'approved': 'success',
        'rejected': 'error',
        'pending_purchase': 'cyan',
        'purchased': 'purple',
        'pending_acceptance': 'orange',
        'accepted': 'green',
        'pending_reimbursement': 'gold',
        'settled': 'blue',
        // 兼容旧状态
        'pending': 'processing',
        'reimbursed': 'lime'
      }
      return colorMap[status] || 'default'
    }



    // 状态文本映射
    const getStatusText = (status) => {
      const statusMap = {
        'draft': '草稿',
        'pending_approval': '待审批',
        'approved': '已审批',
        'rejected': '已驳回',
        'pending_purchase': '待采购',
        'purchased': '已采购',
        'returned': '已退回',
        'pending_acceptance': '待验收',
        'accepted': '已验收',
        'pending_reimbursement': '待结算',
        'settled': '已结算'
      }
      return statusMap[status] || status
    }

    // 审批颜色映射
    const getApprovalColor = (status) => {
      if (status === 'rejected') return 'error'
      if (['approved', 'pending_purchase', 'purchased', 'pending_acceptance', 'accepted', 'pending_reimbursement', 'reimbursed'].includes(status)) return 'success'
      return 'default'
    }

    // 审批文本映射
    const getApprovalText = (status) => {
      if (status === 'rejected') return '已驳回'
      if (['approved', 'pending_purchase', 'purchased', 'pending_acceptance', 'accepted', 'pending_reimbursement', 'reimbursed'].includes(status)) return '已通过'
      return '未审批'
    }



    // 根据类型获取照片
    const getPhotoByType = (photos, type) => {
      if (!photos || !Array.isArray(photos)) return null

      // 按上传时间排序，获取该类型最新的照片
      const typePhotos = photos.filter(photo => {
        const photoType = photo.photo_type || photo.type || 'front'
        return photoType === type
      })

      if (typePhotos.length === 0) return null

      // 返回最新的照片
      return typePhotos.sort((a, b) => {
        const timeA = new Date(a.upload_time || a.created_at || 0).getTime()
        const timeB = new Date(b.upload_time || b.created_at || 0).getTime()
        return timeB - timeA
      })[0]
    }

    // 获取已上传照片数量
    const getUploadedPhotoCount = (photos) => {
      if (!photos || !Array.isArray(photos)) return 0

      const types = ['front', 'side', 'overall']
      let count = 0

      types.forEach(type => {
        if (getPhotoByType(photos, type)) {
          count++
        }
      })

      return count
    }

    // 获取最新的3张照片（正面、侧面、整体）- 保留原方法以防其他地方使用
    const getLatestPhotos = (photos) => {
      if (!photos || !Array.isArray(photos)) return []

      const result = []
      const types = ['front', 'side', 'overall']

      types.forEach(type => {
        const photo = getPhotoByType(photos, type)
        if (photo) {
          result.push(photo)
        }
      })

      return result
    }

    // 获取照片类型标签
    const getPhotoTypeLabel = (type) => {
      const typeLabels = {
        front: '正面照片',
        side: '侧面照片',
        overall: '整体照片'
      }
      return typeLabels[type] || '照片'
    }

    onMounted(async () => {
      // 获取详情数据
      await getRequestDetail()
    })

    return {
      loading,
      requestDetail,
      isNewMode,
      goBack,
      formatDate: formatDateTimeDetail,
      getStatusColor,
      getStatusText,
      getApprovalColor,
      getApprovalText,
      getPhotoUrl,
      getPhotoByType,
      getUploadedPhotoCount,
      getLatestPhotos,
      getPhotoTypeLabel,
      // 财务退回相关
      canFinanceReturn,
      financeReturnModalVisible,
      financeReturnLoading,
      financeReturnForm,
      showFinanceReturnModal,
      handleFinanceReturn
    }
  }
}
</script>

<style scoped>
.purchase-request-detail {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  margin-bottom: 24px;
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
  color: #ffffff;
}

.header-content p {
  margin: 8px 0 0 0;
  color: rgba(255, 255, 255, 0.9);
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  padding: 24px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.status-item {
  display: flex;
}

.info-item {
  margin-bottom: 16px;
}

.info-item label {
  display: inline-block;
  width: 120px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.info-item span {
  color: #1f2937;
}

.amount {
  font-size: 18px;
  font-weight: 600;
  color: #f59e0b;
}

.description {
  background: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  min-height: 60px;
  white-space: pre-wrap;
  word-break: break-word;
}

.secondary-action-btn {
  border: 2px solid #d1d5db;
  color: #6b7280;
  border-radius: 6px;
  font-weight: 500;
  height: 40px;
  padding: 0 16px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.secondary-action-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #eff6ff;
}

/* 照片网格样式 */
.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.photo-item {
  text-align: center;
}

.photo-caption {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

/* 固定三张照片样式 */
.photos-grid-fixed {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.photo-item-fixed {
  background: #fafafa;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.photo-item-fixed:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.photo-type-header {
  margin-bottom: 12px;
  text-align: center;
}

.photo-type-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.photo-type-desc {
  display: block;
  font-size: 12px;
  color: #8c8c8c;
}

.photo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}

.photo-image {
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  object-fit: cover;
}

.photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 150px;
  background: #f5f5f5;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  color: #bfbfbf;
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 12px;
  color: #8c8c8c;
}

.photo-summary {
  margin-top: 16px;
  padding: 12px 16px;
  background: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #d4edda;
  text-align: center;
}

.summary-text {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

.summary-warning {
  color: #ff4d4f;
  font-size: 12px;
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .photos-grid-fixed {
    grid-template-columns: 1fr;
  }

  .photo-container {
    min-height: 120px;
  }

  .photo-image {
    width: 160px !important;
    height: 120px !important;
  }

  .photo-placeholder {
    width: 160px;
    height: 120px;
  }
}

/* 移除响应式设计，保持固定布局 */

/* 财务退回相关样式 */
.status-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.re-acceptance-tag {
  animation: blink 1.5s infinite;
}

.re-acceptance-count {
  font-weight: bold;
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0.5;
  }
}

.finance-return-info {
  margin-top: 16px;
}

.action-btn {
  font-weight: 500;
}
</style>
