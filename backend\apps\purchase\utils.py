"""
采购管理工具函数
包含导入导出、数据处理等功能
"""
import pandas as pd
import xlsxwriter
from io import BytesIO
from django.http import HttpResponse
from django.utils import timezone
from decimal import Decimal
from .models import PurchaseRequest, Dictionary
from apps.system.models import Department


def get_dict_options(dict_type_code):
    """获取数据字典选项（返回中文名称列表）- 使用最新的字典服务"""
    try:
        from .services.dict_service import DictService
        items = DictService.get_dict_items(dict_type_code, enabled_only=True)
        return [item['name'] for item in items]
    except Exception as e:
        print(f"获取字典选项失败: {dict_type_code}, 错误: {e}")
        return []


def get_leaf_departments():
    """获取叶子节点部门（没有子部门的部门）"""
    try:
        # 获取所有启用的部门
        all_departments = Department.objects.filter(status=True)

        # 找出所有有子部门的部门ID
        parent_ids = set(all_departments.exclude(parent_id__isnull=True).values_list('parent_id', flat=True))

        # 获取叶子节点（不在parent_ids中的部门）
        leaf_departments = all_departments.exclude(id__in=parent_ids).order_by('hierarchy_path')

        return [dept.hierarchy_path for dept in leaf_departments]
    except Exception:
        return []


def generate_purchase_template():
    """生成采购需求导入模板（动态获取字典数据）"""
    # 动态获取下拉选项数据 - 使用正确的字典类型代码
    item_categories = get_dict_options('item_category')
    units = get_dict_options('unit')
    procurement_methods = get_dict_options('procurement_method')
    fund_projects = get_dict_options('fund_project')
    purchase_types = get_dict_options('purchase_type')
    leaf_departments = get_leaf_departments()

    # 如果字典数据为空，提供默认选项
    if not item_categories:
        item_categories = ['办公用品', '电子设备', '办公家具']
    if not units:
        units = ['个', '台', '套', '件', '箱']
    if not procurement_methods:
        procurement_methods = ['询价采购', '直接采购', '单一来源']
    if not fund_projects:
        fund_projects = ['运营经费', '专项经费']
    if not purchase_types:
        purchase_types = ['统一采购', '自行采购']

    # 创建Excel文件
    output = BytesIO()
    workbook = xlsxwriter.Workbook(output)

    # 创建工作表
    worksheet = workbook.add_worksheet('采购需求模板')

    # 定义样式
    header_format = workbook.add_format({
        'bold': True,
        'bg_color': '#4472C4',
        'font_color': 'white',
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })

    required_format = workbook.add_format({
        'bold': True,
        'bg_color': '#FFE6E6',
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })

    example_format = workbook.add_format({
        'bg_color': '#F0F8FF',
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })

    # 定义列标题（添加采购类型列）
    headers = [
        '物品种类*', '物品名称*', '规格型号*', '单位*', '采购数量*',
        '单价*', '需求单位*', '采购方式*', '采购类型*', '需求来源*', '经费项目名称*', '备注'
    ]
    
    # 写入标题行
    for col, header in enumerate(headers):
        if '*' in header:
            worksheet.write(0, col, header, required_format)
        else:
            worksheet.write(0, col, header, header_format)
    
    # 写入示例数据（使用实际的数据字典值）
    example_data = [
        [
            item_categories[0] if item_categories else '办公用品',
            '激光打印机',
            'HP LaserJet Pro M404n',
            units[0] if units else '台',
            2,
            1500.00,
            leaf_departments[0] if leaf_departments else '总部-行政部',
            procurement_methods[0] if procurement_methods else '直接采购',
            purchase_types[0] if purchase_types else '统一采购',
            '日常办公需求',  # 需求来源：手动输入文本
            fund_projects[0] if fund_projects else '办公设备采购',
            '用于日常办公打印'
        ],
        [
            item_categories[1] if len(item_categories) > 1 else '电子设备',
            '笔记本电脑',
            'ThinkPad E14',
            units[1] if len(units) > 1 else '台',
            5,
            4500.00,
            leaf_departments[1] if len(leaf_departments) > 1 else '总部-技术部',
            procurement_methods[1] if len(procurement_methods) > 1 else '询价采购',
            purchase_types[1] if len(purchase_types) > 1 else '自行采购',
            '项目开发需求',  # 需求来源：手动输入文本
            fund_projects[1] if len(fund_projects) > 1 else '研发设备采购',
            '开发人员工作用机'
        ]
    ]

    for row, data in enumerate(example_data, 1):
        for col, value in enumerate(data):
            worksheet.write(row, col, value, example_format)

    # 设置各列的数据验证（下拉选项）
    validations = [
        (0, item_categories, '物品种类'),  # 物品种类列
        (3, units, '计量单位'),  # 计量单位列
        (7, procurement_methods, '采购方式'),  # 采购方式列
        (8, purchase_types, '采购类型'),  # 采购类型列
        # 需求来源列(第9列)不设置下拉验证，允许手动输入
        (10, fund_projects, '经费项目名称'),  # 经费项目名称列
    ]

    for col_index, options, field_name in validations:
        if options:  # 只有当选项不为空时才设置验证
            worksheet.data_validation(1, col_index, 1000, col_index, {
                'validate': 'list',
                'source': options,
                'dropdown': True,
                'error_title': '输入错误',
                'error_message': f'请从下拉列表中选择{field_name}'
            })

    # 特殊处理需求单位列（因为选项太多，使用隐藏工作表）
    if leaf_departments:
        # 创建隐藏的数据工作表
        data_sheet = workbook.add_worksheet('数据源')
        data_sheet.hide()

        # 将部门数据写入隐藏工作表
        for i, dept in enumerate(leaf_departments):
            data_sheet.write(i, 0, dept)

        # 为需求单位列设置数据验证，引用隐藏工作表
        dept_range = f'数据源!$A$1:$A${len(leaf_departments)}'
        worksheet.data_validation(1, 6, 1000, 6, {  # 需求单位列索引为6
            'validate': 'list',
            'source': dept_range,
            'dropdown': True,
            'error_title': '输入错误',
            'error_message': '请从下拉列表中选择需求单位'
        })

    # 设置列宽（添加采购类型列的宽度）
    column_widths = [12, 15, 20, 8, 10, 10, 15, 12, 12, 12, 15, 20]
    for col, width in enumerate(column_widths):
        worksheet.set_column(col, col, width)
    
    # 添加说明工作表
    instruction_sheet = workbook.add_worksheet('填写说明')
    
    instructions = [
        ['字段名称', '是否必填', '说明', '示例'],
        ['物品种类', '是', '从下拉列表中选择（数据字典）', '、'.join(item_categories[:3]) if item_categories else '办公用品、电子设备'],
        ['物品名称', '是', '具体物品名称', '激光打印机、笔记本电脑'],
        ['规格型号', '是', '详细规格型号', 'HP LaserJet Pro M404n'],
        ['单位', '是', '从下拉列表中选择（数据字典）', '、'.join(units[:3]) if units else '台、个、箱'],
        ['采购数量', '是', '正整数', '1、2、5'],
        ['单价', '是', '单价金额，保留2位小数', '1500.00、4500.00'],
        ['需求单位', '是', '从下拉列表中选择（仅叶子节点）', '、'.join(leaf_departments[:2]) if leaf_departments else '总部-行政部'],
        ['采购方式', '是', '从下拉列表中选择（数据字典）', '、'.join(procurement_methods[:3]) if procurement_methods else '直接采购、公开招标'],
        ['采购类型', '是', '从下拉列表中选择', '统一采购、自行采购'],
        ['需求来源', '是', '手动输入需求来源', '日常办公需求、项目开发需求、设备维护需求等'],
        ['经费项目名称', '是', '从下拉列表中选择（数据字典）', '、'.join(fund_projects[:3]) if fund_projects else '运营经费'],
        ['备注', '否', '补充说明信息', '可选填写']
    ]
    
    for row, instruction in enumerate(instructions):
        for col, value in enumerate(instruction):
            if row == 0:
                instruction_sheet.write(row, col, value, header_format)
            else:
                instruction_sheet.write(row, col, value)
    
    # 设置说明表列宽
    instruction_sheet.set_column(0, 0, 15)
    instruction_sheet.set_column(1, 1, 10)
    instruction_sheet.set_column(2, 2, 30)
    instruction_sheet.set_column(3, 3, 20)
    
    workbook.close()
    output.seek(0)
    
    # 创建HTTP响应
    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="采购需求导入模板.xlsx"'
    
    return response


def import_purchase_requests(file, user, skip_errors=False):
    """导入采购需求数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file, sheet_name='采购需求模板')
        
        # 验证必填字段（添加采购类型）
        required_columns = [
            '物品种类*', '物品名称*', '规格型号*', '单位*', '采购数量*',
            '单价*', '需求单位*', '采购方式*', '采购类型*', '需求来源*', '经费项目名称*'
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return {
                'success': False,
                'message': f'缺少必填列: {", ".join(missing_columns)}'
            }
        
        # 数据验证和导入
        success_count = 0
        error_rows = []
        
        for index, row in df.iterrows():
            try:
                # 验证数据
                errors = validate_import_row(row, index + 2)  # +2因为有标题行
                if errors:
                    error_rows.append({
                        'row': index + 2,
                        'errors': errors
                    })
                    continue
                
                # 查找部门ID
                dept = Department.objects.filter(
                    hierarchy_path=row['需求单位*']
                ).first()
                
                if not dept:
                    error_rows.append({
                        'row': index + 2,
                        'errors': [f'部门不存在: {row["需求单位*"]}']
                    })
                    continue
                
                # 验证采购类型
                purchase_type_value = row['采购类型*']
                if purchase_type_value == '统一采购':
                    purchase_type = 'unified'
                elif purchase_type_value == '自行采购':
                    purchase_type = 'self'
                else:
                    error_rows.append({
                        'row': index + 2,
                        'errors': [f'采购类型值无效: {purchase_type_value}，请选择"统一采购"或"自行采购"']
                    })
                    continue

                # 创建采购需求
                purchase_request = PurchaseRequest.objects.create(
                    item_category=row['物品种类*'],
                    item_name=row['物品名称*'],
                    specification=row['规格型号*'],
                    unit=row['单位*'],
                    budget_quantity=int(row['采购数量*']),
                    budget_unit_price=Decimal(str(row['单价*'])),
                    dept_id=dept.id,
                    hierarchy_path=row['需求单位*'],
                    procurement_method=row['采购方式*'],
                    purchase_type=purchase_type,
                    requirement_source=row['需求来源*'],
                    fund_project=row['经费项目名称*'],
                    remarks=row.get('备注', ''),
                    requester=user,
                    status='draft'
                )
                
                success_count += 1
                
            except Exception as e:
                error_rows.append({
                    'row': index + 2,
                    'errors': [f'导入失败: {str(e)}']
                })
                if not skip_errors:
                    # 如果不跳过错误，则停止导入
                    break
        
        return {
            'success': True,
            'message': f'成功导入 {success_count} 条记录',
            'success_count': success_count,
            'error_count': len(error_rows),
            'errors': error_rows
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'文件处理失败: {str(e)}'
        }


def validate_import_row(row, row_number):
    """验证导入行数据"""
    errors = []
    
    # 验证必填字段（添加采购类型）
    required_fields = {
        '物品种类*': '物品种类',
        '物品名称*': '物品名称',
        '规格型号*': '规格型号',
        '单位*': '单位',
        '采购数量*': '采购数量',
        '单价*': '单价',
        '需求单位*': '需求单位',
        '采购方式*': '采购方式',
        '采购类型*': '采购类型',
        '需求来源*': '需求来源',
        '经费项目名称*': '经费项目名称'
    }
    
    for field, name in required_fields.items():
        if pd.isna(row[field]) or str(row[field]).strip() == '':
            errors.append(f'{name}不能为空')
    
    # 验证数量
    try:
        quantity = int(row['采购数量*'])
        if quantity <= 0:
            errors.append('采购数量必须大于0')
    except (ValueError, TypeError):
        errors.append('采购数量必须是正整数')
    
    # 验证单价
    try:
        price = float(row['单价*'])
        if price <= 0:
            errors.append('单价必须大于0')
    except (ValueError, TypeError):
        errors.append('单价必须是有效数字')

    # 验证下拉选项字段
    dropdown_validations = [
        ('物品种类*', '物品种类', get_dict_options('item_category')),
        ('单位*', '单位', get_dict_options('unit')),
        ('需求单位*', '需求单位', get_leaf_departments()),
        ('采购方式*', '采购方式', get_dict_options('procurement_method')),
        ('采购类型*', '采购类型', get_dict_options('purchase_type')),
        ('经费项目名称*', '经费项目名称', get_dict_options('fund_project')),
    ]

    for field_key, field_name, valid_options in dropdown_validations:
        if not pd.isna(row[field_key]) and str(row[field_key]).strip() != '':
            field_value = str(row[field_key]).strip()
            if valid_options and field_value not in valid_options:
                errors.append(f'{field_name}值无效: {field_value}，请从下拉列表中选择')

    return errors


def export_purchase_requests(queryset, selected_fields=None):
    """导出采购需求数据"""
    output = BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('采购需求清单')

    # 定义样式
    header_format = workbook.add_format({
        'bold': True,
        'bg_color': '#4472C4',
        'font_color': 'white',
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })

    data_format = workbook.add_format({
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })

    # 定义所有可用的字段映射
    field_mapping = {
        'id': '需求编号',
        'item_category': '物品种类',
        'item_name': '物品名称',
        'specification': '规格型号',
        'unit': '单位',
        'quantity': '采购数量',
        'budget_quantity': '预算数量',
        'unit_price': '单价',
        'amount': '金额',
        'budget_total_amount': '预算总金额',
        'budget_unit_price': '预算单价',
        'purchase_quantity': '采购数量',
        'purchase_unit_price': '采购单价',
        'purchase_total_amount': '采购总金额',
        'supplier_name': '供应商名称',
        'purchase_remarks': '采购备注',
        'department_name': '需求单位',
        'hierarchy_path': '需求单位',
        'dept_name': '需求单位',
        'purchase_type': '采购类型',
        'procurement_method': '采购方式',
        'requirement_source': '需求来源',
        'fund_project': '经费项目名称',
        'status': '状态',
        'requester': '申请人',
        'requester_name': '申请人姓名',
        'approver': '审批人',
        'approver_name': '审批人姓名',
        'purchaser': '采购人',
        'purchaser_name': '采购员姓名',
        'acceptor': '验收人',
        'acceptor_name': '验收人姓名',
        'reimburser': '报销人',
        'reimburser_name': '报销人姓名',
        'created_at': '创建时间',
        'submission_date': '提交日期',
        'approved_at': '审批时间',
        'approval_date': '审批日期',
        'approval_comment': '审批意见',
        'rejection_reason': '拒绝原因',
        'purchase_date': '采购日期',
        'acceptance_date': '验收日期',
        'acceptance_quantity': '验收数量',
        'reimbursement_date': '报销日期',
        'settlement_amount': '结算金额',
        'reimbursement_voucher_no': '报销凭证号',
        'courier_company': '快递公司',
        'tracking_number': '快递单号',
        'shipping_origin': '发货地',
        'acceptance_remarks': '验收备注',
        'settlement_remarks': '结算备注',
        'payee_name': '收款人户名',
        'payee_account': '收款人账号',
        'remarks': '备注',
        'dept_id': '申请部门ID',
        'transaction_number': '交易流水号'
    }

    # 如果没有指定字段，使用默认字段
    if not selected_fields:
        selected_fields = [
            'id', 'item_category', 'item_name', 'specification', 'unit', 'quantity',
            'unit_price', 'amount', 'department_name', 'purchase_type', 'requirement_source',
            'fund_project', 'status', 'requester', 'submission_date', 'approver', 'approval_date', 'remarks'
        ]

    # 构建标题行
    headers = [field_mapping.get(field, field) for field in selected_fields]

    # 写入标题行
    for col, header in enumerate(headers):
        worksheet.write(0, col, header, header_format)

    # 定义字段值获取函数
    def get_field_value(request, field):
        """根据字段名获取对应的值"""
        try:
            if field == 'id':
                return request.id
            elif field == 'item_category':
                from apps.purchase.services.dict_service import get_item_category_text
                return get_item_category_text(request.item_category)
            elif field == 'item_name':
                return request.item_name
            elif field == 'specification':
                return request.specification or request.spec or ''
            elif field == 'unit':
                from apps.purchase.services.dict_service import get_unit_text
                return get_unit_text(request.unit)
            elif field == 'quantity':
                return request.quantity
            elif field == 'budget_quantity':
                return request.budget_quantity
            elif field == 'unit_price':
                return float(request.budget_unit_price) if request.budget_unit_price else 0
            elif field == 'amount':
                return float(request.budget_total_amount) if request.budget_total_amount else 0
            elif field == 'budget_total_amount':
                return float(request.budget_total_amount) if request.budget_total_amount else 0
            elif field == 'budget_unit_price':
                return float(request.budget_unit_price) if request.budget_unit_price else 0
            elif field == 'purchase_quantity':
                return request.purchase_quantity or ''
            elif field == 'purchase_unit_price':
                return float(request.purchase_unit_price) if request.purchase_unit_price else 0
            elif field == 'purchase_total_amount':
                return float(request.purchase_total_amount) if request.purchase_total_amount else 0
            elif field == 'supplier_name':
                return request.supplier_name or ''
            elif field == 'purchase_remarks':
                return request.purchase_remarks or ''
            elif field == 'department_name':
                return request.hierarchy_path
            elif field == 'hierarchy_path':
                return request.hierarchy_path
            elif field == 'purchase_type':
                from apps.purchase.services.dict_service import get_purchase_type_text
                return get_purchase_type_text(request.purchase_type)
            elif field == 'procurement_method':
                from apps.purchase.services.dict_service import get_procurement_method_text
                return get_procurement_method_text(request.procurement_method)
            elif field == 'requirement_source':
                # requirement_source是用户输入字段，不需要字典转换
                return request.requirement_source or ''
            elif field == 'fund_project':
                from apps.purchase.services.dict_service import get_fund_project_text
                return get_fund_project_text(request.fund_project)
            elif field == 'status':
                from apps.purchase.services.dict_service import get_status_text
                return get_status_text(request.status)
            elif field == 'requester':
                return request.requester.real_name if request.requester else ''
            elif field == 'requester_name':
                return request.requester.real_name if request.requester else ''
            elif field == 'created_at':
                return request.created_at.strftime('%Y/%m/%d') if request.created_at else ''
            elif field == 'submission_date':
                return request.submission_date.strftime('%Y/%m/%d') if request.submission_date else ''
            elif field == 'approver':
                return request.approver.real_name if request.approver else ''
            elif field == 'approver_name':
                return request.approver.real_name if request.approver else ''
            elif field == 'approved_at':
                return request.approved_at.strftime('%Y/%m/%d') if request.approved_at else ''
            elif field == 'approval_date':
                return request.approved_at.strftime('%Y/%m/%d') if request.approved_at else ''
            elif field == 'approval_comment':
                return request.approval_comment or ''
            elif field == 'rejection_reason':
                return request.rejection_reason or ''
            elif field == 'purchaser':
                return request.purchaser.real_name if request.purchaser else ''
            elif field == 'purchaser_name':
                return request.purchaser.real_name if request.purchaser else ''
            elif field == 'purchase_date':
                return request.purchase_date.strftime('%Y/%m/%d') if request.purchase_date else ''
            elif field == 'acceptor':
                return request.acceptor.real_name if request.acceptor else ''
            elif field == 'acceptor_name':
                return request.acceptor.real_name if request.acceptor else ''
            elif field == 'acceptance_date':
                return request.acceptance_date.strftime('%Y/%m/%d') if request.acceptance_date else ''
            elif field == 'acceptance_quantity':
                return request.acceptance_quantity or ''
            elif field == 'courier_company':
                return request.courier_company or ''
            elif field == 'tracking_number':
                return request.tracking_number or ''
            elif field == 'shipping_origin':
                return request.shipping_origin or ''
            elif field == 'acceptance_remarks':
                return request.acceptance_remarks or ''
            elif field == 'reimburser':
                return request.reimburser.real_name if request.reimburser else ''
            elif field == 'reimburser_name':
                return request.reimburser.real_name if request.reimburser else ''
            elif field == 'reimbursement_date':
                return request.reimbursement_date.strftime('%Y/%m/%d') if request.reimbursement_date else ''
            elif field == 'settlement_amount':
                return float(request.settlement_amount) if request.settlement_amount else 0
            elif field == 'reimbursement_voucher_no':
                return request.reimbursement_voucher_no or ''
            elif field == 'transaction_number':
                return request.transaction_number or ''
            elif field == 'settlement_remarks':
                return request.settlement_remarks or ''
            elif field == 'payee_name':
                return request.payee_name or ''
            elif field == 'payee_account':
                return request.payee_account or ''
            elif field == 'dept_id':
                return request.dept_id or ''
            elif field == 'remarks':
                return request.remarks or ''
            else:
                return ''
        except Exception:
            return ''

    # 写入数据
    for row, request in enumerate(queryset, 1):
        for col, field in enumerate(selected_fields):
            value = get_field_value(request, field)
            worksheet.write(row, col, value, data_format)

    # 设置列宽（根据字段数量动态调整）
    default_width = 15
    for col in range(len(selected_fields)):
        worksheet.set_column(col, col, default_width)
    
    workbook.close()
    output.seek(0)
    
    # 创建HTTP响应
    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="采购需求清单_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
    
    return response
