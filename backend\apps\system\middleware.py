"""
系统中间件
用于自动记录API请求日志和安全事件
"""
import time
import json
import logging
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from django.contrib.auth import get_user_model
from .logging_service import LoggingService

User = get_user_model()
logger = logging.getLogger(__name__)


class APILoggingMiddleware(MiddlewareMixin):
    """API请求日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """处理请求开始"""
        request.start_time = time.time()
        return None
    
    def process_response(self, request, response):
        """处理响应结束"""
        # 只记录API请求（以/api/开头的路径）
        if not request.path.startswith('/api/'):
            return response
        
        # 计算响应时间
        response_time = None
        if hasattr(request, 'start_time'):
            response_time = int((time.time() - request.start_time) * 1000)  # 毫秒
        
        # 获取用户信息
        user = request.user if request.user.is_authenticated else None
        
        # 获取IP地址
        ip_address = self.get_client_ip(request)
        
        # 获取用户代理
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # 获取请求数据
        request_data = self.get_request_data(request)
        
        # 获取响应数据（仅在调试模式下记录）
        response_data = None
        if settings.DEBUG and response.status_code < 400:
            response_data = self.get_response_data(response)
        
        # 记录API请求日志
        try:
            LoggingService.log_api_request(
                user=user,
                method=request.method,
                url=request.path,
                request_data=request_data,
                response_data=response_data,
                status_code=response.status_code,
                response_time=response_time,
                ip_address=ip_address,
                user_agent=user_agent
            )
        except Exception as e:
            logger.error(f"记录API请求日志失败: {e}")
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def get_request_data(self, request):
        """获取请求数据"""
        try:
            if request.method in ['POST', 'PUT', 'PATCH']:
                if request.content_type == 'application/json':
                    return json.loads(request.body.decode('utf-8'))
                else:
                    # 过滤敏感字段
                    data = dict(request.POST)
                    return self.filter_sensitive_data(data)
            elif request.method == 'GET':
                return dict(request.GET)
        except Exception:
            return {}
        return {}
    
    def get_response_data(self, response):
        """获取响应数据"""
        try:
            if response.get('Content-Type', '').startswith('application/json'):
                return json.loads(response.content.decode('utf-8'))
        except Exception:
            pass
        return None
    
    def filter_sensitive_data(self, data):
        """过滤敏感数据"""
        sensitive_fields = ['password', 'token', 'secret', 'key', 'auth']
        filtered_data = {}
        
        for key, value in data.items():
            if any(field in key.lower() for field in sensitive_fields):
                filtered_data[key] = '***'
            else:
                filtered_data[key] = value
        
        return filtered_data


class SecurityLoggingMiddleware(MiddlewareMixin):
    """安全事件日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_response(self, request, response):
        """处理响应"""
        # 记录权限拒绝事件
        if response.status_code == 403:
            user = request.user if request.user.is_authenticated else None
            ip_address = self.get_client_ip(request)
            
            try:
                LoggingService.log_security_event(
                    user=user,
                    event_type='permission_denied',
                    event_description=f'访问 {request.path} 被拒绝',
                    ip_address=ip_address,
                    risk_level='HIGH'
                )
            except Exception as e:
                logger.error(f"记录权限拒绝日志失败: {e}")
        
        # 记录认证失败事件
        elif response.status_code == 401:
            ip_address = self.get_client_ip(request)
            
            try:
                LoggingService.log_security_event(
                    user=None,
                    event_type='authentication_failed',
                    event_description=f'访问 {request.path} 认证失败',
                    ip_address=ip_address,
                    risk_level='MEDIUM'
                )
            except Exception as e:
                logger.error(f"记录认证失败日志失败: {e}")
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ErrorLoggingMiddleware(MiddlewareMixin):
    """错误日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_exception(self, request, exception):
        """处理异常"""
        try:
            user = request.user if request.user.is_authenticated else None
            ip_address = self.get_client_ip(request)
            
            error_details = {
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'request_path': request.path,
                'request_method': request.method,
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'ip_address': ip_address
            }
            
            LoggingService.log_system_event(
                event_type='system_error',
                event_description=f'系统异常: {type(exception).__name__}',
                level='ERROR',
                component=request.path,
                error_details=error_details
            )
            
        except Exception as e:
            logger.error(f"记录系统异常日志失败: {e}")
        
        return None  # 继续正常的异常处理流程
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PerformanceLoggingMiddleware(MiddlewareMixin):
    """性能监控日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.slow_request_threshold = 2000  # 慢请求阈值（毫秒）
        super().__init__(get_response)
    
    def process_request(self, request):
        """处理请求开始"""
        request.start_time = time.time()
        return None
    
    def process_response(self, request, response):
        """处理响应结束"""
        if not hasattr(request, 'start_time'):
            return response
        
        response_time = int((time.time() - request.start_time) * 1000)  # 毫秒
        
        # 记录慢请求
        if response_time > self.slow_request_threshold:
            try:
                user = request.user if request.user.is_authenticated else None
                
                LoggingService.log_system_event(
                    event_type='slow_request',
                    event_description=f'慢请求: {request.method} {request.path}',
                    level='WARNING',
                    component='PerformanceMonitor',
                    extra_data={
                        'response_time': response_time,
                        'threshold': self.slow_request_threshold,
                        'user': user.username if user else 'anonymous',
                        'status_code': response.status_code
                    }
                )
            except Exception as e:
                logger.error(f"记录慢请求日志失败: {e}")
        
        return response


class UserActivityMiddleware:
    """用户活动跟踪中间件"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 处理请求前
        self.process_request(request)

        response = self.get_response(request)

        # 处理响应后
        self.process_response(request, response)

        return response

    def process_request(self, request):
        """处理请求，更新用户活动 - 现在使用SystemLog记录"""
        if request.user.is_authenticated:
            try:
                # 更新用户最后登录时间（Django内置功能）
                # 用户活动现在通过SystemLog记录，不需要额外的UserSession表
                pass
            except Exception as e:
                logger.error(f"处理用户活动失败: {e}")

    def process_response(self, request, response):
        """处理响应"""
        return response

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
