#!/usr/bin/env python
"""
开发服务器启动脚本
功能：
1. Django服务热启动（自动重载）
2. 自动清除前后端缓存
3. 监听文件变化
4. 提供开发环境优化配置
"""
import os
import sys
import shutil
import subprocess
from pathlib import Path


def clear_django_cache():
    """清除Django缓存"""
    print("🧹 清除Django缓存...")
    
    # 清除__pycache__目录
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                cache_path = os.path.join(root, dir_name)
                try:
                    shutil.rmtree(cache_path)
                    print(f"   ✓ 删除缓存目录: {cache_path}")
                except Exception as e:
                    print(f"   ✗ 删除缓存目录失败: {cache_path}, 错误: {e}")
    
    # 清除.pyc文件
    for root, dirs, files in os.walk('.'):
        for file_name in files:
            if file_name.endswith('.pyc'):
                pyc_path = os.path.join(root, file_name)
                try:
                    os.remove(pyc_path)
                    print(f"   ✓ 删除缓存文件: {pyc_path}")
                except Exception as e:
                    print(f"   ✗ 删除缓存文件失败: {pyc_path}, 错误: {e}")


def clear_frontend_cache():
    """清除前端缓存"""
    print("🧹 清除前端缓存...")
    
    frontend_path = Path('../frontend')
    if frontend_path.exists():
        # 清除node_modules/.cache
        cache_dirs = [
            frontend_path / 'node_modules' / '.cache',
            frontend_path / 'dist',
            frontend_path / '.nuxt',
            frontend_path / '.next'
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                try:
                    shutil.rmtree(cache_dir)
                    print(f"   ✓ 删除前端缓存目录: {cache_dir}")
                except Exception as e:
                    print(f"   ✗ 删除前端缓存目录失败: {cache_dir}, 错误: {e}")
    else:
        print("   ⚠️  前端目录不存在，跳过前端缓存清除")


def setup_development_environment():
    """设置开发环境"""
    print("⚙️  设置开发环境...")
    
    # 设置环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'purchase_system.settings')
    os.environ['DEBUG'] = 'True'
    os.environ['DJANGO_DEVELOPMENT'] = 'True'
    
    # 确保在开发模式下
    print("   ✓ 设置DEBUG=True")
    print("   ✓ 启用开发模式")


def run_migrations():
    """运行数据库迁移"""
    print("🗄️  检查数据库迁移...")
    try:
        from django.core.management import execute_from_command_line
        execute_from_command_line(['manage.py', 'migrate', '--check'])
        print("   ✓ 数据库迁移已是最新")
    except SystemExit as e:
        if e.code != 0:
            print("   ⚠️  发现未应用的迁移，正在应用...")
            execute_from_command_line(['manage.py', 'migrate'])
            print("   ✓ 数据库迁移完成")


def start_development_server():
    """启动开发服务器"""
    print("🚀 启动Django开发服务器...")
    print("   📍 服务地址: http://127.0.0.1:8000")
    print("   🔄 热重载已启用")
    print("   📝 日志级别: DEBUG")
    print("   ⏹️  按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        from django.core.management import execute_from_command_line
        # 使用runserver命令启动，自带热重载功能
        execute_from_command_line([
            'manage.py', 
            'runserver', 
            '127.0.0.1:8000',
            '--verbosity=2'  # 详细输出
        ])
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")


def main():
    """主函数"""
    print("=" * 50)
    print("🔧 Django开发服务器启动器")
    print("=" * 50)
    
    # 1. 设置开发环境
    setup_development_environment()
    
    # 2. 清除缓存
    clear_django_cache()
    clear_frontend_cache()
    
    # 3. 检查并运行迁移
    run_migrations()
    
    # 4. 启动开发服务器
    start_development_server()


if __name__ == '__main__':
    main()
