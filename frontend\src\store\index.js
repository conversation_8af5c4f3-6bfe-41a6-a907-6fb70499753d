/**
 * 采购管理系统 - Vuex状态管理
 *
 * 功能说明：
 * 1. 管理应用的全局状态和数据流
 * 2. 提供统一的数据获取和更新接口
 * 3. 实现组件间的状态共享和通信
 * 4. 集成API调用和错误处理
 *
 * 技术特点：
 * - 基于Vuex 4的状态管理模式
 * - 模块化的状态组织结构
 * - 异步操作和副作用处理
 * - 开发工具集成和调试支持
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-08
 */
import { createStore } from 'vuex'
import api from '@/api'
import dictModule from './modules/dict'
import permissionModule from './modules/permission'

// 创建Vuex store实例
export default createStore({
  modules: {
    // 其他模块...
    dicts: dictModule,
    permission: permissionModule
  },
  state: {
    user: null,
    token: localStorage.getItem('token') || null,
    permissions: [],
    dictData: {},
    departments: [],
    roles: [],
    // 新增菜单和权限状态
    userMenus: [],
    userPermissions: [],
    menuTree: [],
    menuLoading: false
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user;
    },
    SET_TOKEN(state, token) {
      state.token = token;
    },
    SET_PERMISSIONS(state, permissions) {
      state.permissions = permissions;
    },
    SET_DICT_DATA(state, dictData) {
      state.dictData = dictData;
    },
    SET_DEPARTMENTS(state, departments) {
      state.departments = departments;
    },
    SET_ROLES(state, roles) {
      state.roles = roles;
    },
    // 新增菜单和权限相关mutations
    SET_USER_MENUS(state, menus) {
      state.userMenus = menus;
    },
    SET_USER_PERMISSIONS(state, permissions) {
      state.userPermissions = permissions;
    },
    SET_MENU_TREE(state, tree) {
      state.menuTree = tree;
    },
    SET_MENU_LOADING(state, loading) {
      state.menuLoading = loading;
    }
  },
  actions: {
    // 登录（优化版 - 添加预热机制）
    async login({ commit, dispatch }, userData) {
      try {
        const response = await api.auth.login(userData.username, userData.password);

        if (response && response.code === 200) {
          const { token, user } = response.data;
          localStorage.setItem('token', token);
          commit('SET_USER', user);

          // 登录成功后立即开始预热（不阻塞登录流程）
          setTimeout(() => {
            dispatch('preloadUserData').catch(() => {
              // 用户数据预加载失败：静默处理
            })
          }, 100)

          return Promise.resolve(user);
        }

        return Promise.reject(new Error('登录失败'));
      } catch (error) {
        // 登录失败：返回错误
        return Promise.reject(error);
      }
    },

    // 预加载用户数据（登录后预热）
    async preloadUserData({ dispatch }) {
      // 开始预热用户数据

      try {
        // 并行预加载关键数据
        const preloadTasks = [
          dispatch('getUserMenus'),           // 预加载菜单
          dispatch('getUserPermissions'),     // 预加载权限
          api.dicts.getDict('purchase_type'), // 预加载常用字典
          api.dicts.getDict('item_category'),
          api.departments.getList()           // 预加载部门数据
        ]

        await Promise.allSettled(preloadTasks)
        // 用户数据预热完成
      } catch (error) {
        // 用户数据预热部分失败：静默处理
      }
    },
    // 获取当前用户信息
    async getCurrentUser({ commit, dispatch }) {
      try {
        const response = await api.auth.getCurrentUser();

        if (response && response.code === 200) {
          commit('SET_USER', response.data);

          // 获取用户权限
          try {
            await dispatch('getUserPermissions');
          } catch (error) {
            // 获取用户权限失败：静默处理
          }

          return Promise.resolve(response.data);
        }

        return Promise.reject(new Error('获取用户信息失败'));
      } catch (error) {
        // 获取用户信息失败：返回错误
        return Promise.reject(error);
      }
    },
    // 获取数据字典
    async getDictData({ commit }) {
      try {
        const response = await api.dicts.getAllDicts();
        
        if (response && response.code === 200) {
          commit('SET_DICT_DATA', response.data);
          return Promise.resolve(response.data);
        }
        
        return Promise.reject(new Error('获取数据字典失败'));
      } catch (error) {
        // 获取数据字典失败：返回错误
        return Promise.reject(error);
      }
    },
    // 获取部门列表
    async getDepartments({ commit }) {
      try {
        const response = await api.departments.getList();
        
        if (response && response.code === 200) {
          commit('SET_DEPARTMENTS', response.data.list);
          return Promise.resolve(response.data.list);
        }
        
        return Promise.reject(new Error('获取部门列表失败'));
      } catch (error) {
        console.error('获取部门列表失败:', error);
        return Promise.reject(error);
      }
    },
    // 获取角色列表
    async getRoles({ commit }) {
      try {
        const response = await api.roles.getList();

        if (response && response.code === 200) {
          commit('SET_ROLES', response.data.list);
          return Promise.resolve(response.data.list);
        }

        return Promise.reject(new Error('获取角色列表失败'));
      } catch (error) {
        console.error('获取角色列表失败:', error);
        return Promise.reject(error);
      }
    },
    // 更新用户信息
    updateUserInfo({ commit }, userInfo) {
      commit('SET_USER', userInfo);
    },
    // 登出
    logout({ commit, dispatch }) {
      localStorage.removeItem('token');
      commit('SET_USER', null);
      commit('SET_TOKEN', null);
      commit('SET_PERMISSIONS', []);
      commit('SET_USER_MENUS', []);
      commit('SET_USER_PERMISSIONS', []);
      commit('SET_MENU_TREE', []);

      // 清除菜单缓存和本地存储
      dispatch('clearUserMenus');

      // 清除权限模块的持久化数据
      dispatch('permission/clearRoutes', null, { root: true });
    },

    // 获取用户菜单（支持缓存和持久化）
    async getUserMenus({ commit, state }) {
      // 检查内存缓存
      if (state.userMenus && state.userMenus.length > 0) {
        console.log('📦 使用内存缓存的菜单数据')
        return Promise.resolve(state.userMenus)
      }

      // 检查本地存储
      try {
        const storedMenus = localStorage.getItem('user_menus')
        if (storedMenus) {
          const menus = JSON.parse(storedMenus)
          if (menus && menus.length > 0) {
            console.log('📦 从本地存储恢复菜单数据:', menus.length, '个菜单')
            commit('SET_USER_MENUS', menus)
            return Promise.resolve(menus)
          }
        }
      } catch (error) {
        console.warn('⚠️ 本地存储菜单数据解析失败:', error)
        localStorage.removeItem('user_menus')
      }

      commit('SET_MENU_LOADING', true);
      try {
        const response = await api.menus.getUserMenus();

        if (response && response.code === 200) {
          const menus = response.data || []
          commit('SET_USER_MENUS', menus);

          // 保存到本地存储
          try {
            localStorage.setItem('user_menus', JSON.stringify(menus))
          } catch (error) {
            console.warn('保存菜单数据到本地存储失败:', error)
          }

          return Promise.resolve(menus);
        }

        return Promise.reject(new Error('获取用户菜单失败'));
      } catch (error) {
        console.error('获取用户菜单失败:', error);
        return Promise.reject(error);
      } finally {
        commit('SET_MENU_LOADING', false);
      }
    },

    // 清除菜单缓存
    clearUserMenus({ commit }) {
      commit('SET_USER_MENUS', [])
      // 清除本地存储
      try {
        localStorage.removeItem('user_menus')
        console.log('🗑️ 菜单缓存和本地存储已清除')
      } catch (error) {
        console.warn('⚠️ 清除本地存储失败:', error)
      }
    },

    // 刷新用户菜单
    async refreshUserMenus({ commit }) {
      commit('SET_MENU_LOADING', true);
      try {
        console.log('🔄 刷新用户菜单...')
        const response = await api.menus.getUserMenus();

        if (response && response.code === 200) {
          const menus = response.data || []
          commit('SET_USER_MENUS', menus);
          console.log('✅ 菜单数据刷新成功:', menus.length, '个菜单')
          return Promise.resolve(menus);
        }

        return Promise.reject(new Error('刷新菜单失败'));
      } catch (error) {
        console.error('❌ 刷新用户菜单失败:', error);
        return Promise.reject(error);
      } finally {
        commit('SET_MENU_LOADING', false);
      }
    },

    // 获取用户权限（优化版）
    async getUserPermissions({ commit, state }) {
      try {
        // 检查缓存
        if (state.userPermissions && state.userPermissions.length > 0) {
          console.log('🔑 使用缓存的用户权限');
          return Promise.resolve(state.userPermissions);
        }

        console.log('🔑 开始获取用户权限...');
        const response = await api.menus.getUserPermissions();
        console.log('🔑 权限API响应:', response);

        if (response && response.code === 200) {
          const permissions = response.data || [];
          console.log('🔑 用户权限列表:', permissions);

          // 存储到Vuex
          commit('SET_USER_PERMISSIONS', permissions);

          // 存储到localStorage（持久化）
          try {
            localStorage.setItem('user_permissions', JSON.stringify(permissions));
            localStorage.setItem('user_permissions_time', Date.now().toString());
          } catch (error) {
            console.warn('保存权限数据到本地存储失败:', error);
          }

          return Promise.resolve(permissions);
        }

        console.error('❌ 获取用户权限失败:', response);
        return Promise.reject(new Error('获取用户权限失败'));
      } catch (error) {
        console.error('❌ 获取用户权限异常:', error);
        return Promise.reject(error);
      }
    },

    // 从本地存储恢复权限数据
    restoreUserPermissions({ commit }) {
      try {
        const cachedPermissions = localStorage.getItem('user_permissions');
        const cacheTime = localStorage.getItem('user_permissions_time');

        if (cachedPermissions && cacheTime) {
          const timeDiff = Date.now() - parseInt(cacheTime);
          // 如果缓存时间小于30分钟，使用缓存
          if (timeDiff < 30 * 60 * 1000) {
            const permissions = JSON.parse(cachedPermissions);
            console.log('🔑 从本地存储恢复权限数据:', permissions);
            commit('SET_USER_PERMISSIONS', permissions);
            return permissions;
          }
        }
      } catch (error) {
        console.warn('恢复权限数据失败:', error);
        localStorage.removeItem('user_permissions');
        localStorage.removeItem('user_permissions_time');
      }
      return null;
    },

    // 清除权限缓存
    clearUserPermissions({ commit }) {
      commit('SET_USER_PERMISSIONS', []);
      try {
        localStorage.removeItem('user_permissions');
        localStorage.removeItem('user_permissions_time');
        console.log('🗑️ 权限缓存已清除');
      } catch (error) {
        console.warn('⚠️ 清除权限缓存失败:', error);
      }
    },

    // 获取菜单树
    async getMenuTree({ commit }) {
      try {
        const response = await api.menus.getTree();

        if (response && response.code === 200) {
          commit('SET_MENU_TREE', response.data);
          return Promise.resolve(response.data);
        }

        return Promise.reject(new Error('获取菜单树失败'));
      } catch (error) {
        console.error('获取菜单树失败:', error);
        return Promise.reject(error);
      }
    }
  },
  getters: {
    // 获取用户权限
    permissions: state => {
      return state.permissions;
    },
    // 获取数据字典
    getDict: state => type => {
      return state.dictData[type] || [];
    },
    // 获取部门树
    departmentTree: state => {
      // 实现部门树构建逻辑
      const buildTree = (departments, parentId = null) => {
        return departments
          .filter(dept => dept.parent_id === parentId)
          .map(dept => ({
            value: dept.id,
            title: dept.dept_name,
            key: dept.id,
            children: buildTree(departments.filter(d => d.parent_id === dept.id))
          }));
      };
      
      return buildTree(state.departments);
    },
    // 获取角色列表
    roleList: state => {
      return state.roles.map(role => ({
        value: role.id,
        label: `${role.name}(${role.code})`
      }));
    },

    // 新增菜单和权限相关getters
    currentUser: state => state.user,
    isAuthenticated: state => !!state.token && !!state.user,
    userRole: state => state.user?.role,
    userMenus: state => state.userMenus,
    userPermissions: state => state.userPermissions,
    menuTree: state => state.menuTree,
    menuLoading: state => state.menuLoading,

    // 权限检查函数（优化版）
    hasPermission: state => permission => {
      // 如果没有权限数据，返回false
      if (!state.userPermissions || state.userPermissions.length === 0) {
        return false;
      }

      // 超级管理员拥有所有权限
      if (state.userPermissions.includes('*')) {
        return true;
      }

      // 检查具体权限
      if (Array.isArray(permission)) {
        // 数组形式：需要拥有其中任意一个权限
        return permission.some(p => state.userPermissions.includes(p));
      } else {
        // 字符串形式：需要拥有该权限
        return state.userPermissions.includes(permission);
      }
    },

    // 角色检查函数（优化版）
    hasRole: state => roleCode => {
      if (!state.user || !state.user.role) {
        return false;
      }

      if (Array.isArray(roleCode)) {
        return roleCode.includes(state.user.role.code);
      } else {
        return state.user.role.code === roleCode;
      }
    },

    // 检查按钮权限
    hasButtonPermission: state => (module, action) => {
      const permission = `${module}:${action}`;
      return state.userPermissions?.includes('*') || state.userPermissions?.includes(permission);
    },

    // 检查页面权限
    hasPagePermission: state => pagePath => {
      // 页面权限映射
      const pagePermissionMap = {
        '/purchase/requests': 'purchase:request:view',
        '/purchase/approval': 'purchase:approval:view',
        '/purchase/procurement': 'purchase:procurement:view',
        '/purchase/acceptance': 'purchase:acceptance:view',
        '/purchase/reimbursement': 'purchase:reimbursement:view'
      };

      const requiredPermission = pagePermissionMap[pagePath];
      if (!requiredPermission) {
        return true; // 没有配置权限要求的页面默认允许访问
      }

      return state.userPermissions?.includes('*') || state.userPermissions?.includes(requiredPermission);
    }
  }
})
