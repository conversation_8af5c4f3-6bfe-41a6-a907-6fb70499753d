"""
采购管理系统 - URL路由配置

功能说明：
1. 定义采购模块的所有API端点
2. 配置RESTful风格的URL规则
3. 组织业务功能的路由映射
4. 支持版本控制和API文档生成

技术特点：
- 基于Django URL配置模式
- RESTful API设计规范
- 模块化的路由组织
- 统一的命名空间管理

<AUTHOR>
@version 1.0.0
@since 2025-01-08
"""
from django.urls import path
from . import views

urlpatterns = [
    # 采购需求管理（整合验收和报销功能）
    path('requests/', views.PurchaseRequestListCreateView.as_view(), name='request-list-create'),
    path('requests/<int:pk>/', views.PurchaseRequestDetailView.as_view(), name='request-detail'),

    # 状态转换接口
    path('requests/<int:pk>/submit/', views.submit_request, name='request-submit'),
    path('requests/<int:pk>/approve/', views.approve_request, name='request-approve'),
    path('requests/<int:pk>/reject/', views.reject_request, name='request-reject'),
    path('requests/<int:pk>/return/', views.return_request, name='request-return'),
    path('requests/<int:pk>/purchase/', views.purchase_request, name='purchase-request'),
    path('requests/<int:pk>/accept/', views.accept_request, name='accept-request'),
    path('requests/<int:pk>/reimburse/', views.reimburse_request, name='reimburse-request'),
    path('requests/<int:pk>/finance_return_to_acceptance/', views.finance_return_to_acceptance, name='finance-return-to-acceptance'),
    path('requests/<int:pk>/upload_photo/', views.upload_photo, name='upload-photo'),

    # 新增状态转换接口
    path('requests/<int:pk>/generate_purchase_order/', views.generate_purchase_order_single, name='generate-purchase-order-single'),
    path('requests/<int:pk>/notify_shipping/', views.notify_shipping, name='notify-shipping'),
    path('requests/<int:pk>/generate_reimbursement/', views.generate_reimbursement, name='generate-reimbursement'),

    # 批量操作接口
    path('requests/batch_submit/', views.batch_submit_requests, name='batch-submit-requests'),
    path('requests/batch_apply_purchase/', views.batch_apply_purchase, name='batch-apply-purchase'),
    path('requests/batch_approve/', views.batch_approve_requests, name='batch-approve-requests'),
    path('requests/batch_generate_purchase_order/', views.batch_generate_purchase_order, name='batch-generate-purchase-order'),
    path('requests/batch_purchase/', views.batch_purchase_requests, name='batch-purchase-requests'),
    path('requests/batch_notify_shipping/', views.batch_notify_shipping, name='batch-notify-shipping'),
    path('requests/batch_generate_reimbursement/', views.batch_generate_reimbursement, name='batch-generate-reimbursement'),

    # 打印功能
    path('requests/<int:pk>/print_acceptance/', views.print_acceptance, name='print-acceptance'),

    # 历史价格查询
    path('requests/historical_average_price/', views.get_historical_average_price, name='historical-average-price'),
    path('requests/history/', views.get_history_data, name='history-data'),

    # 时间预警
    path('requests/time_warnings/', views.get_time_warnings, name='time-warnings'),

    # 导入导出功能
    path('requests/export/', views.export_requests, name='export-requests'),
    path('requests/template/', views.download_template, name='download-template'),
    path('requests/import/', views.import_requests, name='import-requests'),

    # 仪表板统计 API
    path('dashboard/statistics/', views.dashboard_statistics, name='dashboard-statistics'),
    path('dashboard/trend/', views.dashboard_trend, name='dashboard-trend'),
    path('dashboard/price-warnings/', views.dashboard_price_warnings, name='dashboard-price-warnings'),
    path('dashboard/status-distribution/', views.status_distribution, name='status-distribution'),
    path('dashboard/department-status/', views.department_status_distribution, name='department-status-distribution'),
    path('dashboard/budget-statistics/', views.budget_statistics, name='budget-statistics'),
    path('dashboard/method-statistics/', views.method_statistics, name='method-statistics'),
    path('dashboard/purchase-type-statistics/', views.purchase_type_statistics, name='purchase-type-statistics'),
    path('dashboard/department-statistics/', views.department_statistics, name='department-statistics'),

    # 优化的统计API
    path('overview/statistics/', views.get_overview_statistics, name='overview-statistics'),
    path('overview/charts/', views.get_overview_charts, name='overview-charts'),

    # 优化的列表API
    path('optimized/list/', views.get_optimized_list, name='optimized-list'),
    path('optimized/filter-options/', views.get_filter_options, name='filter-options'),

    # 快速列表API
    path('fast/list/', views.get_fast_list, name='fast-list'),

    # 需求提报页面优化API
    path('requirements/optimized/', views.get_requirements_optimized, name='requirements-optimized'),

    # 物资采购页面专用优化API
    path('procurement/optimized/', views.get_procurement_optimized, name='procurement-optimized'),

    # 结算报销统计API
    path('reimbursement-statistics/', views.get_reimbursement_statistics, name='reimbursement-statistics'),

    # 兼容性路径 - 重定向到正确的接口
    path('acceptances/', views.acceptances_redirect_view, name='acceptances-redirect'),
]
