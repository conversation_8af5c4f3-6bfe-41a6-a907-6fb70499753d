from django.urls import path
from . import views
from .views import permission_matrix_view

urlpatterns = [
    # 角色管理
    path('', views.RoleListCreateView.as_view(), name='role-list-create'),
    path('<int:pk>/', views.RoleDetailView.as_view(), name='role-detail'),
    path('<int:pk>/permissions/', views.role_permissions_view, name='role-permissions'),
    path('<int:pk>/users/', views.role_users_view, name='role-users'),
    path('<int:pk>/users/<int:user_id>/', views.role_user_remove_view, name='role-user-remove'),
    path('simple/', views.roles_simple_list_view, name='roles-simple'),
    path('template/', views.download_role_template, name='role-template'),
    path('import/', views.import_roles, name='role-import'),
    path('export/', views.export_roles, name='role-export'),
    
    # 用户角色关联（已删除 - 改用User.role字段）
    # path('user-roles/', views.UserRoleListCreateView.as_view(), name='user-role-list-create'),
    # path('user-roles/<int:pk>/', views.UserRoleDetailView.as_view(), name='user-role-detail'),
    
    # 权限管理
    path('permissions/', views.permissions_list_view, name='permissions-list'),
    path('permission-matrix/', permission_matrix_view, name='permission-matrix'),
]
