"""
通用基础序列化器
减少重复代码，统一验证逻辑和字段处理
"""
from rest_framework import serializers
from django.core.exceptions import ValidationError
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class BaseModelSerializer(serializers.ModelSerializer):
    """基础模型序列化器"""
    
    # 通用只读字段
    created_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    updated_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    
    class Meta:
        abstract = True
    
    def validate(self, attrs):
        """通用验证逻辑"""
        # 调用模型的clean方法进行验证
        if hasattr(self.Meta.model, 'clean'):
            instance = self.Meta.model(**attrs)
            if self.instance:
                # 更新时，合并现有实例的数据
                for field, value in attrs.items():
                    setattr(instance, field, value)
                instance.pk = self.instance.pk
            
            try:
                instance.clean()
            except ValidationError as e:
                raise serializers.ValidationError(e.message_dict if hasattr(e, 'message_dict') else str(e))
        
        return super().validate(attrs)
    
    def create(self, validated_data):
        """创建实例"""
        try:
            return super().create(validated_data)
        except Exception as e:
            logger.error(f"创建{self.Meta.model.__name__}失败: {e}")
            raise serializers.ValidationError(f"创建失败: {str(e)}")
    
    def update(self, instance, validated_data):
        """更新实例"""
        try:
            return super().update(instance, validated_data)
        except Exception as e:
            logger.error(f"更新{self.Meta.model.__name__}失败: {e}")
            raise serializers.ValidationError(f"更新失败: {str(e)}")


class StatusMixin:
    """状态管理混入类"""
    
    def validate_status_transition(self, old_status, new_status):
        """验证状态转换是否合法"""
        # 子类可以重写此方法定义具体的状态转换规则
        return True
    
    def validate_status(self, value):
        """状态字段验证"""
        if self.instance and hasattr(self.instance, 'status'):
            old_status = self.instance.status
            if not self.validate_status_transition(old_status, value):
                raise serializers.ValidationError(f"不能从{old_status}状态转换到{value}状态")
        return value


class TimestampMixin:
    """时间戳混入类"""
    
    def validate_date_range(self, start_date, end_date):
        """验证日期范围"""
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("开始日期不能晚于结束日期")
        return True


class UniqueFieldMixin:
    """唯一字段验证混入类"""
    
    def validate_unique_field(self, field_name, value, error_message=None):
        """验证字段唯一性"""
        if not error_message:
            error_message = f"{field_name}已存在"
        
        queryset = self.Meta.model.objects.filter(**{field_name: value})
        
        if self.instance:
            # 更新时排除自身
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError(error_message)
        
        return value


class FileFieldMixin:
    """文件字段处理混入类"""
    
    def validate_file_size(self, file, max_size_mb=10):
        """验证文件大小"""
        if file and hasattr(file, 'size'):
            max_size = max_size_mb * 1024 * 1024  # 转换为字节
            if file.size > max_size:
                raise serializers.ValidationError(f"文件大小不能超过{max_size_mb}MB")
        return file
    
    def validate_file_type(self, file, allowed_types=None):
        """验证文件类型"""
        if not allowed_types:
            allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx']
        
        if file and hasattr(file, 'name'):
            file_ext = file.name.split('.')[-1].lower()
            if file_ext not in allowed_types:
                raise serializers.ValidationError(f"不支持的文件类型，允许的类型: {', '.join(allowed_types)}")
        
        return file


class BusinessStageSerializer(BaseModelSerializer, StatusMixin):
    """业务流程阶段序列化器基类"""
    
    def get_editable_fields(self, status):
        """根据状态获取可编辑字段"""
        # 子类需要重写此方法
        return []
    
    def validate(self, attrs):
        """业务阶段验证"""
        if self.instance and hasattr(self.instance, 'status'):
            current_status = self.instance.status
            editable_fields = self.get_editable_fields(current_status)
            
            # 检查是否有不可编辑的字段被修改
            for field_name, value in attrs.items():
                if field_name not in editable_fields and hasattr(self.instance, field_name):
                    old_value = getattr(self.instance, field_name)
                    if old_value != value:
                        raise serializers.ValidationError(f"当前状态下不允许修改{field_name}字段")
        
        return super().validate(attrs)


class BatchOperationSerializer(serializers.Serializer):
    """批量操作序列化器"""
    ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        error_messages={'min_length': '请至少选择一个项目'}
    )
    
    def validate_ids(self, value):
        """验证ID列表"""
        if not value:
            raise serializers.ValidationError("请选择要操作的项目")
        return value


class StatusUpdateSerializer(serializers.Serializer):
    """状态更新序列化器"""
    status = serializers.CharField(max_length=50)
    reason = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate_status(self, value):
        """验证状态值"""
        # 子类可以重写此方法定义允许的状态值
        return value


class ExportConfigSerializer(serializers.Serializer):
    """导出配置序列化器"""
    fields = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text="要导出的字段列表"
    )
    format = serializers.ChoiceField(
        choices=['xlsx', 'csv'],
        default='xlsx',
        help_text="导出格式"
    )
    filename = serializers.CharField(
        max_length=100,
        required=False,
        help_text="文件名"
    )


class FilterSerializer(serializers.Serializer):
    """通用筛选序列化器"""
    page = serializers.IntegerField(min_value=1, default=1)
    page_size = serializers.IntegerField(min_value=1, max_value=1000, default=20)
    search = serializers.CharField(max_length=100, required=False, allow_blank=True)
    ordering = serializers.CharField(max_length=50, required=False, allow_blank=True)
    
    # 时间范围筛选
    start_date = serializers.DateField(required=False)
    end_date = serializers.DateField(required=False)
    
    def validate(self, attrs):
        """验证筛选参数"""
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("开始日期不能晚于结束日期")
        
        return attrs


# 常用字段定义
class CommonFields:
    """常用字段定义"""
    
    @staticmethod
    def status_field(choices=None):
        """状态字段"""
        return serializers.CharField(max_length=50, help_text="状态")
    
    @staticmethod
    def money_field():
        """金额字段"""
        return serializers.DecimalField(
            max_digits=12, 
            decimal_places=2, 
            min_value=0,
            help_text="金额"
        )
    
    @staticmethod
    def quantity_field():
        """数量字段"""
        return serializers.IntegerField(min_value=0, help_text="数量")
    
    @staticmethod
    def phone_field():
        """电话字段"""
        return serializers.RegexField(
            regex=r'^1[3-9]\d{9}$',
            max_length=11,
            error_message="请输入正确的手机号码"
        )
    
    @staticmethod
    def email_field():
        """邮箱字段"""
        return serializers.EmailField(help_text="邮箱地址")
    
    @staticmethod
    def url_field():
        """URL字段"""
        return serializers.URLField(help_text="URL地址")


# 导出常用的类和函数
__all__ = [
    'BaseModelSerializer',
    'StatusMixin',
    'TimestampMixin',
    'UniqueFieldMixin',
    'FileFieldMixin',
    'BusinessStageSerializer',
    'BatchOperationSerializer',
    'StatusUpdateSerializer',
    'ExportConfigSerializer',
    'FilterSerializer',
    'CommonFields'
]
