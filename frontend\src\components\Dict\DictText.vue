<template>
  <span :class="textClass">
    <a-spin v-if="loading" size="small" />
    <template v-else>
      <a-tag v-if="showTag" :color="tagColor">{{ displayText }}</a-tag>
      <template v-else>{{ displayText }}</template>
    </template>
  </span>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import dictService from '@/services/dictService'

const props = defineProps({
  dictType: {
    type: String,
    required: true
  },
  code: {
    type: String,
    default: ''
  },
  defaultText: {
    type: String,
    default: '-'
  },
  showTag: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  tagColor: {
    type: String,
    default: 'default'
  },
  textClass: {
    type: String,
    default: ''
  }
})

// 状态
const loading = ref(false)
const text = ref('')

// 计算属性
const displayText = computed(() => {
  return text.value || props.defaultText
})

// 方法
const loadText = async () => {
  if (!props.code || loading.value) {
    text.value = props.defaultText
    return
  }
  
  try {
    loading.value = true
    const result = await dictService.getText(props.dictType, props.code, props.defaultText)
    text.value = result
  } catch (error) {
    console.error(`获取字典文本失败: ${props.dictType}.${props.code}`, error)
    text.value = props.code || props.defaultText
  } finally {
    loading.value = false
  }
}

// 监听变化
watch([() => props.dictType, () => props.code], () => {
  loadText()
}, { immediate: true })

// 暴露方法
defineExpose({
  refresh: loadText
})
</script>

<style scoped>
.ant-spin {
  font-size: 12px;
}
</style>
