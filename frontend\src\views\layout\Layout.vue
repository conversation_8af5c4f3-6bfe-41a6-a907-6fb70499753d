<template>
  <div class="layout">
    <!-- 顶部导航栏 -->
    <header class="layout-header">
      <div class="header-left">
        <div class="logo">
          <div class="logo-icon">
            <ShoppingCartOutlined />
          </div>
          <div class="logo-text">
            <h2>采购管理系统</h2>
            <span class="logo-subtitle">Enterprise Procurement Management</span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <!-- 通知中心 -->
        <NotificationCenter />

        <div class="user-info">
          <a-avatar :size="36" :style="{
            backgroundColor: getUserAvatarColor(),
            marginRight: '12px',
            fontSize: '16px',
            fontWeight: '600'
          }">
            {{ getUserAvatarText() }}
          </a-avatar>
          <div class="user-details">
            <span class="user-name">{{ userInfo.real_name || userInfo.username || '未知用户' }}</span>
            <span class="user-role">{{ getUserRoleText() }}</span>
          </div>
          <a-dropdown placement="bottomRight">
            <template #overlay>
              <a-menu class="user-menu">
                <a-menu-item @click="showUserProfile">
                  <UserOutlined />
                  个人资料
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item @click="logout" class="logout-item">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
            <a-button type="text" class="user-dropdown">
              <DownOutlined />
            </a-button>
          </a-dropdown>
        </div>
      </div>
    </header>

    <div class="layout-content">
      <!-- 侧边菜单栏 -->
      <aside class="layout-sider">
        <div class="sider-content">
          <!-- 动态菜单 -->
          <DynamicMenu
            theme="light"
            @menu-click="handleDynamicMenuClick"
            class="main-menu"
          />
        </div>
      </aside>

      <!-- 主内容区 -->
      <main class="layout-main">
        <div class="main-content">
          <router-view />
        </div>
      </main>
    </div>
     <!-- 底部状态栏 -->
    <footer class="layout-footer">
      <div class="footer-content">
        <div class="footer-left">
          <span>© 2025 采购管理系统 v1.0.0</span>
          <a-divider type="vertical" />
          <span>技术支持：相当英俊绝对潇洒的系统开发团队</span>
        </div>
        <div class="footer-right">
          <span>当前在线用户：{{ onlineUsers }} 人</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import {
  UserOutlined,
  LogoutOutlined,
  ShoppingCartOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import api from '@/api'
import NotificationCenter from '@/components/NotificationCenter.vue'
import DynamicMenu from '@/components/menu/DynamicMenu.vue'

export default {
  name: 'Layout',
  components: {
    UserOutlined,
    LogoutOutlined,
    ShoppingCartOutlined,
    DownOutlined,
    NotificationCenter,
    DynamicMenu
  },
  setup() {
    const router = useRouter()
    const store = useStore()
    
    // 响应式数据
    const userInfo = reactive({
      username: '',
      real_name: '',
      role: null
    })
    const onlineUsers = ref(0)
    
    // 计算属性
    const currentUser = computed(() => store.getters.currentUser)
    
    /**
     * 获取用户头像颜色
     */
    const getUserAvatarColor = () => {
      const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
      const username = userInfo.username || 'default'
      const index = username.charCodeAt(0) % colors.length
      return colors[index]
    }

    /**
     * 获取用户头像文字
     */
    const getUserAvatarText = () => {
      const name = userInfo.real_name || userInfo.username || '用户'
      return name.charAt(0).toUpperCase()
    }

    /**
     * 获取用户角色文本
     */
    const getUserRoleText = () => {
      if (userInfo.role && userInfo.role.name) {
        return userInfo.role.name
      }
      return '普通用户'
    }

    /**
     * 显示用户资料
     */
    const showUserProfile = () => {
      router.push('/userInfo/profile')
    }

    /**
     * 用户退出登录
     */
    const logout = () => {
      // 清除所有localStorage数据
      localStorage.clear()

      // 清除所有sessionStorage数据
      sessionStorage.clear()

      // 清除Vuex状态
      store.dispatch('logout')

      // 清除菜单缓存
      store.dispatch('clearUserMenus')

      // 清除动态路由缓存
      if (window.dynamicRouterManager) {
        window.dynamicRouterManager.clearCache()
      }

      // 跳转到登录页
      router.push('/login')

      // 强制刷新页面以确保所有缓存都被清除
      setTimeout(() => {
        window.location.reload()
      }, 100)
    }

    /**
     * 处理动态菜单点击事件
     */
    const handleDynamicMenuClick = () => {
      // 处理菜单点击逻辑
    }

    /**
     * 获取用户信息
     */
    const getUserInfo = async () => {
      try {
        const response = await api.users.getCurrentUser()
        if (response.code === 200) {
          Object.assign(userInfo, response.data)
        }
      } catch (error) {
        // 获取用户信息失败，静默处理
      }
    }

    /**
     * 获取在线用户数
     */
    const getOnlineUsers = async () => {
      try {
        const response = await api.users.getOnlineCount()
        if (response.code === 200) {
          onlineUsers.value = response.data.count || 0
        }
      } catch (error) {
        // 获取在线用户数失败，静默处理
      }
    }

    // 组件挂载时初始化数据
    onMounted(() => {
      getUserInfo()
      getOnlineUsers()
      
      // 定时更新在线用户数
      setInterval(getOnlineUsers, 30000)
    })

    return {
      userInfo,
      onlineUsers,
      currentUser,
      getUserAvatarColor,
      getUserAvatarText,
      getUserRoleText,
      showUserProfile,
      logout,
      handleDynamicMenuClick
    }
  }
}
</script>

<style scoped>
.layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  height: 64px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left .logo {
  display: flex;
  align-items: center;
}

.logo-icon {
  font-size: 32px;
  color: #1890ff;
  margin-right: 12px;
}

.logo-text h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.logo-subtitle {
  font-size: 12px;
  color: #8c8c8c;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  display: flex;
  flex-direction: column;
  margin-right: 8px;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.user-role {
  font-size: 12px;
  color: #8c8c8c;
}

.layout-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sider {
  width: 240px;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
}

.sider-content {
  height: 100%;
}

.layout-main {
  flex: 1;
  background: #f0f2f5;
  overflow: auto;
}

.main-content {
  padding: 24px;
  min-height: 100%;
}

.layout-footer {
  height: 56px;
  background: #ffffff;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  padding: 0 32px;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.05);
}

.footer-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #666;
  font-size: 13px;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-right {
  color: #999;
}

:deep(.user-menu .logout-item) {
  color: #ff4d4f;
}

:deep(.user-menu .logout-item:hover) {
  background: #ff4d4f;
  color: #fff;
}
</style>
