# Generated by Django 5.2.4 on 2025-07-17 13:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("purchase", "0035_copy_type_field_data"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="dictionary",
            options={
                "ordering": ["type_code", "order"],
                "verbose_name": "数据字典",
                "verbose_name_plural": "数据字典管理",
            },
        ),
        migrations.RemoveIndex(
            model_name="dictionary",
            name="idx_dict_type",
        ),
        migrations.RemoveIndex(
            model_name="dictionary",
            name="idx_dict_type_status",
        ),
        migrations.RemoveIndex(
            model_name="dictionary",
            name="idx_dict_type_order",
        ),
        migrations.AlterUniqueTogether(
            name="dictionary",
            unique_together={("type_code", "code")},
        ),
        migrations.AlterField(
            model_name="dictionary",
            name="type_code",
            field=models.CharField(
                choices=[
                    ("status", "状态"),
                    ("purchase_type", "采购类型"),
                    ("procurement_method", "采购方式"),
                    ("fund_project", "经费项目"),
                    ("unit", "计量单位"),
                    ("item_category", "物品种类"),
                    ("requirement_source", "需求来源"),
                    ("urgency_level", "紧急程度"),
                    ("其他", "其他"),
                ],
                help_text="英文类型编码，用于代码匹配",
                max_length=50,
                verbose_name="字典类型编码",
            ),
        ),
        migrations.AlterField(
            model_name="dictionary",
            name="type_name",
            field=models.CharField(
                help_text="中文类型名称，用于界面显示",
                max_length=50,
                verbose_name="字典类型名称",
            ),
        ),
        migrations.AddIndex(
            model_name="dictionary",
            index=models.Index(fields=["type_code"], name="idx_dict_type_code"),
        ),
        migrations.AddIndex(
            model_name="dictionary",
            index=models.Index(
                fields=["type_code", "status"], name="idx_dict_type_code_status"
            ),
        ),
        migrations.AddIndex(
            model_name="dictionary",
            index=models.Index(
                fields=["type_code", "order"], name="idx_dict_type_code_order"
            ),
        ),
        migrations.RemoveField(
            model_name="dictionary",
            name="type",
        ),
        migrations.RemoveField(
            model_name="dictionary",
            name="type_display",
        ),
    ]
