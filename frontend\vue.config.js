const { defineConfig } = require('@vue/cli-service')
const webpack = require('webpack')
const CompressionPlugin = require('compression-webpack-plugin')

module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    plugins: [
      new webpack.DefinePlugin({
        __VUE_OPTIONS_API__: JSON.stringify(true),
        __VUE_PROD_DEVTOOLS__: JSON.stringify(false),
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: JSON.stringify(false)
      }),
      new CompressionPlugin({
        algorithm: 'gzip',
        test: /\.(js|css|html|svg)$/,
        threshold: 8192, // 超过8KB的文件才压缩
        minRatio: 0.8
      })
    ],
    optimization: {
      splitChunks: {
        chunks: 'all',
        maxInitialRequests: 20,
        maxAsyncRequests: 20,
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true,
            priority: 5
          },
          // 为采购模块创建单独的 chunk
          purchase: {
            test: /[\\/]src[\\/]views[\\/]purchase[\\/]/,
            name: 'purchase-module',
            chunks: 'all',
            priority: 8
          }
        }
      },
      // 添加运行时 chunk 分离
      runtimeChunk: {
        name: 'runtime'
      }
    }
  },
  devServer: {
    port: 8080,
    // 配置SPA路由回退，解决刷新404问题
    historyApiFallback: {
      index: '/index.html',
      rewrites: [
        // API请求不进行回退
        { from: /^\/api\/.*$/, to: function(context) {
          return context.parsedUrl.pathname;
        }},
        // 所有其他请求回退到index.html
        { from: /./, to: '/index.html' }
      ]
    },
    client: {
      overlay: {
        warnings: false,
        errors: true
      }
    },
    proxy: {
      '/api': {
        target: process.env.VUE_APP_BACKEND_URL || 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false
      }
    }
  }
})