/**
 * 动态路由核心模块
 * 基于优化后的数据库菜单表结构，实现混合路由架构
 * 参考RuoYi-Vue3和Vue-Element-Admin的最佳实践
 *
 * 架构设计：
 * 1. 静态路由：基础页面（dashboard、profile）
 * 2. 动态路由：权限功能页面
 * 3. 组件分离：一级菜单作为目录，二级菜单对应页面
 * 4. 路由持久化：解决刷新后路由丢失问题
 */

import store from '@/store'

/**
 * 创建带错误处理和预加载的动态导入函数
 * 解决 ChunkLoadError 问题并优化加载性能
 */
const createAsyncComponent = (importFn, chunkName, preload = false) => {
  const componentLoader = () => importFn().catch(error => {
    // 重试机制：清除缓存后重新加载
    if (error.name === 'ChunkLoadError') {

      // 清除可能的缓存
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name))
        })
      }

      // 延迟重试，避免立即重新加载
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          importFn().then(resolve).catch(() => {
            // 最终重试失败，重新加载页面
            window.location.reload()
            reject(error)
          })
        }, 1000)
      })
    }
    throw error
  })

  // 如果需要预加载，立即开始加载但不阻塞
  if (preload) {
    componentLoader().catch(() => {
      // 预加载失败不影响正常流程，静默处理
    })
  }

  return componentLoader
}

/**
 * 预加载关键组件
 * 在用户登录后立即开始预加载常用页面
 */
// eslint-disable-next-line no-unused-vars
const preloadCriticalComponents = () => {
  const criticalChunks = [
    'purchase-requests',
    'purchase-approval',
    'purchase-procurement',
    'purchase-reimbursement-list'
  ]

  criticalChunks.forEach(chunkName => {
    const importFn = routeComponents[Object.keys(routeComponents).find(path =>
      routeComponents[path].toString().includes(chunkName)
    )]

    if (importFn) {
      importFn().catch(() => {
        // 静默处理预加载失败
      })
    }
  })
}

/**
 * 路由组件映射表
 * 排除静态路由，只包含动态功能页面
 * 使用webpackChunkName注释来控制chunk命名
 */
const routeComponents = {

  // 系统管理模块
  '/settings/users': () => import(/* webpackChunkName: "settings-users" */ '@/views/settings/users/Users.vue'),
  '/settings/roles': () => import(/* webpackChunkName: "settings-roles" */ '@/views/settings/roles/Roles.vue'),
  '/settings/permissions': () => import(/* webpackChunkName: "settings-permissions" */ '@/views/settings/permissions/PermissionsMatrix.vue'),
  '/settings/menus': () => import(/* webpackChunkName: "settings-menus" */ '@/views/settings/menus/MenuManagement.vue'),
  '/settings/departments': () => import(/* webpackChunkName: "settings-departments" */ '@/views/settings/departments/Departments.vue'),
  '/settings/dicts': () => import(/* webpackChunkName: "settings-dicts" */ '@/views/settings/dicts/Dicts.vue'),
  '/settings/logs': () => import(/* webpackChunkName: "settings-logs" */ '@/views/settings/logs/LogManagement.vue'),

  // 个人管理模块
  '/userInfo/profile': () => import(/* webpackChunkName: "userinfo-profile" */ '@/views/userInfo/profile/UserProfile.vue'),
  '/userInfo/notifications': () => import(/* webpackChunkName: "userinfo-notifications" */ '@/views/userInfo/notifications/NotificationList.vue'),

  // 采购管理模块
  '/purchase/overview': () => import(/* webpackChunkName: "purchase-overview" */ '@/views/purchase/overview/Overview.vue'),
  '/purchase/requests': () => import(/* webpackChunkName: "purchase-requests" */ '@/views/purchase/requests/List.vue'),
  '/purchase/requests/new': () => import(/* webpackChunkName: "purchase-requests-form" */ '@/views/purchase/requests/Form.vue'),
  '/purchase/requests/:id/edit': () => import(/* webpackChunkName: "purchase-requests-form" */ '@/views/purchase/requests/Form.vue'),
  '/purchase/approval': () => import(/* webpackChunkName: "purchase-approval" */ '@/views/purchase/approval/Approval.vue'),
  '/purchase/procurement': () => import(/* webpackChunkName: "purchase-procurement" */ '@/views/purchase/procurement/Procurement.vue'),
  '/purchase/acceptance': () => import(/* webpackChunkName: "purchase-acceptance" */ '@/views/purchase/acceptance/List.vue'),
  '/purchase/reimbursement': createAsyncComponent(
    () => import(/* webpackChunkName: "purchase-reimbursement-list" */ '@/views/purchase/reimbursement/List.vue'),
    'purchase-reimbursement-list'
  )
}

/**
 * 静态路由路径列表
 * 用于避免动态路由冲突
 */
const STATIC_ROUTES = ['dashboard']

/**
 * 动态路由状态管理
 */
class DynamicRouterManager {
  constructor() {
    this.isInitialized = false
    this.addedRoutes = []
    this.menuCache = null
    this.cacheExpiry = null
    this.CACHE_DURATION = 30 * 60 * 1000 // 30分钟缓存
  }

  /**
   * 检查菜单缓存是否有效
   */
  isCacheValid() {
    return this.menuCache && this.cacheExpiry && Date.now() < this.cacheExpiry
  }

  /**
   * 设置菜单缓存
   */
  setMenuCache(menus) {
    this.menuCache = menus
    this.cacheExpiry = Date.now() + this.CACHE_DURATION
  }

  /**
   * 清除菜单缓存
   */
  clearMenuCache() {
    this.menuCache = null
    this.cacheExpiry = null
  }

  /**
   * 强制刷新菜单缓存
   */
  forceRefreshCache() {
    this.clearMenuCache()
  }

  /**
   * 清除所有缓存（用于退出登录）
   */
  clearCache() {
    this.clearMenuCache()
    this.isInitialized = false
    this.addedRoutes = []
  }

  /**
   * 获取用户菜单数据（优化版 - 减少延迟）
   */
  async getUserMenus() {
    // 1. 优先检查内存缓存（最快）
    if (this.isCacheValid()) {
      return this.menuCache
    }

    // 2. 检查Vuex中的菜单数据（次快）
    const storeMenus = store.getters['permission/userMenus']
    if (storeMenus && storeMenus.length > 0) {
      this.setMenuCache(storeMenus)
      return storeMenus
    }

    // 3. 检查localStorage（避免重复API调用）
    try {
      const cachedMenus = localStorage.getItem('user_menus_cache')
      const cacheTime = localStorage.getItem('user_menus_cache_time')

      if (cachedMenus && cacheTime) {
        const timeDiff = Date.now() - parseInt(cacheTime)
        // 如果缓存时间小于10分钟，直接使用
        if (timeDiff < 10 * 60 * 1000) {
          const menus = JSON.parse(cachedMenus)
          this.setMenuCache(menus)
          store.commit('SET_USER_MENUS', menus)
          return menus
        }
      }
    } catch (error) {
      // 静默处理localStorage缓存解析失败
    }

    // 4. 从服务器获取（最慢，但会更新所有缓存）
    const menus = await store.dispatch('getUserMenus')

    if (menus && menus.length > 0) {
      this.setMenuCache(menus)

      // 更新localStorage缓存
      try {
        localStorage.setItem('user_menus_cache', JSON.stringify(menus))
        localStorage.setItem('user_menus_cache_time', Date.now().toString())
      } catch (error) {
        // 静默处理保存菜单缓存失败
      }

      return menus
    } else {
      return []
    }
  }

  /**
   * 根据菜单数据生成动态路由
   */
  generateRoutesFromMenus(menus) {
    const routes = []

    const processMenu = (menu) => {
      // 跳过隐藏的菜单
      if (menu.hidden) {
        return null
      }

      // 检查是否有路由路径
      if (!menu.route_path) {
        return null
      }

      // 检查是否与静态路由冲突
      const staticCheckPath = menu.route_path.replace('/', '')
      if (STATIC_ROUTES.includes(staticCheckPath)) {
        return null
      }

      // 生成路由配置
      // 保持原始路径，因为这些是Layout的子路由
      let routePath = menu.route_path
      // 移除开头的斜杠，因为这些是Layout的子路由
      if (routePath.startsWith('/')) {
        routePath = routePath.substring(1)
      }

      const route = {
        path: routePath,
        name: menu.menu_code,
        meta: {
          title: menu.menu_name,
          icon: menu.icon,
          requiresAuth: true,
          permission: menu.permission_code,
          keepAlive: menu.keep_alive || false,
          hidden: menu.hidden || false,
          alwaysShow: menu.always_show || false
        }
      }

      // 处理重定向
      if (menu.redirect) {
        route.redirect = menu.redirect
      } else {
        // 获取组件
        const component = routeComponents[menu.route_path]
        if (!component) {
          // 如果没有组件且有子菜单，可能是父菜单，跳过
          if (menu.children && menu.children.length > 0) {
            return null
          }
          return null
        }

        route.component = component
      }

      return route
    }

    // 递归处理菜单
    const processMenuRecursive = (menuList) => {
      menuList.forEach(menu => {
        const route = processMenu(menu)
        if (route) {
          routes.push(route)
        }

        // 处理子菜单
        if (menu.children && menu.children.length > 0) {
          processMenuRecursive(menu.children)
        }
      })
    }

    processMenuRecursive(menus)

    return routes
  }

  /**
   * 添加动态路由到路由器
   */
  async addDynamicRoutes(router, menus) {
    try {
      // 清除之前的动态路由（如果存在）
      this.clearDynamicRoutes(router)

      // 生成动态路由
      const dynamicRoutes = this.generateRoutesFromMenus(menus)

      if (dynamicRoutes.length === 0) {
        return false
      }

      // 检查Layout路由是否存在
      const layoutRoute = router.getRoutes().find(r => r.name === 'Layout')
      if (!layoutRoute) {
        return false
      }

      // 添加到Layout路由的children中
      let addedCount = 0
      dynamicRoutes.forEach(route => {
        try {
          router.addRoute('Layout', route)
          this.addedRoutes.push(route.name)
          addedCount++
        } catch (error) {
          // 静默处理路由添加失败
        }
      })

      if (addedCount > 0) {
        this.isInitialized = true

        // 确保404路由在最后添加
        this.add404Route(router)

        return true
      } else {
        return false
      }
    } catch (error) {
      return false
    }
  }

  /**
   * 添加404通配符路由（必须在所有动态路由之后）
   */
  add404Route(router) {
    try {
      // 检查404路由是否已存在
      const existingNotFound = router.getRoutes().find(r => r.name === 'NotFound')
      if (existingNotFound) {
        return
      }

      const notFoundRoute = {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('@/views/error/404.vue'),
        meta: {
          title: '页面未找到',
          requiresAuth: false
        }
      }

      router.addRoute(notFoundRoute)
    } catch (error) {
      // 静默处理404路由添加失败
    }
  }

  /**
   * 清除动态路由
   */
  clearDynamicRoutes(router) {
    try {
      // 清除已添加的动态路由
      this.addedRoutes.forEach(routeName => {
        if (router.hasRoute(routeName)) {
          router.removeRoute(routeName)
        }
      })

      // 清除404路由
      if (router.hasRoute('NotFound')) {
        router.removeRoute('NotFound')
      }

      this.addedRoutes = []
      this.isInitialized = false
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 初始化动态路由系统（优化版）
   */
  async initDynamicRoutes(router) {
    try {
      // 检查用户是否已登录
      const token = localStorage.getItem('token')
      if (!token) {
        return false
      }

      // 如果已经初始化，先清除
      if (this.isInitialized) {
        this.clearDynamicRoutes(router)
      }

      // 获取用户菜单
      const userMenus = await this.getUserMenus()

      if (userMenus && userMenus.length > 0) {
        // 添加动态路由
        const success = await this.addDynamicRoutes(router, userMenus)

        if (success) {
          // 启动组件预加载
          this.preloadComponents(userMenus)
        }

        return success
      } else {
        return false
      }
    } catch (error) {
      return false
    }
  }

  /**
   * 预加载组件（基于用户菜单）
   */
  preloadComponents(userMenus) {
    const preloadRoutes = []

    // 递归收集所有路由路径
    const collectRoutes = (menus) => {
      menus.forEach(menu => {
        if (menu.route_path && routeComponents[menu.route_path]) {
          preloadRoutes.push(menu.route_path)
        }
        if (menu.children && menu.children.length > 0) {
          collectRoutes(menu.children)
        }
      })
    }

    collectRoutes(userMenus)

    // 异步预加载组件（不阻塞主流程）
    setTimeout(() => {
      preloadRoutes.forEach(routePath => {
        const componentLoader = routeComponents[routePath]
        if (componentLoader) {
          componentLoader().catch(() => {
            // 静默处理预加载失败
          })
        }
      })
    }, 1000) // 延迟1秒开始预加载，避免影响首屏渲染
  }

  /**
   * 重新加载动态路由
   */
  async reloadDynamicRoutes(router) {
    // 清除缓存
    this.clearMenuCache()

    // 重新初始化
    return await this.initDynamicRoutes(router)
  }




}

// 创建全局实例
const dynamicRouterManager = new DynamicRouterManager()

// 导出主要方法
export const initDynamicRoutes = (router) => dynamicRouterManager.initDynamicRoutes(router)
export const clearDynamicRoutes = (router) => dynamicRouterManager.clearDynamicRoutes(router)
export const reloadDynamicRoutes = (router) => dynamicRouterManager.reloadDynamicRoutes(router)
export const getUserMenus = () => dynamicRouterManager.getUserMenus()
export const clearCache = () => dynamicRouterManager.clearCache()

// 将实例挂载到全局，方便在其他地方调用
window.dynamicRouterManager = dynamicRouterManager

export default dynamicRouterManager
