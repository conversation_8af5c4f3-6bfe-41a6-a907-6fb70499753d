/**
 * 全局错误处理工具
 * 用于处理各种前端错误，包括 ResizeObserver 错误
 */

/**
 * 处理 ResizeObserver 错误
 * 这是一个常见的浏览器警告，不影响应用功能
 */
export function handleResizeObserverError() {
  // 重写 ResizeObserver 的错误处理
  const originalResizeObserver = window.ResizeObserver;
  
  if (originalResizeObserver) {
    window.ResizeObserver = class extends originalResizeObserver {
      constructor(callback) {
        const wrappedCallback = (entries, observer) => {
          try {
            callback(entries, observer);
          } catch (error) {
            // 忽略 ResizeObserver 相关错误
            if (error.message && error.message.includes('ResizeObserver loop completed')) {
              return;
            }
            throw error;
          }
        };
        super(wrappedCallback);
      }
    };
  }
}

/**
 * 全局错误监听器
 */
export function setupGlobalErrorHandlers() {
  // 处理 JavaScript 错误
  window.addEventListener('error', (event) => {
    const { message } = event;

    // 忽略 ResizeObserver 错误
    if (message && message.includes('ResizeObserver loop completed')) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  });
  
  // 处理未捕获的 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    const { reason } = event;
    
    // 忽略 ResizeObserver 相关的 Promise 错误
    if (reason && reason.message && reason.message.includes('ResizeObserver')) {
      event.preventDefault();
      return false;
    }
  });
}

/**
 * 初始化所有错误处理器
 */
export function initErrorHandlers() {
  handleResizeObserverError();
  setupGlobalErrorHandlers();
}

/**
 * API 错误处理
 */
export function handleApiError(error, defaultMessage = '请求失败') {
  let messageContent = defaultMessage;

  if (error.response) {
    switch (error.response.status) {
      case 400:
        messageContent = '请求参数错误';
        break;
      case 401:
        messageContent = '未授权，请重新登录';
        break;
      case 403:
        messageContent = '没有访问权限';
        break;
      case 404:
        messageContent = '请求资源不存在';
        break;
      case 500:
        messageContent = '服务器内部错误';
        break;
      default:
        messageContent = defaultMessage;
    }
  } else if (error.request) {
    messageContent = '网络异常，请检查您的网络连接';
  } else {
    messageContent = '未知错误';
  }

  return {
    message: messageContent,
    error
  };
}

/**
 * 开发环境错误处理
 */
export function setupDevErrorHandlers() {
  if (process.env.NODE_ENV === 'development') {
    // 监听 Vue 警告
    if (window.Vue && window.Vue.config) {
      const originalWarn = window.Vue.config.warnHandler;
      window.Vue.config.warnHandler = (msg, vm, trace) => {
        // 忽略 ResizeObserver 相关警告
        if (msg && msg.includes('ResizeObserver')) {
          return;
        }

        if (originalWarn) {
          originalWarn(msg, vm, trace);
        }
      };
    }
  }
}

export default {
  initErrorHandlers,
  handleApiError,
  setupDevErrorHandlers,
  handleResizeObserverError,
  setupGlobalErrorHandlers
};
