# Generated by Django 5.2.3 on 2025-06-26 12:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0018_update_model_structure'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='purchaserequest',
            name='idx_purchase_approved',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='exception_approver_id',
        ),
        migrations.RemoveField(
            model_name='purchaserequest',
            name='final_approver_id',
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='acceptance_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='验收完成时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='acceptance_remark',
            field=models.TextField(blank=True, help_text='验收过程中的备注信息', verbose_name='验收备注'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='acceptor',
            field=models.ForeignKey(blank=True, help_text='负责验收的人员', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='accepted_requests', to=settings.AUTH_USER_MODEL, verbose_name='验收人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='actual_quantity',
            field=models.PositiveIntegerField(default=0, help_text='实际采购的数量', verbose_name='实际采购数量'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='actual_unit_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='实际采购单价', max_digits=12, null=True, verbose_name='实际采购单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='approval_attachment',
            field=models.FileField(blank=True, null=True, upload_to='approvals/%Y/%m/', verbose_name='审批附件'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='approval_comment',
            field=models.TextField(blank=True, help_text='审批时的意见或建议', verbose_name='审批意见'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='审批时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='attachment',
            field=models.FileField(blank=True, help_text='支持Excel/PDF格式的需求说明文档', null=True, upload_to='purchase_attachments/%Y/%m/', verbose_name='需求说明文档'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='courier_company',
            field=models.CharField(blank=True, help_text='物流配送公司', max_length=100, verbose_name='快递公司'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='courier_receipt_photo',
            field=models.ImageField(blank=True, null=True, upload_to='receipts/%Y/%m/', verbose_name='快递单照片'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='dept_id',
            field=models.PositiveIntegerField(db_index=True, verbose_name='所属部门ID'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='exception_approval_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='异常审批时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='exception_reason',
            field=models.TextField(blank=True, help_text='异常的具体描述', verbose_name='异常原因'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='financial_serial_no',
            field=models.CharField(blank=True, help_text='财务系统流水号', max_length=50, verbose_name='财务流水号'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='fund_project_name',
            field=models.CharField(help_text='资金来源项目', max_length=100, verbose_name='经费项目名称'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='has_exception',
            field=models.BooleanField(default=False, help_text='验收过程中是否发现异常', verbose_name='存在异常'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_avg_price',
            field=models.DecimalField(decimal_places=2, default=0, help_text='同部门同物品的历史平均单价', max_digits=12, verbose_name='历史平均单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_max_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='历史最高单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_min_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='历史最低单价'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='history_purchase_count',
            field=models.PositiveIntegerField(default=0, help_text='同部门同物品的历史采购次数', verbose_name='历史采购次数'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='internal_approval',
            field=models.BooleanField(default=False, help_text='内部财务审批状态', verbose_name='内部审批状态'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='item_category',
            field=models.CharField(help_text='用户手动输入', max_length=50, verbose_name='物品种类'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='item_name',
            field=models.CharField(db_index=True, max_length=100, verbose_name='物品名称'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='item_photo',
            field=models.ImageField(blank=True, null=True, upload_to='items/%Y/%m/', verbose_name='物品照片'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='payee_account',
            field=models.CharField(blank=True, help_text='收款人银行账号', max_length=200, verbose_name='收款账号'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='payee_name',
            field=models.CharField(blank=True, help_text='收款人户名', max_length=100, verbose_name='收款人姓名'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='procurement_method',
            field=models.CharField(help_text='如：公开招标、询价采购等', max_length=50, verbose_name='采购方式'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='purchase_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='自动计算：实际单价 × 实际数量', max_digits=15, null=True, verbose_name='实际采购金额'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='purchase_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='采购完成时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='purchase_remark',
            field=models.TextField(blank=True, help_text='采购过程中的备注信息', verbose_name='采购备注'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='purchase_type',
            field=models.CharField(choices=[('unified', '统一采购'), ('self', '自行采购')], db_index=True, default='unified', max_length=20, verbose_name='采购类型'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='purchaser',
            field=models.ForeignKey(blank=True, help_text='负责采购的人员', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchased_requests', to=settings.AUTH_USER_MODEL, verbose_name='采购人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='quantity_difference',
            field=models.IntegerField(default=0, help_text='验收数量 - 采购数量', verbose_name='数量差异'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='reimbursement_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='结算完成时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='reimbursement_generated_date',
            field=models.DateTimeField(blank=True, help_text='验收完成后生成报销单的时间', null=True, verbose_name='报销单生成时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='reimbursement_remarks',
            field=models.TextField(blank=True, help_text='结算过程中的备注', verbose_name='结算备注'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='reimbursement_voucher_no',
            field=models.CharField(blank=True, help_text='财务系统凭证号', max_length=50, verbose_name='报销凭证号'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='reimburser',
            field=models.ForeignKey(blank=True, help_text='负责结算的财务人员', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reimbursed_requests', to=settings.AUTH_USER_MODEL, verbose_name='结算人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='remarks',
            field=models.TextField(blank=True, help_text='补充说明信息', verbose_name='需求备注'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='requester',
            field=models.ForeignKey(help_text='需求提报人', on_delete=django.db.models.deletion.CASCADE, related_name='purchase_requests', to=settings.AUTH_USER_MODEL, verbose_name='申请人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='requirement_source',
            field=models.CharField(help_text='需求产生的原因或背景', max_length=100, verbose_name='需求来源'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='return_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='退回时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='return_reason',
            field=models.TextField(blank=True, help_text='退回到申请人的原因', verbose_name='退回理由'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='returner',
            field=models.ForeignKey(blank=True, help_text='执行退回操作的人员', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='returned_requests', to=settings.AUTH_USER_MODEL, verbose_name='退回人'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='settlement_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='最终结算金额', max_digits=15, null=True, verbose_name='结算金额'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='settlement_exception_reason',
            field=models.TextField(blank=True, help_text='结算过程中的异常情况', verbose_name='结算异常原因'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='settlement_remark',
            field=models.TextField(blank=True, help_text='结算相关的补充说明', verbose_name='结算说明'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='shipping_date',
            field=models.DateTimeField(blank=True, help_text='采购完成后的发货通知时间', null=True, verbose_name='发货通知时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='spec',
            field=models.CharField(blank=True, max_length=200, verbose_name='规格型号'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='submission_date',
            field=models.DateTimeField(blank=True, help_text='提交审批的时间', null=True, verbose_name='提交时间'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='supplier',
            field=models.CharField(blank=True, help_text='实际供应商', max_length=200, verbose_name='供应商名称'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='tracking_number',
            field=models.CharField(blank=True, help_text='物流跟踪号码', max_length=100, verbose_name='快递单号'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='transaction_number',
            field=models.CharField(blank=True, help_text='银行或支付系统的交易流水号', max_length=50, verbose_name='交易流水号'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='unit',
            field=models.CharField(help_text='如：个、台、套等', max_length=20, verbose_name='单位'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='最后更新时间'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['approved_at'], name='idx_purchase_approved'),
        ),
    ]
