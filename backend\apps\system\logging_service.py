"""
日志服务模块
用于记录系统运行过程中的各类事件和操作
为系统维护、故障排查、安全审计提供依据
"""
import json
import logging
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import SystemLog

User = get_user_model()
logger = logging.getLogger(__name__)


class LoggingService:
    """日志服务类"""
    
    @staticmethod
    def log_user_action(user, log_type, action, target_model=None, target_id=None, 
                       ip_address=None, user_agent=None, request_data=None, 
                       response_data=None, extra_data=None):
        """
        记录用户操作日志
        
        Args:
            user: 操作用户（User对象或用户ID）
            log_type: 日志类型
            action: 操作描述
            target_model: 目标模型名称
            target_id: 目标对象ID
            ip_address: IP地址
            user_agent: 用户代理
            request_data: 请求数据
            response_data: 响应数据
            extra_data: 额外数据
        """
        try:
            # 处理用户对象
            if isinstance(user, int):
                user_obj = User.objects.get(id=user)
                username = user_obj.username
            elif isinstance(user, str):
                user_obj = User.objects.get(username=user)
                username = user
            else:
                user_obj = user
                username = user.username if user else 'system'
            
            # 创建日志记录
            log_entry = SystemLog.objects.create(
                user=user_obj,
                username=username,
                log_type=log_type,
                action=action,
                target_model=target_model,
                target_id=str(target_id) if target_id else None,
                ip_address=ip_address,
                user_agent=user_agent,
                request_data=request_data or {},
                response_data=response_data or {}
            )
            
            # 记录额外数据
            if extra_data:
                log_entry.request_data.update(extra_data)
                log_entry.save()
            
            logger.info(f"用户操作日志记录成功: {log_entry.id} - {action}")
            return log_entry
            
        except Exception as e:
            logger.error(f"记录用户操作日志失败: {e}")
            return None
    
    @staticmethod
    def log_system_event(event_type, event_description, level='INFO', 
                        component=None, error_details=None, extra_data=None):
        """
        记录系统事件日志
        
        Args:
            event_type: 事件类型
            event_description: 事件描述
            level: 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
            component: 系统组件
            error_details: 错误详情
            extra_data: 额外数据
        """
        try:
            log_entry = SystemLog.objects.create(
                user=None,
                username='system',
                log_type='system',
                action=event_description,
                target_model=component,
                request_data={
                    'event_type': event_type,
                    'level': level,
                    'error_details': error_details,
                    'extra_data': extra_data or {}
                }
            )
            
            logger.info(f"系统事件日志记录成功: {log_entry.id} - {event_description}")
            return log_entry
            
        except Exception as e:
            logger.error(f"记录系统事件日志失败: {e}")
            return None
    
    @staticmethod
    def log_security_event(user, event_type, event_description, ip_address=None, 
                          user_agent=None, risk_level='MEDIUM', extra_data=None):
        """
        记录安全事件日志
        
        Args:
            user: 相关用户
            event_type: 事件类型（login_success, login_failed, permission_denied等）
            event_description: 事件描述
            ip_address: IP地址
            user_agent: 用户代理
            risk_level: 风险级别（LOW, MEDIUM, HIGH, CRITICAL）
            extra_data: 额外数据
        """
        try:
            # 处理用户对象
            if isinstance(user, int):
                try:
                    user_obj = User.objects.get(id=user)
                    username = user_obj.username
                except User.DoesNotExist:
                    user_obj = None
                    username = f'user_id_{user}'
            elif isinstance(user, str):
                try:
                    user_obj = User.objects.get(username=user)
                    username = user
                except User.DoesNotExist:
                    user_obj = None
                    username = user
            else:
                user_obj = user
                username = user.username if user else 'anonymous'
            
            log_entry = SystemLog.objects.create(
                user=user_obj,
                username=username,
                log_type='security',
                action=event_description,
                target_model='SecurityEvent',
                ip_address=ip_address,
                user_agent=user_agent,
                request_data={
                    'event_type': event_type,
                    'risk_level': risk_level,
                    'extra_data': extra_data or {}
                }
            )
            
            logger.warning(f"安全事件日志记录: {log_entry.id} - {event_description}")
            return log_entry
            
        except Exception as e:
            logger.error(f"记录安全事件日志失败: {e}")
            return None
    
    @staticmethod
    def log_business_operation(user, operation_type, operation_description, 
                             business_object=None, object_id=None, 
                             before_data=None, after_data=None, extra_data=None):
        """
        记录业务操作日志
        
        Args:
            user: 操作用户
            operation_type: 操作类型
            operation_description: 操作描述
            business_object: 业务对象类型
            object_id: 对象ID
            before_data: 操作前数据
            after_data: 操作后数据
            extra_data: 额外数据
        """
        try:
            # 处理用户对象
            if isinstance(user, int):
                user_obj = User.objects.get(id=user)
                username = user_obj.username
            elif isinstance(user, str):
                user_obj = User.objects.get(username=user)
                username = user
            else:
                user_obj = user
                username = user.username if user else 'system'
            
            # 从before_data中提取IP地址和用户代理信息
            ip_address = None
            user_agent = None
            if before_data:
                ip_address = before_data.get('ip_address')
                user_agent = before_data.get('user_agent')
                # 如果没有从before_data获取到，尝试从请求上下文获取
                if not ip_address or not user_agent:
                    try:
                        from .request_context import RequestContext
                        ip_address = ip_address or RequestContext.get_client_ip()
                        user_agent = user_agent or RequestContext.get_user_agent()
                    except Exception:
                        pass

            log_entry = SystemLog.objects.create(
                user=user_obj,
                username=username,
                log_type=operation_type,
                action=operation_description,
                target_model=business_object,
                target_id=str(object_id) if object_id else None,
                ip_address=ip_address,
                user_agent=user_agent,
                request_data={
                    'before_data': before_data or {},
                    'extra_data': extra_data or {}
                },
                response_data={
                    'after_data': after_data or {}
                }
            )
            
            logger.info(f"业务操作日志记录成功: {log_entry.id} - {operation_description}")
            return log_entry
            
        except Exception as e:
            logger.error(f"记录业务操作日志失败: {e}")
            return None
    
    @staticmethod
    def log_api_request(user, method, url, request_data=None, response_data=None, 
                       status_code=None, response_time=None, ip_address=None, 
                       user_agent=None):
        """
        记录API请求日志
        
        Args:
            user: 请求用户
            method: HTTP方法
            url: 请求URL
            request_data: 请求数据
            response_data: 响应数据
            status_code: 响应状态码
            response_time: 响应时间（毫秒）
            ip_address: IP地址
            user_agent: 用户代理
        """
        try:
            # 处理用户对象
            if isinstance(user, int):
                user_obj = User.objects.get(id=user)
                username = user_obj.username
            elif isinstance(user, str):
                user_obj = User.objects.get(username=user)
                username = user
            else:
                user_obj = user
                username = user.username if user else 'anonymous'
            
            log_entry = SystemLog.objects.create(
                user=user_obj,
                username=username,
                log_type='api',
                action=f'{method} {url}',
                target_model='APIRequest',
                ip_address=ip_address,
                user_agent=user_agent,
                request_data={
                    'method': method,
                    'url': url,
                    'request_data': request_data or {},
                    'response_time': response_time
                },
                response_data={
                    'status_code': status_code,
                    'response_data': response_data or {}
                }
            )
            
            # 只在调试模式下记录详细API日志
            if settings.DEBUG:
                logger.debug(f"API请求日志记录: {log_entry.id} - {method} {url}")
            
            return log_entry
            
        except Exception as e:
            logger.error(f"记录API请求日志失败: {e}")
            return None


# 便捷函数
def log_user_login(user, ip_address=None, user_agent=None, success=True):
    """用户登录日志"""
    if success:
        return LoggingService.log_security_event(
            user=user,
            event_type='login_success',
            event_description=f'用户 {user.username} 登录成功',
            ip_address=ip_address,
            user_agent=user_agent,
            risk_level='LOW'
        )
    else:
        return LoggingService.log_security_event(
            user=user,
            event_type='login_failed',
            event_description=f'用户 {user.username} 登录失败',
            ip_address=ip_address,
            user_agent=user_agent,
            risk_level='MEDIUM'
        )

def log_user_logout(user, ip_address=None):
    """用户登出日志"""
    return LoggingService.log_security_event(
        user=user,
        event_type='logout',
        event_description=f'用户 {user.username} 登出',
        ip_address=ip_address,
        risk_level='LOW'
    )

def log_permission_denied(user, resource, ip_address=None):
    """权限拒绝日志"""
    return LoggingService.log_security_event(
        user=user,
        event_type='permission_denied',
        event_description=f'用户 {user.username} 访问 {resource} 被拒绝',
        ip_address=ip_address,
        risk_level='HIGH'
    )

def log_data_export(user, export_type, record_count, ip_address=None):
    """数据导出日志"""
    return LoggingService.log_user_action(
        user=user,
        log_type='export',
        action=f'导出{export_type}数据，共{record_count}条记录',
        target_model='DataExport',
        ip_address=ip_address,
        extra_data={'export_type': export_type, 'record_count': record_count}
    )

def log_system_error(error_message, component=None, error_details=None):
    """系统错误日志"""
    return LoggingService.log_system_event(
        event_type='system_error',
        event_description=error_message,
        level='ERROR',
        component=component,
        error_details=error_details
    )
