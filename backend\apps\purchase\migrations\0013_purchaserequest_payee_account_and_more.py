# Generated by Django 5.2.3 on 2025-06-17 17:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0012_purchaserequest_acceptance_remark'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaserequest',
            name='payee_account',
            field=models.CharField(blank=True, max_length=200, verbose_name='收款账户'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='payee_name',
            field=models.CharField(blank=True, max_length=100, verbose_name='收款人'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='settlement_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='实际结算金额'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='settlement_exception_reason',
            field=models.TextField(blank=True, verbose_name='结算异常原因'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='settlement_method',
            field=models.CharField(blank=True, max_length=50, verbose_name='结算方式'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='settlement_remark',
            field=models.TextField(blank=True, verbose_name='结算备注'),
        ),
    ]
