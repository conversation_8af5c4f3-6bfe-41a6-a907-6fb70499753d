import api from '@/api'
import { message } from 'ant-design-vue'

// 获取字典类型编码 - 直接使用传入的类型，不再进行映射转换
const getDictTypeCode = (type) => {
  return type;
};

// 创建dict模块
export default {
  namespaced: true,
  state: {
    data: {},
    loading: false,
    error: null
  },
  mutations: {
    SET_DICT_DATA(state, payload) {
      // 处理两种情况：单个类型数据和所有类型数据
      if (typeof payload.type === 'string') {
        // 单个类型数据
        state.data[payload.type] = payload.items;
      } else if (typeof payload === 'object' && !Array.isArray(payload)) {
        // 所有类型数据
        state.data = payload;
      }
    },
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    SET_ERROR(state, error) {
      state.error = error;
    }
  },
  actions: {
    // 获取指定类型的数据字典
    async getDict({ commit }, type) {
      try {
        commit('SET_LOADING', true);
        // 将中文类型名称转换为英文编码
        const typeCode = getDictTypeCode(type);

        const response = await api.dicts.getDict(typeCode);

        if (response.code === 200) {
          const items = response.data.items || [];
          commit('SET_DICT_DATA', { type, items });
          commit('SET_LOADING', false);
          return items;
        } else {
          throw new Error(response.message || '获取数据字典失败');
        }
      } catch (error) {
        commit('SET_ERROR', error);
        commit('SET_LOADING', false);
        throw error;
      }
    },

    // 获取所有数据字典
    async getAllDicts({ commit }) {
      try {
        commit('SET_LOADING', true);
        const response = await api.dicts.getAllDicts();

        if (response.code === 200) {
          // 将数据按类型存储
          const dictData = {};
          if (response.data.types && response.data.items) {
            response.data.types.forEach(type => {
              dictData[type] = response.data.items.filter(item => item.type === type);
            });
          }

          commit('SET_DICT_DATA', dictData);
          commit('SET_LOADING', false);
          return dictData;
        } else {
          throw new Error(response.message || '获取数据字典失败');
        }
      } catch (error) {
        commit('SET_ERROR', error);
        commit('SET_LOADING', false);
        throw error;
      }
    },

    // 创建数据字典项
    async createDictItem({ commit }, { type, data }) {
      try {
        commit('SET_LOADING', true);
        const response = await api.dicts.create({ ...data, type });
        commit('SET_LOADING', false);

        if (response.code === 200) {
          message.success(`新增${type}成功`);
          return response.data;
        } else {
          throw new Error(response.message || '创建失败');
        }
      } catch (error) {
        commit('SET_ERROR', error);
        commit('SET_LOADING', false);
        message.error('创建数据字典项失败');
        throw error;
      }
    },

    // 更新数据字典项
    async updateDictItem({ commit }, { id, data }) {
      try {
        commit('SET_LOADING', true);
        const response = await api.dicts.update(id, data);
        commit('SET_LOADING', false);

        if (response.code === 200) {
          message.success('数据字典项更新成功');
          return response.data;
        } else {
          throw new Error(response.message || '更新失败');
        }
      } catch (error) {
        commit('SET_ERROR', error);
        commit('SET_LOADING', false);
        message.error('更新数据字典项失败');
        throw error;
      }
    },

    // 删除数据字典项
    async deleteDictItem({ commit }, id) {
      try {
        commit('SET_LOADING', true);
        const response = await api.dicts.delete(id);
        commit('SET_LOADING', false);

        if (response.code === 200) {
          message.success('数据字典项删除成功');
          return response.data;
        } else {
          throw new Error(response.message || '删除失败');
        }
      } catch (error) {
        commit('SET_ERROR', error);
        commit('SET_LOADING', false);
        message.error('删除数据字典项失败');
        throw error;
      }
    }
  },
  getters: {
    // 获取指定类型的数据字典
    getDict: (state) => (type) => {
      return state.data[type] || [];
    },
    // 获取所有数据字典
    getAllDicts: (state) => {
      return state.data;
    },
    // 获取加载状态
    isLoading: (state) => {
      return state.loading;
    },
    // 获取错误信息
    getError: (state) => {
      return state.error;
    }
  }
};