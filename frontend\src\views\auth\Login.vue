<template>
  <div class="login-container">
    <div class="login-form">
      <div class="login-header">
        <h2>采购管理系统</h2>
        <p>企业采购流程管理平台</p>
      </div>

      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        @finish="handleSubmit"
      >
        <a-form-item label="用户名" name="username">
          <a-input
            v-model:value="formData.username"
            placeholder="请输入用户名"
            size="large"
            :prefix="h(UserOutlined)"
          />
        </a-form-item>

        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="formData.password"
            placeholder="请输入密码"
            size="large"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="loading"
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>

      <div class="login-footer">
        <p>默认管理员账号：admin / admin123</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import api from '@/api'

export default {
  name: 'LoginView',
  setup() {
    const router = useRouter()
    const store = useStore()
    const formRef = ref()
    const loading = ref(false)

    // 表单数据
    const formData = reactive({
      username: '',
      password: ''
    })

    // 表单验证规则
    const formRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ]
    }

    // 提交登录
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        loading.value = true

        const response = await api.auth.login(formData.username, formData.password)

        if (response.code === 200) {
          const { token, user } = response.data

          // 保存token
          localStorage.setItem('token', token)

          // 保存用户信息到store
          store.commit('SET_USER', user)
          store.commit('SET_TOKEN', token)

          // 登录成功后立即获取用户权限
          try {
            await store.dispatch('getUserPermissions')
            console.log('✅ 用户权限获取成功')
          } catch (error) {
            console.warn('⚠️ 获取用户权限失败:', error)
            // 权限获取失败不影响登录流程
          }

          message.success('登录成功')

          // 跳转到首页
          router.push('/dashboard')
        } else {
          message.error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        if (error.response?.status === 401) {
          message.error('用户名或密码错误')
        } else {
          message.error('登录失败，请稍后重试')
        }
      } finally {
        loading.value = false
      }
    }

    return {
      formRef,
      formData,
      formRules,
      loading,
      handleSubmit,
      h,
      UserOutlined,
      LockOutlined
    }
  }
}
</script>

<style scoped>
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 400px;
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 28px;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
}

.login-footer p {
  margin: 0;
  color: #8c8c8c;
  font-size: 12px;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #262626;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 8px;
}

:deep(.ant-btn) {
  border-radius: 8px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}
</style>

SELF
SELF
SELF
UNIFIED
UNIFIED
UNIFIED
UNIFIED