from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()

router.register(r'notifications', views.NotificationViewSet)
router.register(r'menus', views.MenuViewSet)

urlpatterns = [
    # 部门管理
    path('departments/', views.DepartmentListCreateView.as_view(), name='department-list-create'),
    path('departments/<int:pk>/', views.DepartmentDetailView.as_view(), name='department-detail'),
    path('departments/all/', views.department_all_view, name='department-all'),
    path('departments/tree/', views.department_tree_view, name='department-tree'),
    path('departments/statistics/', views.department_statistics_view, name='department-statistics'),
    path('departments/export/', views.export_departments, name='department-export'),
    path('departments/import/', views.import_departments, name='department-import'),
    path('departments/template/', views.download_department_template, name='department-template'),

    # 数据字典管理
    path('dicts/', views.DictDataListCreateView.as_view(), name='dict-list-create'),
    path('dicts/<int:pk>/', views.DictDataDetailView.as_view(), name='dict-detail'),
    path('dicts/statistics/', views.dict_statistics_view, name='dict-statistics'),
    path('dicts/export/', views.export_dicts, name='dict-export'),
    path('dicts/import/', views.import_dicts, name='dict-import'),
    path('dicts/template/', views.download_dict_template, name='dict-template'),
    path('dicts/<str:dict_type>/', views.dict_by_type_view, name='dict-by-type'),



    # 通知管理
    path('notifications/create/', views.create_notification, name='notification-create'),
    path('notifications/<int:pk>/read/', views.mark_notification_read, name='notification-read'),
    path('notifications/read-all/', views.mark_all_notifications_read, name='notifications-read-all'),
    path('notifications/count/', views.notification_count, name='notification-count'),
    path('notifications/statistics/', views.notification_statistics, name='notification-statistics'),

    # 日志管理
    path('logs/', views.get_system_logs, name='get-system-logs'),
    path('logs/statistics/', views.get_log_statistics, name='get-log-statistics'),
    path('logs/export/', views.export_system_logs, name='export-system-logs'),
    path('logs/batch-export/', views.batch_export_system_logs, name='batch-export-system-logs'),

    path('', include(router.urls)),
]
