# Generated manually on 2025-07-26 22:30

from django.db import migrations, models


def migrate_menu_types(apps, schema_editor):
    """迁移现有菜单类型数据"""
    Menu = apps.get_model('system', 'Menu')
    
    # 更新所有菜单的类型
    updated_count = 0
    for menu in Menu.objects.all():
        old_type = menu.menu_type
        if menu.menu_type == 'button':
            # 按钮类型保持不变
            continue
        elif menu.component_path:
            # 有组件路径的是页面
            menu.menu_type = 'page'
        else:
            # 没有组件路径的是目录
            menu.menu_type = 'directory'
        
        if old_type != menu.menu_type:
            menu.save()
            updated_count += 1
            print(f"✅ 更新菜单 '{menu.menu_name}': {old_type} -> {menu.menu_type}")
    
    print(f"✅ 菜单类型迁移完成，共更新 {updated_count} 个菜单")


def reverse_migrate_menu_types(apps, schema_editor):
    """回滚菜单类型迁移"""
    Menu = apps.get_model('system', 'Menu')
    
    # 将所有非按钮类型的菜单改回 'menu'
    updated_count = 0
    for menu in Menu.objects.all():
        if menu.menu_type in ['directory', 'page']:
            menu.menu_type = 'menu'
            menu.save()
            updated_count += 1
    
    print(f"✅ 菜单类型回滚完成，共回滚 {updated_count} 个菜单")


class Migration(migrations.Migration):

    dependencies = [
        ("system", "0020_remove_permission_menu_and_more"),
    ]

    operations = [
        # 修改菜单类型字段的选择项
        migrations.AlterField(
            model_name="menu",
            name="menu_type",
            field=models.CharField(
                choices=[("directory", "目录"), ("page", "页面"), ("button", "按钮")],
                default="page",
                max_length=10,
                verbose_name="菜单类型",
            ),
        ),
        # 迁移现有数据
        migrations.RunPython(migrate_menu_types, reverse_migrate_menu_types),
    ]
