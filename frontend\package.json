{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 8080", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "^7.0.0", "ant-design-vue": "^4.0.0", "axios": "^1.9.0", "compression-webpack-plugin": "^11.1.0", "core-js": "^3.8.3", "dayjs": "^1.11.10", "echarts": "^5.6.0", "file-type": "^17.0.3", "jszip": "^3.10.1", "vue": "^3.2.13", "vue-router": "^4.5.1", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true, "vue/setup-compiler-macros": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false}, "rules": {"vue/multi-word-component-names": "off", "no-unused-vars": "warn", "vue/no-v-model-argument": "off"}, "globals": {"defineProps": "readonly", "defineEmits": "readonly", "defineExpose": "readonly", "withDefaults": "readonly"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}